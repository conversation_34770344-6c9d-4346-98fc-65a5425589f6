# 数据库配置
spring:
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: ************************************************************************************************************
          username: dev2
          password: xianmu619
          driver-class-name: com.mysql.cj.jdbc.Driver
        offline:
          url: *****************************************************************************************************************************************
          username: test
          password: xianmu619
          driver-class-name: com.mysql.cj.jdbc.Driver
#    username: test
#    password: xianmu619
#    #?serverTimezone=UTC解决时区的报错
#    url: *******************************************************************************************************************************************************
    # mysql5 的驱动是 com.mysql.jdbc.Driver, mysql8的驱动是 com.mysql.cj.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 自定义数据源
    type: com.alibaba.druid.pool.DruidDataSource
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 80
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    offline:
      username: test
      password: xianmu619
      url: *****************************************************************************************************************************************
  schedulerx2:
    enabled: false
    endpoint: acm.aliyun.com
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
    groupId: saas-manage
    appKey: 71EHy1agw53QVmu/8OYYFw==
  # redis配置
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 6000
    database: 0
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 5
        max-idle: 5
        min-idle: 5
        max-wait: 5
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
saasmall:
  api-host: http://cosfo-mall-svc

redisson:
  address: test-redis.summerfarm.net:6379
  password: xianmu619
  type: STANDALONE
  enabled: true
  database: 0

summerfarm:
  mall:
    api-host: http://mall-svc/
  api-host: http://manage-svc/


rocketmq:
  #name-server: **************:9876
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    enable-msg-trace: off
    group: GID_saas-manage
    send-message-timeout: 10000
    access-key: Rocketmq
    secret-key: Rocketmq
  consumer:
    access-key: Rocketmq
    secret-key: Rocketmq

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

tenant:
  # 是否开启租户模式
  enable: true
  # 需要排除的多租户的表
  exclusionTable:
    - "system_parameters"
    - "area_city_group"
    - "area_city_group_mapping"
    - "brand_category_mapping"
    - "category"
    - "common_location_city"
    - "file_download_record"
    - "location_province"
    - "order_self_lifting"
    - "product_pricing_supply_city_mapping"
    - "sms_scene"
    - "supplier_delivery_info"
    - "system_admin"
    - "system_admin_role"
    - "system_menu"
    - "system_parameters"
    - "system_permission"
    - "system_role"
    - "system_role_menu"
    - "system_role_permission"
    - "tenant"
    - "tenant_agreement"
    - "user"
    - "wechat_authorizer"
    - "wechat_lite_config"
    - "wechat_template_package"
    - "brand"
    - "administrative_division"
    - "common_location"
    - "market_detail"
    - "market_item_detail"
    - "msg_scene"
    - "system_wechant_tp_info"
    - "product_city_stock"
    - "product_sku_order_summary"
    - "tenant_account_supplier_mapping"
    - "tenant_measure_item"
    - "trans_log_result_list"
    - "xm_contact_mapping"
    - "hui_fu_mock_refund"
    - "hui_fu_mock_transaction_summary"
    - "hui_fu_mock_account"
    - "hui_fu_mock_payment"
    - "tenant_function_set"
    - "tenant_guide_info"
  # 租户字段名称
  column: tenant_id

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    # address: nacos://127.0.0.1:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
  protocol:
    id: dubbo
    name: dubbo
    port: 20881
  provider:
    version: 1.0.0
    group: mon
    timeout: 5000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    timeout: 10000
    check: false
tp:
  appId: wx85a9fed1e711916e

huifu:
  sys_id: ****************
  product_id: PAYUN
  channel_no: ********
  private_key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIkLjvPSkZhis7K3oFStvyrs0UXEADzLeDD5rb5j7VAVzeRA3Fx0kMBqtD+WEnqxGi0H3tM+mGRSNuc/ImF+3Iz9xAuwJCOS5g9ZDwQvwmvG9YrzDvcK1u957U53ukT3BHYt+WXZ46/Iyqsq7pSgLo/EzbMf8IfuqZqIKWHsx1YFAgMBAAECgYAMD8BdJUM7LjSynga2bTROCtnAUifTMfU6Gj94akMQsVqVpD/Aw2GaDcofbo3hzoSHQhISdYfkDHhYke3stsWiYgoDK2Cqow3BtocGSGePwFprJXWQJfKBO1ADb4zEka6q3zo9lcxsCqa+fx1G3uLIJNin3QWqOLXquo0GXOgEAQJBAMd/WNIeqKi/MNv5MtpiyIlGxOmdH7NPn7oW0exEzFeWsPLsl+******************************+XXhKmUCQQCv3BRFLKAH9aivHcVJZaqzd5VmFaGSWeZkiBLBa1i8ZneQv4rc1/p8M5Rvg2WHY+JTx7KxqygyahN3teqvx/MhAkEAruCSErbvb+URRnL/QfLACZ4wtPyYMk4FHVItuKhiXBFrkbcWOE/P0ashKEWsmZp45ufvviglR08LGbEJe2zjBQJALyQvyttLitavgUHZwPMf7zv/MH5b8X9n40sWvAKqptZQ9txhvRGoc+Lfx4TRkpmT8iF2JWpcPCdzUIPThYt0AQJAVIJZBYLsGAcmdhs22OCL0J62FVoxcYFBAKXA2en33goOpF/idpWUo3w++eZGAFqdLh5TKyJCj5xOgkVuYFuUAA==
server:
  port: 8080

xm:
  log:
    biz:
      enable: true
#xm:
#  oss:
#    persistent-storage:
#      bucketName: test-app-perm
#      endpoint: oss-cn-hangzhou.aliyuncs.com
#      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
#      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
#      accessKeySecret: ******************************
#      domain: devossperm.summerfarm.net
#    temporary-storage:
#      bucketName: test-app-temp
#      endpoint: oss-cn-hangzhou.aliyuncs.com
#      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
#      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
#      accessKeySecret: ******************************
#      domain: devosstemp.summerfarm.net

H5:
  mall:
    url: https://devmall.confo.cn#/pages/loading/index?token=

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 19b82444-16f9-4d22-a522-b7ac6495c954
spring:
  application:
    name: cosfo-manage
  profiles:
    active: dev2
#logging:
#  level:
#    root: info
#    org.springframework: INFO
#    org.mybatis: INFO
#    com.cosfo.manage: INFO
  pattern:
    console: "%d - %msg%n"
server:
  port: 8081
# 日志文件路径
log-path: ${APP_LOG_DIR:./log}
#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: true  # 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据
  supportMethodsArguments: true
  params: count=countSql

# 七牛云配置
qiniu:
  ACCESS_KEY: D7qgaXDWTIv0ormcO8IozO1RyT7P1YLP-y3tObIJ
  SECRET_KEY: ZLVTkrc7cwZlyNhLVieyeBwJ-UD_0p4Co9ZdSu3T
  bucketname: cosfo
# 汇付配置
huifu:
  sys_id: 6666000125128386
  product_id: PAYUN

xm:
  log:
    enable: true
    resp: true
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
  downloadcenter:
    consumer_group_suffix: cosfo-manage
    # tag前缀为tag_
    consumer_tag_suffix: cosfo-manage

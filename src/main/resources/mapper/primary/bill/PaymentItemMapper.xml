<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.PaymentItemMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.po.PaymentItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="payment_id" jdbcType="BIGINT" property="paymentId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_price" jdbcType="DECIMAL" property="orderPrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="payment_receipt" jdbcType="VARCHAR" property="paymentReceipt" />
    <result column="financial_receipt" jdbcType="VARCHAR" property="financialReceipt" />
    <result column="receipt_date" jdbcType="DATE" property="receiptDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="offline_pay_type" jdbcType="VARCHAR" property="offlinePayType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, payment_id, order_id, order_price, create_time, payment_receipt, financial_receipt, receipt_date, remark, offline_pay_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from payment_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from payment_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.PaymentItem" useGeneratedKeys="true">
    insert into payment_item (tenant_id, payment_id, order_id, 
      order_price, create_time)
    values (#{tenantId,jdbcType=BIGINT}, #{paymentId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, 
      #{orderPrice,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.PaymentItem" useGeneratedKeys="true">
    insert into payment_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.bill.model.po.PaymentItem">
    update payment_item
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.bill.model.po.PaymentItem">
    update payment_item
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      payment_id = #{paymentId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      order_price = #{orderPrice,jdbcType=DECIMAL},
      payment_receipt = #{paymentReceipt,jdbcType=VARCHAR},
      financial_receipt = #{financialReceipt,jdbcType=VARCHAR},
      receipt_date = #{receiptDate,jdbcType=DATE},
      remark = #{remark,jdbcType=VARCHAR},
      offline_pay_type = #{offlinePayType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByOrderId" resultMap="BaseResultMap">
    select pi.id, pi.tenant_id, payment_id, order_id, order_price, pi.create_time,
       pi.payment_receipt ,
       pi.financial_receipt,
       pi.receipt_date,
       pi.remark,
       pi.offline_pay_type
    from payment_item pi
           left join payment p on pi.payment_id = p.id
    where p.status = 1 and pi.tenant_id = #{tenantId} and pi.order_id = #{orderId}
  </select>
  <select id="selectByPaymentId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from payment_item
    where payment_id = #{paymentId}
  </select>

  <select id="batchQueryByPaymentIds" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from payment_item
    where payment_id in
    <foreach collection="paymentIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectPaySuccessByOrderId" resultMap="BaseResultMap">
    select pi.id, pi.tenant_id, payment_id, order_id, order_price, pi.create_time
    from payment_item pi
           left join payment p on pi.payment_id = p.id
    where p.status = 1 and pi.tenant_id = #{tenantId} and pi.order_id = #{orderId}
  </select>
  <select id="selectItemByOrderId" resultType="com.cosfo.manage.bill.model.po.PaymentItem">
    select <include refid="Base_Column_List"/> from payment_item
    where order_id = #{orderId} and tenant_id = #{tenantId}
  </select>
    <select id="selectByOrderIds" resultType="com.cosfo.manage.bill.model.po.PaymentItem">
      select <include refid="Base_Column_List"/> from payment_item
      where order_id in
      <foreach collection="orderIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach> and tenant_id = #{tenantId}
    </select>
</mapper>
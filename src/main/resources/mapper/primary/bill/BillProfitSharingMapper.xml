<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.BillProfitSharingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.po.BillProfitSharing">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="receiver_tenant_id" jdbcType="BIGINT" property="receiverTenantId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="transaction_id" jdbcType="VARCHAR" property="transactionId" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="success_time" jdbcType="TIMESTAMP" property="successTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="bankOrderId" jdbcType="VARCHAR" property="wxOrderId" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="detail_id" jdbcType="VARCHAR" property="detailId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="business_type" property="businessType"/>
    <result column="after_sale_id" property="afterSaleId"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, receiver_tenant_id, order_id, app_id, `type`, account, transaction_id, out_trade_no, price,
    `status`, success_time, description, wx_order_id bankOrderId, fail_reason, detail_id, create_time,
    update_time, business_type, after_sale_id, account_type accountType
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_profit_sharing
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="listAll" resultType="com.cosfo.manage.bill.model.dto.BillProfitSharingDTO">
    select <include refid="Base_Column_List" /> from bill_profit_sharing
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="orderId != null">
        and order_id = #{orderId}
      </if>
      <if test="orderIds != null and orderIds.size() != 0">
        and order_id in
        <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="startTime != null and endTime != null ">
<!--        and create_time between #{startTime} and #{endTime}-->
        and create_time between date_format(#{startTime},'%Y-%m-%d 00:00:00') and date_format(#{endTime},'%Y-%m-%d 23:59:59')
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="transactionId != null">
        and transaction_id like concat('%',#{transactionId},'%')
      </if>
      <if test="outTradeNo != null">
        and out_trade_no = #{outTradeNo}
      </if>
      <if test="bankOrderId != null">
        and wx_order_id like concat('%',#{bankOrderId},'%')
      </if>
      <if test="receiverTenantIds != null and receiverTenantIds.size() != 0">
        and receiver_tenant_id in
        <foreach collection="receiverTenantIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and receiver_tenant_id is not null
    </where>
    order by id desc
  </select>

  <select id="exportListAll" resultType="com.cosfo.manage.bill.model.dto.BillProfitSharingDTO" fetchSize="1000">
    select <include refid="Base_Column_List" /> from bill_profit_sharing
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="startTime != null and endTime != null ">
        and create_time between date_format(#{startTime},'%Y-%m-%d 00:00:00') and date_format(#{endTime},'%Y-%m-%d 23:59:59')
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="transactionId != null">
        and transaction_id like concat('%',#{transactionId},'%')
      </if>
      <if test="outTradeNo != null">
        and out_trade_no = #{outTradeNo}
      </if>
      <if test="bankOrderId != null">
        and wx_order_id like concat('%',#{bankOrderId},'%')
      </if>
      <if test="orderIds != null">
        <choose>
          <when test="orderIds.size > 0">
            and order_id in
            <foreach item="orderId" index="index" collection="orderIds" open="(" separator="," close=")">
              #{orderId}
            </foreach>
          </when>
          <otherwise>
            and 1 = 0
          </otherwise>
        </choose>
      </if>
      and receiver_tenant_id is not null
    </where>
    order by id desc
  </select>

  <select id="selectByOrderId" resultType="com.cosfo.manage.bill.model.dto.BillProfitSharingDTO">
    select
    <include refid="Base_Column_List" />
    from bill_profit_sharing
    where order_id = #{orderId,jdbcType=BIGINT} and tenant_id = #{tenantId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_profit_sharing
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharing" useGeneratedKeys="true">
    insert into bill_profit_sharing (tenant_id, receiver_tenant_id, order_id, app_id,
      `type`, account, transaction_id, 
      out_trade_no, price, `status`, 
      success_time, description, wx_order_id, 
      fail_reason, detail_id, create_time, 
      update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{receiverTenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{transactionId,jdbcType=VARCHAR}, 
      #{outTradeNo,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, 
      #{successTime,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR}, #{wxOrderId,jdbcType=VARCHAR}, 
      #{failReason,jdbcType=VARCHAR}, #{detailId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharing" useGeneratedKeys="true">
    insert into bill_profit_sharing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="receiverTenantId != null">
        receiver_tenant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="transactionId != null">
        transaction_id,
      </if>
      <if test="outTradeNo != null">
        out_trade_no,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="successTime != null">
        success_time,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="wxOrderId != null">
        wx_order_id,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
      <if test="detailId != null">
        detail_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="receiverTenantId != null">
        #{receiverTenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null">
        #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="successTime != null">
        #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="wxOrderId != null">
        #{wxOrderId,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="detailId != null">
        #{detailId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharing">
    update bill_profit_sharing
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="receiverTenantId != null">
        receiver_tenant_id = #{receiverTenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null">
        transaction_id = #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        out_trade_no = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="successTime != null">
        success_time = #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="wxOrderId != null">
        wx_order_id = #{wxOrderId,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="detailId != null">
        detail_id = #{detailId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharing">
    update bill_profit_sharing
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      receiver_tenant_id = #{receiverTenantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR},
      transaction_id = #{transactionId,jdbcType=VARCHAR},
      out_trade_no = #{outTradeNo,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      success_time = #{successTime,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=VARCHAR},
      wx_order_id = #{wxOrderId,jdbcType=VARCHAR},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      detail_id = #{detailId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
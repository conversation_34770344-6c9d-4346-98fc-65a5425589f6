<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.BillProfitSharingOrderMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.po.BillProfitSharingOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="supplier_id" jdbcType="BIGINT" property="supplierId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="profit_sharing_type" jdbcType="TINYINT" property="profitSharingType" />
    <result column="profit_sharing_no" jdbcType="VARCHAR" property="profitSharingNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, order_id, `status`, create_time, update_time, supplier_id, profit_sharing_type, profit_sharing_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_profit_sharing_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_profit_sharing_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharingOrder" useGeneratedKeys="true">
    insert into bill_profit_sharing_order (tenant_id, order_id, `status`, 
      create_time, update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharingOrder" useGeneratedKeys="true">
    insert into bill_profit_sharing_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharingOrder">
    update bill_profit_sharing_order
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharingOrder">
    update bill_profit_sharing_order
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryProfitSharingOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_order
    where order_id in (SELECT
    id
    FROM `order`
    where warehouse_type = 1
    and tenant_id = #{tenantBillQueryDTO.tenantId}
    and status in (5,7)
    and supplier_tenant_id = #{tenantBillQueryDTO.supplyTenantId}
    and (DATE_FORMAT(finished_time ,'%Y-%m-%d') between DATE_FORMAT( #{tenantBillQueryDTO.startTime},'%Y-%m-%d') and DATE_FORMAT(#{tenantBillQueryDTO.endTime},'%Y-%m-%d')))
  </select>

  <select id="batchQueryByStatus" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_order
    where
    status = #{orderQuery.status}
    <if test="orderQuery.maxId != null">
      and id > #{orderQuery.maxId}
    </if>
    <if test="orderQuery.orderIds != null and orderQuery.orderIds.size > 0">
    and order_id in
      <foreach collection="orderQuery.orderIds" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    order by id
    limit #{orderQuery.batchSize}
  </select>

  <select id="queryByOrderIdAndTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_order
    where tenant_id = #{tenantId}
    and order_id = #{orderId}
  </select>

  <update id="updateStatusById">
    update bill_profit_sharing_order
    set status = #{finalStatus}
    where id = #{id} and status = #{orgStatus}
  </update>

  <select id="queryByTenantAndOrderAndSupplierId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from bill_profit_sharing_order
    <where>
      tenant_id = #{tenantId}
      and order_id = #{orderId}
      <if test="supplierId != null">
        and supplier_id = #{supplierId}
      </if>
    </where>
  </select>
</mapper>
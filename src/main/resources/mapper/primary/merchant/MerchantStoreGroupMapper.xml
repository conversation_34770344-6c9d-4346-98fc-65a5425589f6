<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantStoreGroupMapper">

    <select id="list" resultType="com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO">
        select
          id, `name`, create_time createTime, update_time updateTime, type
        from
        merchant_store_group
        <where>
            tenant_id = #{tenantId}
            <if test="id != null">
               and id = #{id}
            </if>
            <if test="name != null">
               and `name` like concat('%',#{name},'%')
            </if>
        </where>
        <if test="updateTimeSort != null">
            order by `type` desc, update_time ${updateTimeSort}
        </if>
        <if test="updateTimeSort == null and createTimeSort == null">
            order by `type` desc, id desc
        </if>
        <if test="createTimeSort != null">
            order by `type` desc, id ${createTimeSort}
        </if>
    </select>
    <select id="queryBatchByStoreIds" resultType="com.cosfo.manage.merchant.model.dto.MerchantStoreGroupInfoDTO">
        select m.store_id storeId, g.name merchantStoreGroupName, g.id merchantStoreGroupId
        from merchant_store_group g
        inner join merchant_store_group_mapping m on g.id = m.group_id
        where m.store_id in
            <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        and m.tenant_id = #{tenantId}
    </select>
</mapper>

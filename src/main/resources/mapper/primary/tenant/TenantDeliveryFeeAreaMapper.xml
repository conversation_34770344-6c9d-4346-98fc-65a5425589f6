<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantDeliveryFeeAreaMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="default_price" jdbcType="DECIMAL" property="defaultPrice" />
    <result column="rule" jdbcType="VARCHAR" property="rule" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, rule_id, province, city, area, default_price, `rule`, create_time, 
    update_time, group_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tenant_delivery_fee_area
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tenant_delivery_fee_area
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea" useGeneratedKeys="true">
    insert into tenant_delivery_fee_area (tenant_id, rule_id, province, 
      city, area, default_price, 
      `rule`, create_time, update_time, 
      group_id)
    values (#{tenantId,jdbcType=BIGINT}, #{ruleId,jdbcType=BIGINT}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{defaultPrice,jdbcType=DECIMAL}, 
      #{rule,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{groupId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea" useGeneratedKeys="true">
    insert into tenant_delivery_fee_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="defaultPrice != null">
        default_price,
      </if>
      <if test="rule != null">
        `rule`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="defaultPrice != null">
        #{defaultPrice,jdbcType=DECIMAL},
      </if>
      <if test="rule != null">
        #{rule,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea">
    update tenant_delivery_fee_area
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="defaultPrice != null">
        default_price = #{defaultPrice,jdbcType=DECIMAL},
      </if>
      <if test="rule != null">
        `rule` = #{rule,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea">
    update tenant_delivery_fee_area
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      rule_id = #{ruleId,jdbcType=BIGINT},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      default_price = #{defaultPrice,jdbcType=DECIMAL},
      `rule` = #{rule,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      group_id = #{groupId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
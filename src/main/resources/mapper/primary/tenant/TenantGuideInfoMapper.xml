<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantGuideInfoMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.TenantGuideInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="step_num" jdbcType="TINYINT" property="stepNum" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="select_item_ids" jdbcType="VARCHAR" property="selectItemIds" />
    <result column="select_item" jdbcType="VARCHAR" property="selectItem" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, phone, step_num, title, select_item_ids, select_item, create_time, 
    update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tenant_guide_info
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="selectByPhone" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tenant_guide_info
    where phone = #{phone}
  </select>

  <select id="selectByPhoneAndStepNum" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tenant_guide_info
    where phone = #{phone} and step_num = #{stepNum}
    limit 1
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tenant_guide_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantGuideInfo" useGeneratedKeys="true">
    insert into tenant_guide_info (tenant_id, phone, step_num, 
      title, select_item_ids, select_item, 
      create_time, update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{phone,jdbcType=VARCHAR}, #{stepNum,jdbcType=TINYINT}, 
      #{title,jdbcType=VARCHAR}, #{selectItemIds,jdbcType=VARCHAR}, #{selectItem,jdbcType=VARCHAR}, 
      now(), now())
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantGuideInfo" useGeneratedKeys="true">
    insert into tenant_guide_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="stepNum != null">
        step_num,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="selectItemIds != null">
        select_item_ids,
      </if>
      <if test="selectItem != null">
        select_item,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="stepNum != null">
        #{stepNum,jdbcType=TINYINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="selectItemIds != null">
        #{selectItemIds,jdbcType=VARCHAR},
      </if>
      <if test="selectItem != null">
        #{selectItem,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.tenant.model.po.TenantGuideInfo">
    update tenant_guide_info
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="stepNum != null">
        step_num = #{stepNum,jdbcType=TINYINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="selectItemIds != null">
        select_item_ids = #{selectItemIds,jdbcType=VARCHAR},
      </if>
      <if test="selectItem != null">
        select_item = #{selectItem,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.tenant.model.po.TenantGuideInfo">
    update tenant_guide_info
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      phone = #{phone,jdbcType=VARCHAR},
      step_num = #{stepNum,jdbcType=TINYINT},
      title = #{title,jdbcType=VARCHAR},
      select_item_ids = #{selectItemIds,jdbcType=VARCHAR},
      select_item = #{selectItem,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.TenantAuthConnection">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="huifu_id" jdbcType="VARCHAR" property="huifuId" />
    <result column="secret_key" jdbcType="VARCHAR" property="secretKey" />
    <result column="public_key" jdbcType="VARCHAR" property="publicKey" />
    <result column="huifu_public_key" jdbcType="VARCHAR" property="huifuPublicKey" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, app_id, tenant_id, `status`, create_time, update_time, huifu_id, secret_key, public_key, huifu_public_key, sync_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tenant_auth_connection
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByAppId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tenant_auth_connection
    where app_id = #{appId,jdbcType=VARCHAR}
  </select>
  <select id="selectAllTenants" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from tenant_auth_connection
      where status = 1
      <if test="appIds != null and appIds.size()&gt;0">
        AND app_id IN
        <foreach close=")" collection="appIds" item="appId" open="(" separator=",">
          #{appId}
        </foreach>
      </if>
  </select>
<!--  <select id="selectTenantInfos" resultType="com.cosfo.manage.wechat.model.dto.TenantTemplateDto">-->
<!--      select  a.app_id,b.tenant_id,c.tenant_name,a.create_time from  wechat_authorizer a-->
<!--      left join tenant_auth_connection b on a.app_id = b.app_id-->
<!--      left join tenant c on b.tenant_id = c.id-->
<!--      where a.status = 1 and b.status = 1 and c.status = 1-->
<!--  </select>-->
    <select id="selectByTenantId" resultType="com.cosfo.manage.tenant.model.po.TenantAuthConnection">
      select
      <include refid="Base_Column_List" />
      from tenant_auth_connection
      where tenant_id = #{tenantId,jdbcType=BIGINT}
    </select>
    <select id="selectTenantsByTenantIds" resultType="com.cosfo.manage.tenant.model.po.TenantAuthConnection">
      select
      <include refid="Base_Column_List" />
      from tenant_auth_connection
      where tenant_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="selectByHuiFuId" resultType="com.cosfo.manage.tenant.model.po.TenantAuthConnection">
      select
      <include refid="Base_Column_List" />
      from tenant_auth_connection
      where huifu_id = #{huifuId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tenant_auth_connection
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantAuthConnection" useGeneratedKeys="true">
    insert into tenant_auth_connection (app_id, tenant_id, `status`, 
      create_time, update_time, huifu_id, secret_key, public_key, huifu_public_key, sync_time)
    values (#{appId,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{huifuId,jdbcType=VARCHAR}, #{secretKey,jdbcType=VARCHAR},
            #{publicKey,jdbcType=VARCHAR}, #{huifuPublicKey,jdbcType=VARCHAR}, #{syncTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantAuthConnection" useGeneratedKeys="true">
    insert into tenant_auth_connection
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="huifuId != null">
        huifu_id,
      </if>
      <if test="secretKey != null">
        secret_key,
      </if>
      <if test="publicKey != null">
        public_key,
      </if>
      <if test="huifuPublicKey != null">
        huifu_public_key,
      </if>
      <if test="syncTime != null">
        sync_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="huifuId != null">
        #{huifuId,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="publicKey != null">
        #{publicKey,jdbcType=VARCHAR},
      </if>
      <if test="huifuPublicKey != null">
        #{huifuPublicKey,jdbcType=VARCHAR},
      </if>
      <if test="syncTime != null">
        #{syncTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.tenant.model.po.TenantAuthConnection">
    update tenant_auth_connection
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="huifuId != null">
        huifu_id = #{huifuId,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        secret_key = #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="publicKey != null">
        public_key = #{publicKey,jdbcType=VARCHAR},
      </if>
      <if test="huifuPublicKey != null">
        huifu_public_key = #{huifuPublicKey,jdbcType=VARCHAR},
      </if>
      <if test="syncTime != null">
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.tenant.model.po.TenantAuthConnection">
    update tenant_auth_connection
    set app_id = #{appId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
        huifu_id = #{huifuId,jdbcType=VARCHAR},
        secret_key = #{secretKey,jdbcType=VARCHAR},
        public_key = #{publicKey,jdbcType=VARCHAR},
        huifu_public_key = #{huifuPublicKey,jdbcType=VARCHAR},
        sync_time = #{syncTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
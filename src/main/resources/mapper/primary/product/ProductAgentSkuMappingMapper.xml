<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductAgentSkuMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="agent_tenant_id" jdbcType="BIGINT" property="agentTenantId" />
    <result column="agent_sku_id" jdbcType="BIGINT" property="agentSkuId" />
    <result column="agent_sku_code" jdbcType="VARCHAR" property="agentSkuCode"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="spu_id" jdbcType="BIGINT" property="spuId" />
    <result column="agent_spu_id" jdbcType="BIGINT" property="agentSpuId" />
    <result column="agent_spu_code" jdbcType="VARCHAR" property="agentSpuCode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, sku_id, agent_sku_code, agent_tenant_id, agent_sku_id, create_time, update_time,spu_id, agent_spu_id, agent_spu_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from product_agent_sku_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_agent_sku_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductAgentSkuMapping" useGeneratedKeys="true">
    insert into product_agent_sku_mapping (tenant_id, sku_id, agent_tenant_id,
      agent_sku_id,agent_sku_code,spu_id, agent_spu_id, agent_spu_code)
    values (#{tenantId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT}, #{agentTenantId,jdbcType=BIGINT},
      #{agentSkuId,jdbcType=BIGINT},
      #{agentSkuCode,jdbcType=VARCHAR},#{spuId,jdbcType=BIGINT}, #{agentSpuId,jdbcType=BIGINT}, #{agentSpuCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductAgentSkuMapping" useGeneratedKeys="true">
    insert into product_agent_sku_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="agentTenantId != null">
        agent_tenant_id,
      </if>
      <if test="agentSkuId != null">
        agent_sku_id,
      </if>
      <if test="agentSkuCode != null">
        agent_sku_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="agentSpuId != null">
         agent_spu_id,
      </if>
      <if test="agentSpuCode != null">
        agent_spu_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="agentTenantId != null">
        #{agentTenantId,jdbcType=BIGINT},
      </if>
      <if test="agentSkuId != null">
        #{agentSkuId,jdbcType=BIGINT},
      </if>
      <if test="agentSkuCode != null">
        #{agentSkuCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=BIGINT},
      </if>
      <if test="agentSpuId != null">
        #{agentSpuId,jdbcType=BIGINT},
      </if>
      <if test="agentSpuCode != null">
        #{agentSpuCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.product.model.po.ProductAgentSkuMapping">
    update product_agent_sku_mapping
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="agentTenantId != null">
        agent_tenant_id = #{agentTenantId,jdbcType=BIGINT},
      </if>
      <if test="agentSkuId != null">
        agent_sku_id = #{agentSkuId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=BIGINT},
      </if>
      <if test="agentSpuId != null">
        agent_spu_id=#{agentSpuId,jdbcType=BIGINT},
      </if>
      <if test="agentSpuCode != null">
        agent_spu_code=#{agentSpuCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.po.ProductAgentSkuMapping">
    update product_agent_sku_mapping
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=BIGINT},
      agent_tenant_id = #{agentTenantId,jdbcType=BIGINT},
      agent_sku_id = #{agentSkuId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listAll" parameterType="com.cosfo.manage.product.model.dto.ProductAgentSkuQueryDTO" resultType="com.cosfo.manage.product.model.vo.ProductAgentSkuVO">
    select
      agent.id id,agent.sku_id skuId,agent.agent_sku_id supplySkuId,agent.agent_sku_code supplySkuCode,agent.agent_tenant_id supplyTenantId,spu.title,spu.main_picture mainPicture,
      sku.specification, sku.specification_unit specificationUnit, spu.category_id categoryId, sku.sku supplySku, spu.origin, spu.storage_location storageLocation,
      spu.storage_temperature storageTemperature, spu.guarantee_period guaranteePeriod, spu.guarantee_unit guaranteeUnit, spu.brand_name brandName,
      sku.custom_sku_code customSkuCode
    from
      product_agent_sku_mapping agent
    left join product_sku sku on sku.id = agent.sku_id
    left join product_spu spu on spu.id = sku.spu_id
    where agent.tenant_id = #{tenantId} and sku.use_flag = 1
    <if test="title != null and title != ''">
      and spu.title like concat('%',#{title},'%')
    </if>
    <if test="categoryIds != null">
      and spu.category_id in
      <foreach close=")" collection="categoryIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="skuId != null and skuId !=''">
      and sku.id = #{skuId}
    </if>
    <if test="sku != null and sku != ''">
      and sku.sku like concat('%',#{sku},'%')
    </if>
    order by agent.id desc
  </select>

  <select id="selectByTenantIdAndSkuId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    product_agent_sku_mapping
    where tenant_id = #{tenantId}
    and sku_id = #{skuId}
  </select>

  <select id="queryAgentSkuInfo" resultType="com.cosfo.manage.product.model.vo.ProductAgentSkuVO">
    select
      agent.id id, agent.sku_id skuId,agent.agent_sku_id supplySkuId,agent.agent_tenant_id supplyTenantId,spu.title,spu.main_picture mainPicture,
      sku.specification, sku.specification_unit specificationUnit, spu.category_id categoryId, sku.sku supplySku
    from
      product_agent_sku_mapping agent
        left join product_sku sku on sku.id = agent.agent_sku_id
        left join product_spu spu on spu.id = sku.spu_id
    where agent.tenant_id = #{tenantId}
      and agent.sku_id = #{skuId}
  </select>

  <select id="queryAll" resultType="com.cosfo.manage.product.model.dto.ProductAgentSkuDTO">
    select  t.id, t.tenant_id tenantId, t.sku_id skuId, t.agent_tenant_id agentTenantId, t.agent_sku_id agentSkuId
    from product_agent_sku_mapping t
    left join product_sku sk on t.agent_sku_id = sk.id
    left join product_spu sp on sp.id = sk.spu_id
    <where>
      <if test="tenantId != null">
        and t.tenant_id = #{tenantId}
      </if>
      <if test="title != null">
        and sp.title like concat('%',#{title},'%')
      </if>
      <if test="categoryIds != null and categoryIds.size() > 0">
        and category_id in
        <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="brandIds != null and brandIds.size() > 0">
        and sp.brand_id in
        <foreach collection="brandIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="associated == 0">
        and t.sku_id is null
      </if>
      <if test="associated == 1">
        and t.sku_id is not null
      </if>
    </where>
    group by t.id
    order by t.id desc
  </select>
  <select id="selectByAgentSkuId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from product_agent_sku_mapping
    where agent_sku_id = #{agentSkuId} and tenant_id = #{tenantId}
  </select>

 <select id="selectByAgentSkuIdAndAgentTenantIdAndTenantId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from product_agent_sku_mapping
    where agent_sku_id = #{agentSkuId} and agent_tenant_id = #{agentTenantId}
    and tenant_id = #{tenantId}
  </select>

  <select id="selectHavingMappingByTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    product_agent_sku_mapping
    where tenant_id = #{tenantId} and sku_id is not null
  </select>

  <select id="selectNoSynchronizedAgentSku" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from product_agent_sku_mapping
    where sku_id is null and tenant_id is not null
  </select>


  <select id="batchQueryByAgentSkuId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from product_agent_sku_mapping
    where agent_sku_id in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    and tenant_id != 1
  </select>

  <select id="batchQueryBySkuIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from product_agent_sku_mapping
    where sku_id in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectBySkuIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from product_agent_sku_mapping
    where sku_id in
    <foreach collection="skuIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="queryAgentSkuInfoByAgentSkuIds" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"></include>
    from product_agent_sku_mapping
    where agent_tenant_id = #{agentTenantId}
    and tenant_id = #{tenantId}
    and agent_sku_id in
    <foreach collection="agentSkuIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="queryAgentSkuInfoByAgentSkuCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from product_agent_sku_mapping
    where agent_sku_code in
    <foreach collection="agentSkuCodes" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectByAgentTenantInfo" resultMap="BaseResultMap">
    SELECT
    pasm.id, pasm.tenant_id, pasm.sku_id, pasm.agent_sku_code, pasm.agent_tenant_id, pasm.agent_sku_id, pasm.create_time, pasm.update_time
    FROM
      product_agent_sku_mapping pasm
      INNER JOIN `product_pricing_supply` pps on pps.`supply_sku_id` = pasm.`sku_id`
      and pps.`supply_tenant_id` = pasm.`tenant_id`
    where
      pasm.`agent_sku_code` = #{agentSkuCode}
      and pasm.`agent_tenant_id` = #{agentTenantId}
    LIMIT
      1;
  </select>

  <select id="queryByAgentTenantIdAndSku" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from product_agent_sku_mapping
    where agent_tenant_id = #{agentTenantId}
    and sku_id = #{skuId}
  </select>

    <select id="selectBySkuCodes" resultMap="BaseResultMap">
        SELECT
            pasm.id, pasm.tenant_id, pasm.sku_id, pasm.agent_sku_code, pasm.agent_tenant_id, pasm.agent_sku_id, pasm.create_time, pasm.update_time
        FROM
            product_agent_sku_mapping pasm
                INNER JOIN `product_sku` ps on ps.`id` = pasm.`sku_id`
                and ps.`tenant_id` = pasm.`tenant_id`
        where
            pasm.`agent_sku_code` in
        <foreach collection="skuCodes" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and (
            (ps.`agent_type` = 0 and ps.`tenant_id` = 1)
            or
            (ps.`tenant_id` != 1)
        )

    </select>


    <select id="selectSupplySkuByTenantId" resultMap="BaseResultMap">
        SELECT
            distinct pasm.sku_id, pasm.agent_sku_code
        FROM
        product_agent_sku_mapping pasm
        left join product_pricing_supply pps on pasm.sku_id = pps.supply_sku_id
        left join product_pricing_supply_city_mapping m on pps.id = m.product_pricing_supply_id
        where pasm.tenant_id = 1
        and m.supply_type = 1
        and pps.tenant_id = #{tenantId}
        and m.start_time <![CDATA[<]]> now() and m.end_time <![CDATA[>]]> now()
    </select>

</mapper>

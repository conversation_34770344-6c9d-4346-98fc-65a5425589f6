<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductCityStockSyncMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductCityStock">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="city_id" jdbcType="BIGINT" property="cityId"/>
        <result column="quantity" jdbcType="BIGINT" property="quantity"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, sku_id, city_id, quantity, create_time, update_time
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.dto.ProductCityStockDTO"
            useGeneratedKeys="true">
        insert into product_city_stock (sku_id, city_id, quantity)
        values (#{skuId,jdbcType=BIGINT}, #{cityId,jdbcType=BIGINT}, #{quantity,jdbcType=BIGINT})
    </insert>

    <select id="selectOneByParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from product_city_stock
        <where>
            <if test="cityId != null">
                and city_id = #{cityId}
            </if>
            <if test="skuId != null">
                and sku_id = #{skuId}
            </if>
        </where>
    </select>

    <select id="batchSelectByParam" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from product_city_stock
        WHERE
        <if test="productCityStockDtoList != null and productCityStockDtoList.size() != 0">
            (
            <foreach collection="productCityStockDtoList" item="item" separator="OR" index="index">
                (
                    city_id = #{item.cityId}
                    and sku_id = #{item.skuId}
                )
            </foreach>
            )
        </if>
    </select>

    <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.dto.ProductCityStockDTO">
        update product_city_stock
        <set>
            <if test="quantity != null">
                `quantity` = #{quantity},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByUniqueKey" parameterType="com.cosfo.manage.product.model.dto.ProductCityStockDTO">
        update product_city_stock
        <set>
            <if test="quantity != null">
                `quantity` = #{quantity},
            </if>
        </set>
        where sku_id = #{skuId} and city_id = #{cityId}
    </update>

    <update id="batchUpdate">
        <foreach collection="productCityStockDtoList" item="item" separator=";">
            update product_city_stock set `quantity` = #{item.quantity} where id = #{item.id}
        </foreach>
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductStockForewarningReportMapper">

    <select id="queryAllTenantIdList" resultType="java.lang.Long">
        select distinct tenant_id
        from product_sku_order_summary
    </select>


  <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO product_stock_forewarning_report (tenant_id, sku_id, sku_code, warehouse_no, goods_type, sale_days, sale_quantity, quantity, forewarning_status, use_flag)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantId}, #{item.skuId}, #{item.skuCode}, #{item.warehouseNo}, #{item.goodsType}, #{item.saleDays}, #{item.saleQuantity}, #{item.quantity}, #{item.forewarningStatus}, #{item.useFlag})
    </foreach>
    ON DUPLICATE KEY UPDATE
    tenant_id = VALUES(tenant_id),
    sku_id = VALUES(sku_id),
    sku_code = VALUES(sku_code),
    warehouse_no = VALUES(warehouse_no),
    goods_type = VALUES(goods_type),
    sale_days = VALUES(sale_days),
    sale_quantity = VALUES(sale_quantity),
    quantity = VALUES(quantity),
    forewarning_status = VALUES(forewarning_status),
    use_flag = VALUES(use_flag)
  </insert>

</mapper>

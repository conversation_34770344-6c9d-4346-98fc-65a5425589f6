<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.order.mapper.OrderMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.order.model.po.Order">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="supplier_tenant_id" jdbcType="BIGINT" property="supplierTenantId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="payable_price" jdbcType="DECIMAL" property="payablePrice" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="finished_time" property="finishedTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="apply_end_time" property="applyEndTime"/>
    <result column="auto_finished_time" property="autoFinishedTime"/>
    <result column="warehouse_no" property="warehouseNo"/>
    <result column="combine_order_id" property="combineOrderId"/>
    <result column="order_type" property="orderType"/>
    <result column="profit_sharing_finish_time" property="profitSharingFinishTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, account_id, supplier_tenant_id, order_no, warehouse_type,
    payable_price, delivery_fee, total_price, `status`, pay_type, pay_time, delivery_time, finished_time,
    create_time, update_time, remark, apply_end_time, auto_finished_time, warehouse_no, combine_order_id, order_type, profit_sharing_finish_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `order`
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from `order`
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.order.model.po.Order" useGeneratedKeys="true">
    insert into `order` (tenant_id, store_id, account_id,
      supplier_tenant_id, order_no, warehouse_type,
      payable_price, delivery_fee, total_price,
      `status`, pay_type, pay_time,
      delivery_time, finished_time, create_time, update_time, combine_order_id, order_type
      )
    values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT},
      #{supplierTenantId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{warehouseType,jdbcType=TINYINT},
      #{payablePrice,jdbcType=DECIMAL}, #{deliveryFee,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL},
      #{status,jdbcType=TINYINT}, #{payType,jdbcType=TINYINT}, #{payTime,jdbcType=TIMESTAMP},
      #{deliveryTime,jdbcType=TIMESTAMP}, #{finishedTime}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{combineOrderId}, #{orderType}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.order.model.po.Order" useGeneratedKeys="true">
    insert into `order`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="supplierTenantId != null">
        supplier_tenant_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="warehouseType != null">
        warehouse_type,
      </if>
      <if test="payablePrice != null">
        payable_price,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="finishedTime != null">
        finished_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="supplierTenantId != null">
        #{supplierTenantId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseType != null">
        #{warehouseType,jdbcType=TINYINT},
      </if>
      <if test="payablePrice != null">
        #{payablePrice,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishedTime != null">
        #{finishedTime},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.order.model.po.Order">
    update `order`
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="supplierTenantId != null">
        supplier_tenant_id = #{supplierTenantId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseType != null">
        warehouse_type = #{warehouseType,jdbcType=TINYINT},
      </if>
      <if test="payablePrice != null">
        payable_price = #{payablePrice,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=TINYINT},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishedTime != null">
        finished_time = #{finishedTime},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.order.model.po.Order">
    update `order`
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      supplier_tenant_id = #{supplierTenantId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      warehouse_type = #{warehouseType,jdbcType=TINYINT},
      payable_price = #{payablePrice,jdbcType=DECIMAL},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      pay_type = #{payType,jdbcType=TINYINT},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      finished_time = #{finishedTime},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- -->
  <select id="selectCountByOrderQueryDTO" resultType="integer">
    select
        count(o.id)
    from
        `order` o
    <where>
        o.status != 1
        and o.tenant_id = #{orderQueryDTO.tenantId}
        <if test="orderQueryDTO.status != null">
            and o.status = #{orderQueryDTO.status}
        </if>
        <if test="orderQueryDTO.storeIds != null &amp; orderQueryDTO.storeIds.size() &gt; 0">
            and o.store_id in
          <foreach collection="orderQueryDTO.storeIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test="orderQueryDTO.orderNo != null">
           and o.order_no = #{orderQueryDTO.orderNo}
        </if>
        <if test="orderQueryDTO.warehouseType != null">
          and o.warehouse_type = #{orderQueryDTO.warehouseType}
        </if>
        <if test="orderQueryDTO.accountIds != null  &amp; orderQueryDTO.accountIds.size() &gt; 0 ">
          and o.account_id in
          <foreach collection="orderQueryDTO.accountIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test="orderQueryDTO.startTime != null &amp; orderQueryDTO.endTime != null">
          and o.create_time between #{orderQueryDTO.startTime} and #{orderQueryDTO.endTime}
        </if>
    </where>
  </select>

  <select id="selectByOrderQueryDTO" resultType="com.cosfo.manage.order.model.vo.OrderVO">
    select
        distinct o.id orderId, o.order_no orderNo, o.store_id storeId,
        o.create_time orderTime, o.warehouse_type warehouseType,
        ifNull(o.total_price,0) totalPrice,o.status, o.pay_type, o.delivery_fee deliveryFee,
        o.account_id accountId, o.warehouse_no warehouseNo, o.combine_order_id combineOrderId, o.order_type orderType
    from
    `order` o
    left join order_item oi on o.id = oi.order_id
    <where>
      o.tenant_id = #{orderQueryDTO.tenantId}
      <if test="orderQueryDTO.status != null and orderQueryDTO.status != 3 and orderQueryDTO.status != 4">
        and o.status = #{orderQueryDTO.status}
      </if>
      <if test="orderQueryDTO.status != null and orderQueryDTO.status == 3">
        and o.status in (3,10,11)
      </if>
      <if test="orderQueryDTO.status != null and orderQueryDTO.status == 4">
        and o.status in (4,12)
      </if>
      <if test="orderQueryDTO.storeIds != null &amp; orderQueryDTO.storeIds.size() &gt; 0">
        and o.store_id in
        <foreach collection="orderQueryDTO.storeIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="orderQueryDTO.orderNo != null">
        and o.order_no = #{orderQueryDTO.orderNo}
      </if>
      <if test="orderQueryDTO.warehouseType != null">
        and o.warehouse_type = #{orderQueryDTO.warehouseType}
      </if>
      <if test="orderQueryDTO.warehouseNo != null">
        and o.warehouse_no = #{orderQueryDTO.warehouseNo}
      </if>
      <if test="orderQueryDTO.accountIds != null  &amp; orderQueryDTO.accountIds.size() &gt; 0 ">
        and o.account_id in
        <foreach collection="orderQueryDTO.accountIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="orderQueryDTO.startTime != null &amp; orderQueryDTO.endTime != null">
        and o.create_time between #{orderQueryDTO.startTime} and #{orderQueryDTO.endTime}
      </if>
      <if test="orderQueryDTO.payType != null">
        and o.pay_type = #{orderQueryDTO.payType}
      </if>
      <if test="orderQueryDTO.itemIds != null  &amp; orderQueryDTO.itemIds.size() &gt; 0 ">
        and oi.item_id in
        <foreach collection="orderQueryDTO.itemIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="orderQueryDTO.orderItemIds != null  &amp; orderQueryDTO.orderItemIds.size() &gt; 0 ">
        and oi.id in
        <foreach collection="orderQueryDTO.orderItemIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
    order by o.id desc,o.combine_order_id desc
  </select>

  <select id="queryOrderVOByOrderId" resultType="com.cosfo.manage.order.model.vo.OrderVO">
    select
      o.supplier_tenant_id supplierTenantId,o.payable_price payablePrice, o.delivery_fee deliveryFee,
      o.create_time orderTime, o.order_no orderNo, o.account_id accountId, o.delivery_time deliveryTime, o.begin_delivery_time beginDeliveryTime,
      o.pay_type payType, o.pay_time payTime, ifNull(o.total_price,0) totalPrice,o.warehouse_type warehouseType,o.warehouse_no warehouseNo,
      o.status, o.id orderId, o.tenant_id tenantId,o.finished_time finishedTime, o.remark, o.apply_end_time applyEndTime, o.auto_finished_time autoFinishedTime, o.store_id storeId,
      o.combine_order_id combineOrderId, o.order_type orderType
    from
      `order` o
    where
      o.id = #{orderId}
  </select>

  <select id="calculatePayOrderTotalPrice" resultType="decimal">
    select
    sum(o.total_price)
    from
      `order` o
    where o.pay_time &gt; #{startTime}
    and o.pay_time &lt; #{endTime}
    and o.status not in (1,2)
    and tenant_id = #{tenantId}
  </select>

  <select id="calculateDirectStorePayOrderTotalPrice" resultType="decimal">
    select
      sum(o.total_price)
    from
      `order` o
    where o.pay_time &gt; #{startTime}
      and o.pay_time &lt; #{endTime}
      and o.status not in (1,2)
      and o.tenant_id = #{tenantId}
      <if test="storeIds != null &amp; storeIds.size() &gt; 0">
        and o.store_id in
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
  </select>

  <select id="calculatePayOrderNum" resultType="integer">
    select
      count(o.id)
    from
      `order` o
    where o.pay_time &gt; #{startTime}
      and o.pay_time &lt; #{endTime}
      and o.status not in (1,2)
      and tenant_id = #{tenantId}
  </select>

  <select id="calculateDirectStorePayOrderNum" resultType="integer">
    select
      count(o.id)
    from
      `order` o
    where o.pay_time &gt; #{startTime}
      and o.pay_time &lt; #{endTime}
      and o.status not in (1,2)
      and o.tenant_id = #{tenantId}
      <if test="storeIds != null &amp; storeIds.size() &gt; 0">
        and o.store_id in
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
  </select>

  <select id="calculatePayOrderStoreNum" resultType="integer">
    select
      count(distinct o.store_id)
    from
      `order` o
    where o.pay_time &gt; #{startTime}
      and o.pay_time &lt; #{endTime}
      and o.status not in (1,2)
      and o.tenant_id = #{tenantId}
  </select>

  <select id="calculateDirectStorePayOrderStoreNum" resultType="integer">
    select
      count(distinct o.store_id)
    from
      `order` o
    where o.pay_time &gt; #{startTime}
      and o.pay_time &lt; #{endTime}
      and o.status not in (1,2)
      and o.tenant_id = #{tenantId}
      <if test="storeIds != null &amp; storeIds.size() &gt; 0">
        and o.store_id in
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
  </select>

  <select id="getWaitDeliveryNum" resultType="integer">
    select
        count(o.id)
    from
      `order` o
    where o.status in (3,10,11)
      and tenant_id = #{tenantId}
  </select>

  <select id="selectExportOrderListByOrderQueryDTO" resultType="com.cosfo.manage.order.model.vo.OrderVO" fetchSize="1000" >
    select
    distinct o.id orderId, o.order_no orderNo,
    o.create_time orderTime, o.warehouse_type warehouseType,
    ifNUll(o.total_price,o.payable_price) totalPrice, o.status, o.pay_type payType, o.delivery_fee deliveryFee,o.delivery_time, o.remark, o.store_id storeId,
    o.warehouse_no warehouseNo, o.tenant_id tenantId,o.account_id accountId
    from
    `order` o
    left join order_item oi on o.id = oi.order_id
    <where>
      o.tenant_id = #{orderQueryDTO.tenantId}
      <if test="orderQueryDTO.status != null and orderQueryDTO.status != 3 and orderQueryDTO.status != 4">
        and o.status = #{orderQueryDTO.status}
      </if>
      <if test="orderQueryDTO.status != null and orderQueryDTO.status == 3">
        and o.status in (3,10,11)
      </if>
      <if test="orderQueryDTO.status != null and orderQueryDTO.status == 4">
        and o.status in (4,12)
      </if>
      <if test="orderQueryDTO.storeIds != null &amp; orderQueryDTO.storeIds.size() &gt; 0">
        and o.store_id in
        <foreach collection="orderQueryDTO.storeIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="orderQueryDTO.orderNo != null">
        and o.order_no = #{orderQueryDTO.orderNo}
      </if>
      <if test="orderQueryDTO.warehouseType != null">
        and o.warehouse_type = #{orderQueryDTO.warehouseType}
      </if>
      <if test="orderQueryDTO.warehouseNo != null">
        and o.warehouse_no = #{orderQueryDTO.warehouseNo}
      </if>
      <if test="orderQueryDTO.accountIds != null  &amp; orderQueryDTO.accountIds.size() &gt; 0 ">
        and o.account_id in
        <foreach collection="orderQueryDTO.accountIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="orderQueryDTO.startTime != null &amp; orderQueryDTO.endTime != null">
        and o.create_time between #{orderQueryDTO.startTime} and #{orderQueryDTO.endTime}
      </if>
      <if test="orderQueryDTO.itemIds != null  &amp; orderQueryDTO.itemIds.size() &gt; 0 ">
        and oi.item_id in
        <foreach collection="orderQueryDTO.itemIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="orderQueryDTO.payType != null">
        and o.pay_type = #{orderQueryDTO.payType}
      </if>
      <if test="orderQueryDTO.orderItemIds != null  &amp; orderQueryDTO.orderItemIds.size() &gt; 0 ">
        and oi.id in
        <foreach collection="orderQueryDTO.orderItemIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
    order by o.id desc
  </select>
  <select id="queryAfterSaleVOByOrderId" resultType="com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleVO">
    select
      o.supplier_tenant_id supplierTenantId, o.status,o.store_id storeId,
      o.create_time orderTime, o.order_no orderNo, o.account_id accountId, o.delivery_time deliveryTime,
      o.pay_type payType, o.pay_time payTime,o.warehouse_type warehouseType,o.warehouse_no warehouseNo, o.store_id storeId, o.tenant_id tenantId
    from
      `order` o
    where
      o.id = #{orderId}
  </select>

    <update id="updateOrderStatusById">
    update `order`
    set status = #{status},
        <if test='status = "4"'>
          delivery_time = now(),
        </if>
        update_time = now()
    where id = #{id}
    and tenant_id = #{tenantId}
  </update>

  <select id="selectByOrderNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from `order` o
    <where>
      <if test="orderNo != null">
        and order_no = #{orderNo}
      </if>
      <if test="tenantId != null">
        and o.tenant_id = #{tenantId}
      </if>
    </where>
  </select>

  <select id="queryByOrderNo" resultMap="BaseResultMap">
     select
     <include refid="Base_Column_List"></include>
     from
      `order`
     <where>
       tenant_id = #{tenantId}
       <if test="orderNo != null">
         and order_no like concat(#{orderNo} , '%')
       </if>
     </where>
  </select>

  <select id="batchQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    `order`
    where tenant_id = #{tenantId}
    and id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="queryOrderItemInfo" resultType="com.cosfo.manage.order.model.dto.BillOrderDTO">
    select
        o.id orderId, o.order_no orderNo, o.store_id storeId, o.create_time createTime,
        o.pay_time payTime, o.pay_type payType, o.delivery_time deliveryTime, o.finished_time finishedTime,
        s.title, item.item_id itemId, s.sku_id skuId, s.specification, item.amount, item.payable_price skuPrice, o.delivery_fee deliveryFee,
        o.total_price orderPrice, item.id orderItemId, s.supplier_tenant_id supplyTenantId, s.supplier_sku_id supplySkuId,
        s.supply_price supplyPrice,s.main_picture mainPicture, item.item_id itemId, o.warehouse_type warehouseType,
        s.pricing_type pricingType, s.pricing_number pricingNumber, s.goods_type goodsType
    from
    `order` o
    left join `order_item` item on item.tenant_id = o.tenant_id and item.order_id = o.id
    left join `order_item_snapshot` s on s.tenant_id = o.tenant_id and s.order_item_id = item.id
    where
    o.tenant_id = #{tenantId}
    and o.id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="queryTenantBillInfo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"></include>
    FROM `order`
    where warehouse_type = 1
    and tenant_id = #{tenantId}
    and status in (5,7,9)
    and supplier_tenant_id = #{supplyTenantId}
    and (DATE_FORMAT(finished_time ,'%Y-%m-%d') between DATE_FORMAT( #{startTime},'%Y-%m-%d') and DATE_FORMAT(#{endTime},'%Y-%m-%d'))
  </select>

  <select id="queryBillOrderByStartTimeAndEndTime" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"></include>
    FROM `order`
    where
    tenant_id = #{tenantId}
    and status in (5,7,9)
    and finished_time between #{startTime} and #{endTime} and pay_type = 2
  </select>

  <update id="updateOrderStatus">
    update  `order`
    set
      status = #{updateStatus}
    where tenant_id = #{tenantId}
    and id = #{orderId}
    and status = #{originStatus}
  </update>

  <update id="updateOrderStatusWhenDelivery">
    update  `order`
    set
    status = #{updateStatus}
    <if test='updateStatus == "4"'>
      ,delivery_time = now()
    </if>
    <if test='originStatus == "10"'>
      ,begin_delivery_time = now()
    </if>
    where tenant_id = #{tenantId}
    and id = #{orderId}
    and status = #{originStatus}
  </update>

  <update id="updateProfitSharingFinishTime">
    update `order`
    set
        profit_sharing_finish_time = #{profitSharingFinishTime}
    where tenant_id = #{tenantId}
    and id = #{orderId}
  </update>

  <select id="querySkuSaleAmount" resultType="com.cosfo.manage.order.model.dto.OrderSkuSaleDTO">
    select
      s.sku_id skuId, sum(i.amount) saleAmount
    from `order` o
           left join `order_item` i on o.id = i.order_id
           left join `order_item_snapshot` s on i.id = s.order_item_id
    where o.tenant_id = #{tenantId} and o.`status` in (3,4,5,10,11,12)
    and s.sku_id in
    <foreach collection="skuIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    and o.create_time between #{startTime} and #{endTime}
    group by s.sku_id
  </select>

  <select id="querySkuSaleAmountWithStoreNo" resultType="com.cosfo.manage.order.model.dto.OrderSkuSaleDTO">
    select
        s.sku_id skuId, i.store_no warehouseId, sum(i.amount) saleAmount
    from `order` o
        left join `order_item` i on o.id = i.order_id and o.tenant_id = i.tenant_id
        left join `order_item_snapshot` s on i.id = s.order_item_id
    where o.tenant_id = #{tenantId} and o.`status` in (3,4,5,10,11,12) and i.store_no >= 0
    and s.sku_id in
    <foreach collection="skuIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    and o.create_time between #{startTime} and #{endTime}
    group by s.sku_id,i.store_no
  </select>

  <select id="querySkuSaleAmountByCity" resultType="com.cosfo.manage.order.model.dto.OrderSkuSaleDTO">
    select
    s.sku_id skuId, oa.city city, sum(i.amount) saleAmount
    from `order` o
    left join `order_item` i on o.id = i.order_id and o.tenant_id = i.tenant_id
    left join `order_item_snapshot` s on i.id = s.order_item_id
    left join `order_address` oa on oa.order_id = o.id
    where o.tenant_id = #{tenantId} and o.`status` in (3,4,5,10,11,12) and i.store_no >= 0
    and s.sku_id in
    <foreach collection="skuIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    and o.create_time between #{startTime} and #{endTime}
    group by s.sku_id, oa.city
  </select>

  <select id="queryByOrderNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    `order`
    where tenant_id = #{tenantId} and  order_no in
    <foreach collection="orderNos" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

    <select id="queryNeedDeliveryData" resultType="java.lang.String">
      select order_no
      from
        `order`
      where tenant_id = #{tenantId} and status = 10 and warehouse_type = 2
      <if test="warehouseNoList != null and warehouseNoList.size() != 0">
        and warehouse_no IN
        <foreach collection="warehouseNoList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="payTime != null">
        and pay_time <![CDATA[<=]]> #{payTime}
      </if>
      <if test="createTime != null">
        and create_time <![CDATA[<=]]> #{createTime}
      </if>
    </select>

    <update id="fixOrder">
    update `order`
    set total_price = payable_price,
        pay_time = create_time
    where  status in (3,4,5,10,7,8,9)   and total_price is null
  </update>

  <update id="batchUpdateOrderStatus">
    update  `order`
    set
      status = #{updateStatus}
      <if test='updateStatus == "12"'>
        ,begin_delivery_time = now()
      </if>
    where tenant_id = #{tenantId}
    and status = #{originStatus}
    and order_no IN
    <foreach collection="orderNoList" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </update>
</mapper>

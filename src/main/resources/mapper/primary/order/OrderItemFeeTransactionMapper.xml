<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.order.mapper.OrderItemFeeTransactionMapper">
    <select id="calTotalFeeBy" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(sum(fee), 0)
        FROM
            order_item_fee_transaction
        WHERE
            order_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
          AND fee_type = #{feeType}
          AND transaction_type = #{transactionType}
          AND tenant_id = #{tenantId}
    </select>

    <select id="listOrderItemFee" resultType="com.cosfo.manage.order.model.po.OrderItemFeeTransaction">
        SELECT
            order_item_id, fee
        FROM
            order_item_fee_transaction
        WHERE
            order_item_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        AND fee_type = #{feeType}
        AND transaction_type = #{transactionType}
        AND tenant_id = #{tenantId}

    </select>

    <select id="listOrderItemFeeByAfterSaleIds" resultType="com.cosfo.manage.order.model.po.OrderItemFeeTransaction">
        SELECT
        order_after_sale_id, fee
        FROM
        order_item_fee_transaction
        WHERE
        order_after_sale_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND fee_type = #{feeType}
        AND tenant_id = #{tenantId}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.common.mapper.DocCodeMappingMapper">
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO doc_code_mapping
        (id, tenant_id, target_type, channel_type, out_code, out_name, target_code)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.tenantId}, #{item.targetType}, #{item.channelType}, #{item.outCode}, #{item.outName}, #{item.targetCode})
        </foreach>
        ON DUPLICATE KEY UPDATE
        out_name = VALUES(out_name),
        out_code = VALUES(out_code)
    </insert>
</mapper>

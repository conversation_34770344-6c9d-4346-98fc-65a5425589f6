<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.PaymentCombinedOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.po.PaymentCombinedOrderDetail">
    <!--@mbg.generated-->
    <!--@Table payment_combined_order_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="combined_detail_id" jdbcType="BIGINT" property="combinedDetailId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, combined_detail_id, order_id, total_price, create_time, update_time, order_no,
    tenant_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from payment_combined_order_detail
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getListByOrderIds" resultType="com.cosfo.manage.bill.model.vo.PaymentCombinedOrderDetailVO">
    select pcod.order_id orderId, pcod.total_price totalPrice, pcod.order_no orderNo,
           pcod.id, pcd.trade_type tradeType
    from payment_combined_order_detail pcod
    left join payment_combined_detail pcd
    on pcod.combined_detail_id = pcd.id
    where pcod.order_id in
    <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
      #{orderId}
    </foreach>

  </select>

  <select id="getTotalPriceByCombinedDetailIdAndOrderId" resultType="java.math.BigDecimal">
    select total_price
    from payment_combined_order_detail
    where combined_detail_id = #{combinedDetailId,jdbcType=BIGINT}
      and order_id = #{orderId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from payment_combined_order_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.PaymentCombinedOrderDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_combined_order_detail (combined_detail_id, order_id, total_price,
      create_time, update_time, order_no,
      tenant_id)
    values (#{combinedDetailId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{totalPrice,jdbcType=DECIMAL},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR},
      #{tenantId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.PaymentCombinedOrderDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_combined_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="combinedDetailId != null">
        combined_detail_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="combinedDetailId != null">
        #{combinedDetailId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.bill.model.po.PaymentCombinedOrderDetail">
    <!--@mbg.generated-->
    update payment_combined_order_detail
    <set>
      <if test="combinedDetailId != null">
        combined_detail_id = #{combinedDetailId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.bill.model.po.PaymentCombinedOrderDetail">
    <!--@mbg.generated-->
    update payment_combined_order_detail
    set combined_detail_id = #{combinedDetailId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
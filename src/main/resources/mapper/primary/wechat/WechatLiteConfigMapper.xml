<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.wechat.mapper.WechatLiteConfigMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.wechat.model.po.WechatLiteConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="appid" jdbcType="VARCHAR" property="appid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="original_id" jdbcType="VARCHAR" property="originalId" />
    <result column="auth_ids" jdbcType="VARCHAR" property="authIds" />
    <result column="head_img" jdbcType="VARCHAR" property="headImg" />
    <result column="principal_name" jdbcType="VARCHAR" property="principalName" />
    <result column="verify" jdbcType="INTEGER" property="verify" />
    <result column="online_id" jdbcType="BIGINT" property="onlineId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, appid, `name`, original_id, auth_ids, head_img, principal_name, verify, online_id, 
    `status`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_lite_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByAppId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from wechat_lite_config
      where appid = #{appId,jdbcType=VARCHAR}
  </select>
  <select id="selectByOriginalId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_lite_config
    where original_id = #{OriginalId,jdbcType=VARCHAR}
  </select>

  <select id="selectAuthorizedList" resultType="com.cosfo.manage.wechat.model.vo.AuthorizedVo">
    select
    c.name as name,c.appid as appId,t.tenant_id as tenantId,a.create_time as createTime
    from wechat_lite_config c
    left join wechat_authorizer a on c.appid = a.app_id
    left join tenant_auth_connection t on a.app_id = t.app_id
    where a.status = 1 and t.status = 1 and t.tenant_id = #{tenantId,jdbcType=BIGINT}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wechat_lite_config
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.wechat.model.po.WechatLiteConfig" useGeneratedKeys="true">
    insert into wechat_lite_config (appid, `name`, original_id, 
      auth_ids, head_img, principal_name, 
      verify, online_id, `status`, 
      create_time, update_time)
    values (#{appid,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{originalId,jdbcType=VARCHAR}, 
      #{authIds,jdbcType=VARCHAR}, #{headImg,jdbcType=VARCHAR}, #{principalName,jdbcType=VARCHAR}, 
      #{verify,jdbcType=INTEGER}, #{onlineId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.wechat.model.po.WechatLiteConfig" useGeneratedKeys="true">
    insert into wechat_lite_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        appid,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="originalId != null">
        original_id,
      </if>
      <if test="authIds != null">
        auth_ids,
      </if>
      <if test="headImg != null">
        head_img,
      </if>
      <if test="principalName != null">
        principal_name,
      </if>
      <if test="verify != null">
        verify,
      </if>
      <if test="onlineId != null">
        online_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appid != null">
        #{appid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="originalId != null">
        #{originalId,jdbcType=VARCHAR},
      </if>
      <if test="authIds != null">
        #{authIds,jdbcType=VARCHAR},
      </if>
      <if test="headImg != null">
        #{headImg,jdbcType=VARCHAR},
      </if>
      <if test="principalName != null">
        #{principalName,jdbcType=VARCHAR},
      </if>
      <if test="verify != null">
        #{verify,jdbcType=INTEGER},
      </if>
      <if test="onlineId != null">
        #{onlineId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.wechat.model.po.WechatLiteConfig">
    update wechat_lite_config
    <set>
      <if test="appid != null">
        appid = #{appid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="originalId != null">
        original_id = #{originalId,jdbcType=VARCHAR},
      </if>
      <if test="authIds != null">
        auth_ids = #{authIds,jdbcType=VARCHAR},
      </if>
      <if test="headImg != null">
        head_img = #{headImg,jdbcType=VARCHAR},
      </if>
      <if test="principalName != null">
        principal_name = #{principalName,jdbcType=VARCHAR},
      </if>
      <if test="verify != null">
        verify = #{verify,jdbcType=INTEGER},
      </if>
      <if test="onlineId != null">
        online_id = #{onlineId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.wechat.model.po.WechatLiteConfig">
    update wechat_lite_config
    set appid = #{appid,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      original_id = #{originalId,jdbcType=VARCHAR},
      auth_ids = #{authIds,jdbcType=VARCHAR},
      head_img = #{headImg,jdbcType=VARCHAR},
      principal_name = #{principalName,jdbcType=VARCHAR},
      verify = #{verify,jdbcType=INTEGER},
      online_id = #{onlineId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
package com.cosfo.manage.good.controller;

import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.good.model.dto.ProductAgentApplicationQueryDTO;
import com.cosfo.manage.good.model.dto.ProductAgentApplyingDTO;
import com.cosfo.manage.good.model.dto.ProductAgentSkuParamDTO;
import com.cosfo.manage.good.model.vo.ProductSkuApplyDetailVO;
import com.cosfo.manage.good.service.ProductAgentApplicationRecordService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 代仓申请记录管理
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/4
 */
@RestController
@RequestMapping("/product-agent-application")
public class ProductAgentApplicationRecordController extends BaseController {

    @Autowired
    private ProductAgentApplicationRecordService productAgentApplicationRecordService;

    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    /**
     * 申请记录列表
     *
     * @param productAgentApplicationQueryDTO
     * @return
     */
    @PostMapping("query/list")
    @Deprecated
    public CommonResult<PageInfo<ProductSkuApplyDetailVO>> list(@RequestBody ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        if (grayReleaseConfig.executeProductCenterGray(contextInfoDTO.getTenantId())) {
            PageInfo<ProductSkuApplyDetailVO> pageInfo = productAgentApplicationRecordService.list(productAgentApplicationQueryDTO, contextInfoDTO);
            return CommonResult.ok(pageInfo);
        } else {
            PageInfo<ProductSkuApplyDetailVO> pageInfo = productAgentApplicationRecordService.listOld(productAgentApplicationQueryDTO, contextInfoDTO);
            return CommonResult.ok(pageInfo);
        }
    }


    /**
     * 代仓服务申请
     *
     * @param productAgentApplyingDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/apply")
    public CommonResult<Boolean> apply(@Validated @RequestBody ProductAgentApplyingDTO productAgentApplyingDTO) {
        productAgentApplicationRecordService.apply(productAgentApplyingDTO, getMerchantInfoDTO());
        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 取消代仓服务申请
     *
     * @param productAgentSkuParamDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/cancel")
    public CommonResult<Boolean> cancel(@Validated @RequestBody ProductAgentSkuParamDTO productAgentSkuParamDTO) {
        productAgentApplicationRecordService.cancel(productAgentSkuParamDTO, getMerchantInfoDTO());
        return CommonResult.ok(Boolean.TRUE);
    }

}

package com.cosfo.manage.good.controller;

import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.good.model.dto.DetailQueryInput;
import com.cosfo.manage.good.model.dto.ProductAgentApplicationQueryDTO;
import com.cosfo.manage.good.model.dto.ProductAgentSkuParamDTO;
import com.cosfo.manage.good.model.vo.AgentSkuBizLogVO;
import com.cosfo.manage.good.model.vo.ProductSkuAgentRecordDetailVO;
import com.cosfo.manage.good.model.vo.ProductSkuApplyDetailVO;
import com.cosfo.manage.good.service.ProductAgentApplicationRecordService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 代仓申请记录，与鲜沐共用接口
 *
 * @author: monna.chen
 * @Date: 2023/12/20 18:48
 * @Description:
 */
@RestController
@RequestMapping("/cosfo-manage/product-agent-application")
public class ProductAgentApplicationRecordXMController extends BaseController {

    @Autowired
    private ProductAgentApplicationRecordService productAgentApplicationRecordService;

    /**
     * 申请记录列表 - 以sku为基准
     *
     * @param productAgentApplicationQueryDTO
     * @return
     */
    @PostMapping("/sku-agent-record/list")
    public CommonResult<PageInfo<ProductSkuApplyDetailVO>> listSkuAgentRecord(@RequestBody ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        PageInfo<ProductSkuApplyDetailVO> pageInfo = productAgentApplicationRecordService.listSkuAgentRecord(productAgentApplicationQueryDTO, contextInfoDTO);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 审核详情
     *
     * @param input
     * @return
     */
    @PostMapping("/sku-agent-record/detail")
    public CommonResult<ProductSkuAgentRecordDetailVO> skuAgentRecordDetail(@Validated @RequestBody DetailQueryInput input) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        ProductSkuAgentRecordDetailVO recordDetailVO = productAgentApplicationRecordService.skuAgentRecordDetail(input.getId(), contextInfoDTO.getTenantId());
        return CommonResult.ok(recordDetailVO);
    }

    /**
     * 查询sku的代仓申请日志
     *
     * @param productAgentSkuParamDTO
     * @return
     */
    @PostMapping("/biz-log")
    public CommonResult<List<AgentSkuBizLogVO>> listSkuAgentBizLog(@Validated @RequestBody ProductAgentSkuParamDTO productAgentSkuParamDTO) {
        return CommonResult.ok(productAgentApplicationRecordService.listSkuAgentBizLog(productAgentSkuParamDTO.getSkuId(), getMerchantInfoDTO()));
    }
}

package com.cosfo.manage.good.model.vo;

import lombok.Data;
import net.summerfarm.goods.client.resp.ProductsMappingResp;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/7
 */
@Data
public class ProductSkuVo {
    /**
     * skuId
     */
    private Long id;
    /** 规格类型 0容量*数量 1区间 **/
    private Integer specificationType;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 产地类型 0进口1国产
     */
    private Integer placeType;
    /**
     * 体积
     */
    private String volume;
    /**
     * 体积单位
     */
    private Long volumeUnit;
    /**
     * 重量
     */
    private Double weight;
    /**
     * 重量备注
     */
    private String weightNotes;

    /**
     * 是否代仓 0不代仓1代仓
     */
    private Integer agentType;
    /**
     * 状态：0待审核1已通过2已拒绝
     * 前端只是用了！=3    这个字段和agentstatus重复
     */
    private Integer status;
    /**
     * 拒绝原因
     */
    private String reason;
    /**
     * 库存数量
     */
    private Integer amount;

    /**
     * 是否关联0 未关联 1已关联
     */
    private Integer havingRelated;

    /**
     * 税率
     */
    private BigDecimal taxRateValue;

    /**
     * 是否申请过代仓服务 false 未申请 ture 已申请
     */
    private Boolean havingApplied;

    /**
     * 代仓申请状态  0-审核中 1-通过 2-拒绝 3-未申请
     */
    private Integer agentStatus;

    /**
     * 自有编码
     */
    private String customSkuCode;

    /**
     * 是否显示「删除」按钮 true-显示 false-不显示
     */
    private Boolean showDeleteTag;

    /**
     * 停用状态 0-停用 1-启用
     */
    private Integer useFlag;

    /**
     * 代仓sku编码
     */
    private String agentSkuCode;

    /**
     * 映射关系
     **/
    private ProductsMappingResp skuMapping;
}

package com.cosfo.manage.good.model.dto;

import com.cosfo.manage.common.model.dto.PageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductQueryInput extends PageDTO {

    /**
     * 货品名称
     */
    private String title;
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 类目Id
     */
    private List<Long> categoryIds;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * skuIds
     */
    private List<Long> skuIds;

    /** 品牌名称 **/
    private String brandName;
    /**
     * 申请状态 0待审核1已通过2已拒绝
     */
    private Integer status;
    /**
     * 仓储服务商 0自营 1自营及代仓
     */
    private Integer agentType;

    /**
     * 是否关联商品
     */
    private Integer associated;

    /**
     * spuId
     */
    private Long spuId;

    /**
     * spuIds
     */
    private List<Long> spuIds;
    /**
     * 停用状态 0-停用 1-启用
     */
    private Integer useFlag;

    /**
     * 自营列表分页查询的skuID
     * 新增字段，为与其它skuID查询分开
     */
    private Long paramSkuId;
}

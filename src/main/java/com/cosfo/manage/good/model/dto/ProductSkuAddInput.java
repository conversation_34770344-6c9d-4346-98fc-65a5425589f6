package com.cosfo.manage.good.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/7
 */
@Data
public class ProductSkuAddInput {
    /**
     * skuId
     */
    private Long id;
    /**
     * 规格
     */
    private String specification;
    /** 规格类型 0-容量*数量 1-区间 **/
    private Integer specificationType;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 地点类型0进口1国产
     */
    private Integer placeType;
    /**
     * 体积
     */
    private String volume;
    /**
     * 体积单位
     */
    private Long volumeUnit;
    /**
     * 重量
     */
    private Double weight;
    /**
     * 备注
     */
    private String weightNotes;
    /**
     * 仓储服务商 0自营 1自营及代仓
     */
    private Integer agentType;
    /**
     * 库存数量
     */
    private Integer amount;

    /**
     * 税率
     */
    private BigDecimal taxRateValue;
    /**
     * 自有编码
     */
    private String customSkuCode;
    /**
     * xm编码
     */
    private String skuCode;
}

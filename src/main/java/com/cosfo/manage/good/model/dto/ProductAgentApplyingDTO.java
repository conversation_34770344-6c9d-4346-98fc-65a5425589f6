package com.cosfo.manage.good.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/4
 */
@Data
public class ProductAgentApplyingDTO {
    /**
     * skuId
     */
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    /**
     * 代理租户Id
     */
    @NotNull(message = "agentTenantId不能为空")
    private Long agentTenantId;
}

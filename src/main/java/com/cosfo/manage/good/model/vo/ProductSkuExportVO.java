package com.cosfo.manage.good.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * 自营货品导出实体类
 * @author: xiaowk
 * @date: 2023/6/20 下午3:34
 */
@Data
public class ProductSkuExportVO {
    /**
     * 序号
     */
    private String serialNumber;


    //--------------spu信息 以下为--------------
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 货品名称
     */
    private String title;
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目
     */
    private String thirdCategory;

    /**
     * [一级类目]/[二级类目]/[三级类目]
     */
    private String categoryStr;

    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 产地
     */
    private String origin;
    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;

    private String storageLocationStr;

    /**
     * 存储温度
     */
    private String storageTemperature;

    /** 保质期类型：长期/定期 **/
    private String guaranteePeriodType;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     * 保质期单位
     */
    private Integer guaranteeUnit;

    private String guaranteeUnitStr;

    //--------------sku信息 以下为--------------
    /**
     * skuId
     */
    private Long skuId;

    /**
     * 规格类型
     */
    private String specificationType;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 产地类型 0进口1国产
     */
    private Integer placeType;
    /**
     * 产地类型 0进口1国产
     */
    private String placeTypeStr;
    /**
     * 体积
     */
    private String volume;
    /**
     * 体积单位
     */
    private Long volumeUnit;

    private String volumeUnitStr;
    /**
     * 重量
     */
    private Double weight;

    private String weightStr;
    /**
     * 重量备注
     */
    private String weightNotes;

    /**
     * 税率
     */
    private BigDecimal taxRateValue;

    /**
     * 税率
     */
    private String taxRateValueStr;

    /**
     * 是否申请通过代仓服务 false 未申请 ture 已申请
     */
    private Boolean havingApplied;

    private String havingAppliedStr;

    /**
     * 自有编码
     */
    private String customSkuCode;

    /**
     * 停用状态 0-停用 1-启用
     */
    private Integer useFlag;

    private String useFlagStr;
}

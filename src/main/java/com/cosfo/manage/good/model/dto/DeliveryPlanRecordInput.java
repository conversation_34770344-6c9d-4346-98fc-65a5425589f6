package com.cosfo.manage.good.model.dto;

import com.cosfo.manage.common.model.dto.PageDTO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/9
 */
@Data
public class DeliveryPlanRecordInput {
    /**
     * 仓库编号
     */
    private Long warehouseNo;
    /**
     * 配送城市
     */
    private String cityName;
    /**
     * 编号
     */
    private String businessNo;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 分页数量
     */
    private Integer pageSize;
}

package com.cosfo.manage.good.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/12/18 16:13
 * @Description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProductAgentRecordQueryDTO {

    /**
     * 租户ID
     */
    private List<Long> tenantIds;

    /**
     * sku列表
     */
    private List<Long> skuIds;

    /**
     * 审核状态 0审核中 1已通过 2已拒绝 3已取消
     */
    private Integer status;

    /**
     * 自定义排序
     */
    private String orderBy;
}

package com.cosfo.manage.good.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProductSkuApplyDetailVO {
    /**
     * skuId
     */
    private Long id;
    /**
     * 记录ID
     */
    private Long recordId;
    /**
     * 鲜沐Sku编码
     */
    private String agentSkuCode;

    /**
     * 租户
     */
    private Long tenantId;
    /**
     * 商城名称
     */
    private String tenantName;

    /**
     * spuId
     */
    private Long spuId;
    /**
     * 货品名称
     */
    private String title;
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 产地
     */
    private String origin;
    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    @NotNull(message = "储存区域 不能空")
    private Integer storageLocation;
    /**
     * 存储温度
     */
    @NotNull(message = "存储温度")
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目
     */
    private String thirdCategory;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 规格类型 0容量*数量 1区间
     */
    private Integer specificationType;
    /**
     * 产地类型 0进口1国产
     */
    private Integer placeType;
    /**
     * 体积
     */
    private String volume;

    /**
     * 体积单位
     */
    private Long volumeUnit;
    /**
     * 重量
     */
    private Double weight;

    /**
     * 重量备注
     */
    private String weightNotes;
    /**
     * 是否代仓 0不代仓1代仓
     */
    @Deprecated
    private Integer agentType;
    /**
     * 状态：0待审核1已通过2已拒绝 3已取消
     */
    private Integer status;
    /**
     * 拒绝原因
     */
    private String reason;
    /**
     * 提交时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime commitTime;
    /**
     * 审核时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 代仓租户Id
     */
    private Integer agentTenantId;
    /**
     * 代仓租户名称
     */
    private String agentTenantName;

    /**
     * 自有编码
     */
    private String customSkuCode;

    /**
     * 税率
     */
    private BigDecimal taxRateValue;
    /**
     * 首次申请时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstApplyTime;
    /**
     * 最后更新时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;
}

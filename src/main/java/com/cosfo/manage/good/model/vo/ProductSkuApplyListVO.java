package com.cosfo.manage.good.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/7
 */
@Data
public class ProductSkuApplyListVO {
    /**
     * skuId
     */
    private Long id;

    /**
     * spuId
     */
    private Long spuId;
    /**
     * 货品名称
     */
    private String title;

    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目
     */
    private String thirdCategory;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 产地类型 0进口1国产
     */
    private Integer placeType;
    /**
     * 体积
     */
    private String volume;

    /**
     * 体积单位
     */
    private Long volumeUnit;
    /**
     * 重量
     */
    private Double weight;
    /**
     * 重量备注
     */
    private String weightNotes;
    /**
     * 是否代仓 0不代仓1代仓
     */
    @Deprecated
    private Integer agentType;
    /**
     * 状态：0待审核1已通过2已拒绝
     */
    private Integer status;
    /**
     * 拒绝原因
     */
    private String reason;
    /**
     * 提交时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime commitTime;
    /**
     * 审核时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;
}

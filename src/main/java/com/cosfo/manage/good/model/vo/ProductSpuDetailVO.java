package com.cosfo.manage.good.model.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ProductSpuDetailVO {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 租户
     */
    private Long tenantId;
    /**
     * 货品名称
     */
    private String title;
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目
     */
    private String thirdCategory;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 产地
     */
    private String origin;
    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    @NotNull(message = "储存区域 不能空")
    private Integer storageLocation;
    /**
     * 存储温度
     */
    @NotNull(message = "存储温度")
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 代仓申请状态 1-该spu下至少有一个sku申请中 2-该spu下至少有一个sku申请通过 3-该spu下所有sku未申请代仓/申请被拒
     */
    private Integer agentStatus;
    /**
     * sku列表
     */
    private List<ProductSkuVo> productSkuVoList;

}

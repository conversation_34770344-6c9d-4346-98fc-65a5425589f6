package com.cosfo.manage.good.model.dto;

import com.cosfo.manage.common.model.dto.PageDTO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/9
 */
@Data
public class WarehousingRecordInput {

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 出库类型
     */
    private Integer outOfStockType;

    /**
     * 入库类型
     */
    private Integer warehousingType;

    /**
     * 货品名称
     */
    private String title;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 记录开始时间
     */
    private LocalDateTime startTime;

    /**
     * 记录结束时间
     */
    private LocalDateTime endTime;

    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 分页数量
     */
    private Integer pageSize;
}

package com.cosfo.manage.good.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 代仓申请记录查询条件
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductAgentApplicationRecordQueryConditionDTO {
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 代理服务商
     */
    private Long agentTenantId;
    /**
     * 品牌方租户Id
     */
    private Long tenantId;
    /**
     * 审核状态 0审核中 1已通过 2已拒绝 3已取消
     */
    private Integer status;
    /**
     * skuIds
     */
    private List<Long> skuIds;
}

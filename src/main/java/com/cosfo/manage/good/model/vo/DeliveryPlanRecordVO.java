package com.cosfo.manage.good.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/9
 */
@Data
public class DeliveryPlanRecordVO {
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店类型 0、直营店 1、加盟店 2、托管店
     */
    private Integer storeType;
    /**
     * 门店类型 0、直营店 1、加盟店 2、托管店
     */
    private String storeTypeDesc;
    /**
     * 仓库
     */
    private String warehouse;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 业务编号
     */
    private String businessNo;
    /**
     * 货品件数
     */
    private Integer itemAmount;
    /**
     * 商品件数
     */
    private Integer amount;
    /**
     * 商品总额
     */
    private BigDecimal totalPrice;
    /**
     * 运费
     */
    private BigDecimal deliveryFee;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 售后Id
     */
    private Long orderAfterSaleId;
    /**
     * 单子类型 1 订单 2售后单
     */
    private Integer businessType;
}

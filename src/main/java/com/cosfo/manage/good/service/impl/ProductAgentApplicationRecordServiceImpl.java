package com.cosfo.manage.good.service.impl;

import com.cosfo.manage.bizlog.convert.BizLogConvert;
import com.cosfo.manage.bizlog.model.dto.BizLogQueryDTO;
import com.cosfo.manage.bizlog.model.vo.BizLogListVO;
import com.cosfo.manage.common.constant.BizLogConstants;
import com.cosfo.manage.common.constant.ProductAgentConstants;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.context.ProductAgentItemStatusEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.facade.BizLogFacade;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.facade.SummerFarmInterfaceServiceFacade;
import com.cosfo.manage.facade.dto.ApplyAgentSkuDTO;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.good.convert.ProductAgentApplicationRecordConvert;
import com.cosfo.manage.good.dao.ProductAgentApplicationRecordDao;
import com.cosfo.manage.good.mapper.ProductAgentApplicationRecordMapper;
import com.cosfo.manage.good.model.dto.*;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import com.cosfo.manage.good.model.vo.AgentSkuBizLogVO;
import com.cosfo.manage.good.model.vo.ProductSkuAgentRecordDetailVO;
import com.cosfo.manage.good.model.vo.ProductSkuApplyDetailVO;
import com.cosfo.manage.good.service.ProductAgentApplicationRecordService;
import com.cosfo.manage.good.service.SkuAgentLogService;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuQueryConditionDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.summerfarm.enums.CreateTypeEnums;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.input.PageSortInput;
import net.xianmu.log.annation.BizLogRecord;
import net.xianmu.log.config.BizLogRecordContext;
import net.xianmu.log.dto.OperatorDto;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/4
 */
@Slf4j
@Service
public class ProductAgentApplicationRecordServiceImpl implements ProductAgentApplicationRecordService {
    @Resource
    private ProductAgentApplicationRecordDao productAgentApplicationRecordDao;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;
    @Resource
    private ProductAgentApplicationRecordMapper productAgentApplicationRecordMapper;
    @Resource
    private ProductFacade productFacade;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private BizLogFacade bizLogFacade;
    @Resource
    private SkuAgentLogService skuAgentLogService;

    @Override
    public PageInfo<ProductSkuApplyDetailVO> listOld(ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 组装查询条件
        ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
        productAgentApplicationRecordQueryConditionDTO.setAgentTenantId(productAgentApplicationQueryDTO.getAgentTenantId());
        productAgentApplicationRecordQueryConditionDTO.setTenantId(loginContextInfoDTO.getTenantId());
        productAgentApplicationRecordQueryConditionDTO.setStatus(productAgentApplicationQueryDTO.getStatus());
        productAgentApplicationRecordQueryConditionDTO.setSkuId(productAgentApplicationQueryDTO.getSkuId());
        // 商品名称查询
        if (!StringUtils.isEmpty(productAgentApplicationQueryDTO.getTitle()) || Objects.nonNull(productAgentApplicationQueryDTO.getCategoryId())) {
            ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO();
            productSkuQueryConditionDTO.setTenantId(loginContextInfoDTO.getTenantId());
            productSkuQueryConditionDTO.setTitle(productAgentApplicationQueryDTO.getTitle());
            productSkuQueryConditionDTO.setCategoryIds(productAgentApplicationQueryDTO.getCategoryIds());
            productSkuQueryConditionDTO.setCategoryId(productAgentApplicationQueryDTO.getCategoryId());
            List<ProductSkuDTO> productSkuDTOS = productSkuService.queryByConditionOld(productSkuQueryConditionDTO);
            if (CollectionUtils.isEmpty(productSkuDTOS)) {
                return PageInfoHelper.createPageInfo(new ArrayList<>(), productAgentApplicationQueryDTO.getPageSize());
            }

            List<Long> skuIds = productSkuDTOS.stream().map(ProductSkuDTO::getId).collect(Collectors.toList());
            productAgentApplicationRecordQueryConditionDTO.setSkuIds(skuIds);
        }

        // 查询代仓服务申请记录
        PageHelper.startPage(productAgentApplicationQueryDTO.getPageIndex(), productAgentApplicationQueryDTO.getPageSize());
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition(productAgentApplicationRecordQueryConditionDTO);
        if (CollectionUtils.isEmpty(productAgentApplicationRecords)) {
            return PageInfoHelper.createPageInfo(new ArrayList<>(), productAgentApplicationQueryDTO.getPageSize());
        }

        PageInfo pageInfo = PageInfoHelper.createPageInfo(productAgentApplicationRecords, productAgentApplicationQueryDTO.getPageSize());
        // 货品skuId
        Set<Long> skuIds = productAgentApplicationRecords.stream().map(ProductAgentApplicationRecord::getSkuId).collect(Collectors.toSet());
        ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO();
        productSkuQueryConditionDTO.setSkuIds(new ArrayList<>(skuIds));
        // 查询货品信息
        List<ProductSkuDTO> productSkuDTOS = productSkuService.queryByConditionOld(productSkuQueryConditionDTO);
        Map<Long, ProductSkuDTO> productSkuDTOMap = productSkuDTOS.stream().collect(Collectors.toMap(ProductSkuDTO::getId, item -> item));
        List<ProductSkuApplyDetailVO> productSkuApplyDetailVOS = productAgentApplicationRecords.stream().map(productAgentApplicationRecord -> {
            ProductSkuDTO productSkuDTO = productSkuDTOMap.get(productAgentApplicationRecord.getSkuId());
            return ProductAgentApplicationRecordConvert.convertToProductSkuApplyDetailVO(productAgentApplicationRecord, productSkuDTO);
        }).collect(Collectors.toList());
        pageInfo.setList(productSkuApplyDetailVOS);
        return pageInfo;
    }

    @Override
    public PageInfo<ProductSkuApplyDetailVO> list(ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 组装查询条件
        ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
        productAgentApplicationRecordQueryConditionDTO.setAgentTenantId(productAgentApplicationQueryDTO.getAgentTenantId());
        productAgentApplicationRecordQueryConditionDTO.setTenantId(loginContextInfoDTO.getTenantId());
        productAgentApplicationRecordQueryConditionDTO.setStatus(productAgentApplicationQueryDTO.getStatus());
        productAgentApplicationRecordQueryConditionDTO.setSkuId(productAgentApplicationQueryDTO.getSkuId());
        // 商品名称查询
        if (!StringUtils.isEmpty(productAgentApplicationQueryDTO.getTitle()) || Objects.nonNull(productAgentApplicationQueryDTO.getCategoryId())) {
            ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO();
            productSkuQueryConditionDTO.setTenantId(loginContextInfoDTO.getTenantId());
            productSkuQueryConditionDTO.setTitle(productAgentApplicationQueryDTO.getTitle());
            productSkuQueryConditionDTO.setCategoryIds(productAgentApplicationQueryDTO.getCategoryIds());
            productSkuQueryConditionDTO.setCategoryId(productAgentApplicationQueryDTO.getCategoryId());
            List<ProductSkuDTO> productSkuDTOS = productSkuService.queryByCondition(productSkuQueryConditionDTO);
            if (CollectionUtils.isEmpty(productSkuDTOS)) {
                return PageInfoHelper.createPageInfo(new ArrayList<>(), productAgentApplicationQueryDTO.getPageSize());
            }

            List<Long> skuIds = productSkuDTOS.stream().map(ProductSkuDTO::getId).collect(Collectors.toList());
            productAgentApplicationRecordQueryConditionDTO.setSkuIds(skuIds);
        }

        // 查询代仓服务申请记录
        PageHelper.startPage(productAgentApplicationQueryDTO.getPageIndex(), productAgentApplicationQueryDTO.getPageSize());
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition(productAgentApplicationRecordQueryConditionDTO);
        if (CollectionUtils.isEmpty(productAgentApplicationRecords)) {
            return PageInfoHelper.createPageInfo(new ArrayList<>(), productAgentApplicationQueryDTO.getPageSize());
        }

        PageInfo pageInfo = PageInfoHelper.createPageInfo(productAgentApplicationRecords, productAgentApplicationQueryDTO.getPageSize());
        // 货品skuId
        Set<Long> skuIds = productAgentApplicationRecords.stream().map(ProductAgentApplicationRecord::getSkuId).collect(Collectors.toSet());
        ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO();
        productSkuQueryConditionDTO.setSkuIds(new ArrayList<>(skuIds));
        productSkuQueryConditionDTO.setTenantId(loginContextInfoDTO.getTenantId());
        // 查询货品信息
        List<ProductSkuDTO> productSkuDTOS = productSkuService.queryByCondition(productSkuQueryConditionDTO);
        Map<Long, ProductSkuDTO> productSkuDTOMap = productSkuDTOS.stream().collect(Collectors.toMap(ProductSkuDTO::getId, item -> item));
        List<ProductSkuApplyDetailVO> productSkuApplyDetailVOS = productAgentApplicationRecords.stream().map(productAgentApplicationRecord -> {
            ProductSkuDTO productSkuDTO = productSkuDTOMap.get(productAgentApplicationRecord.getSkuId());
            return ProductAgentApplicationRecordConvert.convertToProductSkuApplyDetailVO(productAgentApplicationRecord, productSkuDTO);
        }).collect(Collectors.toList());
        pageInfo.setList(productSkuApplyDetailVOS);
        return pageInfo;
    }

    /**
     * 申请记录列表 - 以sku为基准
     *
     * @param productAgentApplicationQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    @Override
    public PageInfo<ProductSkuApplyDetailVO> listSkuAgentRecord(ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 构造查询参数
        ProductAgentRecordQueryDTO queryDTO = buildAgentQueryDTO(productAgentApplicationQueryDTO, loginContextInfoDTO.getTenantId());
        if (Objects.isNull(queryDTO)) {
            return PageInfo.emptyPageInfo();
        }
        if (!CollectionUtils.isEmpty(productAgentApplicationQueryDTO.getSortList())) {
            PageSortInput sortInput = productAgentApplicationQueryDTO.getSortList().get(0);
            if (ProductAgentConstants.AGENT_ORDER_FIELD_LIST.contains(sortInput.getSortBy()) && ProductAgentConstants.AGENT_ORDER_SORT_LIST.contains(sortInput.getOrderBy())) {
                queryDTO.setOrderBy(ProductAgentConstants.AGENT_ORDER_FIELD_MAP.get(sortInput.getSortBy()) + " " + sortInput.getOrderBy());
            }
        }

        // 分页查询申请记录
        PageHelper.startPage(productAgentApplicationQueryDTO.getPageIndex(), productAgentApplicationQueryDTO.getPageSize());
        List<ProductAgentRecordBySkuDTO> agentApplicationRecords = productAgentApplicationRecordMapper.selectLastRecord(queryDTO);
        if (CollectionUtils.isEmpty(agentApplicationRecords)) {
            return PageInfo.emptyPageInfo();
        }
        PageInfo<ProductAgentRecordBySkuDTO> pageInfo = PageInfoHelper.createPageInfo(agentApplicationRecords, productAgentApplicationQueryDTO.getPageSize());

        // 分租户查询Sku信息
        Map<Long, Set<Long>> tenantSkuIdMap = agentApplicationRecords.stream()
            .collect(Collectors.groupingBy(ProductAgentRecordBySkuDTO::getTenantId,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    tenantList -> tenantList.stream().map(ProductAgentRecordBySkuDTO::getSkuId).collect(Collectors.toSet()))));
        List<ProductSkuDTO> productSkuDtoList = new ArrayList<>();
        tenantSkuIdMap.forEach((tenantId, skuIds) -> {
            ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO();
            productSkuQueryConditionDTO.setSkuIds(new ArrayList<>(skuIds));
            productSkuQueryConditionDTO.setTenantId(tenantId);
            // 查询货品信息
            productSkuDtoList.addAll(productSkuService.queryByCondition(productSkuQueryConditionDTO));
        });

        // 查询所有租户
        Set<Long> tenantIds = tenantSkuIdMap.keySet();
        List<TenantResultResp> tenantsByIds = userCenterTenantFacade.getTenantsByIds(new ArrayList<>(tenantIds));

        Map<Long, ProductSkuDTO> productSkuDtoMap = productSkuDtoList.stream().collect(Collectors.toMap(ProductSkuDTO::getId, Function.identity()));
        List<ProductSkuApplyDetailVO> productSkuApplyDetailVOList = agentApplicationRecords.stream()
            .map(productAgentApplicationRecord -> {
                ProductSkuDTO productSkuDTO = productSkuDtoMap.get(productAgentApplicationRecord.getSkuId());
                return ProductAgentApplicationRecordConvert.convertToProductSkuApplyDetailVO(productAgentApplicationRecord, productSkuDTO, tenantsByIds);
            }).collect(Collectors.toList());
        return PageInfoHelper.pageInfoCopy(pageInfo, productSkuApplyDetailVOList);
    }

    /**
     * 根据条件查询sku
     *
     * @param productAgentApplicationQueryDTO
     * @param tenantId
     * @return
     */
    private ProductAgentRecordQueryDTO buildAgentQueryDTO(ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO, Long tenantId) {
        ProductAgentRecordQueryDTO queryDTO = ProductAgentRecordQueryDTO.builder()
            .status(productAgentApplicationQueryDTO.getStatus())
            .build();

        // 有sku条件(saas侧查询条件)
        if (!XianmuSupplyTenant.TENANT_ID.equals(tenantId)) {
            queryDTO.setTenantIds(Collections.singletonList(tenantId));
            if (!StringUtils.isEmpty(productAgentApplicationQueryDTO.getTitle()) || Objects.nonNull(productAgentApplicationQueryDTO.getCategoryId())
                || Objects.nonNull(productAgentApplicationQueryDTO.getSkuId())) {
                ProductSkuQueryConditionDTO productSkuQueryConditionDTO = new ProductSkuQueryConditionDTO();
                productSkuQueryConditionDTO.setTenantId(tenantId);
                productSkuQueryConditionDTO.setTitle(productAgentApplicationQueryDTO.getTitle());
                productSkuQueryConditionDTO.setCategoryId(productAgentApplicationQueryDTO.getCategoryId());
                Optional.ofNullable(productAgentApplicationQueryDTO.getSkuId()).ifPresent(skuId -> {
                    productSkuQueryConditionDTO.setSkuIds(Collections.singletonList(skuId));
                });
                List<ProductSkuDTO> productSkuDTOS = productSkuService.queryByCondition(productSkuQueryConditionDTO);
                if (CollectionUtils.isEmpty(productSkuDTOS)) {
                    return null;
                }
                List<Long> skuIds = productSkuDTOS.stream().map(ProductSkuDTO::getId).collect(Collectors.toList());
                queryDTO.setSkuIds(skuIds);
            }
        }

        // 有sku条件(鲜沐侧查询条件)
        if (XianmuSupplyTenant.TENANT_ID.equals(tenantId)) {
            Long querySkuId = null;
            if (Objects.nonNull(productAgentApplicationQueryDTO.getAgentSkuCode())) {
                Set<Long> skuIdSet = productFacade.querySkuIdBySkuCodes(Collections.singletonList(productAgentApplicationQueryDTO.getAgentSkuCode()));
                if (CollectionUtils.isEmpty(skuIdSet)) {
                    return null;
                }
                querySkuId = new ArrayList<>(skuIdSet).get(0);

                if (Objects.nonNull(productAgentApplicationQueryDTO.getSkuId()) && !Objects.equals(querySkuId, productAgentApplicationQueryDTO.getSkuId())) {
                    return null;
                }
            }
            if (Objects.nonNull(productAgentApplicationQueryDTO.getSkuId())) {
                queryDTO.setSkuIds(Collections.singletonList(productAgentApplicationQueryDTO.getSkuId()));
            } else if (Objects.nonNull(querySkuId)) {
                queryDTO.setSkuIds(Collections.singletonList(querySkuId));
            }

            List<Long> queryTenantIds = new ArrayList<>();
            if (Objects.nonNull(productAgentApplicationQueryDTO.getQueryTenantName())) {
                TenantQueryReq tenantQueryReq = TenantQueryReq.builder().tenantName(productAgentApplicationQueryDTO.getQueryTenantName()).build();
                List<TenantResultResp> tenantsByQuery = userCenterTenantFacade.getTenantsByQuery(tenantQueryReq);
                if (CollectionUtils.isEmpty(tenantsByQuery)) {
                    return null;
                }
                Set<Long> tenantIds = tenantsByQuery.stream().map(TenantResultResp::getId).collect(Collectors.toSet());
                if (Objects.nonNull(productAgentApplicationQueryDTO.getQueryTenantId()) && !tenantIds.contains(productAgentApplicationQueryDTO.getQueryTenantId())) {
                    return null;
                }
                queryTenantIds = new ArrayList<>(tenantIds);
            }
            if (Objects.nonNull(productAgentApplicationQueryDTO.getQueryTenantId())) {
                queryDTO.setTenantIds(Collections.singletonList(productAgentApplicationQueryDTO.getQueryTenantId()));
            } else {
                queryDTO.setTenantIds(queryTenantIds);
            }
        }
        return queryDTO;
    }


    /**
     * 审核详情
     * （2023.12.19只在鲜沐侧有调用）
     *
     * @param recordId
     * @param tenantId
     * @return
     */
    @Override
    public ProductSkuAgentRecordDetailVO skuAgentRecordDetail(Long recordId, Long tenantId) {
        // 查询对应申请记录
        ProductAgentApplicationRecord record = productAgentApplicationRecordMapper.getOneByid(recordId);
        if (Objects.isNull(record)) {
            throw new BizException("未找到对应申请记录！");
        }
        if (!XianmuSupplyTenant.TENANT_ID.equals(tenantId) && !Objects.equals(tenantId, record.getTenantId())) {
            throw new BizException("未找到对应申请记录！");
        }

        ProductSkuDTO productSkuDTO = skuAgentLogService.getProductSkuDTO(tenantId, record.getSkuId());

        TenantResultResp tenant = userCenterTenantFacade.getTenantById(record.getTenantId());
        return ProductAgentApplicationRecordConvert.convert2RecordDetail(record, productSkuDTO, tenant.getTenantName());

    }


    @Override
    @BizLogRecord(operationName = "代仓发起申请", bizKey = "#content.productSkuVoList[0].id", bizKeyTenantId = "#content.tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public void apply(ProductAgentApplyingDTO productAgentApplyingDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 没有操作人时，使用系统操作人
        if (Objects.isNull(loginContextInfoDTO.getAuthUserId())){
            OperatorDto systemOperator = BizLogConstants.getSystemOperator(loginContextInfoDTO.getTenantId());
            BizLogRecordContext.putDefaultOperator(systemOperator);
        }

        // 查询类目
        ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
        productAgentApplicationRecordQueryConditionDTO.setTenantId(loginContextInfoDTO.getTenantId());
        productAgentApplicationRecordQueryConditionDTO.setAgentTenantId(productAgentApplyingDTO.getAgentTenantId());
        productAgentApplicationRecordQueryConditionDTO.setSkuId(productAgentApplyingDTO.getSkuId());
        // 查询申请记录
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition(productAgentApplicationRecordQueryConditionDTO);
        if (!CollectionUtils.isEmpty(productAgentApplicationRecords)) {
            boolean match = productAgentApplicationRecords.stream().anyMatch(item -> ProductAgentItemStatusEnum.SUCCESS.getStatus().equals(item.getStatus()) || ProductAgentItemStatusEnum.PROCESSING.getStatus().equals(item.getStatus()));
            if (match) {
                throw new BizException("该sku已经申请代仓服务能力，不能重复申请");
            }
        }

        // 查询鲜沐商品映射
        ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByTenantIdAndSkuId(loginContextInfoDTO.getTenantId(), productAgentApplyingDTO.getSkuId());
        if (Objects.isNull(productAgentSkuMapping)) {
            log.error("skuId{}未建立映射关系", productAgentApplyingDTO.getSkuId());
            throw new BizException("该sku暂时不能申请代仓服务");
        }

        // 申请代仓服务
        ApplyAgentSkuDTO applyAgentSkuDTO = new ApplyAgentSkuDTO();
        applyAgentSkuDTO.setCreateType(CreateTypeEnums.SELF_AND_AGENT.getType());
        applyAgentSkuDTO.setSkuId(productAgentSkuMapping.getAgentSkuId());
        applyAgentSkuDTO.setOperator(loginContextInfoDTO.getPhone());
        Boolean applyResult = summerFarmInterfaceServiceFacade.applyAgentSku(applyAgentSkuDTO);
        if (!applyResult) {
            throw new BizException("代仓服务申请失败");
        }

        // 保存代仓服务申请记录
        ProductAgentApplicationRecord productAgentApplicationRecord = new ProductAgentApplicationRecord();
        productAgentApplicationRecord.setSkuId(productAgentApplyingDTO.getSkuId());
        productAgentApplicationRecord.setTenantId(loginContextInfoDTO.getTenantId());
        productAgentApplicationRecord.setAgentTenantId(productAgentApplyingDTO.getAgentTenantId());
        productAgentApplicationRecord.setStatus(ProductAgentItemStatusEnum.PROCESSING.getStatus());
        productAgentApplicationRecordDao.save(productAgentApplicationRecord);

        // 记录货品快照
        ProductSkuDTO productSkuDTO = skuAgentLogService.getProductSkuDTO(loginContextInfoDTO.getTenantId(), productAgentApplyingDTO.getSkuId());
        BizLogRecordContext.put("content", ProductAgentApplicationRecordConvert.getSpuDetailVO(productSkuDTO));
    }


    /**
     * 取消申请
     *
     * @param productAgentSkuParamDTO
     * @param loginContextInfoDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @BizLogRecord(operationName = "代仓取消申请", bizKey = "#content.productSkuVoList[0].id", bizKeyTenantId = "#content.tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public void cancel(ProductAgentSkuParamDTO productAgentSkuParamDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 查询记录
        ProductAgentApplicationRecordQueryConditionDTO productAgentApplicationRecordQueryConditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
        productAgentApplicationRecordQueryConditionDTO.setTenantId(loginContextInfoDTO.getTenantId());
        productAgentApplicationRecordQueryConditionDTO.setSkuId(productAgentSkuParamDTO.getSkuId());
        // 查询申请记录
        List<ProductAgentApplicationRecord> agentRecordList = productAgentApplicationRecordDao.listByCondition(productAgentApplicationRecordQueryConditionDTO);
        if (CollectionUtils.isEmpty(agentRecordList)) {
            throw new BizException("未找到代仓申请记录！");
        }
        ProductAgentApplicationRecord lastAgentRecord = agentRecordList.get(0);
        if (!ProductAgentItemStatusEnum.PROCESSING.getStatus().equals(lastAgentRecord.getStatus())) {
            throw new BizException("只有审核中才可以取消申请！");
        }
        lastAgentRecord.setStatus(ProductAgentItemStatusEnum.CANCEL.getStatus());
        lastAgentRecord.setUpdateTime(LocalDateTime.now());
        productAgentApplicationRecordDao.updateById(lastAgentRecord);

        // 查询鲜沐商品映射
        ProductsMappingResp productAgentSkuMapping = productFacade.getProductMappingBySkuIdAndTenantId(productAgentSkuParamDTO.getSkuId(), loginContextInfoDTO.getTenantId());
        if (Objects.isNull(productAgentSkuMapping)) {
            log.error("skuId{}未建立映射关系", productAgentSkuParamDTO.getSkuId());
            throw new BizException("该sku暂时不能取消代仓服务");
        }

        // 申请代仓服务
        ApplyAgentSkuDTO applyAgentSkuDTO = new ApplyAgentSkuDTO();
        applyAgentSkuDTO.setCreateType(CreateTypeEnums.SELF_AND_AGENT.getType());
        applyAgentSkuDTO.setSkuId(productAgentSkuMapping.getAgentSkuId());
        applyAgentSkuDTO.setOperator(loginContextInfoDTO.getPhone());
        Boolean applyResult = summerFarmInterfaceServiceFacade.cancelAgentSku(applyAgentSkuDTO);
        if (!applyResult) {
            throw new BizException("代仓服务申请失败");
        }

        // 记录货品快照
        ProductSkuDTO productSkuDTO = skuAgentLogService.getProductSkuDTO(loginContextInfoDTO.getTenantId(), productAgentSkuParamDTO.getSkuId());
        BizLogRecordContext.put("content", ProductAgentApplicationRecordConvert.getSpuDetailVO(productSkuDTO));
    }

    @Override
    public List<AgentSkuBizLogVO> listSkuAgentBizLog(Long skuId, LoginContextInfoDTO loginContextInfoDTO) {

        BizLogQueryDTO queryDTO = new BizLogQueryDTO();
        queryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        queryDTO.setBizDomain(BizLogConstants.SKU_AGENT_DOMAIN);
        queryDTO.setEntityType(BizLogConstants.SKU_AGENT_ENTITY_TYPE);
        queryDTO.setBizKey(skuId.toString());
        queryDTO.setPageIndex(BizLogConstants.DEFAULT_PAGE_INDEX);
        queryDTO.setPageSize(BizLogConstants.DEFAULT_PAGE_SIZE);
        PageInfo<BizLogListVO> bizLogListVOPageInfo = bizLogFacade.listBizLog(queryDTO);
        List<BizLogListVO> bizLogListVOList = bizLogListVOPageInfo.getList();
        if (CollectionUtils.isEmpty(bizLogListVOList)) {
            return Collections.emptyList();
        }
        return BizLogConvert.convert2AgentLogList(bizLogListVOList);
    }

//    @Override
//    @BizLogRecord(operationName = "代仓发起申请", bizKey = "#content.productSkuVoList[0].id", bizKeyTenantId = "#content.tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
//    public void importApplyRecord(Long skuId, Long tenantId) {
//        ProductAgentApplicationRecord productAgentApplicationRecord = new ProductAgentApplicationRecord();
//        productAgentApplicationRecord.setSkuId(skuId);
//        productAgentApplicationRecord.setTenantId(tenantId);
//        productAgentApplicationRecord.setAgentTenantId(XianmuSupplyTenant.TENANT_ID);
//        productAgentApplicationRecord.setStatus(ProductAgentItemStatusEnum.PROCESSING.getStatus());
//        productAgentApplicationRecordDao.save(productAgentApplicationRecord);
//        // 记录货品快照
//        ProductSkuDTO productSkuDTO = skuAgentLogService.getProductSkuDTO(tenantId, skuId);
//        BizLogRecordContext.put("content", ProductAgentApplicationRecordConvert.getSpuDetailVO(productSkuDTO));
//    }
}
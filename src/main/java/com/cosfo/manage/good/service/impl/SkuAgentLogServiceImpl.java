package com.cosfo.manage.good.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.BizLogConstants;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.facade.BizLogFacade;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.good.convert.ProductAgentApplicationRecordConvert;
import com.cosfo.manage.good.service.SkuAgentLogService;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.req.bizlog.SaveBizLogRecordReq;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.log.annation.BizLogRecord;
import net.xianmu.log.config.BizLogRecordContext;
import net.xianmu.log.dto.OperatorDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/12/28 16:47
 * @Description:
 */
@Service
@Slf4j
public class SkuAgentLogServiceImpl implements SkuAgentLogService {

    @Resource
    private ProductFacade productFacade;
    @Resource
    private BizLogFacade bizLogFacade;

    /**
     * 查询sku
     *
     * @param tenantId
     * @param skuId
     * @return
     */
    @Override
    public ProductSkuDTO getProductSkuDTO(Long tenantId, Long skuId) {
        // 查询sku信息
        ProductSkuDTO productSkuDTO = productFacade.selectProductSkuDetailById(skuId);
        if (Objects.isNull(productSkuDTO)) {
            throw new BizException("未找到对应货品！");
        }
        if (!XianmuSupplyTenant.TENANT_ID.equals(tenantId) && !Objects.equals(tenantId, productSkuDTO.getTenantId())) {
            throw new BizException("未找到对应货品！");
        }
        return productSkuDTO;
    }

    @Override
    @BizLogRecord(operationName = "代仓审核通过", bizKey = "#content.productSkuVoList[0].id", bizKeyTenantId = "#content.tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public void saveAuditSuccessBizLog(Long skuId) {
        OperatorDto defaultOperator = BizLogConstants.getDefaultOperator();
        BizLogRecordContext.putDefaultOperator(defaultOperator);
        // 记录货品快照
        ProductSkuDTO productSkuDTO = getProductSkuDTO(XianmuSupplyTenant.TENANT_ID, skuId);
        BizLogRecordContext.put("content", ProductAgentApplicationRecordConvert.getSpuDetailVO(productSkuDTO));
    }

    @Override
    @BizLogRecord(operationName = "代仓审核拒绝", bizKey = "#skuInfo.productSkuVoList[0].id", bizKeyTenantId = "#skuInfo.tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public void saveAuditFailBizLog(Long skuId, String refuseReason) {
        OperatorDto defaultOperator = BizLogConstants.getDefaultOperator();
        BizLogRecordContext.putDefaultOperator(defaultOperator);
        // 记录货品快照
        ProductSkuDTO productSkuDTO = getProductSkuDTO(XianmuSupplyTenant.TENANT_ID, skuId);
        BizLogRecordContext.put("skuInfo", ProductAgentApplicationRecordConvert.getSpuDetailVO(productSkuDTO));
        BizLogRecordContext.put("refuseReason", refuseReason);

        Map<String, Object> content = new HashMap<>();
        content.put("skuInfo", BizLogRecordContext.get("skuInfo"));
        content.put("refuseReason", BizLogRecordContext.get("refuseReason"));
        BizLogRecordContext.put("content", content);
    }

    @Override
    @BizLogRecord(operationName = "代仓修改申请", bizKey = "#skuAgentContent.productSkuVoList[0].id", bizKeyTenantId = "#skuAgentContent.tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#skuAgentContent)")
    public void saveAgentSkuEditBizLog(Long tenantId, Long skuId) {
        // 记录货品快照
        ProductSkuDTO productSkuDTO = getProductSkuDTO(tenantId, skuId);
        BizLogRecordContext.put("skuAgentContent", ProductAgentApplicationRecordConvert.getSpuDetailVO(productSkuDTO));
    }

    @Override
    public void saveSpuBizLog(Long tenantId, ProductSpu spu, List<ProductSku> skuList) {
        Long authUserId = BizLogConstants.NO_EXIST_USER_ID;
        try {
            Object authUserIdObj = BizLogRecordContext.get("authUserId");
            if (Objects.nonNull(authUserIdObj)) {
                authUserId = Long.parseLong(authUserIdObj.toString());
            }
            Map<String, Object> content = new HashMap<>();
            content.put("spuTitle", spu.getTitle());
            SaveBizLogRecordReq logReq = SaveBizLogRecordReq.builder()
                .source(SystemOriginEnum.COSFO_MANAGE.getType())
                .operationName("新增spu")
                .bizKey(spu.getId().toString())
                .bizKeyTenantId(tenantId)
                .content(JSON.toJSONString(content))
                .tenantId(tenantId)
                .createdTime(LocalDateTime.now())
                .createdId(authUserId)
                .build();
            bizLogFacade.saveBizLog(logReq);

            saveBatchSkuBizLog(tenantId, authUserId, skuList);
        } catch (Exception e) {
            log.info("新增货品，生成日志失败的参数：tenantId:{},authUserId:{},spu:{},skuList:{}", tenantId, authUserId, JSON.toJSONString(spu), JSON.toJSONString(skuList));
            log.error("新增货品，生成日志失败！", e);
        }
    }

    @Override
    public void updateSpuBizLog(Long tenantId, ProductSpu newSpu, ProductSpu oriSpu, List<ProductSku> skuList) {
        Long authUserId = BizLogConstants.NO_EXIST_USER_ID;
        try {
            Object authUserIdObj = BizLogRecordContext.get("authUserId");
            if (Objects.nonNull(authUserIdObj)) {
                authUserId = Long.parseLong(authUserIdObj.toString());
            }
            Map<String, Object> content = new HashMap<>();
            content.put("newSpu", newSpu);
            content.put("oriSpu", oriSpu);
            SaveBizLogRecordReq logReq = SaveBizLogRecordReq.builder()
                .source(SystemOriginEnum.COSFO_MANAGE.getType())
                .operationName("编辑spu")
                .bizKey(newSpu.getId().toString())
                .bizKeyTenantId(tenantId)
                .content(JSON.toJSONString(content))
                .tenantId(tenantId)
                .createdTime(LocalDateTime.now())
                .createdId(authUserId)
                .build();
            bizLogFacade.saveBizLog(logReq);

            saveBatchSkuBizLog(tenantId, authUserId, skuList);
        } catch (Exception e) {
            log.info("更新货品，生成日志失败的参数：tenantId:{},authUserId:{},newSpu:{},oriSpu:{},skuList:{}", tenantId, authUserId, JSON.toJSONString(newSpu), JSON.toJSONString(oriSpu), JSON.toJSONString(skuList));
            log.error("更新货品，生成日志失败！", e);
        }
    }

    @Override
    public void saveBatchSkuBizLog(Long tenantId, Long authUserId, List<ProductSku> skuList) {
        if (CollectionUtils.isNotEmpty(skuList)) {
            Map<Long, String> saveSkuContent = skuList.stream().collect(Collectors.toMap(ProductSku::getId, sku -> {
                Map<String, Object> skuContent = new HashMap<>();
                skuContent.put("skuId", sku.getId());
                return JSON.toJSONString(skuContent);
            }));
            SaveBizLogRecordReq saveSkuReq = SaveBizLogRecordReq.builder()
                .source(SystemOriginEnum.COSFO_MANAGE.getType())
                .operationName("新增sku")
                .bizKey(JSON.toJSONString(saveSkuContent.keySet()))
                .bizKeyTenantId(tenantId)
                .content(JSON.toJSONString(saveSkuContent))
                .tenantId(tenantId)
                .createdTime(LocalDateTime.now())
                // openAPI同步时，没有创建人
                .createdId(Optional.ofNullable(authUserId).orElse(BizLogConstants.NO_EXIST_USER_ID))
                .build();
            bizLogFacade.saveBizLog(saveSkuReq);
        }
    }

    @Override
    public void saveImportSkuBizLog(Long tenantId, Long authUserId, Map<Long, String> saveSpuContentMap, Map<Long, String> saveSkuContentMap, Map<Long, String> updateSpuContentMap, Map<Long, String> agentSkuContentMap) {
        Set<Long> saveSpuSet = saveSpuContentMap.keySet();
        if (CollectionUtils.isNotEmpty(saveSpuSet)){
            SaveBizLogRecordReq logReq = SaveBizLogRecordReq.builder()
                .source(SystemOriginEnum.COSFO_MANAGE.getType())
                .operationName("新增spu")
                .bizKey(JSON.toJSONString(saveSpuSet))
                .bizKeyTenantId(tenantId)
                .content(JSON.toJSONString(saveSpuContentMap))
                .tenantId(tenantId)
                .createdTime(LocalDateTime.now())
                .createdId(authUserId)
                .build();
            bizLogFacade.saveBizLog(logReq);
        }

        Set<Long> updateSpuSet = updateSpuContentMap.keySet();
        if (CollectionUtils.isNotEmpty(updateSpuSet)){
            SaveBizLogRecordReq logReq = SaveBizLogRecordReq.builder()
                .source(SystemOriginEnum.COSFO_MANAGE.getType())
                .operationName("编辑spu")
                .bizKey(JSON.toJSONString(updateSpuSet))
                .bizKeyTenantId(tenantId)
                .content(JSON.toJSONString(updateSpuContentMap))
                .tenantId(tenantId)
                .createdTime(LocalDateTime.now())
                .createdId(authUserId)
                .build();
            bizLogFacade.saveBizLog(logReq);
        }

        Set<Long> saveSkuSet = saveSkuContentMap.keySet();
        if (CollectionUtils.isNotEmpty(saveSkuSet)){
            SaveBizLogRecordReq logReq = SaveBizLogRecordReq.builder()
                .source(SystemOriginEnum.COSFO_MANAGE.getType())
                .operationName("新增sku")
                .bizKey(JSON.toJSONString(saveSkuSet))
                .bizKeyTenantId(tenantId)
                .content(JSON.toJSONString(saveSkuContentMap))
                .tenantId(tenantId)
                .createdTime(LocalDateTime.now())
                .createdId(authUserId)
                .build();
            bizLogFacade.saveBizLog(logReq);
        }

        Set<Long> agentSkuSet = agentSkuContentMap.keySet();
        if (CollectionUtils.isNotEmpty(agentSkuSet)){
            SaveBizLogRecordReq logReq = SaveBizLogRecordReq.builder()
                .source(SystemOriginEnum.COSFO_MANAGE.getType())
                .operationName("代仓发起申请")
                .bizKey(JSON.toJSONString(agentSkuSet))
                .bizKeyTenantId(tenantId)
                .content(JSON.toJSONString(agentSkuContentMap))
                .tenantId(tenantId)
                .createdTime(LocalDateTime.now())
                .createdId(authUserId)
                .build();
            bizLogFacade.saveBizLog(logReq);
        }
    }


}

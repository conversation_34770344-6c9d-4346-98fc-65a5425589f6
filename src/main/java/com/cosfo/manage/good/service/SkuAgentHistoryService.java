package com.cosfo.manage.good.service;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/1/3 17:14
 * @Description:
 */
public interface SkuAgentHistoryService {

    /**
     * 查询所有需要洗数据的租户
     *
     * @return
     */
    List<Long> listAllTenantIds();

    /**
     * 按租户 生成历史申请日志
     *
     * @param tenantId
     * @param skuIds
     */
    void createTenantHistoryRecord(Long tenantId, List<Long> skuIds);
}

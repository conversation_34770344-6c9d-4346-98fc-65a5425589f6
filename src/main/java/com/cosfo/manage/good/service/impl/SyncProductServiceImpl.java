package com.cosfo.manage.good.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.facade.SummerFarmInterfaceServiceFacade;
import com.cosfo.manage.good.model.vo.ItemIdAndSkuIdVO;
import com.cosfo.manage.good.service.ProductService;
import com.cosfo.manage.good.service.SyncProductService;
import com.cosfo.manage.market.service.MarketClassificationService;
import com.cosfo.manage.product.convert.ProductConverter;
import com.cosfo.manage.product.model.dto.SummerFarmSynchronizedSkuDTO;
import com.cosfo.manage.product.model.vo.SummerFarmSynchronizedSkuVO;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.saas.req.SummerFarmSynchronizedSkuReq;
import net.summerfarm.manage.client.saas.resp.SummerFarmSynchronizedSkuResp;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @author: xiaowk
 * @time: 2023/7/1 下午10:31
 */
@Service
@Slf4j
public class SyncProductServiceImpl implements SyncProductService {

    @Resource
    private ProductService productService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private MarketClassificationService marketClassificationService;
    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;
    @Resource
    private TenantService tenantService;

    @Override
    public void syncAgentProductFromXianmu(Long tenantId, List<Long> skuIdList) {
        TenantDTO tenantDTO = tenantService.queryTenantById(tenantId);
        if(tenantDTO == null){
            throw new BizException("租户信息不存在" + tenantId);
        }
        Long adminId = tenantDTO.getAdminId();

        List<SummerFarmSynchronizedSkuVO> addSkuVOList = new ArrayList<>(skuIdList.size());

        for (Long skuId : skuIdList) {
            SummerFarmSynchronizedSkuReq input = new SummerFarmSynchronizedSkuReq();
            // TODO 批量查询
            input.setSkuIds(Collections.singletonList(skuId));
            // 查询需要同步的sku信息
            try {
                SummerFarmSynchronizedSkuResp summerFarmSynchronizedSkuResp = summerFarmInterfaceServiceFacade.queryNeedSynchronizedSku(input);
                log.info("同步sku信息，skuId: {} , 具体信息: {}", skuId, JSON.toJSONString(summerFarmSynchronizedSkuResp));
                if(adminId == null || !adminId.equals(summerFarmSynchronizedSkuResp.getAdminId())){
                    throw new BizException("skuId所属大客户和租户不一致, adminId=" + adminId);
                }
                // 处理需要同步的sku信息
                SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO = ProductConverter.convertSummerFarmSynchronizedSkuDTO(summerFarmSynchronizedSkuResp);
                SummerFarmSynchronizedSkuVO skuVO = productSkuService.doneSkuSynchronized(summerFarmSynchronizedSkuDTO, tenantId);
                addSkuVOList.add(skuVO);
            } catch (Exception e) {
                log.error("同步的代仓sku信息失败, tenantId={}, xmSkuId={}", tenantId, skuId, e);
            }
        }

        // 新租户默认的【全部】商品分组id
        Long classificationId = marketClassificationService.createDefaultClassification(tenantId);

        // 货品spuId映射marketId
        Map<Long, Long> spuId2MarketIdMap = new HashMap<>();

        // 依据货品skuId 同步到帆台商品信息
        for (SummerFarmSynchronizedSkuVO skuVO : addSkuVOList) {
            try {
                // 可能为空，为空说明需要新建market
                Long sourceMarketId = spuId2MarketIdMap.get(skuVO.getSpuId());
                ItemIdAndSkuIdVO itemIdAndSkuIdVO = productService.createDefaultMarketItemByProductSkuId(skuVO.getSkuId(), sourceMarketId, classificationId, tenantId);
                if(itemIdAndSkuIdVO != null) {
                    spuId2MarketIdMap.put(skuVO.getSpuId(), itemIdAndSkuIdVO.getMarketId());
                }
            } catch (Exception e) {
                log.error("同步代仓货品到帆台商品失败, skuVO={}", skuVO, e);
            }
        }
    }
}

package com.cosfo.manage.good.convert;

import com.cosfo.manage.client.product.resp.ProductPricingSupplyCityMappingResp;
import com.cosfo.manage.client.product.resp.ProductSkuResp;
import com.cosfo.manage.good.model.vo.SelfGoodWarehouseDataVO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/18 14:55
 * @Description:
 */
@Mapper
public interface ProductInfoConvert {
    ProductInfoConvert INSTANCE = Mappers.getMapper(ProductInfoConvert.class);

    List<SelfGoodWarehouseDataVO> convert2VOs(List<ProductAgentWarehouseDataVO> agentDataVOs);

    @Mapping(source = "warehouseSkuFenceAreaDTOList", target = "cityNames")
    @Mapping(source = "enabledQuantity", target = "enabledTotalQuantity")
    SelfGoodWarehouseDataVO convert2VO(ProductAgentWarehouseDataVO agentDataVO);

    ProductSkuResp convert2SkuResp(ProductSkuDTO dto);

    List<ProductSkuResp> convert2SkuRespList(List<ProductSkuDTO> dto);

    List<ProductPricingSupplyCityMappingResp> convert2MappingResp(List<ProductPricingSupplyCityMappingDTO> mappingDTOS);

    ProductSpu copySpu(ProductSpu spu);

    ProductSku copySku(ProductSku sku);

}

package com.cosfo.manage.good.convert;

import com.cosfo.manage.common.context.StoreTypeEnum;
import com.cosfo.manage.good.model.dto.DeliveryPlanRecordInput;
import com.cosfo.manage.good.model.vo.DeliveryPlanRecordVO;
import net.summerfarm.ofc.client.req.DistributionRecordReq;
import net.summerfarm.ofc.client.resp.DistributionRecordResp;

import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/12
 */
public class DistributionRecordConvert {

    /**
     * 转化为DistributionRecordQueryInput
     *
     * @param deliveryPlanRecordInput
     * @return
     */
    public static DistributionRecordReq convertToDistributionRecordQueryInput(DeliveryPlanRecordInput deliveryPlanRecordInput){

        if (deliveryPlanRecordInput == null) {
            return null;
        }

        DistributionRecordReq distributionRecordReq = new DistributionRecordReq();
        distributionRecordReq.setBusinessNo(deliveryPlanRecordInput.getBusinessNo());
        distributionRecordReq.setStoreName(deliveryPlanRecordInput.getStoreName());
        distributionRecordReq.setStoreType(deliveryPlanRecordInput.getStoreType());
        distributionRecordReq.setStartTime(deliveryPlanRecordInput.getStartTime());
        distributionRecordReq.setEndTime(deliveryPlanRecordInput.getEndTime());
        distributionRecordReq.setWarehouseNo(deliveryPlanRecordInput.getWarehouseNo());
        distributionRecordReq.setCity(deliveryPlanRecordInput.getCityName());
        distributionRecordReq.setPageNum(deliveryPlanRecordInput.getPageIndex());
        distributionRecordReq.setPageSize(deliveryPlanRecordInput.getPageSize());
        return distributionRecordReq;
    }

    /**
     * 转化为DeliveryPlanRecordVO
     *
     * @param distributionRecordResp
     * @return
     */
    public static DeliveryPlanRecordVO convertToDeliveryPlanRecordVO(DistributionRecordResp distributionRecordResp){
        if (distributionRecordResp == null) {
            return null;
        }
        DeliveryPlanRecordVO deliveryPlanRecordVO = new DeliveryPlanRecordVO();
        deliveryPlanRecordVO.setStoreName(distributionRecordResp.getStoreName());
        deliveryPlanRecordVO.setStoreType(distributionRecordResp.getStoreType());
        if(Objects.nonNull(deliveryPlanRecordVO.getStoreType())){
            deliveryPlanRecordVO.setStoreTypeDesc(StoreTypeEnum.getDesc(deliveryPlanRecordVO.getStoreType()));
        }

        deliveryPlanRecordVO.setBusinessNo(distributionRecordResp.getBusinessNo());
        deliveryPlanRecordVO.setItemAmount(distributionRecordResp.getItemAmount());
        deliveryPlanRecordVO.setAmount(distributionRecordResp.getAmount());
        deliveryPlanRecordVO.setDeliveryTime(distributionRecordResp.getDeliveryTime());
        deliveryPlanRecordVO.setFinishedTime(distributionRecordResp.getFinishedTime());
        deliveryPlanRecordVO.setWarehouse(distributionRecordResp.getWarehouseName());
        deliveryPlanRecordVO.setCityName(distributionRecordResp.getCity());
        return deliveryPlanRecordVO;
    }
}

package com.cosfo.manage.good.convert;

import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.vo.ProductSkuDetailVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/7
 */
public class ProductConvert {

    /**
     * 转化为ProductSkuDetailVO
     *
     * @param productSkuDTO
     * @return
     */
    public static ProductSkuDetailVO convertToProductSkuDetailVO(ProductSkuDTO productSkuDTO){
        if (productSkuDTO == null) {
            return null;
        }

        ProductSkuDetailVO productSkuDetailVO = new ProductSkuDetailVO();
        productSkuDetailVO.setId(productSkuDTO.getId());
        productSkuDetailVO.setSupplySkuId(productSkuDTO.getId());
        productSkuDetailVO.setSku(productSkuDTO.getSku());
        productSkuDetailVO.setTenantId(productSkuDTO.getTenantId());
        productSkuDetailVO.setSpuId(productSkuDTO.getSpuId());
        productSkuDetailVO.setSpecification(productSkuDTO.getSpecification());
        productSkuDetailVO.setSpecificationUnit(productSkuDTO.getSpecificationUnit());
        productSkuDetailVO.setPrice(productSkuDTO.getPrice());
        productSkuDetailVO.setPriceStr(productSkuDTO.getPriceStr());
        productSkuDetailVO.setBrandName(productSkuDTO.getBrandName());
        productSkuDetailVO.setTitle(productSkuDTO.getTitle());
        productSkuDetailVO.setMainPicture(productSkuDTO.getMainPicture());
        productSkuDetailVO.setStartTime(productSkuDTO.getStartTime());
        productSkuDetailVO.setEndTime(productSkuDTO.getEndTime());
        productSkuDetailVO.setThirdCategory(productSkuDTO.getThirdCategory());
        productSkuDetailVO.setBrandId(productSkuDTO.getBrandId());
        productSkuDetailVO.setCategoryId(productSkuDTO.getCategoryId());
        productSkuDetailVO.setCityNum(productSkuDTO.getCityNum());
        productSkuDetailVO.setCategory(productSkuDTO.getFirstCategory());
        productSkuDetailVO.setSecondaryCategory(productSkuDTO.getSecondCategory());
        productSkuDetailVO.setMinPrice(productSkuDTO.getMinPrice());
        productSkuDetailVO.setMaxPrice(productSkuDTO.getMaxPrice());
        productSkuDetailVO.setOrigin(productSkuDTO.getOrigin());
        productSkuDetailVO.setStorageLocation(productSkuDTO.getStorageLocation());
        productSkuDetailVO.setStorageTemperature(productSkuDTO.getStorageTemperature());
        productSkuDetailVO.setGuaranteePeriod(productSkuDTO.getGuaranteePeriod());
        productSkuDetailVO.setGuaranteeUnit(productSkuDTO.getGuaranteeUnit());
        productSkuDetailVO.setEnabledTotalQuantity(productSkuDTO.getEnabledTotalQuantity());
        productSkuDetailVO.setProductAgentWarehouseDataVOS(productSkuDTO.getProductAgentWarehouseDataVOS());
        productSkuDetailVO.setProductSupplyPriceId(productSkuDTO.getProductSupplyPriceId());
        productSkuDetailVO.setAfterSaleUnit (productSkuDTO.getAfterSaleUnit ());
        productSkuDetailVO.setMaxAfterSaleAmount (productSkuDTO.getMaxAfterSaleAmount ());
        productSkuDetailVO.setCustomSkuCode(productSkuDTO.getCustomSkuCode());
        return productSkuDetailVO;
    }

    public static List<ProductSkuDetailVO> convertToList(List<ProductSkuDTO> productSkuDtos){

        if (productSkuDtos == null) {
            return Collections.emptyList();
        }
        List<ProductSkuDetailVO> productSkuDetailVOList = new ArrayList<>();
        for (ProductSkuDTO productSkuDTO : productSkuDtos) {
            productSkuDetailVOList.add(convertToProductSkuDetailVO(productSkuDTO));
        }
        return productSkuDetailVOList;
    }
}

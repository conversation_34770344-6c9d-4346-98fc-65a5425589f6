package com.cosfo.manage.good.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.good.model.dto.ProductAgentRecordBySkuDTO;
import com.cosfo.manage.good.model.dto.ProductAgentRecordQueryDTO;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 货品代仓服务申请记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Mapper
public interface ProductAgentApplicationRecordMapper extends BaseMapper<ProductAgentApplicationRecord> {

    /**
     * 查询SKU最新的申请记录
     *
     * @param queryDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentRecordBySkuDTO> selectLastRecord(ProductAgentRecordQueryDTO queryDTO);

    /**
     * 主键查询，不查租户
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductAgentApplicationRecord getOneByid(@Param("id") Long id);
}

//package com.cosfo.manage.filter;
//
//import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
//import com.cosfo.manage.common.result.ResultDTOEnum;
//import com.cosfo.manage.common.util.JwtUtils;
//import com.cosfo.manage.common.util.RedisUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.annotation.Order;
//import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.util.StringUtils;
//import org.springframework.web.context.WebApplicationContext;
//import org.springframework.web.context.support.WebApplicationContextUtils;
//import org.springframework.web.filter.OncePerRequestFilter;
//
//import javax.servlet.FilterChain;
//import javax.servlet.ServletException;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.util.Map;
//import java.util.concurrent.TimeUnit;
//
///**
// * @Package: com.cosfo.manage.jwt.filter
// * @Description:
// * @author: <EMAIL>
// * @Date: 2022/05/05
// */
//@Slf4j
//@Order(2)
//public class JwtAuthenticationFilter extends OncePerRequestFilter {
//    @Autowired
//    private RedisUtils redisUtils;
//
//    @Override
//    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {
//        String token = httpServletRequest.getHeader(JwtUtils.HEADER_STRING);
//        if (!httpServletRequest.getRequestURI().equals("/ok")) {
//            log.info("{}请求了接口：{} {}", token, httpServletRequest.getMethod(), httpServletRequest.getRequestURI());
//        }
//
//        // 是否需要校验
//        String phone = null;
//        if (JwtUtils.isProtectedUrl(httpServletRequest)) {
//            if (StringUtils.isEmpty(token) || !token.startsWith(JwtUtils.TOKEN_PREFIX)) {
//                log.error("token错误，token:{}", token);
//                filterChain.doFilter(httpServletRequest, httpServletResponse);
//                return;
//            }
//
//            redisUtils = getBean(RedisUtils.class, httpServletRequest);
//            if (!redisUtils.hasKey(token)) {
//                log.error("{} 用户信息缓存不存在", token);
//            }
//
//            // 判断是否有缓存
//            if (redisUtils.get(token) == null) {
//                log.error("未获取到redis缓存信息");
//                filterChain.doFilter(httpServletRequest, httpServletResponse);
//                return;
//            }
//
//            if (redisUtils.getExpire(token, TimeUnit.SECONDS) < 1 * 60 * 5) {
//                redisUtils.expire(token, JwtUtils.EXPIRATION_TIME, TimeUnit.MILLISECONDS);
//            }
//
//            // 获取缓存信息
//            LoginContextInfoDTO loginContextInfoDTO = (LoginContextInfoDTO) redisUtils.get(token);
//            try {
//                // 令牌验证
//                Map<String, Object> claims = JwtUtils.validateTokenAndGetClaims(loginContextInfoDTO.getJwtToken());
//                //
//                phone = String.valueOf(claims.get(JwtUtils.PHONE));
//            } catch (Exception e) {
//                log.error("e={}", e.getMessage(), e);
//                httpServletResponse.sendError(ResultDTOEnum.NO_PERMISSION.getCode(), e.getMessage());
//                return;
//            }
//        }
//
//        //最关键的部分就是这里, 我们直接注入了
//        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(
//                phone, null, null));
//        filterChain.doFilter(httpServletRequest, httpServletResponse);
//    }
//
//    /**
//     * 处理拦截器中redis无法注入问题
//     *
//     * @param clazz
//     * @param request
//     * @param <T>
//     * @return
//     */
//    public <T> T getBean(Class<T> clazz, HttpServletRequest request) {
//        WebApplicationContext applicationContext = WebApplicationContextUtils.getRequiredWebApplicationContext(request.getServletContext());
//        return applicationContext.getBean(clazz);
//    }
//}

package com.cosfo.manage.file.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.file.dao.CommonResourceDao;
import com.cosfo.manage.file.mapper.CommonResourceMapper;
import com.cosfo.manage.file.model.dto.QueryPicResourceDTO;
import com.cosfo.manage.file.model.po.CommonResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 文件资源表(CommonResource)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-25 21:03:53
 */
@Service
public class CommonResourceDaoImpl extends ServiceImpl<CommonResourceMapper, CommonResource> implements CommonResourceDao {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchResource(List<CommonResource> resourceList) {
        saveBatch(resourceList);
    }

    @Override
    public Page<CommonResource> pageCommonResource(QueryPicResourceDTO dto) {
        Page<CommonResource> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return page(page, buildQueryWrapper(dto));
    }


    private LambdaQueryWrapper<CommonResource> buildQueryWrapper(QueryPicResourceDTO dto) {
        LambdaQueryWrapper<CommonResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommonResource::getTenantId, dto.getTenantId());
        wrapper.like(Objects.nonNull(dto.getPicName()), CommonResource::getResourceName, dto.getPicName());

        wrapper.orderByDesc(CommonResource::getId);
        return wrapper;
    }
}

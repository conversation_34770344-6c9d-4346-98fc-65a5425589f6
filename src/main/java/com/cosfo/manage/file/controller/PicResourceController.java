package com.cosfo.manage.file.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.file.model.dto.PicResourceInput;
import com.cosfo.manage.file.model.dto.QueryPicResourceDTO;
import com.cosfo.manage.file.model.vo.PicResourceVO;
import com.cosfo.manage.file.service.PicResourceService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: monna.chen
 * @Date: 2023/7/5 10:31
 * @Description:
 */
@RestController
@RequestMapping("/pic-resource")
@Slf4j
public class PicResourceController extends BaseController {
    @Resource
    private PicResourceService picResourceService;

    /**
     * 保存图片资源
     *
     * @param input
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/insert")
    public CommonResult<Void> insertPicResource(@RequestBody PicResourceInput input) {
        picResourceService.insertPicResource(input, getMerchantInfoDTO().getTenantId());
        return CommonResult.ok();
    }


    /**
     * 查询图片资源库
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/list")
    public CommonResult<PageInfo<PicResourceVO>> listPicResource(@RequestBody QueryPicResourceDTO dto) {
        dto.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(picResourceService.queryPicResourceList(dto));
    }
}

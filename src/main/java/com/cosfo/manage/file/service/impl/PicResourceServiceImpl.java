package com.cosfo.manage.file.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.common.context.ResourceTypeEnum;
import com.cosfo.manage.file.converter.ResourceConvert;
import com.cosfo.manage.file.dao.CommonResourceDao;
import com.cosfo.manage.file.model.dto.PicResourceDTO;
import com.cosfo.manage.file.model.dto.PicResourceInput;
import com.cosfo.manage.file.model.dto.QueryPicResourceDTO;
import com.cosfo.manage.file.model.po.CommonResource;
import com.cosfo.manage.file.model.vo.PicResourceVO;
import com.cosfo.manage.file.service.PicResourceService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/7/5 10:51
 * @Description:
 */
@Service
@Slf4j
public class PicResourceServiceImpl implements PicResourceService {

    @Resource
    private CommonResourceDao commonResourceDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertPicResource(PicResourceInput input,Long tenantId) {
        List<PicResourceDTO> picResourceList = input.getPicResourceList();
        if (CollectionUtils.isEmpty(picResourceList)){
            return;
        }

        List<CommonResource> saveList = new ArrayList<>();
        for (PicResourceDTO picResourceDTO : picResourceList) {
            CommonResource saveRecord = new CommonResource();
            saveRecord.setTenantId(tenantId);
            saveRecord.setResourceType(ResourceTypeEnum.PIC_RESOURCE.getCode());
            saveRecord.setResourceName(picResourceDTO.getPicName());
            saveRecord.setResourcePath(picResourceDTO.getResourcePath());
            saveList.add(saveRecord);
        }
        commonResourceDao.saveBatch(saveList);
    }

    @Override
    public PageInfo<PicResourceVO> queryPicResourceList(QueryPicResourceDTO dto) {
        Page<CommonResource> commonResourcePage = commonResourceDao.pageCommonResource(dto);
        PageInfo<PicResourceVO> pageInfo = new PageInfo<>();
        pageInfo.setList(ResourceConvert.INSTANCE.convert2Vos(commonResourcePage.getRecords()));
        pageInfo.setTotal(commonResourcePage.getTotal());
        pageInfo.setPageNum(dto.getPageNum());
        pageInfo.setPageSize(dto.getPageSize());
        return pageInfo;

    }
}

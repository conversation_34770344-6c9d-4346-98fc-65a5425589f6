package com.cosfo.manage.file.service.impl;

import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.config.OfficialWebsiteTableConfig;
import com.cosfo.manage.common.constant.FeishuConstants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.facade.FeiShuFacade;
import com.cosfo.manage.file.model.dto.SubscribeOfficialWebsiteDTO;
import com.cosfo.manage.file.service.BitableService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.apache.http.entity.ContentType;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Author: fansongsong
 * @Date: 2023-12-18
 * @Description:
 */
@Slf4j
@Service
public class BitableServiceImpl implements BitableService {

    @Resource
    private FeiShuFacade feiShuFacade;
    @Resource
    private OfficialWebsiteTableConfig officialWebsiteTableConfig;
    @Resource
    private Environment environment;

    @Override
    public void subscribeOfficialWebsite(SubscribeOfficialWebsiteDTO dto) {
        String feiShuToken = feiShuFacade.queryFeiShuToken();

        String appToken = officialWebsiteTableConfig.getAppToken();
        String tableId = officialWebsiteTableConfig.getTableId();
        String paramFields = officialWebsiteTableConfig.getFields();
        String remark = Optional.ofNullable(dto.getRemark()).orElse("预约回电");
        String json = String.format(paramFields, System.currentTimeMillis(), dto.getPhone(), remark);

        insertRecord(appToken, tableId, json, feiShuToken);
    }

    /**
     * 新增多维表格记录
     * @param appToken 多维表格的唯一标识符
     * @param tableId 多维表格数据表的唯一标识符
     * @param body 填充数据内容
     * @param authorizationToken 飞书token
     */
    private void insertRecord(String appToken, String tableId, String body, String authorizationToken) {
        String url = FeishuConstants.BIT_TABLE_RECORDS_URL;
        url = String.format(url, appToken, tableId);

        log.info("新增多维表格 url:{},appToken:{},tableId:{},body:{},authorizationToken:{}", url, appToken, tableId, body, authorizationToken);
        HttpGlobalConfig.setTimeout(30000);
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(url)
                .header("Authorization", "Bearer " + authorizationToken)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(body).execute();
        String result = response.body();
        log.info("新增多维表格 url:{},body:{},response:{}", url, body, result);

        // 错误码，非 0 表示失败
        JSONObject jsonObject = JSONObject.parseObject(result);
        Integer code = jsonObject.getInteger("code");
        if (NumberConstant.ZERO.equals(code)) {
            return;
        }
        throw new ProviderException("操作失败,请稍后再试");
    }
}

package com.cosfo.manage.file.model.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * file_download_record
 * <AUTHOR>
@Data
public class FileDownloadRecord implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 存储地址
     */
    private String url;

    /**
     * 查询条件
     */
    private String params;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 0、门店 1、商品
     */
    private Integer type;

    /**
     * 1、处理中 2、处理完毕
     */
    private Integer status;

    /**
     * 原始查询条件
     */
    private String originParams;

    private static final long serialVersionUID = 1L;
}

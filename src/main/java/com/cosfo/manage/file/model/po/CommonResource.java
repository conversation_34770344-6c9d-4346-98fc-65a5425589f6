package com.cosfo.manage.file.model.po;

import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 文件资源表(CommonResource)实体类
 *
 * <AUTHOR>
 * @since 2023-06-25 18:56:50
 */
@Data
@TableName("common_resource")
public class CommonResource implements Serializable {
    private static final long serialVersionUID = 306641276518648392L;
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 资源类型：1-图片资源库
     */
    private Integer resourceType;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 云服务地址
     */
    private String resourcePath;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;

}


package com.cosfo.manage.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.tenant.dao.TenantDeliveryFeeAreaDao;
import com.cosfo.manage.tenant.mapper.TenantDeliveryFeeAreaMapper;
import com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/15
 */
@Service
public class TenantDeliveryFeeAreaDaoImpl extends ServiceImpl<TenantDeliveryFeeAreaMapper, TenantDeliveryFeeArea> implements TenantDeliveryFeeAreaDao {

//    @Override
//    public Integer queryMaxGroupId(Long tenantId, Long ruleId) {
//        QueryWrapper<TenantDeliveryFeeArea> queryWrapper= new QueryWrapper <>();
//        queryWrapper.select(" IFNULL( max(group_id), 0) as maxGroupId");
//        queryWrapper.eq("tenant_id", tenantId);
//        queryWrapper.eq("rule_id", ruleId);
//        Map<String, Object> map = getMap(queryWrapper);
//        return Integer.valueOf(map.get("maxGroupId").toString());
//    }

//    @Override
//    public void delete(Long tenantId, Long ruleId) {
//        LambdaQueryWrapper<TenantDeliveryFeeArea> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(TenantDeliveryFeeArea::getTenantId, tenantId);
//        queryWrapper.eq(TenantDeliveryFeeArea::getRuleId, ruleId);
//        remove(queryWrapper);
//    }

    @Override
    public List<TenantDeliveryFeeArea> queryByTenantIdAndRuleId(Long tenantId, Long ruleId) {
        LambdaQueryWrapper<TenantDeliveryFeeArea> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantDeliveryFeeArea::getTenantId, tenantId);
        queryWrapper.eq(TenantDeliveryFeeArea::getRuleId, ruleId);
        return baseMapper.selectList(queryWrapper);
    }
}

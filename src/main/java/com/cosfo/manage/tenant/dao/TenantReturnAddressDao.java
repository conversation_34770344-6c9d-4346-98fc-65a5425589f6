package com.cosfo.manage.tenant.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.order.model.vo.ReturnAddressAddInput;
import com.cosfo.manage.order.model.vo.ReturnAddressUpdateInput;
import com.cosfo.manage.order.model.vo.ReturnAddressVO;
import com.cosfo.manage.tenant.model.po.TenantReturnAddress;

import java.util.List;

/**
 * <p>
 * 租户售后退货地址信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-27
 */
public interface TenantReturnAddressDao extends IService<TenantReturnAddress> {

    List<ReturnAddressVO> queryByTenantId(Long tenantId);

    ReturnAddressVO getByPrimaryKey(Long id);

    ReturnAddressVO getRecentlyUsedAddress(Long tenantId);

    void useAddress(Long id);

    Long insert(ReturnAddressAddInput returnAddressAddInput, Long tenantId);

    void update(ReturnAddressUpdateInput returnAddressUpdateInput);

    void delete(Long id);

    /**
     * 根据id批量查询
     * @param ids
     * @return
     */
    List<TenantReturnAddress> queryByIds(List<Long> ids);


}

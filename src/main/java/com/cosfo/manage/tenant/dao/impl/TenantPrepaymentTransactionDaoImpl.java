package com.cosfo.manage.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.common.util.DateUtil;
import com.cosfo.manage.bill.model.dto.PrepaymentTransactionQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionTotalVO;
import com.cosfo.manage.common.context.prepay.TenantPrepayTransactionEnum;
import com.cosfo.manage.tenant.dao.TenantPrepaymentTransactionDao;
import com.cosfo.manage.tenant.mapper.TenantCompanyMapper;
import com.cosfo.manage.tenant.mapper.TenantPrepaymentTransactionMapper;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentTransaction;
import com.cosfo.manage.tenant.service.TenantCompanyService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 预付交易明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
public class TenantPrepaymentTransactionDaoImpl extends ServiceImpl<TenantPrepaymentTransactionMapper, TenantPrepaymentTransaction> implements TenantPrepaymentTransactionDao {

    @Resource
    private TenantCompanyService tenantCompanyService;

    @Override
    public Page<TenantPrepaymentTransaction> queryPrepaymentTransactionPage(PrepaymentTransactionQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentTransaction> wrapper = buildWrapper(queryDTO, null);
        if (StringUtils.isNotBlank(queryDTO.getTransactionName())) {
            // 获取 租户id
            List<Long> supplierIds = tenantCompanyService.selectByCompanyName(queryDTO.getTransactionName());
            if (CollectionUtils.isEmpty(supplierIds)) {
                return new Page<>();
            }
            wrapper.in(TenantPrepaymentTransaction::getSupplierTenantId, supplierIds);
        }
        return page(new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize()), wrapper);
    }

    private static LambdaQueryWrapper<TenantPrepaymentTransaction> buildWrapper(PrepaymentTransactionQueryDTO queryDTO, LambdaQueryWrapper<TenantPrepaymentTransaction> sourceWrapper) {
        LambdaQueryWrapper<TenantPrepaymentTransaction> wrapper = sourceWrapper == null ? new LambdaQueryWrapper<>() : sourceWrapper;
        if (queryDTO.getStartTime() != null && queryDTO.getEndTime() != null) {
            wrapper.between(TenantPrepaymentTransaction::getCreateTime, DateUtil.startOfDay(queryDTO.getStartTime()), DateUtil.endOfDay(queryDTO.getEndTime()));
        }
        wrapper.eq(queryDTO.getType() != null, TenantPrepaymentTransaction::getType, queryDTO.getType());
        wrapper.eq(queryDTO.getTransactionType() != null, TenantPrepaymentTransaction::getTransactionType, queryDTO.getTransactionType());
        wrapper.eq(StringUtils.isNotBlank(queryDTO.getTransactionId()), TenantPrepaymentTransaction::getTransactionNo, queryDTO.getTransactionId());
        wrapper.eq(queryDTO.getOrderNo() != null, TenantPrepaymentTransaction::getAssociatedOrderNo, queryDTO.getOrderNo());
        wrapper.eq(TenantPrepaymentTransaction::getTenantId, queryDTO.getTenantId());
        wrapper.orderByDesc(Lists.newArrayList(TenantPrepaymentTransaction::getCreateTime, TenantPrepaymentTransaction::getId));
        return wrapper;
    }

    @Override
    public List<TenantPrepaymentTransaction> queryPrepaymentTransactionList(PrepaymentTransactionQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentTransaction> wrapper = buildWrapper(queryDTO, null);
        if (StringUtils.isNotBlank(queryDTO.getTransactionName())) {
            // 获取 租户id
            List<Long> supplierIds = tenantCompanyService.selectByCompanyName(queryDTO.getTransactionName());
            wrapper.in(TenantPrepaymentTransaction::getSupplierTenantId, supplierIds);
            if (CollectionUtils.isEmpty(supplierIds)) {
                return Lists.newArrayList();
            }
        }
        return list(wrapper);
    }

    @Override
    public PrepaymentTransactionTotalVO queryPrepaymentTransactionTotal(PrepaymentTransactionQueryDTO queryDTO) {
        QueryWrapper<TenantPrepaymentTransaction> accountQueryWrapper = new QueryWrapper<>();
        accountQueryWrapper.select("type, sum(ifnull(transaction_amount, 0)) as transactionAmount");
        LambdaQueryWrapper<TenantPrepaymentTransaction> lambdaQueryWrapper = buildWrapper(queryDTO, accountQueryWrapper.lambda());
        if (StringUtils.isNotBlank(queryDTO.getTransactionName())) {
            // 获取 租户id
            List<Long> supplierIds = tenantCompanyService.selectByCompanyName(queryDTO.getTransactionName());
            lambdaQueryWrapper.in(TenantPrepaymentTransaction::getSupplierTenantId, supplierIds);
        }
        lambdaQueryWrapper.groupBy(TenantPrepaymentTransaction::getType);
        List<Map<String, Object>> result = listMaps(lambdaQueryWrapper);
        PrepaymentTransactionTotalVO totalVO = new PrepaymentTransactionTotalVO();
        if (CollectionUtils.isEmpty(result)) {
            return totalVO;
        }
        result.forEach(map -> {
            Integer type = (Integer) map.get("type");
            BigDecimal amount = (BigDecimal) map.getOrDefault("transactionAmount", BigDecimal.ZERO);
            TenantPrepayTransactionEnum.Type typeEnum = TenantPrepayTransactionEnum.Type.fromType(type);
            switch (typeEnum) {
                case INCOME:
                    totalVO.setIncome(amount);
                    break;
                case EXPENDITURE:
                    totalVO.setExpenses(amount);
                    break;
                default:
            }
        });
        return totalVO;
    }
}

package com.cosfo.manage.tenant.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.tenant.model.po.TenantDeliveryFeeArea;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/15
 */
public interface TenantDeliveryFeeAreaDao extends IService<TenantDeliveryFeeArea> {
    /**
     * 查询最大分组Id
     *
     * @param tenantId
     * @param ruleId
     * @return
     */
//    Integer queryMaxGroupId(Long tenantId,Long ruleId);

    /**
     * 删除区域运费规则
     *
     * @param tenantId
     * @param ruleId
     */
//    void delete(Long tenantId,Long ruleId);

    /**
     * 查询品牌方和ruleId
     *
     * @param tenantId
     * @param ruleId
     * @return
     */
    List<TenantDeliveryFeeArea> queryByTenantIdAndRuleId(Long tenantId,Long ruleId);
}

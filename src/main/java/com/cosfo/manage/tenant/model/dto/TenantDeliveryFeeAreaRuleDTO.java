package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述: TenantDeliveryFeeAreaRuleDTO
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/14
 */
@Data
public class TenantDeliveryFeeAreaRuleDTO {
    /**
     * 分组Id
     */
    private Integer groupId;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 运费金额
     */
    private BigDecimal defaultPrice;
    /**
     * 免配送费条件
     */
    private List<DeliveryFeeAreaRuleDTO> deliveryFeeAreaRuleDTOList;
}

package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class RoleDTO {
    /**
     * 角色Id
     */
    private Integer id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 菜单ids
     */
    private List<Long> menuPurviewIds;

    /**
     * 最后操作人ID
     */
    private String lastUpdater;

    /**
     * 最后更改时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 系统来源，0:admin 1:cosfomanage 2:cosfo
     */
    private Byte systemOrigin;


    /**
     * 0 默认 超级管理员 1
     */
    private Byte superAdmin;

    /**
     * 账号统计
     */
    private Integer roleCount;

    /**
     * true-是供应商配送员 false-不是
     */
    private Boolean supplierDistributorFlag;

    /**
     * 租户id
     */
    private Long tenantId;
}

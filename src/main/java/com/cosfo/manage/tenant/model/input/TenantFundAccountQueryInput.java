package com.cosfo.manage.tenant.model.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/21 11:10
 * @PackageName:com.cosfo.manage.tenant.model.input
 * @ClassName: TenantFundAccountInsertInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class TenantFundAccountQueryInput extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 配置ID（tenant_fund_account_config.id）
     */
    private Long configId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * Id
     */
    private Long id;
}

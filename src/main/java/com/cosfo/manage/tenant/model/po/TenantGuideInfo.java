package com.cosfo.manage.tenant.model.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * tenant_guide_info
 * <AUTHOR>
@Data
public class TenantGuideInfo implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 引导问题步骤 0-nps调研，1-第一步，2-第二步，3-第三步
     */
    private Integer stepNum;

    /**
     * 引导问题标题
     */
    private String title;

    /**
     * 问题已选ids
     */
    private String selectItemIds;

    /**
     * 问题已选json，step_num=0、3保存建议反馈
     */
    private String selectItem;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
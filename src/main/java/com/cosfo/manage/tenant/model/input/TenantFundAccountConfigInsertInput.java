package com.cosfo.manage.tenant.model.input;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/21 11:40
 * @PackageName:com.cosfo.manage.tenant.model.input
 * @ClassName: TenantFundAccountConfigInsertInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class TenantFundAccountConfigInsertInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户名称
     */
    @NotNull(message = "账户名称不能为空")
    @Size(max = 30, message = "账户名称不能超过30个字符")
    private String accountName;

    /**
     * 限制商品分组（逗号分隔的分组编码）
     */
    private String limitGoodsGroup;

    /**
     * 是否可支付运费，1-是，0-否
     * @see com.cosfo.manage.common.context.TenantFundAccountConfigEnum.AllowShippingFeeEnum
     */
    private Integer allowShippingFee;
}

package com.cosfo.manage.tenant.model.input;

import lombok.Data;

/**
 * @author: xiaowk
 * @time: 2023/12/20 下午2:51
 */
@Data
public class FlowSchemeInput {

    /**
     * 方案id
     */
    private Long schemeId;

    /**
     * 门店id
     */
    private Long storeId;

    private Long tenantId;

    /**
     * 方案名称
     */
    private String schemeName;

    /**
     * 默认方案类型 1-直营门店方案 2-加盟门店方案，例外方案表示来源默认方案类型
     */
    private Integer schemeType;

    /**
     * 是否默认流程方案0:例外方案;1:默认方案
     */
    private Integer defaultFlag;
}

package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class RegistryDTO {
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 注册功能 0:关闭 1开启
     */
    @Max(1)
    @Min(0)
    @NotNull(message = "registrySwitch不可为空")
    private Integer registrySwitch;

}

package com.cosfo.manage.tenant.model.input;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/21 11:10
 * @PackageName:com.cosfo.manage.tenant.model.input
 * @ClassName: TenantFundAccountInsertInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class TenantFundAccountUpdateInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户名称
     */
    @NotNull(message = "账户名称不能为空")
    @Size(max = 30, message = "账户名称不能超过30个字符")
    private String accountName;

    /**
     * primary key
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}

package com.cosfo.manage.tenant.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * tenant_bill
 * <AUTHOR>
@Data
public class TenantBill implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 账单编号
     * 帆台交易流水号
     */
    private String billNo;

    /**
     * 账单金额
     */
    private BigDecimal billPrice;

    /**
     * 类型：0、收入 1、支出
     */
    private Integer type;

    /**
     * 订单编号、售后单编号
     */
    private String recordNo;

    /**
     * 支付类型1在线支付2账期3余额
     */
    private Integer paymentType;

    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;

    /**
     * 手续费
     */
    private BigDecimal feeAmount;

    private static final long serialVersionUID = 1L;
}

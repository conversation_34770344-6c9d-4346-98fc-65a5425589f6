package com.cosfo.manage.tenant.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * tenant_delivery_fee_rule
 * <AUTHOR>
@Data
public class TenantDeliveryFeeRule implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0、采用鲜沐运费 1、免运费
     */
    private Integer type;

    /**
     * 默认运费金额
     */
    private BigDecimal defaultPrice;

    /**
     * 满减限制
     */
    private BigDecimal freeNumber;

    /**
     * 满减类型 0按金额 1按限制
     */
    private Integer freeType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
@Data
public class TenantDeliveryFeeAreaRuleGroupDTO {
    /**
     * 分组Id
     */
    private Integer groupId;
    /**
     * 城市数量
     */
    private Integer size;
    /**
     * 区域
     */
    private List<TenantDeliveryFeeAreaRuleDTO> tenantDeliveryFeeAreaRuleDTOS;
    /**
     * 运费金额
     */
    private BigDecimal defaultPrice;
    /**
     * 免配送费条件
     */
    private List<DeliveryFeeAreaRuleDTO> deliveryFeeAreaRuleDTOList;
}

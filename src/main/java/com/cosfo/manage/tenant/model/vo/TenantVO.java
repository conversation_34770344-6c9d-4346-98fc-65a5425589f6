package com.cosfo.manage.tenant.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/11
 */
@Data
public class TenantVO {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 手机号
     */
    private String phone;
    /**
     * 名称
     */
    private String tenantName;

    /**
     * 租户类型：0供应商1品牌方2帆台
     */
    private Integer type;

    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 大客户Id
     */
    private Long adminId;

    /**
     * 操作人
     */
    private String opUname;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * merchant名称
     */
    private String merchantName;
}

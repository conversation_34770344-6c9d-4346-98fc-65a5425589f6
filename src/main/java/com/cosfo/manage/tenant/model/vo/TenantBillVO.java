package com.cosfo.manage.tenant.model.vo;

import com.cosfo.manage.tenant.model.po.TenantBill;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 描述: 账单数据
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/26
 */
@Data
public class TenantBillVO extends TenantBill {
    /**
     * 交易对象
     */
    private String tradingObject;
    /**
     *交易类型
     */
    private String exchangeType;
    /**
     * 账单手续费
     */
    private BigDecimal billFee;
    /**
     * 支付方式1微信2账期3汇付
     */
    private String payType;
    /**
     * 银行流水号
     */
    private String bankOrderId;
    /**
     * 支付单总金额
     */
    private BigDecimal payMoney;
    /**
     * 支付单手续费金额
     */
    private BigDecimal feeMoney;
    /**
     * 汇付手续费费率
     */
    private BigDecimal feeRate;

}

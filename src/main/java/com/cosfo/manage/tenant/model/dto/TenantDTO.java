package com.cosfo.manage.tenant.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/28
 */
@Data
public class TenantDTO {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 密码
     */
    private String password;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户类型：0、入驻商户
     */
    private Integer type;

    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 大客户Id
     */
    private Long adminId;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 租户品牌名称
     */
    private String tenantBrandName;
}

package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.tenant.model.vo.TenantPrivilegesInfoVO;
import com.cosfo.manage.tenant.service.TenantPrivilegesInfoService;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 租户权益信息
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tenant/privileges/info")
public class TenantPrivilegesInfoController {

    @Resource
    private TenantPrivilegesInfoService tenantPrivilegesInfoService;

    /**
     * 获取租户当前权益
     * @return
     */
//    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:customer-manage:balance-switch:update", expireError = false)
    @PostMapping("/query")
    public CommonResult<TenantPrivilegesInfoVO> getPrivilegesInfo() {
        Long tenantId = UserLoginContextUtils.getTenantId();
        return CommonResult.ok(tenantPrivilegesInfoService.getPrivilegesInfo(tenantId));
    }
}

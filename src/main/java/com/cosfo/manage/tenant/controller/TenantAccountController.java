package com.cosfo.manage.tenant.controller;

import cn.hutool.core.util.StrUtil;
import com.cosfo.manage.client.productitem.req.ProductItemReq;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.dto.*;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.vo.*;
import com.cosfo.manage.tenant.service.TenantAccountService;
import com.cosfo.manage.tenant.service.TenantService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import net.xianmu.log.annation.BizLogRecord;
import net.xianmu.log.config.BizLogRecordContext;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录模块
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@RestController
@RequestMapping("/tenant/user")
public class TenantAccountController extends BaseController {
    @Resource
    private TenantAccountService tenantAccountService;
    @Resource
    private TenantService tenantService;

//    /**
//     * 预登录(已废弃)
//     *
//     * @param tenantAccountPreLoginDTO
//     * @return
//     */
//    @Deprecated
//    @PostMapping("query/pre-login")
//    public CommonResult<TenantAccountPreLoginVO> preLoginOld(@Valid @RequestBody TenantAccountPreLoginDTO tenantAccountPreLoginDTO) {
//        TenantAccountPreLoginVO tenantAccountPreLoginVO = tenantAccountService.preLoginOld(tenantAccountPreLoginDTO);
//        return CommonResult.ok(tenantAccountPreLoginVO);
//    }

    /**
     * 预登录V2
     *
     * @param tenantAccountPreLoginDTO
     * @return
     */
    @PostMapping("query/pre-loginV2")
    public CommonResult<TenantAccountPreLoginVO> preLogin(@Valid @RequestBody TenantAccountPreLoginDTO tenantAccountPreLoginDTO) {
        TenantAccountPreLoginVO tenantAccountPreLoginVO = tenantAccountService.preLogin(tenantAccountPreLoginDTO);
        return CommonResult.ok(tenantAccountPreLoginVO);
    }

    /**
     * 查询绑定品牌方
     *
     * @param bindTenantQueryDTO
     * @return
     */
    @PostMapping("/query/tenant/list")
    public CommonResult<PageInfo<LoginAccountTenantVO>> queryBindTenantByPhone(@Valid @RequestBody BindTenantQueryDTO bindTenantQueryDTO) {
        PageInfo<LoginAccountTenantVO> pageInfo = tenantService.queryBindTenantByPhone(bindTenantQueryDTO);
        return CommonResult.ok(pageInfo);
    }
    /**
     * 根据手机号查询accout
     *
     * @param dto
     * @return
     */
    @PostMapping("/query/account/byPhone")
    public CommonResult<List<TenantAccountVO>> getTenantAccountByTenantIdsAndPhone(@Valid @RequestBody TenantAccountListQueryDTO dto) {
        dto.setTenantId (getMerchantInfoDTO().getTenantId ());
        List<TenantAccountVO> list = tenantAccountService.getTenantAccountByTenantIdsAndPhone(dto);
        return CommonResult.ok(list);
    }

    /**
     * 登录
     *
     * @param tenantAccountLoginDTO
     * @return
     */
    @PostMapping("query/login")
    public CommonResult<TenantAccountLoginVO> login(@Valid @RequestBody TenantAccountLoginDTO tenantAccountLoginDTO) {
        TenantAccountLoginVO loginVO = tenantAccountService.login(tenantAccountLoginDTO);
        return CommonResult.ok(loginVO);
    }

    /**
     * 更改用户信息1
     *
     * @param tenantAccountDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:user-account:update", expireError = true)
    @PostMapping("upsert/user/info")
    @BizLogRecord(operationName = "变更设置账号", bizKey = "#accountPhone", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult<Boolean> updateUserInfo(@RequestBody TenantAccountDTO tenantAccountDTO) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        BizLogRecordContext.put("tenantId",merchantInfoDTO.getTenantId());
        return CommonResult.ok(tenantAccountService.updateUserInfo(tenantAccountDTO, merchantInfoDTO));
    }

    /**
     * 新增手机号
     *
     * @param tenantAccountDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:user-account:add", expireError = true)
    @PostMapping("/upsert/add")
    @BizLogRecord(operationName = "新增设置账号", bizKey = "#accountPhone", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult<Boolean> add(@Valid @RequestBody TenantAccountDTO tenantAccountDTO) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        tenantAccountDTO.setTenantId(merchantInfoDTO.getTenantId());
        tenantAccountDTO.setAuthBaseId(merchantInfoDTO.getAuthUserId());
        tenantAccountDTO.setOperatorPhone(merchantInfoDTO.getPhone());
        tenantAccountDTO.setUpdater(merchantInfoDTO.getNickName());

        BizLogRecordContext.put("tenantId",merchantInfoDTO.getTenantId());
        BizLogRecordContext.put("accountPhone", StrUtil.isNotBlank(tenantAccountDTO.getPhone()) ? tenantAccountDTO.getPhone() : tenantAccountDTO.getEmail());
        Map<String, Object> content = new HashMap<>();

        content.put("accountPhone", StrUtil.isNotBlank(tenantAccountDTO.getPhone()) ? tenantAccountDTO.getPhone() : tenantAccountDTO.getEmail());
        BizLogRecordContext.put("content", content);

        return CommonResult.ok(tenantAccountService.create(tenantAccountDTO));
    }

    /**
     * 获取登录用户信息
     *
     * @return
     */
    @PostMapping("query/login/user-info")
    // @RequiresPermissions(value = {"admin:select"}, logical = Logical.OR)
    public CommonResult<TenantAccountVO> getLoginUserInfo() {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        if (Boolean.TRUE.equals(loginContextInfoDTO.getIsSuperAccount())) {
            return CommonResult.ok(tenantAccountService.getSuperAccountInfo(loginContextInfoDTO));
        }
        TenantAccountVO tenantAccountVO = tenantAccountService.getTenantAccountVO(loginContextInfoDTO.getAuthUserId());
        return CommonResult.ok(tenantAccountVO);
    }

    /**
     * 账号列表
     *
     * @param tenantAccountListQueryDTO
     * @return
     */
    @PostMapping("query/account/list")
    public CommonResult<PageInfo<TenantAccountVO>> accountList(@RequestBody TenantAccountListQueryDTO tenantAccountListQueryDTO) {
        PageInfo<TenantAccountVO> pageInfo = tenantAccountService.page(tenantAccountListQueryDTO, getMerchantInfoDTO());
        return CommonResult.ok(pageInfo);
    }
    /**
     * 供应商 账号列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("query/supplierDistributor/account/list")
    public CommonResult<List<SupplierDistributorTenantAccountVO>> supplierDistributorAccountList(@RequestBody SupplierDistributorTenantAccountQueryDTO queryDTO) {
        return CommonResult.ok(tenantAccountService.supplierDistributorAccountList (queryDTO, getMerchantInfoDTO()));
    }
    /**
     * 删除供应商 账号绑定关系
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("supplierDistributor/account/delete")
    public CommonResult supplierDistributorAccountDelete(@RequestBody TenantAccountDTO tenantAccountDTO) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        tenantAccountDTO.setTenantId(merchantInfoDTO.getTenantId());
        tenantAccountService.deleteSupplierDistributorAccount(tenantAccountDTO);
        return CommonResult.ok();

    }
    /**
     * 供应商 账号 关闭/开启 消息推送
     *
     * @param dto
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("updpate/account/receiveMsgSwitch")
    public CommonResult accountReceiveMsgSwitchUpdate(@RequestBody @Valid TenantAccountReceiveMsgSwitchDTO dto) {
        tenantAccountService.accountReceiveMsgSwitchUpdate (dto, getMerchantInfoDTO());
        return CommonResult.ok();
    }

    /**
     * 删除用户
     *
     * @param id
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:user-account:delete", expireError = true)
    @PostMapping("upsert/delete")
    @BizLogRecord(operationName = "删除设置账号",  bizKey = "#accountPhone", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public CommonResult<Boolean> deleteUser(Long id) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        tenantAccountService.remove(id, merchantInfoDTO.getTenantId());

        BizLogRecordContext.put("tenantId",merchantInfoDTO.getTenantId());
        Map<String, Object> content = new HashMap<>();
        content.put("accountPhone", BizLogRecordContext.get("accountPhone"));
        BizLogRecordContext.put("content", content);

        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 发送验证码
     *
     * @param sendCodeDTO
     * @return
     */
    @PostMapping(value = "/sendCode")
    public CommonResult<Boolean> sendCode(@Valid @RequestBody SendCodeDTO sendCodeDTO) {
        return CommonResult.ok(tenantAccountService.sendCode(sendCodeDTO));
    }

    /**
     * 校验验证码
     *
     * @param sendCodeDTO
     * @return
     */
    @PostMapping(value = "/examineCode")
    public CommonResult<Boolean> examineCode(@RequestBody SendCodeDTO sendCodeDTO) {
        Boolean result = tenantAccountService.examineCode(sendCodeDTO);
        return CommonResult.ok(result);
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    @PostMapping("query/user-info")
    public CommonResult<TenantAccountVO> getUserInfo(Long accountId) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        TenantAccountVO tenantAccountVO = tenantAccountService.queryAccountInfo(merchantInfoDTO, accountId);
        return CommonResult.ok(tenantAccountVO);
    }

    /**
     * 切换租户
     *
     * @param tenantId
     * @return
     */
    @PostMapping("query/switch-tenant")
    public CommonResult<TenantAccountLoginVO> switchTenant(@RequestBody TenantAccountLoginDTO tenantAccountLoginDTO) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(tenantAccountService.switchTenant(tenantAccountLoginDTO.getTenantId(), merchantInfoDTO));
    }

    /**
     * 获取操作人列表
     * @return
     */
    @PostMapping("/query/tenant-account")
    public CommonResult<List<TenantAccount>> listAccountInfO() {
        return CommonResult.ok(tenantAccountService.listTenantAccount(getMerchantInfoDTO().getTenantId()));
    }
    /**
     * 获取二维码链接
     * @return
     */
    @PostMapping("/query/tenant-account/getWxCareQr")
    public CommonResult<String> getWxCareQr(@RequestBody @Valid WechatCareQrDTO dto) {
        dto.setTenantId (getMerchantInfoDTO().getTenantId());
        return CommonResult.ok (tenantAccountService.getWxCareQr(dto));
    }
    /**
     * 解绑用户的微信状态
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/tenant-account/closeWechatCare")
    public CommonResult closeWechatCare(@RequestBody @Valid WechatCareQrDTO dto) {
        dto.setTenantId (getMerchantInfoDTO().getTenantId());
        tenantAccountService.closeWechatCare(dto);
        return CommonResult.ok ();
    }

    /**
     * 商品下发
     * @param dto
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/marketitem")
    public CommonResult market(@RequestBody @Valid ProductItemReq dto) {
        return CommonResult.ok ();
    }


    /**
     * 不再提示
     *
     * @return
     */
//    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/no-prompt")
    public CommonResult<Void> noPrompt(@RequestBody @Valid TenantAccountPromptDTO tenantAccountPromptDTO) {
        tenantAccountService.noPrompt(tenantAccountPromptDTO);
        return CommonResult.ok();
    }

    /**
     * 超级账号切换租户
     *
     * @param superAccountLoginDTO 目标租户信息
     * @return token
     */
    @RequiresRoles(value = AuthPermissionConstant.SUPER_READ_ROLE_CODE)
    @PostMapping("query/switch-super-account")
    public CommonResult<TenantAccountLoginVO> switchSuperAccount(@RequestBody @Valid SuperAccountLoginDTO superAccountLoginDTO) {
        return CommonResult.ok(tenantAccountService.switchSuperAccount(superAccountLoginDTO, getMerchantInfoDTO()));
    }

    /**
     * 超级账号租户列表
     *
     * @param superAccountQueryDTO 查询条件
     * @return 租户列表
     */
    @RequiresRoles(value = AuthPermissionConstant.SUPER_READ_ROLE_CODE)
    @PostMapping("query/super-account/page")
    public CommonResult<PageInfo<SuperAccountTenantVO>> querySuperAccountPage(@RequestBody SuperAccountQueryDTO superAccountQueryDTO) {
        return CommonResult.ok(tenantAccountService.querySuperAccountPage(superAccountQueryDTO));
    }
}

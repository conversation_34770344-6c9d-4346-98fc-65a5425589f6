package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.tenant.model.input.TenantFundAccountInsertInput;
import com.cosfo.manage.tenant.model.input.TenantFundAccountQueryInput;
import com.cosfo.manage.tenant.model.input.TenantFundAccountUpdateInput;
import com.cosfo.manage.tenant.model.po.TenantFundAccount;
import com.cosfo.manage.tenant.model.vo.TenantFundAccountVO;
import com.cosfo.manage.tenant.service.TenantFundAccountService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21 10:52
 * @PackageName:com.cosfo.manage.tenant.controller
 * @ClassName: TenantFundAccountController
 * @Description: 非现金账号管理
 * @Version 1.0
 */
@RestController
@RequestMapping("/tenant-fund-account")
public class TenantFundAccountController extends BaseController {

    @Resource
    private TenantFundAccountService tenantFundAccountService;

    /**
     * 非现金子账户新增
     *
     * @return
     */
    @RequestMapping(value = "insert", method = RequestMethod.POST)
    public CommonResult<Boolean> insertTenantFundAccount(@Valid @RequestBody TenantFundAccountInsertInput input){
        return CommonResult.ok(tenantFundAccountService.insertTenantFundAccount(input, getMerchantInfoDTO()));
    }

    /**
     * 非现金子账户修改
     *
     * @return
     */
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public CommonResult<Boolean> updateTenantFundAccount(@Valid @RequestBody TenantFundAccountUpdateInput input){
        return CommonResult.ok(tenantFundAccountService.updateTenantFundAccount(input, getMerchantInfoDTO()));
    }

    /**
     * 非现金子账户列表
     *
     * @return
     */
    @RequestMapping(value = "page", method = RequestMethod.POST)
    public CommonResult<PageInfo<TenantFundAccountVO>> pageTenantFundAccount(@RequestBody TenantFundAccountQueryInput input){
        return CommonResult.ok(tenantFundAccountService.pageTenantFundAccount(input, getMerchantInfoDTO()));
    }

    /**
     * 查询租户维度非现金账户
     * @return
     */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public CommonResult<List<TenantFundAccount>> listTenantFundAccount(){
        return CommonResult.ok(tenantFundAccountService.listByTenantId(UserLoginContextUtils.getTenantId()));
    }

}

package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.bill.model.dto.ReceiveAndOutMoneyDTO;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.order.model.dto.OrderQueryDTO;
import com.cosfo.manage.tenant.model.input.TenantBillInput;
import com.cosfo.manage.tenant.model.vo.TenantBillVO;
import com.cosfo.manage.tenant.service.TenantBillService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 租户账单管理
 * <AUTHOR>
 * @date 2022/5/26  14:58
 */
@RestController()
@RequestMapping("/bill")
public class TenantBillController extends BaseController {
    @Resource
    private TenantBillService tenantBillService;

    /**
     * 交易流水
     *
     * @return
     */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public CommonResult<PageInfo<TenantBillVO>> list(@RequestBody TenantBillInput tenantBillInput) {
        return tenantBillService.listPage(getMerchantInfoDTO(), tenantBillInput);
    }

    @RequestMapping(value = "count", method = RequestMethod.POST)
    public CommonResult<ReceiveAndOutMoneyDTO> count(@RequestBody TenantBillInput tenantBillInput) {
        return tenantBillService.countMoney(getMerchantInfoDTO(), tenantBillInput);
    }

    /**
     * 交易流水明细导出
     *
     * @param tenantBillInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody TenantBillInput tenantBillInput) {
        tenantBillService.export(getMerchantInfoDTO(), tenantBillInput);
    }
}

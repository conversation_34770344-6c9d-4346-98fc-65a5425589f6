package com.cosfo.manage.tenant.controller;

import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.tenant.model.input.TenantFundAccountConfigInsertInput;
import com.cosfo.manage.tenant.model.input.TenantFundAccountConfigUpdateInput;
import com.cosfo.manage.tenant.model.vo.TenantFundAccountConfigVO;
import com.cosfo.manage.tenant.service.TenantFundAccountConfigService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/4/21 11:36
 * @PackageName:com.cosfo.manage.tenant.controller
 * @ClassName: TenantFundAccountConfigController
 * @Description: TODO
 * @Version 1.0
 */
@RestController
@RequestMapping("/tenant-fund-account-config")
public class TenantFundAccountConfigController extends BaseController {

    @Resource
    private TenantFundAccountConfigService tenantFundAccountConfigService;

    /**
     * 非现金账户新增
     *
     * @return
     */
    @RequestMapping(value = "insert", method = RequestMethod.POST)
    public CommonResult<Boolean> insertTenantFundAccountConfig (@Valid @RequestBody TenantFundAccountConfigInsertInput input){
        return CommonResult.ok(tenantFundAccountConfigService.insertTenantFundAccountConfig(input, getMerchantInfoDTO()));
    }

    /**
     * 非现金账户修改
     *
     * @return
     */
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public CommonResult<Boolean> updateTenantFundAccountConfig (@Valid @RequestBody TenantFundAccountConfigUpdateInput input){
        return CommonResult.ok(tenantFundAccountConfigService.updateTenantFundAccountConfig(input, getMerchantInfoDTO()));
    }

    /**
     * 非现金详情
     *
     * @return
     */
    @RequestMapping(value = "get-info", method = RequestMethod.POST)
    public CommonResult<TenantFundAccountConfigVO> getInfoTenantFundAccountConfig (){
        return CommonResult.ok(tenantFundAccountConfigService.getInfoTenantFundAccountConfig(getMerchantInfoDTO()));
    }
}

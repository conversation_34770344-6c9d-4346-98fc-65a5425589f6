package com.cosfo.manage.tenant.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.context.DefaultFlagEnum;
import com.cosfo.manage.common.context.FlowRuleAuditBizTypeEnum;
import com.cosfo.manage.common.context.SchemeTypeEnum;
import com.cosfo.manage.common.context.StoreTypeEnum;
import com.cosfo.manage.common.context.SwitchFlagEnum;
import com.cosfo.manage.common.context.TenantAccountEnums;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.AuthUserFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantAccountFacade;
import com.cosfo.manage.tenant.convert.RoleConvert;
import com.cosfo.manage.tenant.convert.TenantAccountConvert;
import com.cosfo.manage.tenant.convert.TenantAccountMapperConvert;
import com.cosfo.manage.tenant.mapper.TenantCommonConfigMapper;
import com.cosfo.manage.tenant.model.dto.FlowSchemeQueryParam;
import com.cosfo.manage.tenant.model.input.FlowSchemeInput;
import com.cosfo.manage.tenant.model.input.FlowSchemeUpdateInput;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.po.TenantCommonConfig;
import com.cosfo.manage.tenant.model.po.TenantFlowRuleAudit;
import com.cosfo.manage.tenant.model.po.TenantFlowScheme;
import com.cosfo.manage.tenant.model.vo.FlowRuleAuditDTO;
import com.cosfo.manage.tenant.model.vo.FlowSchemeDetailVO;
import com.cosfo.manage.tenant.model.vo.FlowSchemeStoreBelongVO;
import com.cosfo.manage.tenant.model.vo.FlowSchemeVO;
import com.cosfo.manage.tenant.model.vo.RoleVO;
import com.cosfo.manage.tenant.model.vo.TenantAccountVO;
import com.cosfo.manage.tenant.repository.TenantFlowRuleAuditRepository;
import com.cosfo.manage.tenant.repository.TenantFlowSchemeRepository;
import com.cosfo.manage.tenant.service.TenantAccountService;
import com.cosfo.manage.tenant.service.TenantFlowSchemeService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthUserRoleDto;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.tenant.req.TenantAccountListQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2023/12/25 下午4:01
 */
@Service
@Slf4j
public class TenantFlowSchemeServiceImpl implements TenantFlowSchemeService {

    @Resource
    private TenantFlowSchemeRepository tenantFlowSchemeRepository;
    @Resource
    private TenantFlowRuleAuditRepository tenantFlowRuleAuditRepository;
    @Resource
    private TenantAccountService tenantAccountService;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private AuthUserFacade authUserFacade;
    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
    @Resource
    private TenantCommonConfigMapper tenantCommonConfigMapper;


    @Override
    public List<FlowSchemeVO> listFlowScheme(FlowSchemeInput flowSchemeInput) {
        FlowSchemeQueryParam param = new FlowSchemeQueryParam();
        param.setSchemeId(flowSchemeInput.getSchemeId());
        param.setTenantId(flowSchemeInput.getTenantId());
        param.setSchemeName(flowSchemeInput.getSchemeName());
        param.setSchemeType(flowSchemeInput.getSchemeType());
        param.setDefaultFlag(flowSchemeInput.getDefaultFlag());
        List<TenantFlowScheme> flowSchemes = tenantFlowSchemeRepository.selectByParam(param);
        List<FlowSchemeVO> flowSchemeVOS = flowSchemes.stream().map(e -> {
            FlowSchemeVO flowSchemeVO = new FlowSchemeVO();
            flowSchemeVO.setSchemeId(e.getId());
            flowSchemeVO.setSchemeName(e.getSchemeName());
            flowSchemeVO.setSchemeType(e.getSchemeType());
            flowSchemeVO.setDefaultFlag(e.getDefaultFlag());
            flowSchemeVO.setStoreIdSet(Sets.newHashSet());
            flowSchemeVO.setStoreNum(0);
            try {
                List<Long> storeIdList = JSONObject.parseArray(e.getSchemeTargetInfo(), Long.class);
                if (!CollectionUtils.isEmpty(storeIdList)) {
                    flowSchemeVO.setStoreIdSet(Sets.newHashSet(storeIdList));
                    flowSchemeVO.setStoreNum(flowSchemeVO.getStoreIdSet().size());
                }
            } catch (Exception ex) {
                log.error("解析流程方案指定门店错误, schemeTargetInfo={}", e.getSchemeTargetInfo(), ex);
            }
            flowSchemeVO.setCreateTime(e.getCreateTime());
            flowSchemeVO.setCreatorName(e.getCreatorName());
            return flowSchemeVO;
        }).collect(Collectors.toList());

        if (flowSchemeInput.getStoreId() != null) {
            flowSchemeVOS = flowSchemeVOS.stream().filter(e -> e.getStoreIdSet().contains(flowSchemeInput.getStoreId())).collect(Collectors.toList());
        }

        return flowSchemeVOS;
    }

    @Override
    public FlowSchemeDetailVO schemeDetail(Long tenantId, Long schemeId) {
        TenantFlowScheme tenantFlowScheme = tenantFlowSchemeRepository.selectById(tenantId, schemeId);
        if (tenantFlowScheme == null) {
            throw new BizException("方案不存在");
        }

        FlowSchemeDetailVO flowSchemeDetailVO = new FlowSchemeDetailVO();
        flowSchemeDetailVO.setSchemeId(tenantFlowScheme.getId());
        flowSchemeDetailVO.setSchemeName(tenantFlowScheme.getSchemeName());
        flowSchemeDetailVO.setSchemeType(tenantFlowScheme.getSchemeType());
        flowSchemeDetailVO.setDefaultFlag(tenantFlowScheme.getDefaultFlag());
        if (DefaultFlagEnum.FALSE.getFlag().equals(tenantFlowScheme.getDefaultFlag())) {
            try {
                flowSchemeDetailVO.setStoreIdList(JSONObject.parseArray(tenantFlowScheme.getSchemeTargetInfo(), Long.class));
            } catch (Exception ex) {
                log.error("解析流程方案指定门店错误, schemeTargetInfo={}", tenantFlowScheme.getSchemeTargetInfo(), ex);
            }
            List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(flowSchemeDetailVO.getStoreIdList());
            List<FlowSchemeDetailVO.FlowSchemeStoreDTO> storeDTOList = merchantStoreList.stream().map(e -> {
                FlowSchemeDetailVO.FlowSchemeStoreDTO storeDTO = new FlowSchemeDetailVO.FlowSchemeStoreDTO();
                storeDTO.setStoreId(e.getId());
                storeDTO.setStoreNo(e.getStoreNo());
                storeDTO.setStoreName(e.getStoreName());
                storeDTO.setType(e.getType());
                return storeDTO;
            }).collect(Collectors.toList());
            flowSchemeDetailVO.setStoreDTOList(storeDTOList);
        }

        flowSchemeDetailVO.setCreateTime(tenantFlowScheme.getCreateTime());
        flowSchemeDetailVO.setCreatorName(tenantFlowScheme.getCreatorName());


        List<TenantFlowRuleAudit> tenantFlowRuleAudits = tenantFlowRuleAuditRepository.selectByTenantAndFlowSchemeId(tenantId, schemeId);
        List<FlowRuleAuditDTO> flowRuleAuditDTOList = tenantFlowRuleAudits.stream().map(e -> {
            FlowRuleAuditDTO flowRuleAuditDTO = new FlowRuleAuditDTO();
            flowRuleAuditDTO.setId(e.getId());
            flowRuleAuditDTO.setBizType(e.getBizType());
            flowRuleAuditDTO.setSwitchFlag(SwitchFlagEnum.OPEN.getFlag().equals(e.getSwitchFlag()));
            flowRuleAuditDTO.setAuditAccountIds(Lists.newArrayList(e.getAuditAccountIds()));
            flowRuleAuditDTO.setAuditAccountList(buildAuditAccount(e.getTenantId(), flowRuleAuditDTO.getAuditAccountIds()));
            return flowRuleAuditDTO;
        }).collect(Collectors.toList());
        flowSchemeDetailVO.setFlowRuleAuditDTOList(flowRuleAuditDTOList);

        return flowSchemeDetailVO;
    }

    private List<TenantAccountVO> buildAuditAccount(Long tenantId, List<Long> auditAccountIds) {
        if (tenantId == null || CollectionUtils.isEmpty(auditAccountIds)) {
            return Collections.emptyList();
        }
        TenantAccountListQueryReq queryReq = new TenantAccountListQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setAuthUserIds(auditAccountIds);

        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(1);
        pageQueryReq.setPageSize(auditAccountIds.size());
        PageInfo<TenantAccountResultResp> tenantAccountInfoPage = userCenterTenantAccountFacade.getTenantAccountInfoPage(queryReq, pageQueryReq);
        List<TenantAccountResultResp> respList = tenantAccountInfoPage.getList();
        List<TenantAccount> tenantAccounts = TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(respList);
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            return Collections.emptyList();
        }

        List<Long> authUserIds = tenantAccounts.stream().map(TenantAccount::getAuthUserId).collect(Collectors.toList());
        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(authUserIds);
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        List<TenantAccountVO> tenantAccountVOS = tenantAccounts.stream().map(tenantAccount -> {
            TenantAccountVO tenantAccountVO = TenantAccountConvert.convertToTenantAccountVO(tenantAccount);
            return getTenantAccountVO(tenantAccountVO, authUserRoleDtoMap);
        }).collect(Collectors.toList());
        return tenantAccountVOS;
    }

    private TenantAccountVO getTenantAccountVO(TenantAccountVO tenantAccountVO, Map<Long, AuthUserRoleDto> authUserRoleDtoMap) {
        if (authUserRoleDtoMap.containsKey(tenantAccountVO.getAuthUserId())) {
            AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(tenantAccountVO.getAuthUserId());
            List<AuthRole> roles = authUserRoleDto.getRoles();
            List<RoleVO> roleVOS = RoleConvert.convertToRoleVOList(roles);
            tenantAccountVO.setRoleVos(roleVOS);
        }
        return tenantAccountVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateScheme(LoginContextInfoDTO loginContextInfoDTO, FlowSchemeUpdateInput flowSchemeUpdateInput) {
        Long tenantId = loginContextInfoDTO.getTenantId();
        Long authUserId = loginContextInfoDTO.getAuthUserId();
        Long schemeId = flowSchemeUpdateInput.getSchemeId();

        if (CollectionUtils.isEmpty(flowSchemeUpdateInput.getFlowRuleAuditDTOList())) {
            throw new BizException("流程审核配置为空");
        }

        if (DefaultFlagEnum.TURE.getFlag().equals(flowSchemeUpdateInput.getDefaultFlag()) && schemeId == null) {
            throw new BizException("默认方案id不能为空");
        }

        if (DefaultFlagEnum.FALSE.getFlag().equals(flowSchemeUpdateInput.getDefaultFlag()) && StringUtils.isBlank(flowSchemeUpdateInput.getSchemeName())) {
            throw new BizException("方案名称不能为空");
        }

        if (tenantFlowSchemeRepository.existRepeatSchemeName(tenantId, flowSchemeUpdateInput.getSchemeName(), flowSchemeUpdateInput.getSchemeId())) {
            throw new BizException("方案名称不能重复");
        }

        if (flowSchemeUpdateInput.getStoreIdList() == null) {
            flowSchemeUpdateInput.setStoreIdList(new ArrayList<>());
        }

        // 查询当前需要保存的方案下门店id，是否属于其他例外方案，如果属于，需要从其他例外方案清除掉
        deleteStoreIdFromOtherFlowScheme(tenantId, schemeId, flowSchemeUpdateInput.getStoreIdList());

        String userName = "";
        TenantAccountVO tenantAccountVO = tenantAccountService.getSaasTenantAccountVO(authUserId);
        if (tenantAccountVO != null) {
            userName = tenantAccountVO.getNickname();
        }

        if (schemeId == null) { // 新增
            TenantFlowScheme tenantFlowScheme = new TenantFlowScheme();
            tenantFlowScheme.setTenantId(tenantId);
            tenantFlowScheme.setSchemeType(flowSchemeUpdateInput.getSchemeType());
            tenantFlowScheme.setSchemeName(flowSchemeUpdateInput.getSchemeName());
            tenantFlowScheme.setDefaultFlag(flowSchemeUpdateInput.getDefaultFlag());
            if (DefaultFlagEnum.FALSE.getFlag().equals(flowSchemeUpdateInput.getDefaultFlag())) {
                tenantFlowScheme.setSchemeTargetInfo(JSONObject.toJSONString(Sets.newLinkedHashSet(flowSchemeUpdateInput.getStoreIdList())));
            }
            tenantFlowScheme.setOperatorName(userName);
            tenantFlowScheme.setCreatorName(userName);

            tenantFlowSchemeRepository.save(tenantFlowScheme);

            schemeId = tenantFlowScheme.getId();
            for (FlowRuleAuditDTO flowRuleAuditDTO : flowSchemeUpdateInput.getFlowRuleAuditDTOList()) {
                TenantFlowRuleAudit tenantFlowRuleAudit = new TenantFlowRuleAudit();
                tenantFlowRuleAudit.setTenantId(tenantId);
                tenantFlowRuleAudit.setFlowSchemeId(schemeId);
                tenantFlowRuleAudit.setBizType(flowRuleAuditDTO.getBizType());
                tenantFlowRuleAudit.setSwitchFlag(flowRuleAuditDTO.getSwitchFlag() ? SwitchFlagEnum.OPEN.getFlag() : SwitchFlagEnum.CLOSE.getFlag());
                tenantFlowRuleAudit.setAuditAccountIds(Sets.newHashSet(flowRuleAuditDTO.getAuditAccountIds()));
                tenantFlowRuleAuditRepository.save(tenantFlowRuleAudit);
            }
        } else {// 更新
            TenantFlowScheme tenantFlowScheme = new TenantFlowScheme();
            tenantFlowScheme.setId(schemeId);
            tenantFlowScheme.setTenantId(tenantId);
            tenantFlowScheme.setSchemeType(flowSchemeUpdateInput.getSchemeType());
            tenantFlowScheme.setSchemeName(flowSchemeUpdateInput.getSchemeName());
            tenantFlowScheme.setDefaultFlag(flowSchemeUpdateInput.getDefaultFlag());
            if (DefaultFlagEnum.FALSE.getFlag().equals(flowSchemeUpdateInput.getDefaultFlag())) {
                tenantFlowScheme.setSchemeTargetInfo(JSONObject.toJSONString(Sets.newLinkedHashSet(flowSchemeUpdateInput.getStoreIdList())));
            }
            tenantFlowScheme.setOperatorName(userName);
            tenantFlowSchemeRepository.updateById(tenantFlowScheme);

            schemeId = tenantFlowScheme.getId();
            for (FlowRuleAuditDTO flowRuleAuditDTO : flowSchemeUpdateInput.getFlowRuleAuditDTOList()) {
                TenantFlowRuleAudit tenantFlowRuleAudit = new TenantFlowRuleAudit();
                tenantFlowRuleAudit.setId(flowRuleAuditDTO.getId());
                tenantFlowRuleAudit.setTenantId(tenantId);
                tenantFlowRuleAudit.setFlowSchemeId(schemeId);
                tenantFlowRuleAudit.setBizType(flowRuleAuditDTO.getBizType());
                tenantFlowRuleAudit.setSwitchFlag(flowRuleAuditDTO.getSwitchFlag() ? SwitchFlagEnum.OPEN.getFlag() : SwitchFlagEnum.CLOSE.getFlag());
                tenantFlowRuleAudit.setAuditAccountIds(Sets.newHashSet(flowRuleAuditDTO.getAuditAccountIds()));
                tenantFlowRuleAuditRepository.updateById(tenantFlowRuleAudit);
            }
        }

    }

    private void deleteStoreIdFromOtherFlowScheme(Long tenantId, Long schemeId, List<Long> storeIdList) {
        List<FlowSchemeStoreBelongVO> storeBelongVOS = queryExtraSchemeByStoreIds(tenantId, storeIdList);

        // 当前方案更新，排除归属当前方案id的记录
        if (schemeId != null) {
            storeBelongVOS = storeBelongVOS.stream().filter(e -> !schemeId.equals(e.getSchemeId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(storeBelongVOS)) {
            return;
        }

        Map<Long, List<Long>> schemeId2StoreIdMap = storeBelongVOS.stream().collect(Collectors.groupingBy(FlowSchemeStoreBelongVO::getSchemeId, Collectors.mapping(FlowSchemeStoreBelongVO::getId, Collectors.toList())));
        for (Map.Entry<Long, List<Long>> entry : schemeId2StoreIdMap.entrySet()) {
            Long updateSchemeId = entry.getKey();
            List<Long> storeIds = entry.getValue();
            tenantFlowSchemeRepository.updateFlowSchemeExcludeStoreIds(tenantId, updateSchemeId, storeIds);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteScheme(Long tenantId, Long schemeId) {
        boolean flag = tenantFlowSchemeRepository.deleteById(tenantId, schemeId);
        if (!flag) {
            log.warn("方案删除失败, tenantId={} schemeId={}", tenantId, schemeId);
        }
    }

    @Override
    public List<FlowSchemeStoreBelongVO> queryExtraSchemeByStoreIds(Long tenantId, List<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Collections.emptyList();
        }
        FlowSchemeInput flowSchemeInput = new FlowSchemeInput();
        flowSchemeInput.setTenantId(tenantId);
        flowSchemeInput.setDefaultFlag(DefaultFlagEnum.FALSE.getFlag());
        List<FlowSchemeVO> flowSchemeVOS = listFlowScheme(flowSchemeInput);

        List<FlowSchemeStoreBelongVO> resultList = new ArrayList<>();
        for (Long storeId : storeIds) {
            for (FlowSchemeVO flowSchemeVO : flowSchemeVOS) {
                if (flowSchemeVO.getStoreIdSet().contains(storeId)) {
                    FlowSchemeStoreBelongVO vo = new FlowSchemeStoreBelongVO();
                    vo.setId(storeId);
                    vo.setSchemeId(flowSchemeVO.getSchemeId());
                    vo.setSchemeName(flowSchemeVO.getSchemeName());
                    vo.setSchemeType(flowSchemeVO.getSchemeType());
                    vo.setDefaultFlag(flowSchemeVO.getDefaultFlag());
                    resultList.add(vo);
                }
            }
        }

        return resultList;
    }

    @Override
    public TenantFlowRuleAudit getAuditByStoreIdAndBizType(Long storeId, FlowRuleAuditBizTypeEnum bizTypeEnum) {
        return getAuditMapByStoreIdsAndBizType(Lists.newArrayList(storeId), bizTypeEnum).get(storeId);
    }

    private Long getSchemeIdByStoreId(Long tenantId, Long storeId, Integer storeType, List<FlowSchemeVO> flowSchemeVOS) {
        // 例外方案
        List<FlowSchemeVO> extraSchemeList = flowSchemeVOS.stream().filter(e -> DefaultFlagEnum.FALSE.getFlag().equals(e.getDefaultFlag())).collect(Collectors.toList());

        Long schemeId = extraSchemeList.stream().filter(e -> e.getStoreIdSet().contains(storeId)).map(e -> e.getSchemeId()).findFirst().orElse(null);

        if (schemeId == null) {
            // 默认方案
            List<FlowSchemeVO> defaultSchemeList = flowSchemeVOS.stream().filter(e -> DefaultFlagEnum.TURE.getFlag().equals(e.getDefaultFlag())).collect(Collectors.toList());
            Integer storeSchemeType = SchemeTypeEnum.DIRECT.getType();
            if (StoreTypeEnum.FRANCHISE.getCode().equals(storeType)) {
                storeSchemeType = SchemeTypeEnum.FRANCHISE.getType();
            }
            Integer finalStoreSchemeType = storeSchemeType;
            schemeId = defaultSchemeList.stream().filter(e -> e.getSchemeType().equals(finalStoreSchemeType)).map(e -> e.getSchemeId()).findFirst().orElse(null);
        }

        return schemeId;
    }

    @Override
    public Map<Long, TenantFlowRuleAudit> getAuditMapByStoreIdsAndBizType(List<Long> storeIds, FlowRuleAuditBizTypeEnum bizTypeEnum) {
        List<MerchantStoreResultResp> storeResultRespList = userCenterMerchantStoreFacade.getMerchantStoreList(storeIds);
        if (CollectionUtils.isEmpty(storeResultRespList)) {
            throw new BizException("门店不存在");
        }

        Map<Long, Integer> storeId2TypeMap = storeResultRespList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, MerchantStoreResultResp::getType, (v1, v2) -> v1));

        Long tenantId = storeResultRespList.get(0).getTenantId();

//        FlowSchemeQueryParam param = new FlowSchemeQueryParam();
//        param.setTenantId(tenantId);
//        List<TenantFlowScheme> flowSchemes = tenantFlowSchemeRepository.selectByParam(param);

        FlowSchemeInput flowSchemeInput = new FlowSchemeInput();
        flowSchemeInput.setTenantId(tenantId);
        List<FlowSchemeVO> flowSchemeVOS = listFlowScheme(flowSchemeInput);


        // 每个门店id对应的方案
        Map<Long, Long> storeId2schemeIdMap = new HashMap<>();
        Set<Long> schemeIdSet = new HashSet<>();
        for (Long storeId : storeIds) {
            Long schemeId = getSchemeIdByStoreId(tenantId, storeId, storeId2TypeMap.get(storeId), flowSchemeVOS);

            if (schemeId == null) {
                log.warn("流程方案不存在， storeId={}, bizTypeEnum={}", storeId, bizTypeEnum);
                continue;
            }
            schemeIdSet.add(schemeId);
            storeId2schemeIdMap.put(storeId, schemeId);
        }

        List<TenantFlowRuleAudit> auditList = tenantFlowRuleAuditRepository.getByFlowSchemeIdListAndBizType(tenantId, schemeIdSet, bizTypeEnum.getType());
        Map<Long, TenantFlowRuleAudit> schemeId2TenantFlowRuleAuditMap = auditList.stream().collect(Collectors.toMap(TenantFlowRuleAudit::getFlowSchemeId, Function.identity(), (v1, v2) -> v1));

        // 返回门店id对应的审核规则项
        return storeId2schemeIdMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> schemeId2TenantFlowRuleAuditMap.get(entry.getValue())));
    }

    @Override
    public boolean getAuditSwitchByStoreIdAndBizType(Long storeId, FlowRuleAuditBizTypeEnum bizTypeEnum) {
        TenantFlowRuleAudit tenantFlowRuleAudit = getAuditByStoreIdAndBizType(storeId, bizTypeEnum);
        if (tenantFlowRuleAudit == null) {
            log.warn("审核配置不存在， storeId={}, bizTypeEnum={}", storeId, bizTypeEnum);
            return false;
        }

        return SwitchFlagEnum.OPEN.getFlag().equals(tenantFlowRuleAudit.getSwitchFlag());
    }

    @Override
    public boolean canAuditByAccountIdAndBizType(Long accountId, Long storeId, FlowRuleAuditBizTypeEnum bizTypeEnum) {
        return canAuditStoreIdsByAccountIdAndBizType(accountId, Lists.newArrayList(storeId), bizTypeEnum).get(storeId);
    }

    @Override
    public Map<Long, Boolean> canAuditStoreIdsByAccountIdAndBizType(Long accountId, List<Long> storeIdList, FlowRuleAuditBizTypeEnum bizTypeEnum) {
        // 当前账号是否超级管理员
        if (checkHavingSuperAdmin(accountId)) {
            return storeIdList.stream().collect(Collectors.toMap(k -> k, v -> true, (v1, v2) -> v1));
        }

        Map<Long, TenantFlowRuleAudit> storeId2flowRuleAuditMap = getAuditMapByStoreIdsAndBizType(storeIdList, bizTypeEnum);

        Map<Long, Boolean> storeIdAuditFlagMap = storeIdList.stream().collect(Collectors.toMap(k -> k, v -> false, (v1, v2) -> v1));
        for (Map.Entry<Long, TenantFlowRuleAudit> entry : storeId2flowRuleAuditMap.entrySet()) {
            Long storeId = entry.getKey();
            TenantFlowRuleAudit tenantFlowRuleAudit = entry.getValue();

            if (SwitchFlagEnum.OPEN.getFlag().equals(tenantFlowRuleAudit.getSwitchFlag())) {
                storeIdAuditFlagMap.put(storeId, tenantFlowRuleAudit.getAuditAccountIds() != null && tenantFlowRuleAudit.getAuditAccountIds().contains(accountId));
            } else {
                storeIdAuditFlagMap.put(storeId, false);
            }
        }

        return storeIdAuditFlagMap;
    }

    private Boolean checkHavingSuperAdmin(Long authUserId) {
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Collections.singletonList(authUserId));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(authUserId);
        if (Objects.nonNull(authUserRoleDto)) {
            List<AuthRole> roles = authUserRoleDto.getRoles();
            // 判断是否属于超级管理员，超级管理员角色不可变更
            return roles.stream().anyMatch(authRole -> TenantAccountEnums.Role.SUPER_ADMIN.getCode().equals(authRole.getSuperAdmin().intValue()));
        }

        return Boolean.FALSE;
    }

    @Override
    public void initDefaultFlowScheme(Long tenantId) {
        FlowSchemeQueryParam param = new FlowSchemeQueryParam();
        param.setTenantId(tenantId);
        param.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        List<TenantFlowScheme> flowSchemes = tenantFlowSchemeRepository.selectByParam(param);
        Map<Integer, Long> defaultFlowSchemeType2idMap = flowSchemes.stream().collect(Collectors.toMap(TenantFlowScheme::getSchemeType, TenantFlowScheme::getId, (v1, v2) -> v1));

        for (SchemeTypeEnum schemeTypeEnum : SchemeTypeEnum.values()) {
            if (!defaultFlowSchemeType2idMap.containsKey(schemeTypeEnum.getType())) {
                TenantFlowScheme tenantFlowScheme = new TenantFlowScheme();
                tenantFlowScheme.setTenantId(tenantId);
                tenantFlowScheme.setSchemeType(schemeTypeEnum.getType());
                tenantFlowScheme.setSchemeName(schemeTypeEnum.getDesc());
                tenantFlowScheme.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
//                tenantFlowScheme.setSchemeTargetInfo("");
                tenantFlowScheme.setOperatorName("系统");
                tenantFlowScheme.setCreatorName("系统");
                tenantFlowScheme.setCreateTime(LocalDateTime.now());
                tenantFlowScheme.setUpdateTime(LocalDateTime.now());
                tenantFlowSchemeRepository.save(tenantFlowScheme);
                defaultFlowSchemeType2idMap.put(schemeTypeEnum.getType(), tenantFlowScheme.getId());
            }

            Long flowSchemeId = defaultFlowSchemeType2idMap.get(schemeTypeEnum.getType());

            List<TenantFlowRuleAudit> tenantFlowRuleAudits = tenantFlowRuleAuditRepository.selectByTenantAndFlowSchemeId(tenantId, flowSchemeId);
            // 审核业务类型对应id的map
            Map<Integer, Long> flowRuleAuditBizType2idMap = tenantFlowRuleAudits.stream().collect(Collectors.toMap(TenantFlowRuleAudit::getBizType, TenantFlowRuleAudit::getId, (v1, v2) -> v1));

            for (FlowRuleAuditBizTypeEnum bizTypeEnum : schemeTypeEnum.getBizTypeEnums()) {
                // 存在审核配置记录，不处理
                if (flowRuleAuditBizType2idMap.containsKey(bizTypeEnum.getType())) {
                    continue;
                }

                TenantFlowRuleAudit tenantFlowRuleAudit = new TenantFlowRuleAudit();
                tenantFlowRuleAudit.setTenantId(tenantId);
                tenantFlowRuleAudit.setFlowSchemeId(flowSchemeId);
                tenantFlowRuleAudit.setBizType(bizTypeEnum.getType());
                tenantFlowRuleAudit.setSwitchFlag(bizTypeEnum.getDefaultSwitchFlag().getFlag());
                if (bizTypeEnum == FlowRuleAuditBizTypeEnum.SELFWAREHOUSE_PRE_DELIVERY_AFTERSALE_AUDIT) {
                    tenantFlowRuleAudit.setSwitchFlag(getConfigForAfterSaleAudit(tenantId));
                }
                tenantFlowRuleAudit.setAuditAccountIds(Sets.newHashSet());
                tenantFlowRuleAudit.setCreateTime(LocalDateTime.now());
                tenantFlowRuleAudit.setUpdateTime(LocalDateTime.now());
                tenantFlowRuleAuditRepository.save(tenantFlowRuleAudit);
            }

        }
    }

    private Integer getConfigForAfterSaleAudit(Long tenantId) {
        TenantCommonConfig config = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantConfigEnum.TenantConfig.SELF_WAREHOUSE_AFTER_SALE_APPROVAL_RULE.getConfigKey());
        if (config != null && config.getConfigValue() != null) {
            return NumberUtils.toInt(config.getConfigValue(), SwitchFlagEnum.CLOSE.getFlag());
        }
        return SwitchFlagEnum.CLOSE.getFlag();
    }
}

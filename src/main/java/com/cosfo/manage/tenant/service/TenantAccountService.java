package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.dto.*;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.vo.*;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
public interface TenantAccountService {
    /**
     * 预登录
     *
     * @param tenantAccountPreLoginDTO
     * @return
     */
    TenantAccountPreLoginVO preLogin(TenantAccountPreLoginDTO tenantAccountPreLoginDTO);


    /**
     * 登录
     *
     * @param tenantAccountLoginDTO
     * @return
     */
    TenantAccountLoginVO login(TenantAccountLoginDTO tenantAccountLoginDTO);

    /**
     * 更新用户信息
     *
     * @param tenantAccountDTO
     */
    Boolean updateUserInfo(TenantAccountDTO tenantAccountDTO,  LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 创建账号
     *
     * @param tenantAccountDTO
     * @return
     */
    Boolean create(TenantAccountDTO tenantAccountDTO);

    /**
     * 获取账号
     *
     * @return
     */
    TenantAccountVO getTenantAccountVO(Long authUserId);

    /**
     * 批量获取账号
     *
     * @return
     */
    List<TenantAccountVO> selectByAuthUserIds(List<Long> authUserIds);

    /**
     * 获取saas账户信息
     * @param authUserId
     * @return
     */
    TenantAccountVO getSaasTenantAccountVO(Long authUserId);

    /**
     * 账号列表
     *
     * @param tenantAccountListQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    PageInfo<TenantAccountVO> page(TenantAccountListQueryDTO tenantAccountListQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 删除用户
     *
     * @param tenantAccountId
     */
    void remove(Long tenantAccountId, Long tenantId);

    /**
     * 发送验证码
     * @param phone
     * @return
     */
    Boolean sendCode(String phone);

    /**
     * 发送验证码
     * @param phone
     * @return
     */
    Boolean sendCode(SendCodeDTO sendCodeDTO);

    /**
     * 校验验证码及修改密码
     * @param sendCodeDTO
     * @return
     */
    Boolean examineCode(SendCodeDTO sendCodeDTO);

    /**
     * 更改密码
     *
     * @param tenantAccountDTO
     * @return
     */
    Boolean updatePassword(TenantAccountDTO tenantAccountDTO);

    /**
     * 查询用户信息
     *
     * @param tenantId
     * @param userId
     * @return
     */
    TenantAccountVO queryAccountInfo(LoginContextInfoDTO merchantInfoDTO , Long userId);

    /**
     * 切换租户
     *
     * @param tenantId
     * @param phone
     * @return
     */
    TenantAccountLoginVO switchTenant(Long tenantId, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 获取租户下所有操作人
     * @param tenantId
     * @return
     */
    List<TenantAccount> listTenantAccount(Long tenantId);

    /**
     * 获取账户信息
     * @param userIds
     * @return
     */
    Map<Long, TenantAccount> queryAccountMap(Set<Long> userIds);

    /**
     * 同步历史账号
     */
//    void synchronizedUser();

    /**
     * 通过手机号查询品牌方
     *
     * @param phone
     * @param tenantIds
     * @return
     */
    List<TenantAccount> selectByPhone(String phone, List<Long> tenantIds);

    /**
     * 更新用户信息
     *
     * @param tenantAccountDTO
     * @param tenantIds 品牌方租户Id
     */
    void update(TenantAccountDTO tenantAccountDTO, List<Long> tenantIds);

    /**
     * 根据userId查询
     *
     * @param authUserId
     * @return
     */
    TenantAccount selectByAuthUserId(Long authUserId);

    /**
     * 查询
     *
     * @param id
     * @param tenantId
     * @return
     */
    TenantAccount selectByIdAndTenantId(Long id, Long tenantId);

    /**
     * 查询
     *
     * @param id
     */
    TenantAccount selectById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void remove(Long id);
    /**
     * 供应商 账号列表
     * @param queryDTO
     * @return
     */
    List<SupplierDistributorTenantAccountVO> supplierDistributorAccountList(SupplierDistributorTenantAccountQueryDTO queryDTO, LoginContextInfoDTO loginContextInfoDTO);
    /**
     * 账号 关闭/开启 消息推送
     *
     * @param dto
     * @return
     */
    void accountReceiveMsgSwitchUpdate(TenantAccountReceiveMsgSwitchDTO dto, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 解绑公众号
     * @param dto
     */
    void closeWechatCare(WechatCareQrDTO dto);

    /**
     * 获取二维码
     * @param dto
     * @return
     */
    String getWxCareQr(WechatCareQrDTO dto);
    /**
     * 删除供应商 账号绑定关系
     * @return
     */
    void deleteSupplierDistributorAccount(TenantAccountDTO tenantAccountDTO);

    /**
     * 根据 手机号查询 账号
     * @param bindTenantQueryDTO
     * @return
     */
    List<TenantAccountVO> getTenantAccountByTenantIdsAndPhone(TenantAccountListQueryDTO dto);

    /**
     * 手机号不再提示
     * @param loginContextInfoDTO
     */
    void noPrompt(TenantAccountPromptDTO tenantAccountPromptDTO);


    /**
     * 获取超级账号可切换租户列表
     * @param superAccountQueryDTO
     * @return
     */
    PageInfo<SuperAccountTenantVO> querySuperAccountPage(SuperAccountQueryDTO superAccountQueryDTO);


    /**
     * 切换超级账号租户
     *
     * @param superAccountLoginDTO
     * @param loginContextInfoDTO
     * @return
     */
    TenantAccountLoginVO switchSuperAccount(SuperAccountLoginDTO superAccountLoginDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 获取超级账号信息
     *
     * @param loginContextInfoDTO 登录信息
     * @return 超级账号信息
     */
    TenantAccountVO getSuperAccountInfo(LoginContextInfoDTO loginContextInfoDTO);

    public List<TenantAccount> selectBrandAccountByEmail(String email);
}

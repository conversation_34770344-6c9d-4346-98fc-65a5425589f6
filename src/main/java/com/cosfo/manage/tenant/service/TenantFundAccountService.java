package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.input.TenantFundAccountInsertInput;
import com.cosfo.manage.tenant.model.input.TenantFundAccountQueryInput;
import com.cosfo.manage.tenant.model.input.TenantFundAccountUpdateInput;
import com.cosfo.manage.tenant.model.po.TenantFundAccount;
import com.cosfo.manage.tenant.model.vo.TenantFundAccountVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21 10:54
 * @PackageName:com.cosfo.manage.tenant.service
 * @ClassName: TenantFundAccountService
 * @Description: TODO
 * @Version 1.0
 */
public interface TenantFundAccountService {

    /**
    * @description 新增子账户
    * @params [input]
    * @return java.lang.Boolean
    * <AUTHOR>
    * @date  2025/4/21 11:23
    */
    Boolean insertTenantFundAccount(TenantFundAccountInsertInput input, LoginContextInfoDTO merchantInfoDTO);

    /**
    * @description 修改子账户
    * @params [input]
    * @return java.lang.Boolean
    * <AUTHOR>
    * @date  2025/4/21 11:28
    */
    Boolean updateTenantFundAccount(TenantFundAccountUpdateInput input, LoginContextInfoDTO merchantInfoDTO);

    /**
    * @description 分页查询子账户
    * @params [input]
    * @return com.github.pagehelper.PageInfo<com.cosfo.manage.tenant.model.vo.TenantFundAccountVO>
    * <AUTHOR>
    * @date  2025/4/21 11:34
    */
    PageInfo<TenantFundAccountVO> pageTenantFundAccount(TenantFundAccountQueryInput input, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 根据ids查询
     * @param ids
     * @return
     */
    List<TenantFundAccount> listByIds(List<Long> ids);

    /**
     * 根据tenantId查询
     * @param tenantId
     * @return
     */
    List<TenantFundAccount> listByTenantId(Long tenantId);
}

package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.po.TenantCompany;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
public interface TenantCompanyService {

    /**
     * 根据租户ID查询
     * @param tenantId
     * @return
     */
    TenantCompany selectByTenantId(Long tenantId);

    /**
     * 根据租户ID查询简要的公司信息
     *
     * @param tenantId
     * @return
     */
    TenantCompany selectSimpleCompanyByTenantId(Long tenantId);

    /**
     * 根据公司名称模糊匹配
     * @param companyName
     * @return
     */
    List<Long> selectByCompanyName(String companyName);

    /**
     * 批量查询
     *
     * @param tenantIds
     * @return
     */
    List<BusinessInformationResultResp> batchQueryByTenantIds(List<Long> tenantIds);
}

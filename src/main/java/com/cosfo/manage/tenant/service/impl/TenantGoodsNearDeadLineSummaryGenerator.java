package com.cosfo.manage.tenant.service.impl;


import cn.hutool.core.lang.Pair;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.Global;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import com.cosfo.manage.market.repository.MarketItemCompositeGoodsRepository;
import com.cosfo.manage.market.service.MarketItemCompositeMarketService;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.report.model.dto.GoodsNearDeadlineSummaryExcelDTO;
import com.cosfo.manage.report.model.po.GoodsNearDeadlineSummary;
import com.cosfo.manage.report.repository.GoodsNearDeadlineSummaryRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TenantGoodsNearDeadLineSummaryGenerator implements ExcelGenerator<Void> {

    @Resource
    private CommonService commonService;
    @Resource
    private ProductFacade productFacade;
    @Resource
    private MarketItemCompositeMarketService marketItemCompositeMarketService;
    @Resource
    private GoodsNearDeadlineSummaryRepository goodsNearDeadlineSummaryRepository;
    @Resource
    private MarketItemCompositeGoodsRepository marketItemCompositeGoodsRepository;

    @Override
    public String getExcelName(Void query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("近15天临期货品截至%s.xlsx", yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, Void query) {
        String timeTag = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<GoodsNearDeadlineSummaryExcelDTO> goodsNearDeadlineSummaries = queryGoodsDeadLineSummary(tenantId, timeTag);
        return commonService.exportExcel(goodsNearDeadlineSummaries, ExcelTypeEnum.GOODS_NEAR_DEADLINE_SUMMARY.getName());
    }

    private List<GoodsNearDeadlineSummaryExcelDTO> queryGoodsDeadLineSummary(Long tenantId, String timeTag) {
        List<GoodsNearDeadlineSummary> goodsNearDeadlineSummaries = goodsNearDeadlineSummaryRepository.listByTenantAndTimeTag(tenantId, timeTag);
        if (CollectionUtils.isEmpty(goodsNearDeadlineSummaries)) {
            return Collections.emptyList();
        }
        // 商品货品id
        Pair<List<Long>, List<Long>> idPairs = getIds(goodsNearDeadlineSummaries);
        Pair<Map<Long, MarketItemCompositeMarket>, Map<Long, ProductSkuDTO>> itemSkuMapPair = marketItemCompositeGoodsRepository.queryByIds(tenantId, idPairs.getKey(), idPairs.getValue());
        Map<Long, ProductSkuDTO> skuInfoMap = itemSkuMapPair.getValue();
        Map<Long, MarketItemCompositeMarket> marketInfoMap = itemSkuMapPair.getKey();

        ProductSkuDTO emptySku = new ProductSkuDTO();
        MarketItemCompositeMarket emptyMarket = new MarketItemCompositeMarket();
        return goodsNearDeadlineSummaries.stream().map(item -> {
            ProductSkuDTO productSkuDTO = skuInfoMap.getOrDefault(item.getSkuId(), emptySku);
            MarketItemCompositeMarket marketItem = marketInfoMap.getOrDefault(item.getItemId(), emptyMarket);
            return GoodsNearDeadlineSummaryExcelDTO.builder()
                    .enterDeadlineDate(item.getEnterDeadlineDate())
                    .expirationDate(item.getExpirationDate())
                    .enterDeadlineBatchStock(item.getEnterDeadlineBatchStock())
                    .endingBatchStock(item.getEndingBatchStock())
                    .warehouseName(item.getWarehouseName())
                    .batch(item.getBatch())
                    .title(productSkuDTO.getTitle())
                    .skuId(item.getSkuId())
                    .specification(Global.subSpecification(productSkuDTO.getSpecification()))
                    .firstCategory(productSkuDTO.getFirstCategory())
                    .secondCategory(productSkuDTO.getSecondCategory())
                    .thirdCategory(productSkuDTO.getThirdCategory())
                    .itemId(marketItem.getItemId())
                    .itemTitle(marketItem.getTitle())
                    .itemSpecification(Global.subSpecification(marketItem.getSpecification()))
                    .salePrice(item.getSalePrice())
                    .firstClassificationName(marketItem.getFirstClassificationName())
                    .secondClassificationName(marketItem.getSecondClassificationName())
                    .build();
        }).collect(Collectors.toList());
    }

    private Pair<List<Long>, List<Long>> getIds(List<GoodsNearDeadlineSummary> goodsNearDeadlineSummaries) {
        List<Long> itemIds = goodsNearDeadlineSummaries.stream().map(GoodsNearDeadlineSummary::getItemId).distinct().collect(Collectors.toList());
        List<Long> skuIds = goodsNearDeadlineSummaries.stream().map(GoodsNearDeadlineSummary::getSkuId).distinct().collect(Collectors.toList());
        return Pair.of(itemIds, skuIds);
    }
}

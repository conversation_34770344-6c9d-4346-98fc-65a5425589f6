package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.dto.RoleDTO;
import com.cosfo.manage.tenant.model.dto.RoleQueryDTO;
import com.cosfo.manage.tenant.model.vo.RoleVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AuthRoleService {

    /**
     * 查询角色列表
     * 过滤 配送员角色
     * @param roleQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    PageInfo<RoleVO> queryRolePage(RoleQueryDTO roleQueryDTO, LoginContextInfoDTO loginContextInfoDTO);
    PageInfo<RoleVO> queryRolePageWithOutSupplierDistributorRoleId(RoleQueryDTO roleQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    Long querySupplierDistributorRoleId(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 新增角色
     * @param roleDTO
     * @return
     */
    List<Long> addAuthRole(RoleDTO roleDTO);

    /**
     * 更新角色
     * @param roleDTO
     * @return
     */
    List<Long> updateAuthRole(RoleDTO roleDTO);

    /**
     * 删除角色
     * @param roleDTO
     * @return
     */
    Boolean delAuthRole(RoleDTO roleDTO);

    /**
     * 获取角色详情
     *
     * @param roleId
     * @param loginContextInfoDTO
     * @return
     */
    RoleVO getAuthRole(Long roleId, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 判断当前账号是否包含该权限
     * @param accountId 账号id
     * @param roleId 权限id
     * @return
     */
    Boolean checkIsIncludeRole(Long accountId, Long roleId);

    /**
     * 判断是否供应商角色
     * @param tenantId
     * @param authUserId
     * @return
     */
    Boolean isSupplierRole(Long tenantId, Long authUserId);
}

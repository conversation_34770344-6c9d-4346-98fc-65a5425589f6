package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.input.TenantFundAccountConfigInsertInput;
import com.cosfo.manage.tenant.model.input.TenantFundAccountConfigUpdateInput;
import com.cosfo.manage.tenant.model.po.TenantFundAccountConfig;
import com.cosfo.manage.tenant.model.vo.TenantFundAccountConfigVO;

/**
 * <AUTHOR>
 * @Date 2025/4/21 10:55
 * @PackageName:com.cosfo.manage.tenant.service
 * @ClassName: TenantFundAccountConfigService
 * @Description: TODO
 * @Version 1.0
 */
public interface TenantFundAccountConfigService {
    /**
    * @description
    * @params [input, merchantInfoDTO]
    * @return java.lang.Boolean
    * <AUTHOR>
    * @date  2025/4/21 11:46
    */
    Boolean insertTenantFundAccountConfig(TenantFundAccountConfigInsertInput input, LoginContextInfoDTO merchantInfoDTO);

    /**
    * @description
    * @params [input, merchantInfoDTO]
    * @return java.lang.Boolean
    * <AUTHOR>
    * @date  2025/4/21 11:46
    */
    Boolean updateTenantFundAccountConfig(TenantFundAccountConfigUpdateInput input, LoginContextInfoDTO merchantInfoDTO);

    /**
    * @description
    * @params [merchantInfoDTO]
    * @return java.lang.Object
    * <AUTHOR>
    * @date  2025/4/21 11:46
    */
    TenantFundAccountConfigVO getInfoTenantFundAccountConfig(LoginContextInfoDTO merchantInfoDTO);
    TenantFundAccountConfigVO getByTenantId(Long tenantId);
}

package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.context.TenantFundAccountConfigEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.tenant.convert.TenantFundAccountConvert;
import com.cosfo.manage.tenant.mapper.TenantFundAccountConfigMapper;
import com.cosfo.manage.tenant.model.input.TenantFundAccountConfigInsertInput;
import com.cosfo.manage.tenant.model.input.TenantFundAccountConfigUpdateInput;
import com.cosfo.manage.tenant.model.po.TenantFundAccountConfig;
import com.cosfo.manage.tenant.model.vo.TenantFundAccountConfigVO;
import com.cosfo.manage.tenant.service.TenantFundAccountConfigService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/21 10:55
 * @PackageName:com.cosfo.manage.tenant.service.impl
 * @ClassName: TenantFundAccountConfigServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Service
@Slf4j
public class TenantFundAccountConfigServiceImpl implements TenantFundAccountConfigService {

    @Resource
    private TenantFundAccountConfigMapper tenantFundAccountConfigMapper;

    @Override
    @XmLock(key = "TenantFundAccountConfigService.insertTenantFundAccountConfig:{merchantInfoDTO.tenantId}")
    public Boolean insertTenantFundAccountConfig(TenantFundAccountConfigInsertInput input, LoginContextInfoDTO merchantInfoDTO) {
        //每个租户目前只能新增一条记录
        TenantFundAccountConfig tenantFundAccountConfig = tenantFundAccountConfigMapper.getInfoByTenantId(merchantInfoDTO.getTenantId());
        AssertCheckParams.expectFalse(tenantFundAccountConfig != null, ResultStatusEnum.SERVER_ERROR.getStatus(), "每个租户只能新增一条非现金账户记录");

        tenantFundAccountConfig = TenantFundAccountConvert.INSTANCE.convertToTenantFundAccountConfig(input);
        tenantFundAccountConfig.setTenantId(merchantInfoDTO.getTenantId());
        if (Objects.isNull(tenantFundAccountConfig.getAllowShippingFee())) {
            tenantFundAccountConfig.setAllowShippingFee(TenantFundAccountConfigEnum.AllowShippingFeeEnum.YES.getCode());
        }
        if (Objects.isNull(tenantFundAccountConfig.getLimitGoodsGroup())) {
            tenantFundAccountConfig.setLimitGoodsGroup("[]");
        }
        tenantFundAccountConfig.setCreateTime(LocalDateTime.now());
        tenantFundAccountConfigMapper.insert(tenantFundAccountConfig);
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateTenantFundAccountConfig(TenantFundAccountConfigUpdateInput input, LoginContextInfoDTO merchantInfoDTO) {
        TenantFundAccountConfig tenantFundAccountConfig = tenantFundAccountConfigMapper.selectByPrimaryKey(input.getId());
        AssertCheckParams.expectFalse(tenantFundAccountConfig == null, ResultStatusEnum.SERVER_ERROR.getStatus(), "当前非现金账户记录不存在");

        TenantFundAccountConfig update = TenantFundAccountConvert.INSTANCE.convertToTenantFundAccountConfig(input);
        int i = tenantFundAccountConfigMapper.updateByPrimaryKeySelective(update);
        return i > 0;
    }

    @Override
    public TenantFundAccountConfigVO getInfoTenantFundAccountConfig(LoginContextInfoDTO merchantInfoDTO) {
       return getByTenantId (merchantInfoDTO.getTenantId ());
    }

    @Override
    public TenantFundAccountConfigVO getByTenantId(Long tenantId) {
        TenantFundAccountConfig tenantFundAccountConfig = tenantFundAccountConfigMapper.getInfoByTenantId (tenantId);
        if (tenantFundAccountConfig == null) {
            return null;
        }
        return TenantFundAccountConvert.INSTANCE.convertToTenantFundAccountConfigVO (tenantFundAccountConfig);
    }
}

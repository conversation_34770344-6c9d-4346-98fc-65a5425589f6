package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.context.FlowRuleAuditBizTypeEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.tenant.model.input.FlowSchemeInput;
import com.cosfo.manage.tenant.model.input.FlowSchemeUpdateInput;
import com.cosfo.manage.tenant.model.po.TenantFlowRuleAudit;
import com.cosfo.manage.tenant.model.vo.FlowSchemeDetailVO;
import com.cosfo.manage.tenant.model.vo.FlowSchemeStoreBelongVO;
import com.cosfo.manage.tenant.model.vo.FlowSchemeVO;

import java.util.List;
import java.util.Map;

public interface TenantFlowSchemeService {

    /**
     * 根据条件查询例外方案
     * @param flowSchemeInput
     * @return
     */
    List<FlowSchemeVO> listFlowScheme(FlowSchemeInput flowSchemeInput);

    /**
     * 查询方案详情
     * @param tenantId
     * @param schemeId
     * @return
     */
    FlowSchemeDetailVO schemeDetail(Long tenantId, Long schemeId);

    /**
     * 更新或保存方案
     * @param loginContextInfoDTO
     * @param flowSchemeUpdateInput
     */
    void updateScheme(LoginContextInfoDTO loginContextInfoDTO, FlowSchemeUpdateInput flowSchemeUpdateInput);

    /**
     * 删除方案
     * @param tenantId
     * @param schemeId
     */
    void deleteScheme(Long tenantId, Long schemeId);

    /**
     * 查询门店归属的例外方案
     * @param tenantId
     * @param storeIds
     * @return
     */
    List<FlowSchemeStoreBelongVO> queryExtraSchemeByStoreIds(Long tenantId, List<Long> storeIds);

    /**
     * 查询门店在审核业务类型下的审核规则配置
     * @param storeId
     * @param bizTypeEnum
     * @return
     */
    TenantFlowRuleAudit getAuditByStoreIdAndBizType(Long storeId, FlowRuleAuditBizTypeEnum bizTypeEnum);

    /**
     * 查询门店在审核业务类型下的审核规则配置
     * @param storeIds
     * @param bizTypeEnum
     * @return
     */
    Map<Long, TenantFlowRuleAudit> getAuditMapByStoreIdsAndBizType(List<Long> storeIds, FlowRuleAuditBizTypeEnum bizTypeEnum);

    /**
     * 查询门店在审核业务类型下的审核规则配置 - 是否开启审核
     * @param storeId
     * @param bizTypeEnum
     * @return
     */
    boolean getAuditSwitchByStoreIdAndBizType(Long storeId, FlowRuleAuditBizTypeEnum bizTypeEnum);

    /**
     * 查询登录用户在审核目标【门店id】和审核业务类型下，是否拥有审核权限（超级管理员用户默认有审核权限）
     * @param accountId
     * @param storeId
     * @param bizTypeEnum
     * @return
     */
    boolean canAuditByAccountIdAndBizType(Long accountId, Long storeId, FlowRuleAuditBizTypeEnum bizTypeEnum);

    /**
     * 查询登录用户在审核目标【门店id】列表和审核业务类型下，是否拥有审核权限（超级管理员用户默认有审核权限）
     * @param accountId
     * @param storeIdList
     * @param bizTypeEnum
     * @return
     */
    Map<Long, Boolean> canAuditStoreIdsByAccountIdAndBizType(Long accountId, List<Long> storeIdList, FlowRuleAuditBizTypeEnum bizTypeEnum);


    /**
     * 初始化租户的流程设置默认方案（直营门店默认方案，加盟门店默认方案）
     * @param tenantId
     */
    void initDefaultFlowScheme(Long tenantId);
}

package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.dto.MarketItemNoSaleExportQueryDTO;

public interface TenantMeasureExportService {

    /**
     * 滞销商品导出
     */
    void exportMarketItemNoSale(MarketItemNoSaleExportQueryDTO query);

    /**
     * 商品售罄详情导出
     */
    void exportMarketItemSoldOut();

    /**
     * 门店采购活跃详情导出
     */
    void exportStorePurchaseActivity();

    /**
     * 履约率详情导出
     */
    void exportOrderFulfillmentRate();

    /**
     * 库存周转天数导出
     */
    void exportStockTurnoverDays();

    /**
     * 滞销货品导出
     */
    void exportNoSaleGoods();

    /**
     * 临期货品导出
     */
    void exportTempGoods();

    /**
     * 过期货品导出
     */
    void exportExpiredGoods();

    /**
     * 供应商到仓准时率导出
     */
    void exportSupplierArrivalRate();

    /**
     * 供应商到仓准确率导出
     */
    void exportSupplierArrivalAccuracy();
}

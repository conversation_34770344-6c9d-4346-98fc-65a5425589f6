package com.cosfo.manage.tenant.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.context.TenantGuideInfoEnum;
import com.cosfo.manage.common.context.TenantScoreStepEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.tenant.mapper.TenantGuideInfoMapper;
import com.cosfo.manage.tenant.model.dto.TenantGuideInfoItemDTO;
import com.cosfo.manage.tenant.model.input.TenantGuideInfoInput;
import com.cosfo.manage.tenant.model.input.TenantScoreInfoInput;
import com.cosfo.manage.tenant.model.po.TenantGuideInfo;
import com.cosfo.manage.tenant.model.vo.TenantGuideInfoVO;
import com.cosfo.manage.tenant.model.vo.TenantScoreInfoVO;
import com.cosfo.manage.tenant.service.TenantGuideInfoService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 租户登录引导信息问题 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
@Service
@Slf4j
public class TenantGuideInfoServiceImpl implements TenantGuideInfoService {

    @Resource
    private TenantGuideInfoMapper tenantGuideInfoMapper;

    @Override
    public List<TenantGuideInfoVO> queryGuideInfoDetail(String phone, String stepNum) {
        List<TenantGuideInfo> guideInfoList = tenantGuideInfoMapper.selectByPhone(phone);
        Map<String, TenantGuideInfo> guideInfoMap = guideInfoList.stream().collect(Collectors.toMap(e -> String.valueOf(e.getStepNum()), Function.identity(), (k1, k2) -> k2));

        List<TenantGuideInfoVO> resultList = new ArrayList<>();
        List<String> stepList = Arrays.asList("1", "2", "3");
        for (String step : stepList) {
            TenantGuideInfoVO tenantGuideInfoVO = new TenantGuideInfoVO();
            tenantGuideInfoVO.setCurrentStep(step);
            resultList.add(tenantGuideInfoVO);

            // 第三步 任务列表
            if("3".equals(step)){
                List<TenantGuideInfoEnum> enumList = TenantGuideInfoEnum.getGuideInfoEnumList(Integer.valueOf(step));

                List<TenantGuideInfoItemDTO> itemList = enumList.stream().map(e ->
                {
                    TenantGuideInfo existGuideInfo = guideInfoMap.get(String.valueOf(e.getSaveId()));

                    TenantGuideInfoItemDTO dto = new TenantGuideInfoItemDTO();
                    dto.setId(e.getId().toString());
                    dto.setText(e.getText());
                    dto.setSelected(e.isDefauleSelected());
                    if(existGuideInfo != null && !StringUtils.isEmpty(existGuideInfo.getSelectItemIds())) {
                        dto.setSelected(String.valueOf(e.getId()).equals(existGuideInfo.getSelectItemIds()));
                    }
                    return dto;
                }).collect(Collectors.toList());

                tenantGuideInfoVO.setItems(itemList);
            }else if("1".equals(step) || "2".equals(step)){
                TenantGuideInfo existGuideInfo = guideInfoMap.get(stepNum);

                List<String> selectedIdList = new ArrayList<>();
                if(existGuideInfo != null && !StringUtils.isEmpty(existGuideInfo.getSelectItemIds())){
                    selectedIdList = Arrays.asList(StringUtils.commaDelimitedListToStringArray(existGuideInfo.getSelectItemIds()));
                }

                List<String> finalList = selectedIdList;

                List<TenantGuideInfoEnum> enumList = TenantGuideInfoEnum.getGuideInfoEnumList(Integer.valueOf(step));
                List<TenantGuideInfoItemDTO> itemList = enumList.stream().map(e -> new TenantGuideInfoItemDTO(e.getId().toString(), e.getText(), finalList.contains(e.getId().toString()))).collect(Collectors.toList());
                tenantGuideInfoVO.setItems(itemList);
            }
        }

        return resultList;
    }

    @Override
    public void guideInfoReport(LoginContextInfoDTO loginContextInfoDTO, TenantGuideInfoInput tenantGuideInfoInput) {
        String stepNum = tenantGuideInfoInput.getStepNum();
        AssertCheckParams.notNull(stepNum, ResultDTOEnum.PARAMETER_MISSING.getCode(), "步骤不能为空");

        String phone = loginContextInfoDTO.getPhone();
        Long tenantId = loginContextInfoDTO.getTenantId();


        String reportStr = null;
        String selectItemIds = null;
        // 第三步 任务列表, stepNum保存每个任务的saveid
        if("3".equals(stepNum)){
            selectItemIds = tenantGuideInfoInput.getSelectItems().stream().collect(Collectors.joining(","));
            TenantGuideInfoEnum tenantGuideInfoEnum = TenantGuideInfoEnum.getGuideInfo(selectItemIds);
            if(tenantGuideInfoEnum != null){
                reportStr = tenantGuideInfoEnum.getText();
                stepNum = tenantGuideInfoEnum.getSaveId().toString();
            }
        }else if("1".equals(stepNum) || "2".equals(stepNum)){
            AssertCheckParams.notEmpty(tenantGuideInfoInput.getSelectItems(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "选项不能为空");

            reportStr = JSONObject.toJSONString(tenantGuideInfoInput.getSelectItems().stream().map(e->TenantGuideInfoEnum.getGuideInfoById(e, true)).collect(Collectors.toList()));
            selectItemIds = tenantGuideInfoInput.getSelectItems().stream().collect(Collectors.joining(","));
        }

        List<TenantGuideInfo> guideInfoList = tenantGuideInfoMapper.selectByPhone(phone);
        Map<String, TenantGuideInfo> guideInfoMap = guideInfoList.stream().collect(Collectors.toMap(e -> String.valueOf(e.getStepNum()), Function.identity(), (k1, k2) -> k2));
        TenantGuideInfo existGuideInfo = guideInfoMap.get(stepNum);
        // 新增记录
        if(existGuideInfo == null){
            TenantGuideInfo guideInfo = new TenantGuideInfo();
            guideInfo.setTenantId(tenantId);
            guideInfo.setPhone(phone);
            guideInfo.setStepNum(Integer.valueOf(stepNum));
            guideInfo.setTitle(tenantGuideInfoInput.getTitle());
            guideInfo.setSelectItemIds(selectItemIds);
            guideInfo.setSelectItem(reportStr);

            tenantGuideInfoMapper.insert(guideInfo);
        }else{
            // 修改记录
            TenantGuideInfo updateObj = new TenantGuideInfo();
            updateObj.setId(existGuideInfo.getId());
            updateObj.setTenantId(tenantId);
            updateObj.setSelectItemIds(selectItemIds);
            updateObj.setSelectItem(reportStr);
            updateObj.setUpdateTime(new Date());

            tenantGuideInfoMapper.updateByPrimaryKeySelective(updateObj);
        }
    }

    @Override
    public TenantGuideInfoVO queryGuideInfoStatus(String phone) {
        TenantGuideInfoVO tenantGuideInfoVO = new TenantGuideInfoVO();
        // 0-未填写，1-填写步骤1 2-步骤2填写 3-步骤3填写
        tenantGuideInfoVO.setCurrentStep("0");
        List<TenantGuideInfo> guideInfoList = tenantGuideInfoMapper.selectByPhone(phone);
        if(!CollectionUtils.isEmpty(guideInfoList)) {
            guideInfoList.stream()
                .max(Comparator.comparing(TenantGuideInfo::getStepNum))
                .ifPresent(maxGuideInfo -> tenantGuideInfoVO.setCurrentStep(Math.min(maxGuideInfo.getStepNum(), 2) + ""));
        }
        return tenantGuideInfoVO;
    }

    @Override
    public TenantScoreInfoVO scoreDetail(String phone) {
        Integer scoreStep = TenantScoreStepEnum.SCORE_STEP;
        TenantGuideInfo existGuideInfo = tenantGuideInfoMapper.selectByPhoneAndStepNum(phone, scoreStep);
        Integer stepNum = TenantScoreStepEnum.WAIT_SCORE.getStep();
        List<String> selectedIdList = Collections.EMPTY_LIST;
        if (existGuideInfo != null && !StringUtils.isEmpty(existGuideInfo.getSelectItemIds())) {
            selectedIdList = Arrays.asList(StringUtils.commaDelimitedListToStringArray(existGuideInfo.getSelectItemIds()));
            stepNum = TenantScoreStepEnum.WAIT_FEEDBACK.getStep();
        }

        TenantScoreInfoVO tenantScoreInfoVO = new TenantScoreInfoVO();
        List<TenantGuideInfoEnum> enumList = TenantGuideInfoEnum.getGuideInfoEnumList(scoreStep);
        List<String> finalList = selectedIdList;
        List<TenantGuideInfoItemDTO> itemList = enumList.stream().map(e -> new TenantGuideInfoItemDTO(e.getId().toString(), e.getText(), finalList.contains(e.getId().toString()))).collect(Collectors.toList());
        tenantScoreInfoVO.setItems(itemList);
        String feeback = Optional.ofNullable(existGuideInfo).map(TenantGuideInfo::getSelectItem).orElse(null);
        // 改进建议
        if (!StringUtils.isEmpty(feeback)) {
            stepNum = TenantScoreStepEnum.SCORE_SUCCESS.getStep();
            tenantScoreInfoVO.setFeedback(feeback);
        }
        tenantScoreInfoVO.setStepNum(stepNum);
        return tenantScoreInfoVO;
    }

    @Override
    public void upsertScore(LoginContextInfoDTO loginContextInfoDTO, TenantScoreInfoInput tenantScoreInfoInput) {
        Integer stepNum = tenantScoreInfoInput.getStepNum();
        AssertCheckParams.notNull(stepNum, ResultDTOEnum.PARAMETER_MISSING.getCode(), "步骤不能为空");
        String phone = loginContextInfoDTO.getPhone();
        Long tenantId = loginContextInfoDTO.getTenantId();

        String reportStr = null;
        String selectItemIds = null;

        if (TenantScoreStepEnum.WAIT_SCORE.getStep().equals(stepNum)) {
            AssertCheckParams.notEmpty(tenantScoreInfoInput.getSelectItems(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "选项不能为空");
            selectItemIds = tenantScoreInfoInput.getSelectItems().stream().collect(Collectors.joining(","));
        } else if (TenantScoreStepEnum.WAIT_FEEDBACK.getStep().equals(stepNum)) {
            AssertCheckParams.notNull(tenantScoreInfoInput.getFeedback(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "意见反馈不能为空");
            reportStr = tenantScoreInfoInput.getFeedback();
        }

        Integer scoreStep = TenantScoreStepEnum.SCORE_STEP;
        TenantGuideInfo existGuideInfo = tenantGuideInfoMapper.selectByPhoneAndStepNum(phone,scoreStep);
        // 新增记录
        if (Objects.isNull(existGuideInfo)) {
            TenantGuideInfo guideInfo = new TenantGuideInfo();
            guideInfo.setTenantId(tenantId);
            guideInfo.setPhone(phone);
            guideInfo.setStepNum(TenantScoreStepEnum.SCORE_STEP);
            guideInfo.setTitle(tenantScoreInfoInput.getTitle());
            guideInfo.setSelectItemIds(selectItemIds);
            tenantGuideInfoMapper.insert(guideInfo);
        } else {
            // 修改记录
            TenantGuideInfo updateObj = new TenantGuideInfo();
            updateObj.setId(existGuideInfo.getId());
            updateObj.setTenantId(tenantId);
            updateObj.setSelectItemIds(selectItemIds);
            updateObj.setSelectItem(reportStr);
            updateObj.setUpdateTime(new Date());
            tenantGuideInfoMapper.updateByPrimaryKeySelective(updateObj);
        }
    }
}

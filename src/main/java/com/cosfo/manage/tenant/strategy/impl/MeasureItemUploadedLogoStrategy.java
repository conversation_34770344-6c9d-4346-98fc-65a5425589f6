package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@Service
@Slf4j
public class MeasureItemUploadedLogoStrategy implements TenantMeasureItemStrategy {

    @Resource
    private UserCenterMerchantInfoFacade merchantInfoFacade;

    @Override
    public void doMeasureItem(TenantMeasureItemResult tenantMeasureItemResult) {
        Long tenantId = tenantMeasureItemResult.getTenantId();
        MerchantResultResp merchantResultResp = merchantInfoFacade.getMerchantByTenantId(tenantId);
        if (Objects.isNull(merchantResultResp)) {
            throw new ProviderException("查询商户信息失败");
        }
        boolean noUploadLogoFlag = Objects.isNull(merchantResultResp.getLogoImage());
        tenantMeasureItemResult.setItemResult(tenantMeasureItemResult.getItemTitle());
        tenantMeasureItemResult.setItemResultState(noUploadLogoFlag ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
    }
}

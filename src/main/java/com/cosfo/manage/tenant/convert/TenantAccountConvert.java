package com.cosfo.manage.tenant.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.vo.TenantAccountVO;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/28
 */
public class TenantAccountConvert {

    /**
     * 转化为tenantAccount
     *
     * @param tenantAccount
     * @return
     */
    public static TenantAccountVO convertToTenantAccountVO(TenantAccount tenantAccount){
        if (tenantAccount == null) {
            return null;
        }

        TenantAccountVO tenantAccountVO = new TenantAccountVO();
        tenantAccountVO.setId(tenantAccount.getId());
        tenantAccountVO.setPhone(tenantAccount.getPhone());
        tenantAccountVO.setNickname(tenantAccount.getNickname());
        tenantAccountVO.setProfilePicture(tenantAccount.getProfilePicture());
        tenantAccountVO.setStatus(tenantAccount.getStatus());
        tenantAccountVO.setOperatorPhone(tenantAccount.getOperatorPhone());
        tenantAccountVO.setOperatorTime(tenantAccount.getOperatorTime());
        tenantAccountVO.setAuthUserId(tenantAccount.getAuthUserId());
        tenantAccountVO.setEmail(tenantAccount.getEmail());
        tenantAccountVO.setUpdater(tenantAccount.getUpdater());
        return tenantAccountVO;
    }

    public static List<TenantAccountVO> convertToTenantAccountVOList(List<TenantAccountResultResp> respList) {
        if(CollectionUtil.isEmpty (respList)){
            return Collections.emptyList ();
        }
        return respList.stream().map (e-> convertToTenantAccountVOByResp(e)).collect(Collectors.toList());
    }

    private static TenantAccountVO convertToTenantAccountVOByResp(TenantAccountResultResp e) {
        TenantAccountVO tenantAccountVO = new TenantAccountVO ();
        tenantAccountVO.setId(e.getId ());
        tenantAccountVO.setPhone(e.getPhone ());
        tenantAccountVO.setTenantId(e.getTenantId ());
        tenantAccountVO.setNickname(e.getNickname ());
        tenantAccountVO.setProfilePicture(e.getProfilePicture ());
        tenantAccountVO.setStatus(e.getStatus ());
        tenantAccountVO.setOperatorTime(e.getOperatorTime ());
        tenantAccountVO.setOperatorPhone(e.getOperatorPhone ());
        tenantAccountVO.setAuthUserId(e.getAuthUserId ());
        tenantAccountVO.setEmail(e.getEmail ());
        return tenantAccountVO;
    }
}

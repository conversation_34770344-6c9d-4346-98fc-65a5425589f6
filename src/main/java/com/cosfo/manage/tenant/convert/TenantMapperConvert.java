package com.cosfo.manage.tenant.convert;

import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.model.vo.SuperAccountTenantVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface TenantMapperConvert {

    TenantMapperConvert INSTANCE = Mappers.getMapper(TenantMapperConvert.class);

    /**
     * 类转换
     * @param tenantResultResp
     * @return
     */
    @Mapping(target = "createTime", expression = "java(com.cosfo.common.util.TimeUtils.localDateTimeConvertDate(tenantResultResp.getCreateTime()))")
    @Mapping(target = "updateTime", expression = "java(com.cosfo.common.util.TimeUtils.localDateTimeConvertDate(tenantResultResp.getUpdateTime()))")
    TenantDTO respToTenantDto(TenantResultResp tenantResultResp);

    @Mapping(target = "id", source = "tenantId")
    TenantDTO respToTenantDto(TenantAndBusinessInfoResultResp tenantResultResp);

    List<TenantDTO> respToTenantDtoList(List<TenantAndBusinessInfoResultResp> tenantResultRespList);

    PageInfo<TenantDTO> respToTenantDtoPageInfo(PageInfo<TenantAndBusinessInfoResultResp> tenantResultRespPageInfo);

    PageInfo<SuperAccountTenantVO> tenantDtoToSuperAccountTenantVOPageInfo(PageInfo<TenantDTO> tenantDTOPageInfo, @Context Map<Long, String> merchantNameMap);

    SuperAccountTenantVO tenantDtoToSuperAccountTenantVO(TenantDTO tenantDTO, @Context Map<Long, String> merchantNameMap);

    @AfterMapping
    default void afterMapping(@MappingTarget SuperAccountTenantVO accountTenantVO, @Context Map<Long, String> merchantNameMap) {
        accountTenantVO.setMerchantName(merchantNameMap.get(accountTenantVO.getId()));
    }

}

package com.cosfo.manage.tenant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.tenant.model.po.TenantMeasureReport;
import com.cosfo.manage.tenant.mapper.TenantMeasureReportMapper;
import com.cosfo.manage.tenant.repository.TenantMeasureReportRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 租户度量报告 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Service
public class TenantMeasureReportRepositoryImpl extends ServiceImpl<TenantMeasureReportMapper, TenantMeasureReport> implements TenantMeasureReportRepository {

    @Override
    public TenantMeasureReport queryLastReport(Long tenantId, String timeRange) {
        LambdaQueryWrapper<TenantMeasureReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantMeasureReport::getTenantId, tenantId);
        queryWrapper.ge(!StringUtils.isBlank(timeRange), TenantMeasureReport::getCreateTime, timeRange);
        queryWrapper.orderByDesc(TenantMeasureReport::getId);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }
}

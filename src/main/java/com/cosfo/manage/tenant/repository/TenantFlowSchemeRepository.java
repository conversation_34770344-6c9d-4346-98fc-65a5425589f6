package com.cosfo.manage.tenant.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.tenant.model.dto.FlowSchemeQueryParam;
import com.cosfo.manage.tenant.model.po.TenantFlowScheme;

import java.util.List;

/**
 * <p>
 * 租户流程方案表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface TenantFlowSchemeRepository extends IService<TenantFlowScheme> {

    List<TenantFlowScheme> selectByParam(FlowSchemeQueryParam param);

    /**
     * 查询是否有重复的方案名记录
     * @param tenantId
     * @param schemeName
     * @return
     */
    boolean existRepeatSchemeName(Long tenantId, String schemeName, Long schemeId);

    TenantFlowScheme selectById(Long tenantId, Long id);

    boolean deleteById(Long tenantId, Long id);

    /**
     * 清除指定例外方案里面的门店id
     * @param tenantId
     * @param id
     */
    void updateFlowSchemeExcludeStoreIds(Long tenantId, Long id, List<Long> storeIds);

}

package com.cosfo.manage.tenant.repository;

import com.cosfo.manage.tenant.model.po.TenantMeasureReport;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 租户度量报告 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
public interface TenantMeasureReportRepository extends IService<TenantMeasureReport> {

    /**
     * 查询时间范围内最新的报告
     *
     * @param tenantId
     * @param timeRange
     * @return
     */
    TenantMeasureReport queryLastReport(Long tenantId, String timeRange);
}

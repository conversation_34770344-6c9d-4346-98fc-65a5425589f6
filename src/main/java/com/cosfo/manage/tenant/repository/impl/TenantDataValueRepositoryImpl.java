package com.cosfo.manage.tenant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.tenant.mapper.TenantDataValueMapper;
import com.cosfo.manage.tenant.model.po.TenantDataValue;
import com.cosfo.manage.tenant.repository.TenantDataValueRepository;
import org.springframework.stereotype.Service;

@Service
public class TenantDataValueRepositoryImpl extends ServiceImpl<TenantDataValueMapper, TenantDataValue> implements TenantDataValueRepository {

    @Override
    public TenantDataValue selectByTenantId(Long tenantId) {
        LambdaQueryWrapper<TenantDataValue> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TenantDataValue::getTenantId, tenantId);

        return getOne(queryWrapper);
    }
}

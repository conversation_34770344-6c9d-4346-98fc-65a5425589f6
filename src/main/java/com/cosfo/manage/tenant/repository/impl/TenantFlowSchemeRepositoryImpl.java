package com.cosfo.manage.tenant.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.tenant.mapper.TenantFlowSchemeMapper;
import com.cosfo.manage.tenant.model.dto.FlowSchemeQueryParam;
import com.cosfo.manage.tenant.model.po.TenantFlowScheme;
import com.cosfo.manage.tenant.repository.TenantFlowRuleAuditRepository;
import com.cosfo.manage.tenant.repository.TenantFlowSchemeRepository;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 租户流程方案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Service
@Slf4j
public class TenantFlowSchemeRepositoryImpl extends ServiceImpl<TenantFlowSchemeMapper, TenantFlowScheme> implements TenantFlowSchemeRepository {

    @Resource
    private TenantFlowRuleAuditRepository tenantFlowRuleAuditRepository;

    @Override
    public List<TenantFlowScheme> selectByParam(FlowSchemeQueryParam param) {
        LambdaQueryWrapper<TenantFlowScheme> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantFlowScheme::getTenantId, param.getTenantId());
        queryWrapper.eq(param.getSchemeId() != null, TenantFlowScheme::getId, param.getSchemeId());
        queryWrapper.eq(param.getSchemeType() != null, TenantFlowScheme::getSchemeType, param.getSchemeType());
        queryWrapper.eq(param.getDefaultFlag() != null, TenantFlowScheme::getDefaultFlag, param.getDefaultFlag());
        queryWrapper.like(StringUtils.isNotBlank(param.getSchemeName()), TenantFlowScheme::getSchemeName, param.getSchemeName());
        return list(queryWrapper);
    }

    @Override
    public boolean existRepeatSchemeName(Long tenantId, String schemeName, Long schemeId) {
        LambdaQueryWrapper<TenantFlowScheme> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantFlowScheme::getTenantId, tenantId);
        queryWrapper.eq(TenantFlowScheme::getSchemeName, schemeName);
        queryWrapper.ne(schemeId != null, TenantFlowScheme::getId, schemeId);
        return getOne(queryWrapper) != null;
    }

    @Override
    public TenantFlowScheme selectById(Long tenantId, Long id) {
        LambdaQueryWrapper<TenantFlowScheme> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantFlowScheme::getTenantId, tenantId);
        queryWrapper.eq(TenantFlowScheme::getId, id);
        return getOne(queryWrapper);
    }

    @Override
    public boolean deleteById(Long tenantId, Long id) {
        return removeById(id) && tenantFlowRuleAuditRepository.deleteBySchemeId(tenantId, id);
    }

    @Override
    public void updateFlowSchemeExcludeStoreIds(Long tenantId, Long id, List<Long> storeIds) {
        if(CollectionUtils.isEmpty(storeIds)){
            return;
        }
        TenantFlowScheme tenantFlowScheme = selectById(tenantId, id);
        if(tenantFlowScheme == null){
            return;
        }

        Set<Long> updateStoreIdSet = Sets.newHashSet();
        try {
            List<Long> storeIdList = JSONObject.parseArray(tenantFlowScheme.getSchemeTargetInfo(), Long.class);
            if(CollectionUtils.isEmpty(storeIdList)) {
                return;
            }
            updateStoreIdSet = Sets.newLinkedHashSet(storeIdList);
            updateStoreIdSet.removeAll(storeIds);
        } catch (Exception ex) {
            log.error("解析流程方案指定门店错误, schemeTargetInfo={}", tenantFlowScheme.getSchemeTargetInfo(), ex);
        }

        LambdaUpdateWrapper<TenantFlowScheme> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(TenantFlowScheme::getSchemeTargetInfo, JSONObject.toJSONString(updateStoreIdSet));
        updateWrapper.eq(TenantFlowScheme::getId, id);
        updateWrapper.eq(TenantFlowScheme::getTenantId, tenantId);
        update(updateWrapper);
    }
}

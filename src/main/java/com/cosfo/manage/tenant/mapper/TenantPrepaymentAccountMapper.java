package com.cosfo.manage.tenant.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.tenant.model.po.TenantPrepaymentAccount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 预付账户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface TenantPrepaymentAccountMapper extends BaseMapper<TenantPrepaymentAccount> {

    /**
     * 获取租户下个类型账号余额总和
     * @param tenantId
     * @return
     */
    List<TenantPrepaymentAccount> calAccountSumGroupByPayTargetType(@Param("tenantId") Long tenantId);

}

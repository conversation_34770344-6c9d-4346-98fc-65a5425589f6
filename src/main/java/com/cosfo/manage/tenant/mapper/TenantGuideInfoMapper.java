package com.cosfo.manage.tenant.mapper;

import com.cosfo.manage.tenant.model.po.TenantGuideInfo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: xiaowk
 * @date: 2023/3/23 上午11:46
 */
@Mapper
public interface TenantGuideInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TenantGuideInfo record);

    int insertSelective(TenantGuideInfo record);

    TenantGuideInfo selectByPrimaryKey(Long id);

    List<TenantGuideInfo> selectByPhone(String phone);

    int updateByPrimaryKeySelective(TenantGuideInfo record);

    int updateByPrimaryKey(TenantGuideInfo record);

    TenantGuideInfo selectByPhoneAndStepNum(@Param("phone") String phone,@Param("stepNum") Integer stepNum);
}
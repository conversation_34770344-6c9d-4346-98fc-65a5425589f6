package com.cosfo.manage.tenant.mapper;

import com.cosfo.manage.tenant.model.po.TenantFundAccountConfig;
import org.apache.ibatis.annotations.Param;

/**
* @description: 租户资金账户配置表Mapper
* @author: George
* @date: 2025-04-21
**/
public interface TenantFundAccountConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TenantFundAccountConfig record);

    int insertSelective(TenantFundAccountConfig record);

    TenantFundAccountConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TenantFundAccountConfig record);

    int updateByPrimaryKey(TenantFundAccountConfig record);

    TenantFundAccountConfig getInfoByTenantId(@Param("tenantId") Long tenantId);
}
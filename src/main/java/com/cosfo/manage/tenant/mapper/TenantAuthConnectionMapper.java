package com.cosfo.manage.tenant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import com.cosfo.manage.wechat.model.dto.TenantTemplateDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TenantAuthConnectionMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(TenantAuthConnection record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(TenantAuthConnection record);

    /**
     * 查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    TenantAuthConnection selectByPrimaryKey(Long id);

    /**
     * 查询
     * @param huifuId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    TenantAuthConnection selectByHuiFuId(String huifuId);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(TenantAuthConnection record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(TenantAuthConnection record);

    /**
     * 根据appId查询
     * @param appId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    TenantAuthConnection selectByAppId(String appId);

    /**
     * 根据appIds查询所有租户id
     * @param appIds
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<TenantAuthConnection> selectAllTenants(@Param("appIds")List<String> appIds);

//    /**
//     * 选择待开发小程序用户列表
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<TenantTemplateDto> selectTenantInfos();

    /**
     * 根据租户id查询
     * @param tenantId
     * @return
     */
    TenantAuthConnection selectByTenantId(Long tenantId);

    /**
     * 根据appIds查询所有租户id
     * @param ids
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<TenantAuthConnection> selectTenantsByTenantIds(@Param("ids")List<Long> ids);
}

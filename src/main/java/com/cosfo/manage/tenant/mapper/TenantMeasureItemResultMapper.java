package com.cosfo.manage.tenant.mapper;

import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 度量项结果 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Mapper
public interface TenantMeasureItemResultMapper extends BaseMapper<TenantMeasureItemResult> {

    /**
     * 批量插入
     * @param entityList
     * @return
     */
    boolean saveBatch(Collection<TenantMeasureItemResult> entityList);

    /**
     * 根据条件查询
     * @param tenantId
     * @param reportId
     * @param itemType
     * @param itemResultState
     * @return
     */
    List<TenantMeasureItemResult> listByCondition(@Param("tenantId") Long tenantId, @Param("reportId") Long reportId, @Param("itemType") Integer itemType, @Param("itemResultState") String itemResultState);
}

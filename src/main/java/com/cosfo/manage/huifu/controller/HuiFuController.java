package com.cosfo.manage.huifu.controller;

import com.cosfo.manage.huifu.dto.HuiFuBasicDataRespDTO;
import com.cosfo.manage.huifu.service.HuiFuAccountService;
import com.cosfo.manage.huifu.vo.HuiFuBasicDataReqVO;
import com.cosfo.manage.huifu.vo.HuiFuBasicDataRespVO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 公共
 *
 * <AUTHOR>
 * @date 2022/9/28 11:58
 */
@RestController
@RequestMapping(value = "/hui-fu")
public class HuiFuController {

    @Resource
    private HuiFuAccountService huiFuAccountService;

    /**
     * 查询汇付商户详细信息
     * @param huiFuBasicDataReqVO
     * @return
     */
    @PostMapping("/basic-data")
    public CommonResult<HuiFuBasicDataRespVO> queryHuiFuBasicData(@RequestBody HuiFuBasicDataReqVO huiFuBasicDataReqVO) {
        String huiFuId = huiFuBasicDataReqVO.getHuifuId();
        HuiFuBasicDataRespDTO huiFuBasicDataRespDTO = huiFuAccountService.queryHuiFuBasicData(huiFuId);
        return CommonResult.ok(HuiFuBasicDataRespVO.builder().regName(huiFuBasicDataRespDTO.getRegName()).build());
    }

}

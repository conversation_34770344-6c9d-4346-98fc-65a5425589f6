package com.cosfo.manage.huifu.service.impl;

import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.config.HuiFuConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.HuiFuPaymentConstant;
import com.cosfo.manage.common.context.AccountTypeEnum;
import com.cosfo.manage.common.context.ProfitSharingSwitchEnum;
import com.cosfo.manage.common.util.Global;
import com.cosfo.manage.common.util.SignatureUtil;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.huifu.converter.Converter;
import com.cosfo.manage.huifu.dto.HuiFuBaseApiReqDTO;
import com.cosfo.manage.huifu.dto.HuiFuBasicDataReqDTO;
import com.cosfo.manage.huifu.dto.HuiFuBasicDataRespDTO;
import com.cosfo.manage.huifu.dto.HuiFuSupplierAccountDTO;
import com.cosfo.manage.huifu.model.po.HuiFuAccount;
import com.cosfo.manage.huifu.repository.HuiFuAccountRepository;
import com.cosfo.manage.huifu.service.HuiFuAccountService;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.cosfo.manage.wechat.api.PayMchAPI.huiFuBaseURI;

/**
 * @description:
 * @author: George
 * @date: 2024-01-04
 **/
@Service
public class HuiFuAccountServiceImpl implements HuiFuAccountService {

    @Resource
    private HuiFuAccountRepository huiFuAccountRepository;
    @Resource
    private HuiFuConfig huiFuConfig;

    @Override
    public List queryByAccountIds(Long tenantId, List<Long> accountIds, Integer accountType) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.EMPTY_LIST;
        }
        List<HuiFuAccount> huiFuAccounts = huiFuAccountRepository.queryByAccountIds(tenantId, accountIds, accountType);
        Map<Long, HuiFuAccount> huiFuAccountMap = huiFuAccounts.stream().collect(Collectors.toMap(HuiFuAccount::getAccountId, item -> item, (k1, k2) -> k1));
        HuiFuAccount defaultAccount = new HuiFuAccount();
        defaultAccount.setSharingSwitch(ProfitSharingSwitchEnum.SHUTDOWN.getCode());
        defaultAccount.setHuifuId(null);
        return accountIds.stream().map(supplierId -> {
            HuiFuAccount account = huiFuAccountMap.getOrDefault(supplierId, defaultAccount);
            account.setAccountId(supplierId);
            return Converter.toSupplierProfitSharingDTO(account);
        }).collect(Collectors.toList());
    }

    @Override
    public void saveOrUpdateSharingInfo(HuiFuSupplierAccountDTO huiFuSupplierAccountDTO) {
        if (huiFuSupplierAccountDTO == null) {
            throw new ParamsException("供应商分账信息不能为空");
        }
        Long accountId = huiFuSupplierAccountDTO.getSupplierId();
        if (accountId == null) {
            throw new ParamsException("供应商id不能为空");
        }
        Long tenantId = UserLoginContextUtils.getTenantId();
        List<HuiFuAccount> huiFuAccounts = huiFuAccountRepository.queryByAccountIds(tenantId, Collections.singletonList(accountId), AccountTypeEnum.SUPPLIER.getType());
        if (!CollectionUtils.isEmpty(huiFuAccounts)) {
            HuiFuAccount huiFuAccount = huiFuAccounts.get(0);
            huiFuAccount.setSharingSwitch(huiFuSupplierAccountDTO.getSharingSwitch());
            huiFuAccount.setHuifuId(huiFuSupplierAccountDTO.getHuifuId());
            huiFuAccount.setRegName(huiFuSupplierAccountDTO.getRegName());
            huiFuAccountRepository.updateById(huiFuAccount);
            return;
        }
        HuiFuAccount huiFuAccount = Converter.toHuiFuAccount(huiFuSupplierAccountDTO);
        Integer sharingSwitch = Optional.ofNullable(huiFuAccount.getSharingSwitch()).orElse(ProfitSharingSwitchEnum.SHUTDOWN.getCode());
        huiFuAccount.setTenantId(tenantId);
        huiFuAccount.setSharingSwitch(sharingSwitch);
        huiFuAccount.setAccountType(AccountTypeEnum.SUPPLIER.getType());
        huiFuAccount.setRegName(huiFuSupplierAccountDTO.getRegName());
        huiFuAccountRepository.save(huiFuAccount);
    }

    @Override
    public HuiFuBasicDataRespDTO queryHuiFuBasicData(String huiFuId) {
        String reqSeqId = Global.createHuiFuNo(Global.HUIFU_USER_CODE);
        HuiFuBasicDataReqDTO huiFuBasicDataReqDTO = new HuiFuBasicDataReqDTO();
        huiFuBasicDataReqDTO.setReqDate(TimeUtils.changeDate2String(new Date(), Constants.HUIFU_DATE));
        huiFuBasicDataReqDTO.setHuifuId(huiFuId);
        huiFuBasicDataReqDTO.setReqSeqId(reqSeqId);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuBasicDataReqDTO), huiFuConfig.getPrivateKey());
        HuiFuBaseApiReqDTO<HuiFuBasicDataReqDTO> huiFuBaseApiReqDTO = HuiFuBaseApiReqDTO.create(huiFuConfig.getSysId(), sign, huiFuConfig.getProductId(), huiFuBasicDataReqDTO);
        String resp = sendRequest4MerchantBasicData(huiFuBaseApiReqDTO);
        JSONObject jsonObject = JSONObject.parseObject(resp);
        HuiFuBasicDataRespDTO data = jsonObject.getObject("data", HuiFuBasicDataRespDTO.class);
        return data;
    }

    private String sendRequest4MerchantBasicData(HuiFuBaseApiReqDTO<HuiFuBasicDataReqDTO> huiFuBaseApiReqDTO) {
        String json = JSON.toJSONString(huiFuBaseApiReqDTO);
        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());
        HttpGlobalConfig.setTimeout(30000);
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + HuiFuPaymentConstant.MERCHANT_QUERY)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(json).execute();
        String body = response.body();
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new ProviderException("汇付商户信息查询失败");
        }
        return body;
    }

    @Override
    public List<Long> queryByRegName(Long tenantId, String regName) {
        List<HuiFuAccount> huiFuAccounts = huiFuAccountRepository.queryByRegName(tenantId, regName, AccountTypeEnum.SUPPLIER.getType());
        if (CollectionUtils.isEmpty(huiFuAccounts)) {
            return Collections.emptyList();
        }
        return huiFuAccounts.stream().map(HuiFuAccount::getAccountId).collect(Collectors.toList());
    }
}

package com.cosfo.manage.huifu.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-01-05
 **/
@Data
public class HuiFuBasicDataReqDTO {

    /**
     * 汇付商户号
     */
    @JSONField(name = "huifu_id")
    private String huifuId;

    /**
     * 请求流水号
     */
    @JSONField(name = "req_seq_id")
    private String reqSeqId;

    /**
     * 请求时间
     */
    @JSONField(name = "req_date")
    private String reqDate;
}

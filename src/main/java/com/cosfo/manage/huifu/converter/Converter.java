package com.cosfo.manage.huifu.converter;

import com.cosfo.manage.huifu.model.po.HuiFuAccount;
import com.cosfo.manage.huifu.dto.HuiFuSupplierAccountDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-01-04
 **/
public class Converter {
    public static List<HuiFuSupplierAccountDTO> convertToSupplierProfitSharingDtos(List<HuiFuAccount> huiFuAccounts) {
        if (huiFuAccounts == null) {
            return Collections.emptyList();
        }
        List<HuiFuSupplierAccountDTO> huiFuSupplierAccountDTOS = new ArrayList<>();
        for (HuiFuAccount huiFuAccount : huiFuAccounts) {
            huiFuSupplierAccountDTOS.add(toSupplierProfitSharingDTO(huiFuAccount));
        }
        return huiFuSupplierAccountDTOS;
    }

    public static HuiFuSupplierAccountDTO toSupplierProfitSharingDTO(HuiFuAccount huiFuAccount) {
        if (huiFuAccount == null) {
            return null;
        }
        HuiFuSupplierAccountDTO huiFuSupplierAccountDTO = new HuiFuSupplierAccountDTO();
        huiFuSupplierAccountDTO.setHuifuId(huiFuAccount.getHuifuId());
        huiFuSupplierAccountDTO.setSharingSwitch(huiFuAccount.getSharingSwitch());
        huiFuSupplierAccountDTO.setSupplierId(huiFuAccount.getAccountId());
        huiFuSupplierAccountDTO.setRegName(huiFuAccount.getRegName());
        return huiFuSupplierAccountDTO;
    }

    public static HuiFuAccount toHuiFuAccount(HuiFuSupplierAccountDTO huiFuSupplierAccountDTO) {
        if (huiFuSupplierAccountDTO == null) {
            return null;
        }
        HuiFuAccount huiFuAccount = new HuiFuAccount();
        huiFuAccount.setHuifuId(huiFuSupplierAccountDTO.getHuifuId());
        huiFuAccount.setSharingSwitch(huiFuSupplierAccountDTO.getSharingSwitch());
        huiFuAccount.setAccountId(huiFuSupplierAccountDTO.getSupplierId());
        return huiFuAccount;
    }
}

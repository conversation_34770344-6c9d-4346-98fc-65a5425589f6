package com.cosfo.manage.huifu.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.huifu.mapper.HuiFuAccountMapper;
import com.cosfo.manage.huifu.model.po.HuiFuAccount;
import com.cosfo.manage.huifu.repository.HuiFuAccountRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 汇付账户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Service
public class HuiFuAccountRepositoryImpl extends ServiceImpl<HuiFuAccountMapper, HuiFuAccount> implements HuiFuAccountRepository {

    @Override
    public List<HuiFuAccount> queryByAccountIds(Long tenantId, List<Long> accountIds, Integer accountType) {
        LambdaQueryWrapper<HuiFuAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HuiFuAccount::getAccountId, accountIds);
        queryWrapper.eq(HuiFuAccount::getAccountType, accountType);
        queryWrapper.eq(HuiFuAccount::getTenantId, tenantId);
        return list(queryWrapper);
    }

    @Override
    public List<HuiFuAccount> queryByRegName(Long tenantId, String regName, Integer type) {
        LambdaQueryWrapper<HuiFuAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HuiFuAccount::getRegName, regName);
        queryWrapper.eq(HuiFuAccount::getAccountType, type);
        queryWrapper.eq(HuiFuAccount::getTenantId, tenantId);
        return list(queryWrapper);
    }
}

package com.cosfo.manage.huifu.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.huifu.model.po.HuiFuAccount;

import java.util.List;

/**
 * <p>
 * 汇付账户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
public interface HuiFuAccountRepository extends IService<HuiFuAccount> {

    /**
     * 根据账号ids查询
     * @param accountIds
     * @return
     */
    List<HuiFuAccount> queryByAccountIds(Long tenantId, List<Long> accountIds, Integer accountType);

    /**
     * 根据名称查询
     * @param tenantId
     * @param regName
     * @param type
     * @return
     */
    List<HuiFuAccount> queryByRegName(Long tenantId, String regName, Integer type);
}

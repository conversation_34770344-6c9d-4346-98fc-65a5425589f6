package com.cosfo.manage.common.context;

import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-03-30
 * @Description: 定义redis的key枚举
 */
@Getter
public enum RedisKeyEnum {
    CM00001("修改运费分布式锁"),
    CM00002("订单ID配送功能分布式锁"),
    CM00003("openApi门店下发门店锁"),
    CM00004("订单退款通知供应商消息缓存"),
    CM00005("订待配送整点通知供应商消息缓存-配送员维度"),
    CM00006("订待每单待配送供应商消息缓存"),
    CM00007("支付单生成销量榜分布式锁"),
    CM00008("支付单生成销量榜幂等键"),
    CM00009("统计货品销量幂等锁"),
    CM00010("修改密码不再提示缓存键"),
    CM00011("发送短信验证码分布式锁"),
    CM00012("短信验证码发送标记"),
    CM00013("门店导入时间"),
    CM00014("代下单一键发送提醒"),
    CM00015("代下单下单失败发送提醒"),
    CM00016("租户是否开启进销存"),
    ;

    /**
     * 锁系统前缀
     */
    private static final String SPACE = "cosfo-manage";

    /**
     * 连接符
     */
    public static final String SEPARATOR = "_";

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    RedisKeyEnum(String desc) {

    }

}

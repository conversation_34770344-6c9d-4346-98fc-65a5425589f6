package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-11-09
 **/
@Getter
@AllArgsConstructor
public enum TenantMeasureStatusEnum {

    TO_BE_PROCESSED(1, "待处理"),

    PROCESSING(2, "处理中"),

    FINISHED(3, "已完成"),

    FAIL(4, "处理失败");

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String desc;
}

package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PosOrderTransOptTypeEnum {
    ORDER(0, "订单"),
    REFUND(1, "售后"),
    ;


    private Integer code;

    private String desc;

    public static PosOrderTransOptTypeEnum getByDesc(String desc) {
        for (PosOrderTransOptTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;
    }
}

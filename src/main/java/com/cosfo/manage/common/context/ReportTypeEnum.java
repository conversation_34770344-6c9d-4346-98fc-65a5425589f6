package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/13 21:26
 */
@Getter
@AllArgsConstructor
public enum ReportTypeEnum {

    /**
     * 商品销售数据导出
     */
    PRODUCT_DETAIL_SALES(3, "商品销售数据导出"),

    /**
     * 门店采购数据导出
     */
    MERCHANT_STORE_DETAIL_PURCHASE(4, "门店采购数据导出");

    /**
     * 类型
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;
}

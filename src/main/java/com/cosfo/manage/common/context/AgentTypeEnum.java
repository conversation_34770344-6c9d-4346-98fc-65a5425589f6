package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 代仓服务类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AgentTypeEnum {
    /**
     * 不申请代仓服务
     */
    NO(0,"不申请代仓服务","否"),


    /**
     * xianmu代仓
     */
    SUMMERFARM_AGENT(1,"申请代仓服务","是"),
    ;
    private Integer code;
    private String desc;
    private String value;

    public static AgentTypeEnum getByValue(String value){
        for(AgentTypeEnum agentType:AgentTypeEnum.values()){
            if(agentType.value.equals(value)){
                return agentType;
            }
        }

        return null;
    }
}

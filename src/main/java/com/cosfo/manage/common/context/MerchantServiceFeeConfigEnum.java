package com.cosfo.mall.common.context;

import com.cosfo.ordercenter.client.common.PayTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-08-29
 **/
public class MerchantServiceFeeConfigEnum {

    @Getter
    @AllArgsConstructor
    public enum PayType {
        /**
         * 微信
         */
        WECHAT("wechat", "微信"),
        /**
         * 支付宝
         */
        ALIPAY("alipay", "支付宝");

        private final String type;
        private final String desc;

    }

    @Getter
    @AllArgsConstructor
    public enum StatusEnum {
        /**
         * 启用
         */
        ENABLE(1, "启用"),
        /**
         * 关闭
         */
        DISABLE(0, "关闭");

        private final Integer status;
        private final String desc;
    }
}

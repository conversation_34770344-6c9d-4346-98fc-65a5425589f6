package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 出库类型
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/14
 */
@Getter
@AllArgsConstructor
public enum OutOfStockTypeEnum {
    STORE_ALLOCATION_OUT(50, "调拨出库"),
    SALE_OUT(51, "销售出库"),
    OWN_SALE_OUT_TASK(58, "自提销售出库"),
    DAMAGE_OUT(53, "货损出库"),
    STOCK_TAKING_OUT(54, "盘亏出库"),
    TRANSFER_OUT(55, "转换出库"),
    PURCHASES_BACK(56, "采购退货出库"),
    SUPPLY_AGAIN_TASK(57, "补货出库"),
    ;

    Integer id;
    String name;

    public static String getNameById(int id) {
        for (OutOfStockTypeEnum storeRecordType : OutOfStockTypeEnum.values()) {
            if (storeRecordType.getId() == id) {
                return storeRecordType.getName();
            }
        }
        return "";
    }
}

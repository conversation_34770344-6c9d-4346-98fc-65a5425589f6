package com.cosfo.manage.common.context.profitsharing;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分账金额类型分账金额类型
 */
@Getter
@AllArgsConstructor
public enum ProfitSharingRuleTypeEnum {

    ERROR(-1, "错误"),
    PROPRIETARY_SKU(1, "自营商品金额"),
    SUPPLY_SKU(2, "供应商商品金额"),
    DELIVERY(3, "运费"),
    SERVICE_CHARGE(4, "订单手续费"),
    ;
    /**
     * 编码
     */
    private Integer code;
    /**
     * 描述
     */
    private String desc;

    public static ProfitSharingRuleTypeEnum getByCode(Integer code){
        for(ProfitSharingRuleTypeEnum profitSharingRuleTypeEnum: ProfitSharingRuleTypeEnum.values()){
            if (profitSharingRuleTypeEnum.code.equals(code)) {
                return profitSharingRuleTypeEnum;
            }
        }
        return ERROR;
    }
}

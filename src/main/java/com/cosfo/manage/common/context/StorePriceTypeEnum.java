package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/15
 */
@Getter
@AllArgsConstructor
public enum StorePriceTypeEnum {
    /**
     * 统一价
     */
    ALL(0, "统一价"),
    /**
     * 其他价
     */
    OTHER(1,"其他价");

    /**
     * 类型编码
     */
    private Integer code;
    /**
     * 类型描述
     */
    private String desc;
}

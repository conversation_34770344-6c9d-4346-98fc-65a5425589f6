package com.cosfo.manage.common.context.usercenter;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-06-07
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum MerchantAddressStatusEnum {
    // 1-正常或审核通过、2-删除、3-待审核、4-审核不通过
    NORMAL(1, "正常或审核通过"),
    DELETED(2, "删除"),
    WAIT(3, "待审核"),
    REFUSE(4, "审核不通过"),
    ;
    private Integer status;
    private String desc;
}

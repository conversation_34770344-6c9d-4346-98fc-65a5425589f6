package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-05-16
 * @Description: 通用是否枚举
 */
@Getter
@AllArgsConstructor
public enum CommonCodeEnum {

    NO(0,"否"),
    YES(1,"是"),
    ;

    /**
     * 门店类型编码
     */
    private Integer code;
    /**
     * 门店类型描述
     */
    private String desc;


    public static String getDesc(Integer code) {
        for (CommonCodeEnum commonCodeEnum : CommonCodeEnum.values()) {
            if (commonCodeEnum.code.equals(code)) {
                return commonCodeEnum.desc;
            }
        }
        return null;
    }

}

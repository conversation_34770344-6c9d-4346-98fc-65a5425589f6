package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DocCodeTargetTypeEnum {

    STORE(1, "门店"),
    MARKET_ITEM(2, "商品"),
    ;


    private Integer code;

    private String desc;

    public static DocCodeTargetTypeEnum getByDesc(String desc) {
        for (DocCodeTargetTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;  // 或者可以选择抛出异常
    }
}

package com.cosfo.manage.common.context.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-04-12
 * @Description:配送按钮类型
 */
@Getter
@AllArgsConstructor
public enum DeliveryButtonStatusEnum {
    NO_SHOW(0, "无需展示"),
    GO_DELIVERY(1, "立即配送"),
    CONTINUE_DELIVERY(2, "继续配送"),
    DELIVERY_FINISH(3, "配送完成"),
    ;

    /**
     * 类型编码
     */
    private Integer type;
    /**
     * 类型描述
     */
    private String desc;

}

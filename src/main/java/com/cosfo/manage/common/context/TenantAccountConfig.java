package com.cosfo.manage.common.context;

import com.cosfo.ordercenter.client.resp.OrderDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-10-17
 * @Description:
 */

public interface TenantAccountConfig {

    @Getter
    @AllArgsConstructor
    enum BussinessMsgTypeEnum {

        WAIT_DELIVERY(1,"付款后消息提醒"),
        REFUND(2,"退款后消息提醒"),
        WAIT_DELIVERY_TOTAL(3,"待发货消息汇总提醒"),
        ;

        private Integer code;
        private String desc;

        public static List<Integer> listAllBussinessType() {
            return Arrays.stream (TenantAccountConfig.BussinessMsgTypeEnum.values ()).map (TenantAccountConfig.BussinessMsgTypeEnum::getCode).collect (Collectors.toList ());
        }
    }

    @Getter
    @AllArgsConstructor
    enum AvailableStatusEnum {

        YES(0,"可接收"),
        NO(1,"不可接收"),
        ;

        private Integer code;
        private String desc;

    }
}

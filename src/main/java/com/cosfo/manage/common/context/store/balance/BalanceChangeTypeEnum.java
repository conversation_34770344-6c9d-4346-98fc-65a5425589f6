package com.cosfo.manage.common.context.store.balance;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date
 */
@Getter
@AllArgsConstructor
public enum BalanceChangeTypeEnum {

    /**
     * 余额变动类型 0、预付
     */
    PRE_PAY(0, "预付"),

    /**
     * 余额变动类型 1、消费
     */
    CONSUME(1, "消费"),

    /**
     * 余额变动类型 2、消费退款
     */
    CONSUME_REFUND(2, "消费退款"),

    /**
     * 余额变动类型 3、扣减
     */
    DEDUCTION(3, "扣减"),

    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    public static BalanceChangeTypeEnum getByType(Integer type){
        for(BalanceChangeTypeEnum balanceAuthorityEnum: BalanceChangeTypeEnum.values()){
            if(balanceAuthorityEnum.getType().equals(type)){
                return balanceAuthorityEnum;
            }
        }
        return null;
    }
}

package com.cosfo.manage.common.context.binlog;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> ct
 * create at:  2022/10/21  16:53
 */
@Getter
@AllArgsConstructor
public enum BinlogEventEnum {

    /**
     * 新增
     */
    INSERT("INSERT", "新增"),
    /**
     * 修改
     */
    UPDATE("UPDATE", "修改"),
    /**
     * 删除
     */
    DELETE("DELETE", "删除");

    /**
     * 事件
     */
    private String event;
    /**
     * 事件描述
     */
    private String eventDesc;
}
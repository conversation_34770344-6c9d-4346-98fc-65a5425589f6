package com.cosfo.manage.common.context.profitsharing;

import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-01-05
 **/
@Getter
@AllArgsConstructor
public enum ProfitSharingDeliveryTypeEnums {

    /**
     * 品牌方配送
     */
    BRAND_DELIVERY(0, "自营"),

    /**
     * 三方配送
     */
    THIRD_DELIVERY(1, "三方"),

    /**
     * 无仓
     */
    NO_WAREHOUSE(2, "无仓"),
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    /**
     * 根据仓类型获取配送类型
     *
     * @param warehouseType
     * @return
     */
    public static Integer getByWarehouseType(Integer warehouseType) {
        WarehouseTypeEnum warehouseTypeEnum = WarehouseTypeEnum.getByCode(warehouseType);
        switch (warehouseTypeEnum) {
            case PROPRIETARY:
                return NO_WAREHOUSE.getType();
            case THREE_PARTIES:
                return THIRD_DELIVERY.getType();
            case SELF_SUPPLY:
                return BRAND_DELIVERY.getType();
        }
        return null;
    }

    public static ProfitSharingDeliveryTypeEnums getByDeliveryType(Integer profitSharingDeliveryType) {
        if (profitSharingDeliveryType == null) {
            return null;
        }
        for (ProfitSharingDeliveryTypeEnums value : values()) {
            if (value.getType().equals(profitSharingDeliveryType)) {
                return value;
            }
        }
        return null;
    }
}

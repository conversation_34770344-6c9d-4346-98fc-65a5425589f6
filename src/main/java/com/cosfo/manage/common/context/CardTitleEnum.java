package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-10-26
 **/
@Getter
@AllArgsConstructor
public enum CardTitleEnum {


    PAYMENT_AMOUNT("支付金额"),
    PAYMENT_STORE_COUNTS("支付门店数"),
    STORE_UNIT_AMOUNT("店单价"),
    REFUND_AMOUNT("成功退款金额"),
    ON_SALE_ITEM_COUNTS("在售商品数"),
    ITEM_SOLD_FREELY_COUNTS("动销商品数"),
    ITEM_SOLD_FREELY_RATE("商品动销率"),
    PAYMENT_COUNTS("付款件数"),
    ;



    private String title;
}

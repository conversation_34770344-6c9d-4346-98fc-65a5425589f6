package com.cosfo.manage.common.context;

import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/25
 */
@Getter
public enum ExcelTemplateType {
    /**
     * 门店分组导入模板
     */
    MERCHANT_GROUP(0,"file/门店分组导入模板.xlsx", "门店分组导入模板"),

    IMPORT_AGENT_PRODUCT(1,"file/导入自营货品模板.xlsx","导入自营货品模板", "file/dev/导入自营货品模板.xlsx"),

    IMPORT_PRICE_STRATEGY(2,"file/商品销售价导入标准模版.xlsx","商品销售价导入标准模版", "file/dev/商品销售价导入标准模版.xlsx"),

    IMPORT_ONSALE_STRATEGY(3,"file/商品按编码上下架导入模版.xlsx","商品按编码上下架导入模版", "file/dev/商品按编码上下架导入模版.xlsx"),

    IMPORT_POS_ORDER(4,"file/门店交易导入模板.xlsx","门店交易导入模板", "file/dev/门店交易导入模板.xlsx"),
    IMPORT_POS_ITEM(5,"file/POS物品导入标准模版.xlsx","POS物品导入标准模版", "file/dev/POS物品导入标准模版.xlsx"),
    IMPORT_POS_STORE(6,"file/POS门店导入标准模板.xlsx","POS门店导入标准模板", "file/dev/POS门店导入标准模板.xlsx"),
    IMPORT_POS_BOM(7,"file/POS成本卡bom标准模板.xlsx","POS成本卡bom标准模板", "file/dev/POS成本卡bom标准模板.xlsx"),
    IMPORT_NO_GOODS_SUPPLY_PRICE(8,"file/供应价导入模板.xlsx","供应价导入模板", "file/dev/供应价导入模板.xlsx"),

    IMPORT_BOSS_STORE_FULFILLMENT(9,"file/批量更新门店履约模版.xlsx","批量更新门店履约模版", "file/dev/批量更新门店履约模版-20231228.xlsx"),

    IMPORT_ORDER_DELIVERY(10, "file/批量发货订单模板.xlsx", "批量发货订单模板", "file/批量发货订单模板.xlsx"),

    IMPORT_ORDER_DELIVERY_SUPPLIER(11, "file/供应商批量发货订单模板.xlsx", "供应商批量发货订单模板", "file/供应商批量发货订单模板.xlsx"),
    ;

    private Integer order;
    private String url;
    private String desc;
    /**
     * 开发环境模板链接，如果开发和线上url不一样时使用
     */
    private String devUrl;


    ExcelTemplateType(Integer order, String url, String desc) {
        this.order = order;
        this.url = url;
        this.desc = desc;
        this.devUrl = url;
    }

    ExcelTemplateType(Integer order, String url, String desc, String devUrl) {
        this.order = order;
        this.url = url;
        this.desc = desc;
        this.devUrl = devUrl;
    }

    public Integer getOrder() {
        return this.order;
    }

    public String getName() {
        return this.url;
    }

    public String getDesc() {
        return this.desc;
    }

    public static ExcelTemplateType getNameByOrder(Integer order) {
        for (ExcelTemplateType excelTemplateType : ExcelTemplateType.values()) {
            if (excelTemplateType.order.equals(order)) {
                return excelTemplateType;
            }
        }

        return null;
    }
}

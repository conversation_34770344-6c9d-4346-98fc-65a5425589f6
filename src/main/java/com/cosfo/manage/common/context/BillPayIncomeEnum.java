package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date : 2022/12/26 11:03
 * 收支类型
 */
@Getter
@AllArgsConstructor
public enum BillPayIncomeEnum {
    /**
     * 按周
     */
    INCOME(0,"收入"),

    /**
     * 按月
     */
    PAY(1,"支出");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 状态描述
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        for (BillPayIncomeEnum billPayIncomeEnum : BillPayIncomeEnum.values()) {
            if (Objects.equals(code, billPayIncomeEnum.getCode())) {
                return billPayIncomeEnum.getDesc();
            }
        }
        return null;
    }
}

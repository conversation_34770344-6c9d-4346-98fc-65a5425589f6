package com.cosfo.manage.common.context;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum SenderPlatformEnum {

    /**
     * 只打印日志，不发送短信
     */
    LOCAL_SMS(0, "本地"),

    /*
     * 创蓝短信
     */
    CHUANGLAN_SMS(1, "创蓝短信");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    SenderPlatformEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}

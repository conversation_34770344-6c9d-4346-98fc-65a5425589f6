package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DocCodeChannelTypeEnum {
    DEFAULT_CHANNEL(0, "不区分"),
    MEI_TUAN(1, "美团"),
    KE_RU_YUN(2, "客如云"),
    QI_MAI(3, "企迈"),
    FL_POS(4, "FILPOS"),
    ;


    private Integer code;

    private String desc;

    public static DocCodeChannelTypeEnum getByDesc(String desc) {
        for (DocCodeChannelTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;  // 或者可以选择抛出异常
    }
}

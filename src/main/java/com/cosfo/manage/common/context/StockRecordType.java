package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/19  11:09
 */
@Getter
@AllArgsConstructor
public enum StockRecordType {
    /**
     *  0、下单
     */
    ORDER(0, "下单"),
    /**
     * 1、取消订单
     */
    ORDER_CANCEL(1, "取消订单"),
    /**
     * 2、售后
     */
    AFTER_SALE(2, "售后"),
    /**
     * 3、手动调整
     */
    MANUALLY_ADJUST(3, "手动调整"),

    /**
     * 4、自营->三方
     */
    SELF_TO_THIRD(4, "自营更换三方"),

    /**
     * 5、三方更换自营
     */
    THIRD_TO_SELF(5, "三方更换自营"),

    /**
     * 6、新建商品
     */
    SAVE_NEW(6, "新建商品");

    private Integer type;
    private String desc;
}

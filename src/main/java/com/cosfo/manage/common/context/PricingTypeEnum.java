package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 18:55
 */
@Getter
@AllArgsConstructor
public enum PricingTypeEnum {


    PERCENTAGE_FLOAT(0, "百分比上浮"),
    QUOTA_FLOAT(1, "定额上浮"),
    FIXED_PRICE(2, "固定价"),
    ;

    /**
     * 状态编码
     */
    private Integer type;
    /**
     * 状态描述
     */
    private String desc;

    public static String getDesc(Integer type) {
        for (PricingTypeEnum pricingTypeEnum : PricingTypeEnum.values()){
            if (pricingTypeEnum.getType().equals(type)) {
                return pricingTypeEnum.getDesc();
            }
        }
        return null;
    }
}

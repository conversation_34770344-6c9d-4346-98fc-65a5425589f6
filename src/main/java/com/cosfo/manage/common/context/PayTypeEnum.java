package com.cosfo.manage.common.context;

import com.cosfo.manage.common.SettlementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/9
 */
@Getter
@AllArgsConstructor
@Deprecated
// 请使用order-center 的 com.cosfo.ordercenter.client.common.PayTypeEnum
public enum PayTypeEnum {
    /**
     * 微信支付
     */
    WECHAT_PAY(1,"微信支付"),
    /**
     * 账期支付
     */
    BILL(2,"账期支付"),

    /**
     * 余额支付
     */
    BALANCE(3, "余额支付"),

    /**
     * 支付宝
     */
    ALI_PAY(4, "支付宝支付"),

    /**
     * 无需支付
     */
    ZERO_PRICE_PAY(5, "无需支付"),

    /**
     * 线下支付
     */
    OFFLINE_PAY(6, "线下支付"),

    /**
     * 非现金支付
     */
    NON_CASH_PAY(7, "非现金支付"),

    /**
     * 组合支付
     */
    COMBINED_PAY(8, "组合支付"),

    ;


    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 获取支付类型
     *
     * @param code
     * @return
     */
    public static PayTypeEnum getPayType(Integer code){
        for (PayTypeEnum payTypeEnum : PayTypeEnum.values()){
            if(payTypeEnum.getCode().equals(code)){
                return payTypeEnum;
            }
        }

        return null;
    }

    /**
     * 是否是现结
     * TODO:暂时先保留这两个静态方法，到时候迁移出去
     * @return
     */
    public static boolean isCurrentSettlement(Integer payType) {
        if (payType == null) {
            return false;
        }
        return payType.equals(WECHAT_PAY.getCode()) || payType.equals(ALI_PAY.getCode());
    }


    /**
     * TODO:暂时先保留这两个静态方法，到时候迁移出去
     * @param payType
     * @return
     */
    public static Integer getCurrentSettleFlag(Integer payType) {
        if (payType == null) {
            return null;
        }
        // 1现结 0非现结
        return payType.equals(WECHAT_PAY.getCode()) || payType.equals(ALI_PAY.getCode()) ? SettlementTypeEnum.CURRENT_SETTLE.getType() : SettlementTypeEnum.BILL_SETTLE.getType();
    }
}

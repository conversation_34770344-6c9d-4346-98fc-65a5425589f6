package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-07-19
 **/
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum DeliveryRefundFeeFlagEnum {

    DO_NOT_NEED_REFUND(0, "不需要退"),
    NEED_REFUND(1, "需要退"),
    ;


    /**
     * 标识
     */
    private Integer flag;

    /**
     * 描述
     */
    private String desc;
}

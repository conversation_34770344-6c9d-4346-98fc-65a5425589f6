package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 描述: 上下架枚举类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/23
 */
@Getter
@AllArgsConstructor
public enum OnSaleTypeEnum {
    /**
     * 下架
     */
    SOLD_OUT(0,"下架","否"),
    /**
     * 上架
     */
    ON_SALE(1,"上架","是");

    /**
     * 售后订单状态编码
     */
    private Integer code;
    /**
     * 售后订单状态描述
     */
    private String desc;
    /**
     * 导出备注
     */
    private String remark;

    /**
     * getDesc
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        for (OnSaleTypeEnum value : OnSaleTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value.getDesc();
            }
        }
        return null;
    }

    /**
     * getRemark
     *
     * @param code
     * @return
     */
    public static String getRemark(Integer code) {
        for (OnSaleTypeEnum value : OnSaleTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value.getRemark();
            }
        }
        return null;
    }

    /**
     * get desc
     * @param desc
     * @return
     */
    public static Integer getType(String desc) {
        for (OnSaleTypeEnum value : OnSaleTypeEnum.values()) {
            if (Objects.equals(desc, value.getDesc())) {
                return value.getCode();
            }
        }
        return null;
    }

    public static OnSaleTypeEnum getOnSaleType(Integer code) {
        for (OnSaleTypeEnum value : OnSaleTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}

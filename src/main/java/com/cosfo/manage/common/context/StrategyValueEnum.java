package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum StrategyValueEnum {
    USE_COST_PRICE (0, "以供应价售卖"),
    USE_ITEM_PRICE (1, "以自定义价售卖"),
    AUTO_SOLD_OUT (2, "自动下架"),
    ;

    private Integer code;
    private String desc;
    public static StrategyValueEnum getByDesc(String desc) {
            for (StrategyValueEnum value : StrategyValueEnum.values()) {
                    if (Objects.equals(value.desc, desc)) {
                            return value;
                    }
            }
            return null;
    }
}
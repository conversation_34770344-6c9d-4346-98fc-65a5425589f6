package com.cosfo.manage.common.context.store.balance;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 用于excel的数据匹配
 * @date 2022/8/19 9:45
 */
@Getter
@AllArgsConstructor
public enum BalancePermissionTypeEnum {

    /**
     * 关闭余额权限
     */
    CLOSE_BALANCE_AUTH(0, "关闭"),

    /**
     * 开通余额权限
     */
    OPEN_BALANCE_AUTH(1, "开启");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;
    public static String getDesc(Integer type) {
        for (BalancePermissionTypeEnum value : BalancePermissionTypeEnum.values()) {
            if (Objects.equals(value.getType (), type)) {
                return value.getDesc();
            }
        }
        return null;
    }
    public static BalancePermissionTypeEnum getByType(Integer type){
        for(BalancePermissionTypeEnum balanceAuthorityEnum: BalancePermissionTypeEnum.values()){
            if(balanceAuthorityEnum.getType().equals(type)){
                return balanceAuthorityEnum;
            }
        }
        return null;
    }

    public static BalancePermissionTypeEnum getByDesc(String desc){
        for(BalancePermissionTypeEnum balanceAuthorityEnum: BalancePermissionTypeEnum.values()){
            if(balanceAuthorityEnum.getDesc().equals(desc)){
                return balanceAuthorityEnum;
            }
        }
        return null;
    }
}

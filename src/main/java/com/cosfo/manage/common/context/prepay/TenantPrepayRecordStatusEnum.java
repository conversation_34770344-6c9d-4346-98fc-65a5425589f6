package com.cosfo.manage.common.context.prepay;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 9:35
 */
@Getter
@AllArgsConstructor
public enum TenantPrepayRecordStatusEnum {

    /**
     * 待审核
     */
    WAIT_AUDIT(0, "待审核"),
    /**
     * 审核通过
     */
    AUDIT_SUCCESS(1, "已通过"),
    /**
     * 审核失败
     */
    AUDIT_FAIL(2, "已拒绝"),
    ;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String desc;

    public static TenantPrepayRecordStatusEnum fromStatus(Integer status) {
        for (TenantPrepayRecordStatusEnum value : TenantPrepayRecordStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }
}

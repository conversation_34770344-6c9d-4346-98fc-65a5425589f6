package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-07-24
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum TenantScoreStepEnum {

    /**
     * 0-待评分
     */
    WAIT_SCORE(0, "待评分"),
    /**
     * 1-待反馈意见
     */
    WAIT_FEEDBACK(1, "待反馈意见"),
    /**
     * 3-评分成功
     */
    SCORE_SUCCESS(3, "评分成功");

    /**
     * 满意度评分步骤
     */
    private Integer step;
    /**
     * 满意度评分步骤描述
     */
    private String desc;

    public static final Integer SCORE_STEP = 0;
}

package com.cosfo.manage.common.context.holder;

import com.cosfo.manage.report.repository.TenantMetricsSummaryRepository;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: George
 * @date: 2023-11-13
 **/
@Component
@Slf4j
public class TenantMetricsSummaryContextHolder {

    @Resource
    private TenantMetricsSummaryRepository tenantMetricsSummaryRepository;

    private final Cache<String, TenantMetricsSummary> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)  // 写入后10分钟过期，根据实际需求调整
            .maximumSize(10) //最多缓存10个键值对
            .build();

    /**
     * 获取租户指标缓存
     * @param tenantId
     * @param timeTag
     * @return
     */
    public TenantMetricsSummary getTenantMetricsSummary(Long tenantId, String timeTag) {
        String cacheKey = tenantId + "_" + timeTag;
        try {
            return cache.get(cacheKey, new Callable<TenantMetricsSummary>() {
                @Override
                public TenantMetricsSummary call() throws Exception {
                    // 缓存未命中或已过期，重新加载或执行相应的操作
                    return Optional.ofNullable(tenantMetricsSummaryRepository.queryByTenantAndTimeTag(tenantId, timeTag)).orElse(new TenantMetricsSummary());
                }
            });
        } catch (Exception e) {
            log.error("生成租户指标缓存失败", e);
        }
        return cache.getIfPresent(cacheKey);
    }

    public void setTenantMetricsSummary(String cacheKey, TenantMetricsSummary tenantMetricsSummary) {
        cache.put(cacheKey, tenantMetricsSummary);
    }
}

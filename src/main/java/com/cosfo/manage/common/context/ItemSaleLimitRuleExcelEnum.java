package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ItemSaleLimitRuleExcelEnum {
    NO_LIMIT(0, "无限制"),
    EVERY_TIME(1, "每笔订单"),
    DAILY(2, "每天(0-24点)"),
    WEEKLY(3, "每周(周一到周日)"),
    MONTHLY(4, "每月(每月1号到月末)"),
    ;


    private final Integer code;
    private final String desc;

    public static ItemSaleLimitRuleExcelEnum getByDesc(String desc) {
        for (ItemSaleLimitRuleExcelEnum value : ItemSaleLimitRuleExcelEnum.values()) {
            if (Objects.equals(value.desc, desc)) {
                return value;
            }
        }
        return null;
    }
}

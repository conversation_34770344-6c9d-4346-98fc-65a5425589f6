package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/16
 */
@Getter
@AllArgsConstructor
public enum SupplyTypeEnum {

    /**
     * 鲜沐供应
     */
    SUPPLY_SKU(1,"鲜沐直供"),
    /**
     * 代仓
     */
    AGENT_SKU(2,"代仓商品");

    /**
     * 供应类型编码
     */
    private Integer code;
    /**
     * 供应类型描述
     */
    private String desc;
}

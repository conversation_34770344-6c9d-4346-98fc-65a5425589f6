package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum BomItemCompositionTypeEnum {
    NONE(0,"错误"),
    ITEM(1,"原料"),
    BOM(2,"bom"),
    ;
    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    public static BomItemCompositionTypeEnum of(Integer code) {
        for (BomItemCompositionTypeEnum billPayEnum : BomItemCompositionTypeEnum.values()) {
            if (Objects.equals(code, billPayEnum.getCode())) {
                return billPayEnum;
            }
        }
        return NONE;
    }
    public static BomItemCompositionTypeEnum getByDesc(String desc) {
        for (BomItemCompositionTypeEnum billPayEnum : BomItemCompositionTypeEnum.values()) {
            if (Objects.equals(desc, billPayEnum.getDesc ())) {
                return billPayEnum;
            }
        }
        return NONE;
    }
}

package com.cosfo.manage.common.context;

import com.cosfo.manage.report.model.dto.StockForWaringConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ForewarningStatusEnum {

    NORMAL(0, "正常"),
    FOREWARNING(1, "预警"),
    SOLD_OUT(2, "售罄"),
    ;

    /**
     * type
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;

    public static String getDescByType(Integer type) {
        for (ForewarningStatusEnum forewarningStatusEnum : ForewarningStatusEnum.values()) {
            if (forewarningStatusEnum.getType().equals(type)) {
                return forewarningStatusEnum.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据库存、销量、预警配置获取预警状态
     * @param quantity
     * @param saleQuantity
     * @param stockForWaringConfigDTO
     * @return
     */
    public static Integer getStatus(Integer quantity, Integer saleQuantity, StockForWaringConfigDTO stockForWaringConfigDTO) {
        if (quantity <= 0) {
            return SOLD_OUT.type;
        }
        // 0,平均销量, 1累计销量
        Integer waringType = stockForWaringConfigDTO.getWaringType();
        int compareValue = saleQuantity;
        if (waringType == 0) {
            compareValue = (int) Math.ceil(saleQuantity * 1.00d / stockForWaringConfigDTO.getDay());
        }
        // 库存小于平均/累计销量
        if(quantity <= compareValue){
            return FOREWARNING.type;
        }
        return NORMAL.type;
    }

    public static void main(String[] args) {
        int compareValue = (int) Math.ceil(1 * 1.00d / 8);
        System.err.println(compareValue);
    }
}

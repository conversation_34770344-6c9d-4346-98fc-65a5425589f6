package com.cosfo.manage.common.context.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-04-12
 * @Description:无仓订单的配送类型
 */
@Getter
@AllArgsConstructor
public enum NoWarehouseDeliveryTypeEnum {
    NOT_NEED_DELIVERY(0, "其它"),
    LOGISTIC_DELIVERY(1, "物流快递");

    /**
     * 类型
     */
    private Integer type;
    /**
     * 类型描述
     */
    private String desc;

    public static NoWarehouseDeliveryTypeEnum getByType(Integer type) {
        for (NoWarehouseDeliveryTypeEnum noWarehouseDeliveryTypeEnum : NoWarehouseDeliveryTypeEnum.values()) {
            if (noWarehouseDeliveryTypeEnum.getType().equals(type)) {
                return noWarehouseDeliveryTypeEnum;
            }
        }
        return null;
    }

    public static NoWarehouseDeliveryTypeEnum getByDesc(String desc) {
        for (NoWarehouseDeliveryTypeEnum noWarehouseDeliveryTypeEnum : NoWarehouseDeliveryTypeEnum.values()) {
            if (noWarehouseDeliveryTypeEnum.getDesc().equals(desc)) {
                return noWarehouseDeliveryTypeEnum;
            }
        }
        return null;
    }
}

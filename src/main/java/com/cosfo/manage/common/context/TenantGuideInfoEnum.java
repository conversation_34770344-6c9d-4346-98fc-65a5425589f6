package com.cosfo.manage.common.context;

import com.cosfo.manage.tenant.model.dto.TenantGuideInfoItemDTO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @date: 2023/3/23 下午1:38
 */
@Getter
public enum TenantGuideInfoEnum {

    /**
     * 评分
     */
    SCORE_501(501, 0, "1"),
    SCORE_502(502, 0, "2"),
    SCORE_503(503, 0, "3"),
    SCORE_504(504, 0, "4"),
    SCORE_505(505, 0, "5"),
    SCORE_506(506, 0, "6"),
    SCORE_507(507, 0, "7"),
    SCORE_508(508, 0, "8"),
    SCORE_509(509, 0, "9"),
    SCORE_510(510, 0, "10"),

    /**
     * 你对「连锁门店一站式采购&管理」系统的熟悉程度是？
     */
    ITEM_1001(1001, 1, "初步接触"),
    ITEM_1002(1002, 1, "有一定了解"),
    ITEM_1003(1003, 1, "非常熟悉"),

    ITEM_2001(2001, 2, "数据可视化"),
    ITEM_2002(2002, 2, "简化操作，缩短工作流"),
    ITEM_2003(2003, 2, "任务批量处理"),
    ITEM_2004(2004, 2, "个性化定制功能"),
    ITEM_2005(2005, 2, "风险及时预警"),
    ITEM_2006(2006, 2, "售后问题及时处理"),

    ITEM_3001(3001, 3, "欢迎，开启你的帆台之旅！", 4, true),
    ITEM_3002(3002, 3, "如何快速新建采购单？", 5),
    ITEM_3003(3003, 3, "如何跟进采购单？", 6),
    ITEM_3004(3004, 3, "如何快速新建商品？", 7),
    ITEM_3005(3005, 3, "如何查看门店订货状况？", 8),
    ITEM_3006(3006, 3, "如何建立仓库档案？", 9),
    ITEM_3007(3007, 3, "如何验收入库？", 10),

    ;
    /**
     * 问题选项id
     */
    private Integer id;

    /**
     * 归属引导问题第几步
     */
    private Integer pid;

    /**
     * 问题选项描述
     */
    private String text;

    /**
     * 任务点击上报保存id
     */
    private Integer saveId;

    private boolean defauleSelected;

    TenantGuideInfoEnum(Integer id, Integer pid, String text) {
        this.id = id;
        this.pid = pid;
        this.text = text;
        this.saveId = pid;
    }

    TenantGuideInfoEnum(Integer id, Integer pid, String text, Integer saveId) {
        this.id = id;
        this.pid = pid;
        this.text = text;
        this.saveId = saveId;
    }

    TenantGuideInfoEnum(Integer id, Integer pid, String text, Integer saveId, boolean defauleSelected) {
        this.id = id;
        this.pid = pid;
        this.text = text;
        this.saveId = saveId;
        this.defauleSelected = defauleSelected;
    }

    public static List<TenantGuideInfoEnum> getGuideInfoEnumList(Integer stepNum) {
        List<TenantGuideInfoEnum> list = new ArrayList<>();
        for (TenantGuideInfoEnum tenantGuideInfoEnum : TenantGuideInfoEnum.values()) {
            if (tenantGuideInfoEnum.pid.equals(stepNum)) {
                list.add(tenantGuideInfoEnum);
            }
        }

        return list;
    }

    public static TenantGuideInfoEnum getGuideInfo(String id) {
        for (TenantGuideInfoEnum tenantGuideInfoEnum : TenantGuideInfoEnum.values()) {
            if (String.valueOf(tenantGuideInfoEnum.id).equals(id)) {
                return tenantGuideInfoEnum;
            }
        }

        return null;
    }

    public static TenantGuideInfoItemDTO getGuideInfoById(String id, boolean selected) {
        List<TenantGuideInfoEnum> list = new ArrayList<>();
        for (TenantGuideInfoEnum tenantGuideInfoEnum : TenantGuideInfoEnum.values()) {
            if (String.valueOf(tenantGuideInfoEnum.id).equals(id)) {
                return new TenantGuideInfoItemDTO(id, tenantGuideInfoEnum.text, selected);
            }
        }

        return null;
    }
}

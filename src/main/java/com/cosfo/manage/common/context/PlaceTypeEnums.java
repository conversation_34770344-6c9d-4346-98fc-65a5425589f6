package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/3
 */
@Getter
@AllArgsConstructor
public enum PlaceTypeEnums {
    /**
     * 进口
     */
    IMPORT(0,"进口"),
    /**
     * 国产
     */
    DOMESTIC(1,"国产");

    private Integer type;
    private String desc;

    public static Integer getType(String desc) {
        for (PlaceTypeEnums placeTypeEnums : PlaceTypeEnums.values()) {
            if (placeTypeEnums.desc.equals(desc)) {
                return placeTypeEnums.getType();
            }
        }

        return null;
    }

    public static String getDescByType(Integer type) {
        for (PlaceTypeEnums placeTypeEnums : PlaceTypeEnums.values()) {
            if (placeTypeEnums.type.equals(type)) {
                return placeTypeEnums.getDesc();
            }
        }

        return "";
    }
}

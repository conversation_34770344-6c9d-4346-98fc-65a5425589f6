package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum  SysParamKey {

    //微信第三方平台参数在DB中的key名称
    SYS_PARAM_KEY_TP_APP_ID("tp_appid","第三方平台appid"),
    SYS_PARAM_KEY_TP_APP_SECRET("tp_app_secret","第三方平台appsecret"),
    SYS_PARAM_KEY_TP_TOKEN("tp_token","第三方平台解密token"),
    SYS_PARAM_KEY_TP_ENCODING_AES_KEY("tp_encoding_aes_key","第三方平台解密key"),
    SYS_PARAM_KEY_TP_APP_TICKET("tp_app_ticket","第三方平台ticket"),
    SYS_PARAM_KEY_TP_ACCESS_TOKEN_JSON("tp_access_token_json","第三方平台accessTokenJson串"),

    LITE_REQUEST_DOWNLOAD_DOMAIN("lite_request_download_domain","downloadFile合法域名"),
    LITE_REQUEST_REQUEST_DOMAIN("lite_request_request_domain","request合法域名"),
    LITE_REQUEST_SOCKET_DOMAIN("lite_request_socket_domain","socket合法域名"),
    LITE_REQUEST_UPLOAD_DOMAIN("lite_request_upload_domain","uploadFile合法域名"),
    LITE_WEBVIEW_WEB_VIEW_DOMAIN("lite_webview_web_view_domain","业务域名"),

    //系统参数
    SYS_PARAM_KEY_BASE_URL("baseUrl","系统基础域名"),
    SYS_PARAM_KEY_MALL_BASE_URL("mallBaseUrl","商城域名"),
    SYS_PARAM_KEY_SCHEME("scheme","请求前缀");

    /**
     * 状态类型编码
     */
    private String key;
    /**
     * 状态类型描述
     */
    private String desc;

}

package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-11-14
 **/
@Getter
@AllArgsConstructor
public enum MeasureItemTypeEnum {

    DATA_REPORT(1, "数据报表"),
    IMMEDIATE_OPTIMIZATION(2, "立即优化")
    ;


    /**
     * 度量项类型
     */
    private Integer type;
    /**
     * 状态描述
     */
    private String desc;
}

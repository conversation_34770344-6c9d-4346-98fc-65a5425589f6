package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 门店账号状态枚举
 * <AUTHOR>
 * @date 2022/12/1 17:14
 */
@Getter
@AllArgsConstructor
public enum MerchantStoreAccountStatusEnum {

    /**
     * 审核中
     */
    AUDITING(0, "审核中"),

    /**
     * 审核通过
     */
    AUDIT_SUCCESS(1, "审核通过"),

    /**
     * 审核拒绝
     */
    AUDIT_REFUSE(2,"审核拒绝");

    private Integer status;
    private String desc;
}

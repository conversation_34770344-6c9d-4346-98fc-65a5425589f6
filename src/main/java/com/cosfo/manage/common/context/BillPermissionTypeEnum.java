package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/19 9:45
 */
@Getter
@AllArgsConstructor
public enum BillPermissionTypeEnum {

//    /**
//     * 开通，关闭在线支付能力
//     */
//    OPEN_BILL(0, "开通，关闭在线支付能力"),

    /**
     * 开通，保留在线支付能力
     */
    OPEN_BILL_AND_ONLINE(1, "开启"),

    /**
     * 关闭
     */
    CLOSE_BILL(2, "关闭");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    /**
     *
     * @param billSwitch
     * @param onlinePayment
     * @return
     */
    public static String getBillPermissionDesc(Integer billSwitch, Integer onlinePayment) {
        if (Objects.equals(billSwitch, BillSwitchEnum.SHUTDOWN.getCode())) {
            return BillPermissionTypeEnum.CLOSE_BILL.getDesc();
        }
        return BillPermissionTypeEnum.OPEN_BILL_AND_ONLINE.getDesc();
    }
}

package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/14
 */
@Getter
@AllArgsConstructor
public enum InStockTypeEnum {
    PURCHASE_IN(11, "采购入库"),
    STORE_ALLOCATION_IN(10, "调拨入库"),
    AFTER_SALE_IN(12, "退货入库"),
    REJECT_SALE_IN(13, "拒收入库"),
    LACK_GOODS_IN(20, "缺货入库"),
    STOCK_TAKING_IN(15, "盘盈入库"),
    TRANSFER_IN(16, "转换入库"),
    STORE_ALLOCATION_BACK_IN(18, "调拨回库"),
    OTHER_IN(24, "其他入库"),
    OPENING_IN(8, "期初入库"),

    ;

    Integer id;
    String name;

    public static String getNameById(int id) {
        for (InStockTypeEnum storeRecordType : InStockTypeEnum.values()) {
            if (storeRecordType.getId() == id) {
                return storeRecordType.getName();
            }
        }
        return "";
    }
}

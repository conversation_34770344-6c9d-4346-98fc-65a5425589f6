package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 商品指定价
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/13
 */
@Getter
@AllArgsConstructor
public enum PriceTypeEnum {

    /**
     * 指定价
     */
    SPECIFIED_PRICE(0, "指定价"),

    /**
     * 鲜沐报价单
     */
    SUMMERFATM_PRICE(1,"鲜沐报价单"),
    /**
     * 鲜沐报价单上浮
     */
    SUMMER_FARM_PRICE_UP(2, "鲜沐商城价(上浮)"),
    /**
     * 鲜沐报价单下浮
     */
    SUMMER_FARM_PRICE_DOWN(3, "鲜沐商城价(下浮)"),
    /**
     * 鲜沐报价单加价
     */
    SUMMER_FARM_PRICE_INCREASE(4, "鲜沐商城价(加价)"),
    /**
     * 鲜沐报价单减价
     */
    SUMMER_FARM_PRICE_DECREASE(5, "鲜沐商城价(减价)"),

    ;
    /**
     * 状态编码
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String desc;
}

package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/5
 */
@Getter
@AllArgsConstructor
public enum CredentialsOperatorTypeEnum {
    /**
     * 收款方
     */
    PAYEE(0, "收款方"),
    /**
     * 付款方
     */
    PAYER(1,"付款方");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;
}

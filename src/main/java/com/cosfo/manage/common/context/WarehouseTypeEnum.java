package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 配送仓类型
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/18
 */
@Getter
@AllArgsConstructor
public enum WarehouseTypeEnum {
    /**
     * 无仓
     */
    NO_WAREHOUSE(0,"无仓"),
    /**
     * 三方仓
     */
    THREE_PARTIES(1,"三方仓"),
    /**
     * 自营仓
     */
    PROPRIETARY(2, "自营仓"),
    /**
     * 非法数据
     */
    NULL_ERROR(-1, "非法数据"),

    /**
     * 全局包邮
     */
    GLOBAL(10, "全局包邮"),

    ;

    /**
     * 配送仓类型编码
     */
    private Integer code;
    /**
     * 配送仓类型描述
     */
    private String desc;

    /**
     * 根据code查询
     *
     * @param code
     * @return
     */
    public static WarehouseTypeEnum getByCode(Integer code){
        for(WarehouseTypeEnum warehouseTypeEnum :WarehouseTypeEnum.values()){
            if(warehouseTypeEnum.getCode().equals(code)){
                return warehouseTypeEnum;
            }
        }

        return WarehouseTypeEnum.NULL_ERROR;
    }

    /**
     * 是否自营仓类型，包含无仓和自营仓
     * @param warehouseType
     * @return
     */
    public static boolean isSelfWarehouseType(Integer warehouseType){
        return NO_WAREHOUSE.code.equals(warehouseType) || PROPRIETARY.code.equals(warehouseType);
    }
}

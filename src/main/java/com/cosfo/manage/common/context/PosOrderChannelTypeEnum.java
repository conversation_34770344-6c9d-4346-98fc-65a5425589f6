package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PosOrderChannelTypeEnum {
    MEI_TUAN(1, "美团"),
    KE_RU_YUN(2, "客如云"),
    QI_MAI(3, "企迈"),
    FILPOS(4, "FILPOS"),
    ;


    private Integer code;

    private String desc;

    public static PosOrderChannelTypeEnum getByDesc(String desc) {
        for (PosOrderChannelTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;  // 或者可以选择抛出异常
    }
}

package com.cosfo.manage.common.context.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date : 2023/4/10 14:25
 * 订单配送类型
 */
@Getter
@AllArgsConstructor
public enum DeliveryOrderTypeEnum {
    ORDER(0,"订单"),
    AFTER_ORDER(1,"售后补发单")
    ;

    /**
     * 类型编码
     */
    private Integer type;
    /**
     * 类型描述
     */
    private String desc;
}

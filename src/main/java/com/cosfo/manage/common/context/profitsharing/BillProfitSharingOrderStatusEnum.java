package com.cosfo.manage.common.context.profitsharing;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-01-06
 **/
@Getter
@AllArgsConstructor
public enum BillProfitSharingOrderStatusEnum {

    WAIT(0, "待分账"),

    CALCULATE_FINISHED(1, "计算金额完毕"),

    SHARING(2, "分账中"),

    SUCCESS(3, "已成功"),

    INIT(4, "初始化"),
    ;


    private Integer status;

    private String desc;
}

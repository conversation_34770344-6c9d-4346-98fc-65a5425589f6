package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ItemSaleModeExcelEnum {
    NORMAL_SALE(0, "可独售"),
    TYING_ADD_ON_SALE(1,"仅可搭售，可凑单"),
    TYING_CANNOT_ADD_ON_SALE(2,"仅可搭售，不可凑单");

    /**
     * 标识
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    public static ItemSaleModeExcelEnum getByDesc(String desc) {
        for (ItemSaleModeExcelEnum value : ItemSaleModeExcelEnum.values()) {
            if (Objects.equals(value.desc, desc)) {
                return value;
            }
        }
        return null;
    }
}

package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/22
 */
@Getter
@AllArgsConstructor
public enum StoreAccountTypeEnum {
    /**
     *错误
     */
    ERROR(-1,"错误"),
    /**
     * 店长
     */
    MANAGER(0,"店长"),
    /**
     * 店员
     */
    SHOP_ASSISTANT(1,"店员");

    /**
     * 类型编码
     */
    private Integer code;
    /**
     * 类型描述
     */
    private String desc;

    public static StoreAccountTypeEnum of(int code) {
        for (StoreAccountTypeEnum e : StoreAccountTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return ERROR;
    }
}

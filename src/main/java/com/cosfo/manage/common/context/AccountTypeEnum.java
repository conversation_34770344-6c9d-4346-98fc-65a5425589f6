package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-01-04
 **/
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {


    PLATFORM(0, "平台"),
    SUPPLIER(1, "供应商"),
    TENANT(2, "租户"),
    NORMAL_RECEIVER(3, "普通接收方")
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    public static Integer getByAccountId(Long accountId) {

        if (accountId == null) {
            return null;
        }
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            if (Objects.equals(accountId, Long.valueOf(accountTypeEnum.type))) {
                return accountTypeEnum.type;
            }
        }
        return TENANT.getType();
    }

    public static Integer getByTenantAndAccountId(Long tenantId, Long accountId) {
        if (tenantId == null || accountId == null) {
            return null;
        }
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            if (Objects.equals(accountId, Long.valueOf(accountTypeEnum.type))) {
                return accountTypeEnum.type;
            }
        }
        if (!tenantId.equals(accountId)) {
            return NORMAL_RECEIVER.getType();
        }
        return TENANT.getType();
    }
}

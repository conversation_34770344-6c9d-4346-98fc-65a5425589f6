package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TenantStatusEnum {
    /**
     * 0-审核中
     */
    IN_AUDIT(0, "审核中"),
    /**
     * 1-审核成功
     */
    AUDIT_SUCCESS(1, "审核成功"),
    /**
     * 2-审核失败
     */
    AUDIT_FAIL(2, "审核失败");

    /**
     * 租户状态编码
     */
    private Integer code;
    /**
     * 租户状态编码
     */
    private String desc;
}

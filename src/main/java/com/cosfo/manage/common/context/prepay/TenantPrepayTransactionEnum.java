package com.cosfo.manage.common.context.prepay;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 9:37
 */
public class TenantPrepayTransactionEnum {

    @Getter
    @AllArgsConstructor
    public enum Type {

        /**
         * 收入
         */
        INCOME(0, "收入"),
        /**
         * 支出
         */
        EXPENDITURE(1, "支出"),
        ;

        /**
         * 类型
         */
        private Integer type;

        /**
         * 描述
         */
        private String desc;

        public static Type fromType(Integer type) {
            for (Type value : Type.values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum TransactionType {


        /**
         * 预付
         */
        PREPAY(0, "预付"),

        /**
         * 预付退款
         */
        PREPAY_REFUND(1, "预付退款"),
        /**
         * 直供货品
         */
        DIRECT_SUPPLY(2, "直供货品"),
        /**
         * 直供货品退款
         */
        DIRECT_SUPPLY_REFUND(3, "直供货品退款"),
        /**
         * 运费
         */
        DELIVERY_FEE(4, "运费"),

        /**
         * 运费退款
         */
        DELIVERY_FEE_REFUND(5, "运费退款"),
        /**
         * 代仓费用
         */
        AGENT_WAREHOUSE_EXPENSE(6, "代仓费用"),
        /**
         * 代仓费用退款
         */
        AGENT_WAREHOUSE_EXPENSE_FEE(7, "代仓费用退款"),
        ;

        /**
         * 类型
         */
        private Integer type;

        /**
         * 描述
         */
        private String desc;

        public static TransactionType fromType(Integer type) {
            for (TransactionType value : TransactionType.values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum AssociatedOrderType {

        /**
         * 订单
         */
        ORDER(0, "订单"),
        /**
         * 支出
         */
        ORDER_AFTER_SALE(1, "售后"),
        ;

        /**
         * 类型
         */
        private Integer type;

        /**
         * 描述
         */
        private String desc;
    }

}

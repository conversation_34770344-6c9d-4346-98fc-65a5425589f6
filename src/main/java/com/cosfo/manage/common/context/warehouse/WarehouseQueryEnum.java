package com.cosfo.manage.common.context.warehouse;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-04-14
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum WarehouseQueryEnum {

    /**
     * 无仓
     */
    NO_WAREHOUSE(-1, "无仓"),

    /**
     * 三方优选仓
     */
    THIRD_WAREHOUSE(-2, "三方优选仓"),

    /**
     * 自营仓一级
     */
    SELF_WAREHOUSE(-3, "自营仓")
    ;

    /**
     * 类型
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    public static WarehouseQueryEnum getById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        for (WarehouseQueryEnum warehouseQueryEnum : WarehouseQueryEnum.values()) {
            if (warehouseQueryEnum.getId().equals(id)) {
                return warehouseQueryEnum;
            }
        }
        return null;
    }

}

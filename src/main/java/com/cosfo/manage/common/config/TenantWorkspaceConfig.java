package com.cosfo.manage.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.stereotype.Component;

@Component
@Data
public class TenantWorkspaceConfig {

    @NacosValue(value = "${tenant.workspace.default.common.function:}", autoRefreshed = true)
    private String defaultCommonFunction;

    @NacosValue(value = "${tenant.workspace.default.sales.ranking.records:10}", autoRefreshed = true)
    private Integer salesRankingRecords;
}

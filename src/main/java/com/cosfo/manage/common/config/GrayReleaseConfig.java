package com.cosfo.manage.common.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Data
@Slf4j
public class GrayReleaseConfig {

    @NacosValue(value = "${gray.productcenter.tenant:2}", autoRefreshed = true)
    private String productCenterGrayTenantIdsStr;
    @NacosValue(value = "${gray.productcenter.release:false}", autoRefreshed = true)
    private boolean productCenterGrayRelease;
    @NacosValue(value = "${gray.productcenter.category.release:false}", autoRefreshed = true)
    private boolean productCenterCategoryGrayRelease;
    /**
     * 发送短信间隔时间
     */
    @NacosValue(value = "${sms.send.interval.time:60000}", autoRefreshed = true)
    private Long smsSendIntervalTime;

    public List<Long> getOrderCenterGrayTenantIds() {
        if (StringUtil.isEmpty(productCenterGrayTenantIdsStr)) {
            return Collections.emptyList();
        }
        List<Long> list = Arrays.stream(productCenterGrayTenantIdsStr.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        return list;
    }


    public boolean executeProductCenterGray(Long tenantId) {
        if (productCenterGrayRelease) {
            return true;
        }
        return getOrderCenterGrayTenantIds().contains(tenantId);
    }
    public boolean executeGray() {
        return productCenterCategoryGrayRelease;
    }

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }
}

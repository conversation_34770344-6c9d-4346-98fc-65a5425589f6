package com.cosfo.manage.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import java.util.List;
import javax.annotation.Resource;
import javax.sql.DataSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.CollectionUtils;


/**
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.cosfo.manage.merchant.mapper","com.cosfo.manage.agentorder.mapper",
        "com.cosfo.manage.tenant.mapper", "com.cosfo.manage.system.mapper", "com.cosfo.manage.wechat.mapper",
        "com.cosfo.manage.stock.mapper","com.cosfo.manage.order.mapper","com.cosfo.manage.market.mapper"
        ,"com.cosfo.manage.product.mapper", "com.cosfo.manage.bill.mapper", "com.cosfo.manage.supplier.mapper",
        "com.cosfo.manage.file.mapper", "com.cosfo.manage.common.mapper", "com.cosfo.aftersale.mapper",
        "com.cosfo.manage.common.sms.mapper","com.cosfo.manage.good.mapper", "com.cosfo.manage.marketing.mapper","com.cosfo.manage.pos.mapper", "com.cosfo.manage.huifu.mapper", "com.cosfo.storeinventory.mapper", "com.cosfo.storeinventory.offline.mapper", "com.cosfo.manage.open.mapper"},
        sqlSessionFactoryRef = "sqlSessionFactory")
public class PrimaryDruidConfiguration {

    @Value(value = "${spring.datasource.dynamic.datasource.master.driver-class-name:}")
    private String driverClassName;
    @Value(value = "${spring.datasource.dynamic.datasource.master.url:}")
    private String url;
    @Value(value = "${spring.datasource.dynamic.datasource.master.username:}")
    private String username;
    @Value(value = "${spring.datasource.dynamic.datasource.master.password:}")
    private String password;
    @Value(value = "${spring.datasource.minIdle:}")
    private int minIdle;
    @Value(value = "${spring.datasource.initialSize:}")
    private int initialSize;
    @Value(value = "${spring.datasource.maxActive:}")
    private int maxActive;
    @Value(value = "${spring.datasource.maxWait:}")
    private int maxWait;
    @Value(value = "${spring.datasource.timeBetweenEvictionRunsMillis:}")
    private int timeBetweenEvictionRunsMillis;
    @Value(value = "${spring.datasource.minEvictableIdleTimeMillis:}")
    private int minEvictableIdleTimeMillis;

//    @Resource
//    private MybatisPlusInterceptor mybatisPlusInterceptor;
    @Resource
    private List<Interceptor> mybatisInterceptors;

    @Primary
    @Bean("dataSource")
    public DataSource druid() {
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setDbType("mysql");
        druidDataSource.setUrl(url);
        druidDataSource.setDriverClassName(driverClassName);
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setMinIdle(minIdle);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMaxActive(maxActive);
        druidDataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
        return druidDataSource;
    }

    @Bean(name = "sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Qualifier("dataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        //bean.setPlugins(mybatisPlusInterceptor);
        if (!CollectionUtils.isEmpty(mybatisInterceptors)) {
            bean.setPlugins(mybatisInterceptors.toArray(new Interceptor[]{}));
        }
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/primary/*/*.xml"));
        return bean.getObject();
    }
}

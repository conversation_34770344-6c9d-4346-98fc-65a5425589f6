package com.cosfo.manage.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Data
@Slf4j
public class WhitelistConfig {


    @NacosValue(value = "${professional.version.control.whitelist:[]}", autoRefreshed = true)
    private String whitelistAuthInfo;

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }

    public List<WhitelistAuthConfig> getWhitelistAuthConfig() {
        log.info("白名单当前配置:{}", whitelistAuthInfo);
        return JSON.parseArray(whitelistAuthInfo, WhitelistAuthConfig.class);
    }

    @Data
    public static class WhitelistAuthConfig implements Serializable {

        private List<Long> tenantIds;

        private String authUrl;

    }

    public List<MenuPurviewDTO> whitelistFilter(List<MenuPurviewDTO> sourceMenu, Long tenantId) {
        List<WhitelistAuthConfig> whitelistAuthConfig = getWhitelistAuthConfig();
        if (whitelistAuthConfig == null || whitelistAuthConfig.isEmpty()) {
            return sourceMenu;
        }
        Map<String, List<Long>> menuWhitelistMap = whitelistAuthConfig.stream().collect(Collectors.toMap(WhitelistAuthConfig::getAuthUrl, WhitelistAuthConfig::getTenantIds));
        return sourceMenu.stream().filter(menu -> {
            List<Long> tenantIds = menuWhitelistMap.get(menu.getUrl());
            if (tenantIds != null && !tenantIds.contains(tenantId)) {
                log.info("租户{}没有{}白名单权限", tenantId, menu.getUrl());
                return false;
            }
            if (CollectionUtils.isEmpty(menu.getChildMenuPurviewVOS())) {;
                return true;
            }
            menu.getChildMenuPurviewVOS().removeIf(childMenu -> {
                List<Long> childTenantIds = menuWhitelistMap.get(childMenu.getUrl());
                if (childTenantIds != null && !childTenantIds.contains(tenantId)) {
                    log.info("租户{}没有{}白名单权限", tenantId, childMenu.getUrl());
                    return true;
                }
                return false;
            });
            return true;
        }).collect(Collectors.toList());
    }

}

package com.cosfo.manage.common.config.qiniu;

import com.qiniu.common.Zone;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 描述: 七牛云配置类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/27
 */
@Data
@Component
//从配置文件里获取
@ConfigurationProperties(prefix = "qiniu")
public class QiNiuConfig {

    // 设置好账号的ACCESS_KEY
    private String ACCESS_KEY;

    // 设置好账号的SECRET_KEY
    private String SECRET_KEY;

    // 设置七牛要上传的空间
    private String bucketname;

    // 设置xm七牛要上传的空间
    private String XM_BUCKETNAME;

    // 设置关联七牛的域名
    //private String QINIU_IMAGE_DOMAIN;

    /**
     * 密钥配置
     *
     * @return
     */
    public Auth getAuth() {
        // 构造一个带指定Zone对象的配置类,不同的七云牛存储区域调用不同的zone
        Auth auth = Auth.create(ACCESS_KEY, SECRET_KEY);
        return auth;
    }

    /**
     * 构造一个带指定Zone对象的配置类,不同的七云牛存储区域调用不同的zone
     *         华东：zone0
     *         华北：zone1
     *         华南：zone2
     *         北美：zoneNa0
     *
     * @return
     */
    public Configuration getConfiguration() {
        Configuration cfg = new Configuration(Zone.zone0());
        return cfg;
    }

    /**
     * 构造一个七牛manager
     *
     * @return
     */
    public UploadManager getUploadManager() {
        UploadManager uploadManager = new UploadManager(getConfiguration());
        return uploadManager;
    }

    /**
     * 构造一个七牛manager
     *
     * @return
     */
    public BucketManager getBucketManager() {
        BucketManager bucketManager = new BucketManager(getAuth(), getConfiguration());
        return bucketManager;
    }

    /**
     *  简单上传，使用默认策略
     *  只需要设置上传的空间名就可以了
     *
     * @return
     */
    public String getUpToken() {
        return getAuth().uploadToken(getBucketname());
    }
}

package com.cosfo.manage.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.merchant.model.dto.address.AddressNameMappingDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import org.springframework.context.annotation.Configuration;

/**
 * @description: 门店地址省市区映射配置
 * @author: zach
 * @date: 2025-04-18
 **/
@Slf4j
@Configuration
public class MerchantAddressMappingNacosConfig {


    /**
     * 门店地址省市区映射关系json
     */
    @NacosValue(value = "${merchant.address.mapping:{\"provinceMapping\":[],\"cityMapping\":[{\"sourceName\":\"北京城区\",\"targetName\":\"北京市\"},{\"sourceName\":\"天津城区\",\"targetName\":\"天津市\"},{\"sourceName\":\"上海城区\",\"targetName\":\"上海市\"},{\"sourceName\":\"重庆城区\",\"targetName\":\"重庆市\"}],\"areaMapping\":[]}}", autoRefreshed = true)
    private String saasMerchantAddressMappingJsonStr;

    public AddressNameMappingDTO querySaasMerchantAddressMapping() {
        if (StringUtils.isBlank(this.saasMerchantAddressMappingJsonStr)) {
            return null;
        }
        AddressNameMappingDTO addressNameMappingDTO = new AddressNameMappingDTO();
        try {
            addressNameMappingDTO = JSON.parseObject(this.saasMerchantAddressMappingJsonStr, AddressNameMappingDTO.class);
        } catch (Exception e) {
            log.error("解析门店地址省市区映射关系json异常", e);
        }
        return addressNameMappingDTO;
    }
}

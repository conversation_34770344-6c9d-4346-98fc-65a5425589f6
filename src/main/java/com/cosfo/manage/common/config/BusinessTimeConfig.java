package com.cosfo.manage.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * @description: 时间配置
 * @author: <PERSON>
 * @date: 2023-09-25
 **/
@Component
@Data
public class BusinessTimeConfig {

    @NacosValue(value = "${tenant.measure.timeout:2}", autoRefreshed = true)
    private long tenantMeasureTimeOut;

}

package com.cosfo.manage.common.config;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.common.util.RedisUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2023-09-19
 * @Description: 飞书群配置信息
 */
@Slf4j
@Configuration
@Data
public class OpenApiConfig implements InitializingBean {

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    /**
     * openApi飞书告警群url
     */
    @NacosValue(value = "${open.api.warn.url:https://open.feishu.cn/open-apis/bot/v2/hook/538aca4f-cc25-4313-aad2-5fb034e901fa}", autoRefreshed = true)
    public String openApiWarnUrl;

    /**
     * openApi飞书告警群url签名
     */
    @NacosValue(value = "${open.api.warn.url.sign:z0pf6AYKrKiLJv2lekOOXf}", autoRefreshed = true)
    public String openApiWarnUrlSign;

    @NacosValue(value = "${open.api.ningji.tenantIds:59}", autoRefreshed = true)
    public Set<Long> openApiNingJiTenantIds;

    @NacosValue(value = "${openapi.default.storeGruop:{4:1086}}", autoRefreshed = true)
    public String storeGruop;

    public Map<Long, Long> getOpenApiDefaultStoreGroupMap() {
        try {
            Map<Long, Long> storeGroupMap = JSON.parseObject(storeGruop, new TypeReference<Map<Long, Long>>() {
            });
            if (CollectionUtil.isEmpty(storeGroupMap)) {
                return Collections.EMPTY_MAP;
            }
            return storeGroupMap;
        } catch (Exception e) {
            log.error("getOpenApiDefaultStoreGroupMap storeGruop:{}", storeGruop, e);
            return Collections.EMPTY_MAP;
        }
    }

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }

    @Override
    public void afterPropertiesSet() {
        log.info("Nacos监听表配置项, application name:{}, :{}", applicationName, JSON.toJSONString(this));
    }

}

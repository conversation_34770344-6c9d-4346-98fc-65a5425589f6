package com.cosfo.manage.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * @Author: fansongsong
 * @Date: 2023-10-18
 * @Description:
 */
@Component
@Data
public class WeChatOaConfig {
    /**
     * 供应商推送消息，每单待配跳转链接
     */
    @NacosValue(value = "${wechat.oa.message.order.jumpurl:https://manage.cosfo.cn/index.html#/order/manage}", autoRefreshed = true)
    private String messageOrderJumpUrl;

    /**
     * 供应商推送消息，退款通知跳转链接
     */
    @NacosValue(value = "${wechat.oa.message.refund.jumpurl:https://manage.cosfo.cn/index.html#/login}", autoRefreshed = true)
    private String messageRefundJumpUrl;

    /**
     * 供应商推送消息，每天待配送整点跳转链接
     */
    @NacosValue(value = "${wechat.oa.message.total.jumpurl:https://manage.cosfo.cn/index.html#/login}", autoRefreshed = true)
    private String messageTotalJumpUrl;
}

package com.cosfo.manage.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import java.util.List;
import javax.annotation.Resource;
import javax.sql.DataSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/16 10:04
 */
@MapperScan(basePackages = {"com.cosfo.manage.report.mapper"}, sqlSessionFactoryRef = "offlineSqlSessionFactory")
@Configuration
public class OfflineDruidConfiguration {

    @Value(value = "${spring.datasource.dynamic.datasource.offline.driver-class-name:}")
    private String driverClassName;
    @Value(value = "${spring.datasource.dynamic.datasource.offline.url:}")
    private String url;
    @Value(value = "${spring.datasource.dynamic.datasource.offline.username:}")
    private String username;
    @Value(value = "${spring.datasource.dynamic.datasource.offline.password:}")
    private String password;
    @Value(value = "${spring.datasource.minIdle:}")
    private int minIdle;
    @Value(value = "${spring.datasource.initialSize:}")
    private int initialSize;
    @Value(value = "${spring.datasource.maxActive:}")
    private int maxActive;
    @Value(value = "${spring.datasource.maxWait:}")
    private int maxWait;
    @Value(value = "${spring.datasource.timeBetweenEvictionRunsMillis:}")
    private int timeBetweenEvictionRunsMillis;
    @Value(value = "${spring.datasource.minEvictableIdleTimeMillis:}")
    private int minEvictableIdleTimeMillis;

//    @Resource
//    private MybatisPlusInterceptor mybatisPlusInterceptor;

    @Bean("offlineDataSource")
    public DataSource druid() {
        System.out.println("生成数据源bean");
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setDbType("mysql");
        druidDataSource.setDriverClassName(driverClassName);
        druidDataSource.setUrl(url);
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMinIdle(minIdle);
        druidDataSource.setMaxActive(maxActive);
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        druidDataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
        return druidDataSource;
    }

    @Resource
    private List<Interceptor> mybatisInterceptors;

    @Bean(name = "offlineSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("offlineDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/offline/*/*.xml"));
        //bean.setPlugins(mybatisPlusInterceptor);
        if (!CollectionUtils.isEmpty(mybatisInterceptors)) {
            bean.setPlugins(mybatisInterceptors.toArray(new Interceptor[]{}));
        }
        return bean.getObject();
    }
}

package com.cosfo.manage.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-04-01
 **/
@Component
@Data
@Slf4j
public class WarningCenterCardConfig {

    @NacosValue(value = "${waring.center.shelf.life.id}", autoRefreshed = true)
    private Long shelfLifeId;

    @NacosValue(value = "${waring.center.inventory.turnover.id}", autoRefreshed = true)
    private Long warningOfInventoryTurnoverId;

    @NacosValue(value = "${waring.center.product.stock.id}", autoRefreshed = true)
    private Long productStockForeWarningId;

    @NacosValue(value = "${waring.center.safety.inventory.id}", autoRefreshed = true)
    private Long safetyInventoryWarningId;
}

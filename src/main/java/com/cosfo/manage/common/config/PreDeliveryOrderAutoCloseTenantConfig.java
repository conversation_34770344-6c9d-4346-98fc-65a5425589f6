package com.cosfo.manage.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2023-09-19
 * @Description: 自动售后配置
 */
@Slf4j
@Configuration
@Data
public class PreDeliveryOrderAutoCloseTenantConfig implements InitializingBean {

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    /**
     * 需要自动发起配送前售后的配置
     */
    @NacosValue(value = "${pre.delivery.auto.close.config:[]}", autoRefreshed = true)
    public String preDeliveryAutoCloseConfig;

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("AutoAfterSaleTenantConfig 配置已更新:{}", newConfig);
    }

    @Override
    public void afterPropertiesSet() {
        log.info("Nacos监听表配置项, application name:{}, :{}", applicationName, JSON.toJSONString(this));
    }

    @Data
    public static class TenantConfig {
        /**
         * 自动发起三方仓订单配送前售后的租户
         */
        private Long tenantId;

        /**
         * 白名单门店id,不会自动发起售后
         */
        private Set<Long> whiteStoreIds;
    }
}

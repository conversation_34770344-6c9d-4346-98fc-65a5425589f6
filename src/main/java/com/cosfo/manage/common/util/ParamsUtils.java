package com.cosfo.manage.common.util;

import cn.hutool.core.bean.BeanUtil;
import org.apache.catalina.util.URLEncoder;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/3 10:38
 */
public class ParamsUtils {

    /**
     * 参数处理
     *
     * @param obj 参数
     * @return map
     */
    public static Map<String, Object> encoderParam(Object obj) {
        if (obj == null) {
            return null;
        }

        Map<String, Object> map = BeanUtil.beanToMap(obj);
        URLEncoder encoder = new URLEncoder();
        map.forEach((k, v) -> {
            if (v instanceof String) {
                map.put(k, encoder.encode((String) v, StandardCharsets.UTF_8));
            }
        });
        return map;
    }
}

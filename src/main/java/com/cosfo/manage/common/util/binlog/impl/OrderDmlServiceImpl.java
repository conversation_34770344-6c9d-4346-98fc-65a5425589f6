package com.cosfo.manage.common.util.binlog.impl;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.context.OrderStatusEnum;
import com.cosfo.manage.common.context.binlog.BinlogEventEnum;
import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.binlog.DbTableDmlService;
import com.cosfo.manage.common.util.binlog.DtsModelHandler;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.wangdiantong.service.WdtOrderService;
import com.cosfo.manage.wechat.service.WeixinShippingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * order表监听
 * @author: xiaowk
 * @time: 2023/7/11 下午4:27
 */
@Slf4j
@Component
public class OrderDmlServiceImpl implements DbTableDmlService {

    @Resource
    private WeixinShippingService weixinShippingService;

    @Resource
    private OrderBusinessService orderBusinessService;

    @Resource
    private WdtOrderService wdtOrderService;

    @Override
    public String getTableDmlName() {
        return DBTableName.COSFO_TABLE_ORDER;
    }

    @Override
    public void tableDml(DtsModelBO dtsModelBo) {
        if (!Objects.equals(BinlogEventEnum.UPDATE.getEvent(), dtsModelBo.getType())) {
            return;
        }
        if (CollectionUtils.isEmpty(dtsModelBo.getData())) {
            return;
        }

        List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData(dtsModelBo);
        for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
            Map<String, String> dataMap = pair.getKey();
            Map<String, String> oldMap = pair.getValue();

            try {
                dealData(dataMap, oldMap);
            } catch (Exception e) {
                log.error("dts消费处理order数据错误， dataMap={}, oldMap={}", JSON.toJSONString(dataMap), JSON.toJSONString(oldMap), e);
            }

            try {
                supplierNotice(dataMap, oldMap);
            } catch (Exception e) {
                log.error("dts消费处理order数据错误，每单待配通知功能， dataMap={}, oldMap={}", JSON.toJSONString(dataMap), JSON.toJSONString(oldMap), e);
            }

            try {
                wdtPushOrder(dataMap, oldMap);
            } catch (Exception e) {
                log.error("dts消费处理order数据错误，旺店通推单， dataMap={}, oldMap={}", JSON.toJSONString(dataMap), JSON.toJSONString(oldMap), e);
            }
        }
    }

    /**
     * 每单待配通知
     * @param dataMap
     */
    public void supplierNotice(Map<String, String> dataMap, Map<String, String> oldMap) {
        Long orderId = Long.valueOf(dataMap.get("id"));
        Integer orderStatus = Optional.ofNullable(dataMap.get("status")).map(status -> Integer.valueOf(status)).orElse(null);
        Integer oldOrderStatus = Optional.ofNullable(oldMap.get("status")).map(status -> Integer.valueOf(status)).orElse(null);
        // 原订单状态不是待配送
        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(oldOrderStatus)) {
            return;
        }

        // 新订单状态为已支付待配送
        if (!OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderStatus)) {
            return;
        }

        orderBusinessService.orderWaitDeliverySupplierNotice(orderId);
    }

    public void wdtPushOrder(Map<String, String> dataMap, Map<String, String> oldMap){
//        Long orderId = Long.valueOf(dataMap.get("id"));
        Long tenantId = Long.valueOf(dataMap.get("tenant_id"));
        Integer warehouseType = Integer.valueOf(dataMap.get("warehouse_type"));
        String orderNo = dataMap.get("order_no");

        Integer orderStatus = Optional.ofNullable(dataMap.get("status")).map(status -> Integer.valueOf(status)).orElse(null);
        Integer oldOrderStatus = Optional.ofNullable(oldMap.get("status")).map(status -> Integer.valueOf(status)).orElse(null);
        // 原订单状态不是待配送
        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(oldOrderStatus)) {
            return;
        }
        // 新订单状态为已支付待配送
        if (!OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderStatus)) {
            return;
        }

        if(!wdtOrderService.canPushOrder(orderNo, tenantId, warehouseType)){
            return;
        }

        wdtOrderService.pushOrder(orderNo);
    }

    private void dealData(Map<String, String> dataMap, Map<String, String> oldMap) {

        Long tenantId = Long.valueOf(dataMap.get("tenant_id"));

        String onlinePayChannelStr = dataMap.get("online_pay_channel");
        if (onlinePayChannelStr == null) {
            return;
        }

        if(oldMap.get("status") != null) {
            Integer oldStatus = Integer.valueOf(oldMap.get("status"));
            // 旧记录status 已配送，不用处理
            if (OrderStatusEnum.ableApplyDeliveredAfterSale(oldStatus)) {
                return;
            }
        }

        Integer status = Integer.valueOf(dataMap.get("status"));
        // 新记录status 非配送状态，不用处理
        if (!OrderStatusEnum.ableApplyDeliveredAfterSale(status)) {
            return;
        }

        Long orderId = Long.valueOf(dataMap.get("id"));

        weixinShippingService.uploadShippingInfoByOrderId(orderId, tenantId);
    }

}

package com.cosfo.manage.common.util.binlog.observer.marketitem;

import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.util.binlog.TableFieldKeys;
import com.cosfo.manage.market.model.po.MarketItemAvailabilityChangeRecord;
import com.cosfo.manage.market.service.MarketItemAvailabilityChangeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class MarketItemOnSaleStatusRecordObserver implements MarketItemObserver {

    @Resource
    private MarketItemAvailabilityChangeRecordService marketItemAvailabilityChangeRecordService;

    @Override
    public void onInsert(Map<String, String> newData) {
        Long tenantId = Long.valueOf(newData.get(TableFieldKeys.TENANT_ID_KEY));

        if (XianmuSupplyTenant.TENANT_ID.equals(tenantId)) {
            log.info("鲜沐商品新增不处理{}", newData);
            return;
        }

        // 新增商品时,记录商品上下架状态
        Integer onSaleStatus = Integer.valueOf(newData.get(TableFieldKeys.ON_SALE_KEY));
        Long itemId = Long.valueOf(newData.get(TableFieldKeys.ID_KEY));
        recordItemOnSale(tenantId, itemId, onSaleStatus, newData);
    }

    @Override
    public void onUpdate(Map<String, String> newData, Map<String, String> oldData) {
        Long tenantId = Long.valueOf(newData.get(TableFieldKeys.TENANT_ID_KEY));
        Long itemId = Long.valueOf(newData.get(TableFieldKeys.ID_KEY));

        if (XianmuSupplyTenant.TENANT_ID.equals(tenantId)) {
            log.info("鲜沐商品新增不处理{}", newData);
            return;
        }

        // 处理商品上下架
        Integer oldOnSaleStatus = Optional.ofNullable(oldData.get(TableFieldKeys.ON_SALE_KEY)).map(Integer::valueOf).orElse(null);
        // 数据未变化
        if (oldOnSaleStatus == null) {
            return;
        }
        Integer newOnSaleStatus = Integer.valueOf(newData.get(TableFieldKeys.ON_SALE_KEY));
        if (!Objects.equals(oldOnSaleStatus, newOnSaleStatus)) {
            recordItemOnSale(tenantId, itemId, newOnSaleStatus, newData);
        }
    }


    private void recordItemOnSale(Long tenantId, Long itemId, Integer onSaleStatus, Map<String, String> newData) {
        log.info("记录商品上下架变化,tenantId:{},itemId:{},onSaleStatus:{}", tenantId, itemId, onSaleStatus);

        OnSaleTypeEnum onSaleType = OnSaleTypeEnum.ON_SALE.getCode().equals(onSaleStatus) ?
                OnSaleTypeEnum.ON_SALE : OnSaleTypeEnum.SOLD_OUT;

        MarketItemAvailabilityChangeRecord record = marketItemAvailabilityChangeRecordService
                .createItemOnSaleChange(tenantId, itemId, onSaleType, LocalDateTime.now());
        marketItemAvailabilityChangeRecordService.saveRecord(record);
    }
}


package com.cosfo.manage.common.util;


import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Encoder;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Random;
import java.util.regex.Pattern;

@Slf4j
public class MD5Util {
    /**
     * 是否包含大写字母
     */
    private static final String reg = "[A-Z]+";
    /**
     * 是否包含小写字母
     */
    private static final String reg2 = "[a-z]+";
    /**
     * 是否包含数字
     */
    private static final String reg3 = "[0-9]+";
    /**
     * 是否包含特殊字符
     */
    private static final String reg4 = "[\\!\\@\\#\\$\\%\\^\\&\\*\\(\\)\\+\\?\\<\\>\\/\\,\\.]+";

    public MD5Util() {
    }

    /**
     * 密码加密
     *
     * @param inStr
     * @return
     */
    public static String string2MD5(String inStr) {
        MessageDigest md5 = null;

        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (Exception var8) {
            log.error("异常信息：{}", var8.getMessage(), var8);
            return "";
        }

        char[] charArray = inStr.toCharArray();
        byte[] byteArray = new byte[charArray.length];

        for (int i = 0; i < charArray.length; ++i) {
            byteArray[i] = (byte) charArray[i];
        }

        byte[] md5Bytes = md5.digest(byteArray);
        StringBuffer hexValue = new StringBuffer();

        for (int i = 0; i < md5Bytes.length; ++i) {
            int val = md5Bytes[i] & 255;
            if (val < 16) {
                hexValue.append("0");
            }

            hexValue.append(Integer.toHexString(val));
        }

        return hexValue.toString();
    }

    /**
     * 密码强度验证
     *
     * @param password
     * @return
     */
    public static Integer passwordLevel(String password) {
        Integer level = 0;
        if (verifyPasswordStrong(password, reg)) {
            level += 1;
        }
        if (verifyPasswordStrong(password, reg2)) {
            level += 1;
        }
        if (verifyPasswordStrong(password, reg3)) {
            level += 1;
        }
        if (verifyPasswordStrong(password, reg4)) {
            level += 1;
        }
        return level;
    }

    private static boolean verifyPasswordStrong(String password, String rule) {
        Pattern p = Pattern.compile(rule);
        if (p.matcher(password).find()) {
            return true;
        }
        return false;
    }

    /**
     * 生成随机数
     * @return
     */
    public static String createRandomNum() {
        String token = (System.currentTimeMillis() + new Random().nextInt(999999999)) + "";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte md5[] =  md.digest(token.getBytes());
            BASE64Encoder encoder = new BASE64Encoder();
            return encoder.encode(md5);
        } catch (NoSuchAlgorithmException e) {
            // TODO Auto-generated catch block
            log.error("异常信息：{}", e.getMessage(), e);
        }
        return null;
    }
}

package com.cosfo.manage.common.util.binlog.impl;

import com.cosfo.manage.common.context.RefundEnum;
import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.merchant.service.MerchantStoreTradeSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-10-25
 **/
@Slf4j
@Component("refundDmlServiceImpl")
public class RefundDmlServiceImpl extends TradeDmlServiceImpl {

    @Resource
    private MerchantStoreTradeSummaryService merchantStoreTradeSummaryService;

    @Override
    protected String getTableName() {
        return DBTableName.COSFO_TABLE_REFUND;
    }

    @Override
    protected void generateTradeSummaryForBusiness(Long businessId) {
        log.info("开始处理退款单：[{}]门店汇总数据", businessId);
        merchantStoreTradeSummaryService.generateStoreDimensionRefundSummary(businessId);
    }

    // Override the common logic to customize the condition for generating trade summaries
    @Override
    protected boolean shouldGenerateTradeSummary(Integer refundStatus) {
        return Objects.equals(refundStatus, RefundEnum.Status.SUCCESS.getStatus());
    }

    @Override
    protected String getChangedFiled() {
        return "refund_status";
    }
}

package com.cosfo.manage.common.util;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.json.JSONUtil;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import com.cosfo.manage.tenant.model.vo.MenuPurviewVO;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class AuthTreeUtil {

    public static List<List<Integer>> purviewIds(List<MenuPurviewDTO> purviews) {
        Set<Integer> nonLeafNodeIds = new HashSet<>();
        Map<Integer, MenuPurviewDTO> purviewMap = new HashedMap();
        for (MenuPurviewDTO purview : purviews) {
            if (purview.getParentId() != 0) {
                nonLeafNodeIds.add(purview.getParentId());
            }
            purviewMap.put(purview.getId().intValue(), purview);
        }

        return purviews.stream()
                .filter(purview -> !nonLeafNodeIds.contains(purview.getId().intValue()))
                .map(purview -> getParentIds(purviewMap, purview.getId().intValue()))
                .collect(Collectors.toList());
    }

    public static List<Integer> getParentIds(Map<Integer, MenuPurviewDTO> purviewMap, Integer id) {
        LinkedList<Integer> parentIds = new LinkedList<>();
        while (purviewMap.containsKey(id)) {
            parentIds.addFirst(id);
            id = purviewMap.get(id).getParentId();
        }
        return parentIds;
    }

    public static List<MenuPurviewVO> convertTree(List<MenuPurviewDTO> menuPreviews) {
        if (CollectionUtils.isEmpty(menuPreviews)) {
            return new ArrayList<>();
        }
        TreeNodeConfig config = new TreeNodeConfig();
        config.setWeightKey("weight");
        config.setParentIdKey("parentId");
        config.setDeep(8);
        config.setChildrenKey("childMenuPurviewVOS");
        config.setIdKey("id");
        List<Tree<String>> treeList = TreeUtil.build(menuPreviews, "0", config, (treeNode, tree) -> {
            tree.setId(treeNode.getId().toString());
            tree.setParentId(treeNode.getParentId().toString());
            tree.setName(treeNode.getMenuName());
            tree.setWeight(treeNode.getWeight());
            tree.putExtra("description", treeNode.getDescription());
            tree.putExtra("type", treeNode.getType());
            tree.putExtra("url", treeNode.getUrl());
            tree.putExtra("purviewName", treeNode.getPurviewName());
            tree.putExtra("menuName", treeNode.getMenuName());
        });
        return JSONUtil.toList(JSONUtil.toJsonStr(treeList), MenuPurviewVO.class);
    }


    public static void main(String[] args) {
        List<MenuPurviewDTO> purviews = new ArrayList<>();
        purviews.add(build(1L, 0));
        purviews.add(build(2L, 1));
        purviews.add(build(3L, 2));
        purviews.add(build(4L, 2));
        System.out.println(purviewIds(purviews));

    }

    public static MenuPurviewDTO build(Long id, Integer parentId) {
        MenuPurviewDTO authMenuPurview = new MenuPurviewDTO();
        authMenuPurview.setId(id);
        authMenuPurview.setParentId(parentId);
        return authMenuPurview;
    }
}
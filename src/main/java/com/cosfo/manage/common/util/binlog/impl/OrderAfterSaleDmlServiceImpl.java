package com.cosfo.manage.common.util.binlog.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.config.WeChatOaConfig;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.context.RedisKeyEnum;
import com.cosfo.manage.common.context.RefundEnum;
import com.cosfo.manage.common.context.TenantAccountConfig;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.common.context.binlog.BinlogEventEnum;
import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.RedisUtils;
import com.cosfo.manage.common.util.binlog.DbTableDmlService;
import com.cosfo.manage.common.util.binlog.DtsModelHandler;
import com.cosfo.manage.facade.AuthWechatFacade;
import com.cosfo.manage.facade.MessageServiceFacade;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.order.mapper.payment.RefundMapper;
import com.cosfo.manage.order.model.po.payment.Refund;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.tenant.model.po.TenantAccountSupplierMapping;
import com.cosfo.manage.tenant.service.TenantAccountSupplierMappingService;
import com.cosfo.message.client.enums.JumpUrlTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.TemplateWechatEnum;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-10-18
 * @Description:
 */
@Slf4j
@Component
public class OrderAfterSaleDmlServiceImpl implements DbTableDmlService {

    @Resource
    private AuthWechatFacade wechatFacade;
    @Resource
    private TenantAccountSupplierMappingService tenantAccountSupplierMappingService;
    @Resource
    private WeChatOaConfig weChatOaConfig;
    @Resource
    private MessageServiceFacade messageServiceFacade;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    @Override
    public String getTableDmlName() {
        return DBTableName.ORDER_AFTER_SALE;
    }

    @Override
    public void tableDml(DtsModelBO dtsModelBo) {
        if (!Objects.equals(BinlogEventEnum.UPDATE.getEvent(), dtsModelBo.getType())) {
            return;
        }
        if (CollectionUtils.isEmpty(dtsModelBo.getData())) {
            return;
        }

        List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData(dtsModelBo);
        for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
            Map<String, String> dataMap = pair.getKey();
            Map<String, String> oldMap = pair.getValue();

            Integer oldRecordVersion = Optional.ofNullable(oldMap.get("record_version")).map(e -> Integer.valueOf(e)).orElse(null);
            Integer newRecordVersion = Optional.ofNullable(dataMap.get("record_version")).map(e -> Integer.valueOf(e)).orElse(null);
            String afterSaleOrderNo = Optional.ofNullable(dataMap.get("after_sale_order_no")).orElse(null);
            if(newRecordVersion != null && oldRecordVersion != null && !newRecordVersion.equals(oldRecordVersion)){
                log.info("更新历史数据，不处理。afterSaleOrderNo={}", afterSaleOrderNo);
                continue;
            }


            Integer newStatus = Optional.ofNullable(dataMap.get("status")).map(status -> Integer.valueOf(status)).orElse(null);
            Integer oldStatus = Optional.ofNullable(oldMap.get("status")).map(status -> Integer.valueOf(status)).orElse(null);

            // 售后单最新状态为已成功
            if (OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue().equals(newStatus) && !OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue().equals(oldStatus)) {
                dealOrderAfterSaleData(dataMap, oldMap);
            }
        }
    }

    public void dealOrderAfterSaleData(Map<String, String> dataMap, Map<String, String> oldMap) {
        Long orderAfterSaleId = Long.valueOf(dataMap.get("id"));
        Long orderId = Long.valueOf(dataMap.get("order_id"));
        Long tenantId = Long.valueOf(dataMap.get("tenant_id"));
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        // 其他直配供应商订单一定是无仓类型
        if (!WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(orderResp.getWarehouseType())) {
            return;
        }
        // 存在支付时间的才进行通知
        if (Objects.isNull(orderResp.getPayTime())) {
            return;
        }
        // 判断是否所有订单项全部完成配送前退款
        boolean orderRefund = checkOrderRefund(orderId);
        if (!orderRefund) {
            return;
        }
        String cacheKey = RedisKeyEnum.CM00004.join(orderId);
        if (Objects.nonNull(redisUtils.get(cacheKey))) {
            log.info("退款通知已发送,订单ID:{}", orderId);
            return;
        }
        Pair<List<TenantAccountResultResp>, List<OrderItemVO>> pair = tenantAccountSupplierMappingService.queryNeedNotifyTenantAccount(orderId, tenantId, TenantAccountConfig.BussinessMsgTypeEnum.REFUND);
        List<TenantAccountResultResp> tenantAccounts = pair.getKey();
        List<OrderItemVO> orderItemVOS = pair.getValue();
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            return;
        }

        List<Long> tenantAccountIds = tenantAccounts.stream().map(TenantAccountResultResp::getId).collect(Collectors.toList());
        List<TenantAccountSupplierMapping> tenantAccountSupplierMappings = tenantAccountSupplierMappingService.queryByTenantAccountIds(tenantId, tenantAccountIds);
        Map<Long, List<Long>> accountSupplierMap = tenantAccountSupplierMappings.stream().collect(Collectors.groupingBy(TenantAccountSupplierMapping::getAccountId,
                Collectors.mapping(TenantAccountSupplierMapping::getSupplierId, Collectors.toList())));

        // 查询openId信息
        List<String> phones = tenantAccounts.stream().map(TenantAccountResultResp::getPhone).collect(Collectors.toList());
        Map<String, String> phoneMap = wechatFacade.queryAuthMapRespByPhones4FTGYL(phones, tenantId);


        // 查询退款成功时间
        OrderAfterSaleResp orderAfterSale = orderAfterSaleQueryFacade.getOrderAfterSaleById(orderAfterSaleId);
        Refund refund = refundMapper.selectByAfterSaleId(tenantId, orderAfterSaleId);
        LocalDateTime successTime = orderAfterSale.getUpdateTime();
        if (Objects.nonNull(refund) && RefundEnum.Status.SUCCESS.getStatus().equals(refund.getRefundStatus())) {
            log.info("退款通知订单ID:{},推送成功时间为退款单Id：{}数据", orderId, refund.getId());
            successTime = Optional.ofNullable(refund.getSuccessTime()).orElse(refund.getUpdateTime());
        }

        // 组装参数，调用消息中心发送消息
        String keyword = TemplateWechatEnum.TemplateCode.SAAS_ORDER_SUPPLIER_REFUND_CODE.getKeyword();
        MessageBodyReq messageBodyReq = new MessageBodyReq();
        messageBodyReq.setContentType(MessageContentTypeEnum.NORMAL.getType());
        messageBodyReq.setTemplateCode(TemplateWechatEnum.TemplateCode.SAAS_ORDER_SUPPLIER_REFUND_CODE.getCode());
        messageBodyReq.setJumpUrlTypeEnum(JumpUrlTypeEnum.PAGE);
        messageBodyReq.setJumpUrl(weChatOaConfig.getMessageRefundJumpUrl());

        // 依次推送
        for (TenantAccountResultResp tenantAccount : tenantAccounts) {
            String openId = phoneMap.get(tenantAccount.getPhone());
            // 不存在openId，日志输出
            if (StringUtils.isEmpty(openId)) {
                log.info("退款通知订单,订单ID:{},推送过滤，不存在openId的账号信息为：{}", orderId, JSON.toJSONString(tenantAccount));
                continue;
            }
            List<Long> supplierIds = accountSupplierMap.get(tenantAccount.getId());
            if (CollectionUtil.isEmpty(supplierIds)) {
                log.info("退款通知订单,订单ID:{},推送过滤，不存在租户供应商映射信息：{}", orderId, JSON.toJSONString(tenantAccount));
                continue;
            }

            BigDecimal supplierPrice = orderItemVOS.stream().filter(orderItemVO -> supplierIds.contains(orderItemVO.getSupplierTenantId())).map(
                    snapshotDTO -> NumberUtil.mul(Optional.ofNullable(snapshotDTO.getSupplyPrice()).orElse(BigDecimal.ZERO), snapshotDTO.getAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            String data = String.format(keyword, NumberUtil.decimalFormat("0.00", supplierPrice), TimeUtils.convertToString(successTime, TimeUtils.FORMAT), orderResp.getOrderNo());
            messageBodyReq.setData(data);
            messageServiceFacade.batchSendFTWechatOaMessage(tenantId, Collections.singletonList(openId), messageBodyReq);
        }

        // 设置退款通知缓存
        redisUtils.set(cacheKey, NumberConstant.ONE, TimeUnit.HOURS.toMillis(6));
        log.info("退款通知订单,订单ID:{},推送成功", orderId);
    }

    /**
     * 校验订单是否全部子项售后，且都是配送前售后
     * @param orderId
     * @return
     */
    private boolean checkOrderRefund(Long orderId) {
        OrderAfterSaleQueryReq queryReq = new OrderAfterSaleQueryReq();
        queryReq.setOrderIds(Lists.newArrayList(orderId));
        queryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(queryReq);
        // 过滤掉换货、补发
        List<OrderAfterSaleResp> afterSaleList = orderAfterSaleResps.stream().filter(el ->
                (!el.getServiceType().equals(OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue()) && !el.getServiceType().equals(OrderAfterSaleServiceTypeEnum.RESEND.getValue()))).collect(Collectors.toList());
        boolean allNotSend = afterSaleList.stream().allMatch(orderAfterSaleDTO -> OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(orderAfterSaleDTO.getAfterSaleType()));
        if (!allNotSend) {
            return false;
        }

        Map<Long, List<OrderAfterSaleResp>> orderAfterSaleMap = afterSaleList.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));

        // 退款回调先更新售后单，再更新订单，所以没有直接根据查询订单状态判断
        // 是否全部订单子项已处理
        boolean isUpdateOrderStatus = false;
        if (!CollectionUtils.isEmpty(orderAfterSaleMap)) {
            // 查询所有订单项
            List<OrderItemAndSnapshotResp> orderItemAndSnapshotResps = orderItemQueryFacade.queryByOrderId(orderId);
            isUpdateOrderStatus = true;
            // 判断是否所有订单项已退款
            for (OrderItemAndSnapshotResp orderItem : orderItemAndSnapshotResps) {
                List<OrderAfterSaleResp> orderAfterSaleDTOS = orderAfterSaleMap.get(orderItem.getOrderItemId());
                if (CollectionUtils.isEmpty(orderAfterSaleDTOS)) {
                    isUpdateOrderStatus = false;
                    break;
                }
                Integer refundReceivedAmount = 0;
                Integer otherAmount = 0;
                for (OrderAfterSaleResp orderAfterSaleDTO : orderAfterSaleDTOS) {
                    Boolean receivedRefundFlag = OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(orderAfterSaleDTO.getServiceType(), orderAfterSaleDTO.getAfterSaleType());
                    if (receivedRefundFlag) {
                        refundReceivedAmount += orderAfterSaleDTO.getAmount();
                        continue;
                    }
                    otherAmount += orderAfterSaleDTO.getAmount();
                }
                Integer totalApplyAmount = refundReceivedAmount + otherAmount * orderItem.getMaxAfterSaleAmount();
                if (totalApplyAmount.compareTo(orderItem.getMaxAfterSaleAmount() * orderItem.getAmount()) != 0) {
                    isUpdateOrderStatus = false;
                    break;
                }
            }
        }
        return isUpdateOrderStatus;
    }
}

package com.cosfo.manage.common.util.binlog.observer.productstock;

import com.cosfo.manage.common.util.binlog.TableFieldKeys;
import com.cosfo.manage.market.model.po.MarketItemAvailabilityChangeRecord;
import com.cosfo.manage.market.service.MarketItemAvailabilityChangeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class ProductStockRecordObserver implements ProductStockObserver {

    @Resource
    private MarketItemAvailabilityChangeRecordService marketItemAvailabilityChangeRecordService;


    @Override
    public void onInsert(Map<String, String> newData) {
        long tenantId = Long.parseLong(newData.get(TableFieldKeys.TENANT_ID_KEY));
        long skuId = Long.parseLong(newData.get(TableFieldKeys.SKU_ID_KEY));

        // 如果插入时库存为0,则不记录为"售罄"
        int quantity = Integer.parseInt(newData.get(TableFieldKeys.QUANTITY_KEY));
        if (quantity == 0) {
            log.info("库存预警表插入时库存为0,不记录为售罄 tenantId: {}, skuId: {}", tenantId, skuId);
            return;
        }
        insertRecord(tenantId, skuId);
    }

    @Override
    public void onUpdate(Map<String, String> newData, Map<String, String> oldData) {
        long tenantId = Long.parseLong(newData.get(TableFieldKeys.TENANT_ID_KEY));
        long skuId = Long.parseLong(newData.get(TableFieldKeys.SKU_ID_KEY));

        Integer oldQuantity = Optional.ofNullable(oldData.get(TableFieldKeys.QUANTITY_KEY)).map(Integer::parseInt).orElse(null);
        // 数据未变化
        if (oldQuantity == null) {
            return;
        }
        int newQuantity = Integer.parseInt(newData.get(TableFieldKeys.QUANTITY_KEY));
        if ((oldQuantity > 0 && newQuantity == 0) || (oldQuantity == 0 && newQuantity > 0)) {
            insertRecord(tenantId, skuId);
        }
    }


    private void insertRecord(long tenantId, long skuId) {
        log.info("商品库存状态变化,开始记录 tenantId: {}, skuId: {}", tenantId, skuId);
        List<MarketItemAvailabilityChangeRecord> records =
                marketItemAvailabilityChangeRecordService.createHasGoodItemStockChange(tenantId, skuId, LocalDateTime.now());
        marketItemAvailabilityChangeRecordService.batchSaveRecords(records);
    }
}

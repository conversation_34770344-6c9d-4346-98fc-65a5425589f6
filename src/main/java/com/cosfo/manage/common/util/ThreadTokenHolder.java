package com.cosfo.manage.common.util;

import com.alibaba.fastjson.JSONArray;

import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * @Package: com.cosfo.manage.common.util
 * @Description: token
 * @author: <EMAIL>
 * @Date: 2022/05/04
 */
@Slf4j
public class ThreadTokenHolder {
    private static final ThreadLocal<LoginContextInfoDTO> tokenHolder = new ThreadLocal<>();

    public static LoginContextInfoDTO getMerchantInfoDTO() {
        return tokenHolder.get();
    }

    public static Long getAuthUserId() {
        return tokenHolder.get() == null ? null : tokenHolder.get().getAuthUserId();
    }
    public static Long getTenantId() {
        return tokenHolder.get() == null ? null : tokenHolder.get().getTenantId();
    }

    public static LoginContextInfoDTO getNoNullToken() {
        LoginContextInfoDTO loginContextInfoDTO = tokenHolder.get();

        if (loginContextInfoDTO == null) {
            throw new DefaultServiceException("用户信息不存在");
        }

        log.error("获取token信息：" + JSONArray.toJSONString(loginContextInfoDTO));

        return loginContextInfoDTO;
    }

    public static void setToken(LoginContextInfoDTO loginContextInfoDTO) {
        tokenHolder.remove();
        tokenHolder.set(loginContextInfoDTO);
    }

    public static void clearMerchantInfo(){
        tokenHolder.remove();
    }
}

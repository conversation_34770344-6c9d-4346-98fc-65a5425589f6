package com.cosfo.manage.common.util;

import cn.hutool.core.util.StrUtil;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/5/8 18:22
 */
public class PasswordUtil {

    public static String createPassword(String phone) {
        // 获取年份
        String year = TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT_YEAR_ONLY);
        // 获取手机后四位
        String substring = RandomStringUtils.randomNumeric(4);
        if(StrUtil.isNotBlank(phone)) {
            substring = phone.substring(phone.length() - NumberConstants.FOUR);
        }
        StringBuffer stringBuffer = new StringBuffer(year).append(StringConstants.CHARACTER).append(substring);
        // 生成随机四位大小写字符串
        String randomStr = RandomStringUtils.randomAlphabetic(4);
        while (!StringUtils.checkAccountPassword(stringBuffer.toString() + randomStr)) {
            randomStr = RandomStringUtils.randomAlphabetic(4);
        }
        stringBuffer.append(randomStr);
        return stringBuffer.toString();
    }


    /**
     * 根据账户信息生成密码
     * 规则：
     * - 手机号格式：租户ID_手机号后6位
     * - 邮箱格式：租户ID_@符号前的内容
     * - 默认情况：租户ID_用户名后6位
     */
    public static String generatePassword(MerchantStoreAccount accountEntity) {
        Long tenantId = accountEntity.getTenantId();
        String username = StrUtil.isBlank(accountEntity.getUsername()) ? accountEntity.getPhone() : accountEntity.getUsername();

        if (StrUtil.isBlank(username)) {
            username = accountEntity.getPhone();
        }

        // 判断是否为手机号格式
        if (StringUtils.isPhoneNumber(username)) {
            // 手机号格式：租户ID_手机号后6位
            String phoneSuffix = username.length() >= 6 ? username.substring(username.length() - 6) : username;
            return tenantId + "_" + phoneSuffix;
        }

        // 判断是否为邮箱格式
        if (StringUtils.isEmail(username)) {
            // 邮箱格式：租户ID_@符号前的内容
            String emailPrefix = username.substring(0, username.indexOf('@'));
            return tenantId + "_" + emailPrefix;
        }

        // 默认情况：按照手机号逻辑处理（取用户名后6位）
        String usernameSuffix = username.length() >= 6 ? username.substring(username.length() - 6) : username;
        return tenantId + "_" + usernameSuffix;
    }
}

package com.cosfo.manage.common.util.qiNiu;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/11 10:18
 */
//public class UploadTokenFactory {
//
//    public UploadTokenFactory() {
//    }
//
//    public static Map<String, String> createToken(String fileName, long expires) {
//        Map<String, String> result = new HashMap();
//        Auth auth = Auth.create(QiNiuConstants.ACCESS_KEY, QiNiuConstants.SECRET_KEY);
//        String token = auth.uploadToken(QiNiuConstants.DEFAULT_BUCKET, fileName, expires, (StringMap)null);
//        result.put("token", token);
//        result.put("key", fileName);
//        return result;
//    }
//}

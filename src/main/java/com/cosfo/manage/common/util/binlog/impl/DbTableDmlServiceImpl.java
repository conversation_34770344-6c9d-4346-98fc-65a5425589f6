package com.cosfo.manage.common.util.binlog.impl;

import com.cosfo.manage.common.context.binlog.BinlogEventEnum;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.binlog.DbTableDmlService;
import com.cosfo.manage.common.util.binlog.DtsModelHandler;
import com.cosfo.manage.common.util.binlog.observer.BinlogObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public abstract class DbTableDmlServiceImpl implements DbTableDmlService {

    @Override
    public void tableDml(DtsModelBO dtsModelBO) {
        if (CollectionUtils.isEmpty(dtsModelBO.getData())) {
            return;
        }

        switch (BinlogEventEnum.valueOf(dtsModelBO.getType())) {
            case INSERT:
                DtsModelHandler.getOnlyNewData(dtsModelBO).forEach(this::onInsert);
                break;
            case UPDATE:
                DtsModelHandler.getAlignedData(dtsModelBO).forEach(pair -> onUpdate(pair.getKey(), pair.getValue()));
                break;
            default:
                break;
        }
    }

    protected abstract List<? extends BinlogObserver> getObservers();

    protected void onInsert(Map<String, String> newData) {
        getObservers().forEach(observer -> {
            try {
                observer.onInsert(newData);
            } catch (Exception e) {
                log.error("处理新增异常,observer:{}", observer.getClass().getSimpleName(), e);
            }
        });
    }

    protected void onUpdate(Map<String, String> newData, Map<String, String> oldData) {
        getObservers().forEach(observer -> {
            try {
                observer.onUpdate(newData, oldData);
            } catch (Exception e) {
                log.error("处理更新异常,observer:{}", observer.getClass().getSimpleName(), e);
            }
        });
    }
}

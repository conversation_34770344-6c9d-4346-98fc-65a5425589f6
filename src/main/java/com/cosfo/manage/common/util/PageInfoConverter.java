package com.cosfo.manage.common.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.cosfo.message.client.common.page.resp.PageResp;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class PageInfoConverter<T,R> {


    private PageInfoConverter() {
        // 无需实现
    }

    public static <T,R> Page<T> toPage(PageInfo<R> pageInfo, Function<T,R> dataConvert) {
        if (pageInfo == null) {
            return null;
        }
        Page<T> page = new Page<T>();
// Not mapped TO fields:
// records
// total
// size
// current
// orders
// optimizeCountSql
// searchCount
// optimizeJoinOfCountSql
// countId
// maxLimit
// Not mapped FROM fields:
// pageNum
// pageSize
// size
// startRow
// endRow
// pages
// prePage
// nextPage
// isFirstPage
// isLastPage
// hasPreviousPage
// hasNextPage
// navigatePages
// navigatepageNums
// navigateFirstPage
// navigateLastPage
// total
// list
        return page;
    }
    public static <T,R> PageInfo<R> toPageInfo(Page<T> page, Function<T,R> dataConvert) {
        PageInfo<R> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(Long.valueOf(page.getCurrent()).intValue());
        pageInfo.setPageSize(Long.valueOf(page.getSize()).intValue());
        pageInfo.setPages(Long.valueOf(page.getPages()).intValue());
        pageInfo.setTotal(Long.valueOf(page.getTotal()).intValue());
        pageInfo.setList(page.getRecords().stream().map(dataConvert).collect(Collectors.toList()));
        return pageInfo;
    }

    public static <T> PageInfo<T> toPageInfo(com.github.pagehelper.Page page, List<T> list) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setPages(page.getPages());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    public static <T,R> PageInfo<R> toPageInfo(com.github.pagehelper.Page<T> page, Function<T,R> dataConvert) {
        PageInfo<R> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(Long.valueOf(page.getPageNum()).intValue());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setPages(Long.valueOf(page.getPages()).intValue());
        pageInfo.setTotal(Long.valueOf(page.getTotal()).intValue());
        pageInfo.setList(page.getResult().stream().map(dataConvert).collect(Collectors.toList()));
        return pageInfo;
    }

    public static <T> PageInfo<T> toPageInfoTransfer(PageInfo page, List<T> list) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(Long.valueOf(page.getPageNum()).intValue());
        pageInfo.setPageSize(Long.valueOf(page.getPageSize()).intValue());
        pageInfo.setPages(Long.valueOf(page.getPages()).intValue());
        pageInfo.setTotal(Long.valueOf(page.getTotal()).intValue());
        pageInfo.setList(list);
        pageInfo.setHasNextPage (page.isHasNextPage ());
        return pageInfo;
    }


    public static <T> PageInfo<T> pageRespToPageInfo(PageResp page, List<T> list) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(Long.valueOf(page.getPageNum()).intValue());
        pageInfo.setPageSize(Long.valueOf(page.getPageSize()).intValue());
        pageInfo.setPages(Long.valueOf(page.getPages()).intValue());
        pageInfo.setTotal(Long.valueOf(page.getTotal()).intValue());
        pageInfo.setHasNextPage (!page.getIsLastPage ());
        pageInfo.setList(list);
        return pageInfo;
    }
}

package com.cosfo.manage.common.util;

import com.cosfo.manage.common.constant.Constants;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/8/31 18:29
 * @Description:
 */
public class PriceUtil {

    public static String buildPriceRange(BigDecimal maxPrice,BigDecimal minPrice){
        String priceRange = "";
        if (Objects.isNull(maxPrice) || Objects.isNull(minPrice)){
            priceRange = Constants.RUNG;
        }else if (BigDecimal.ZERO.compareTo(maxPrice) == 0) {
            priceRange = Constants.RUNG;
        } else if (minPrice.compareTo(maxPrice) == 0) {
            priceRange = minPrice.toString();
        } else {
            priceRange = minPrice + Constants.RUNG + maxPrice;
        }
        return priceRange;
    }

//    public static String buildPriceRangeForSupplyPrice(BigDecimal maxPrice,BigDecimal minPrice){
//        String priceRange = "";
//        if (Objects.isNull(maxPrice) || Objects.isNull(minPrice)) {
//            priceRange = Constants.RUNG;
//        } else if (BigDecimal.ZERO.compareTo(minPrice) == 0) {
//            priceRange = Constants.RUNG;
//        } else if (minPrice.compareTo(maxPrice) == 0) {
//            priceRange = minPrice.setScale(2, RoundingMode.HALF_UP).toString();
//        } else {
//            priceRange = minPrice.setScale(2, RoundingMode.HALF_UP) + Constants.RUNG + maxPrice.setScale(2, RoundingMode.HALF_UP);
//        }
//        return priceRange;
//    }
}

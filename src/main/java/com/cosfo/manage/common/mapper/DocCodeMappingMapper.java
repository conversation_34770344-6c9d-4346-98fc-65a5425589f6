package com.cosfo.manage.common.mapper;

import com.cosfo.manage.common.model.po.DocCodeMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 映射关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Mapper
public interface DocCodeMappingMapper extends BaseMapper<DocCodeMapping> {

    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<DocCodeMapping> docCodeMappingList);
}

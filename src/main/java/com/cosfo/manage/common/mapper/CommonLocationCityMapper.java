package com.cosfo.manage.common.mapper;

import com.cosfo.manage.common.model.po.CommonLocationCity;
import com.cosfo.manage.common.model.vo.CommonLocationCityVO;
import com.cosfo.manage.common.model.vo.LocationProvinceCityVO;
import com.cosfo.manage.system.model.dto.CommonLocationCityDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CommonLocationCityMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(CommonLocationCity record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(CommonLocationCity record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    CommonLocationCity selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CommonLocationCity record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(CommonLocationCity record);

    /**
     * 查询省份下的城市
     * @param provinceId
     * @return
     */
    List<CommonLocationCity> queryCitiesByProvinceId(Long provinceId);

    /**
     * 根据城市Id批量查询
     *
     * @param cityIds
     * @return
     */
    List<CommonLocationCityDTO> queryByCityIds(@Param("cityIds") List<Long> cityIds);

    /**
     * 根据城市Id批量查询
     *
     * @param cityNames
     * @return
     */
    List<CommonLocationCityDTO> queryByCityNames(@Param("cityNames") Set<String> cityNames);

    /**
     * 根据省份Id查询市
     *
     * @param provinceId
     * @return
     */
    List<CommonLocationCityVO> selectByProvinceId(@Param("provinceId") Long provinceId);

    /**
     * 根据城市Id批量城市及省份信息
     *
     * @param cityIds
     * @return
     */
    List<LocationProvinceCityVO> queryWithProvinceByCityIds(@Param("cityIds") Set<Long> cityIds);

    /**
     * 查询所有城市信息
     * @return
     */
    List<CommonLocationCityDTO> queryAllCommonLocationCity();
}

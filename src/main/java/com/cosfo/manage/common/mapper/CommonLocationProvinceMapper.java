package com.cosfo.manage.common.mapper;

import com.cosfo.manage.common.model.po.CommonLocationProvince;
import com.cosfo.manage.common.model.vo.CommonLocationProvinceVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonLocationProvinceMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(CommonLocationProvince record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(CommonLocationProvince record);

    /**
     * 查询
     * @param id
     * @return
     */
    CommonLocationProvince selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CommonLocationProvince record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(CommonLocationProvince record);

    /**
     * 查詢所有省市
     *
     * @return
     */
    List<CommonLocationProvinceVO> listAll();
}

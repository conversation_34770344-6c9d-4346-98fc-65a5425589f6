package com.cosfo.manage.common.exception;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/6  14:25
 */
@Deprecated
@Data
public class DefaultServiceException extends RuntimeException {
    /**
     * 异常编码枚举
     */
    private Integer code;

    /**
     * 异常描述
     */
    private String message;

    public DefaultServiceException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public DefaultServiceException(String message) {
        super(message);
        this.message = message;
    }

    public DefaultServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    public DefaultServiceException(Throwable cause) {
        super(cause);
    }

    public DefaultServiceException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}

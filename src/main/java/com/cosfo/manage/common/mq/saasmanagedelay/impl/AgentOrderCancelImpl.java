package com.cosfo.manage.common.mq.saasmanagedelay.impl;

import com.cosfo.manage.agentorder.service.PlanOrderService;
import com.cosfo.manage.common.constant.MqTagConstant;
import com.cosfo.manage.common.mq.saasmanagedelay.factory.SaasManageDelayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: monna.chen
 * @Date: 2024/2/23 10:15
 * @Description:
 */
@Service
@Slf4j
public class AgentOrderCancelImpl implements SaasManageDelayService {

    @Resource
    private PlanOrderService planOrderService;

    @Override
    public String getTagName() {
        return MqTagConstant.AGENT_ORDER_CANCEL;
    }

    @Override
    public void handle(Object body) {
        log.info("收到tag:{}的消息，消息体内容：{}", MqTagConstant.AGENT_ORDER_CANCEL, body);
        String agentOrderNo = body.toString();
        planOrderService.autoCancelPanOrder(agentOrderNo);
    }
}

package com.cosfo.manage.common.easy.excel.helper;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.util.ExcelUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.input.BasePageInput;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.function.Function;

/**
 * @author: xiaowk
 * @time: 2024/1/18 下午6:48
 */
@Slf4j
public class ExcelExportHelper {

    public static <P extends BasePageInput, R> DownloadCenterOssRespDTO generateExcelFile(Integer pageSize, String filename, ExcelTypeEnum excelTypeEnum, P input, Function<P, PageInfo<R>> function) {
        // 1、表格处理
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(ExcelExportHelper.class, excelTypeEnum.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        input.setPageSize(pageSize);
        int pageIndex = 1;
        //查询数据
        PageInfo<R> pageInfo;
        do {
            input.setPageIndex(pageIndex);
            pageInfo = function.apply(input);

            excelWriter.fill(pageInfo.getList(), fillConfig, writeSheet);
            pageIndex++;
        } while (pageInfo.isHasNextPage());

        excelWriter.finish();

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(filename, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            try {
                if(Files.exists(Paths.get(filePath))) {
                    Files.delete(Paths.get(filePath));
                }
            } catch (IOException e) {
                throw new DefaultServiceException("文件删除失败！");
            }
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }
}

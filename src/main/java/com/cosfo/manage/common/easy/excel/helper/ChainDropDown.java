package com.cosfo.manage.common.easy.excel.helper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 级联下拉框
 */
@AllArgsConstructor
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ChainDropDown {

    public static final String ROOT_KEY = "root";
    /**
     * 是否是根目录
     */
    private boolean rootFlag = true;

    /**
     * sheet 名称
     */
    private String typeName;

    /**
     * 行下标
     */
    private Integer rowIndex = 0;

    private Map<String, List<String>> dataMap = new HashMap<>();


}




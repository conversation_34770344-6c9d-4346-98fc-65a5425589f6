package com.cosfo.manage.common.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.config.WeChatOaConfig;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.context.RedisKeyEnum;
import com.cosfo.manage.common.context.TenantAccountConfig;
import com.cosfo.manage.common.util.RedisUtils;
import com.cosfo.manage.facade.AuthWechatFacade;
import com.cosfo.manage.facade.MessageServiceFacade;
import com.cosfo.manage.facade.ordercenter.OrderStatisticsQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.tenant.dao.TenantAccountSupplierMappingDao;
import com.cosfo.manage.tenant.model.dto.TenantAccountBussinessMsgQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountReceiveMsgQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig;
import com.cosfo.manage.tenant.model.po.TenantAccountReceiveMsgSwitch;
import com.cosfo.manage.tenant.model.po.TenantAccountSupplierMapping;
import com.cosfo.manage.tenant.service.TenantAccountMsgService;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.JumpUrlTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.TemplateWechatEnum;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.ordercenter.client.resp.SupplierOrderTotalResp;
import com.google.common.base.Functions;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-10-18
 * @Description:供应商统计订单发送消息定时任务
 */
@Component
@Slf4j
public class SupplierOrderTotalNotifyTask extends XianMuJavaProcessorV2 {

    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
    @Resource
    private AuthWechatFacade wechatFacade;
    @Resource
    private WeChatOaConfig weChatOaConfig;
    @Resource
    private MessageServiceFacade messageServiceFacade;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private TenantAccountMsgService tenantAccountMsgService;
    @Resource
    private TenantAccountSupplierMappingDao tenantAccountSupplierMappingDao;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private OrderStatisticsQueryFacade orderStatisticsQueryFacade;

    @Override
    public ProcessResult processResult(XmJobInput context) throws InterruptedException {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("供应商统计订单发送消息定时任务-start");
        int pushHour = LocalDateTime.now().getHour();

        // 查询存在租户供应商映射的租户
        List<Long> tenantIdList = tenantAccountSupplierMappingDao.listAllTenantId();
        for (Long tenantId : tenantIdList) {
            // 查询单租户下所有配送员账号
            List<TenantAccountSupplierMapping> tenantAccountSupplierMappings = tenantAccountSupplierMappingDao.queryByTenantId(tenantId);
            if (CollectionUtil.isEmpty(tenantAccountSupplierMappings)) {
                continue;
            }
            Set<Long> tenantAccountIds = tenantAccountSupplierMappings.stream().map(TenantAccountSupplierMapping::getAccountId).collect(Collectors.toSet());

            // 查询当前小时,满足开启汇总提醒业务消息
            TenantAccountBussinessMsgQueryDTO tenantAccountBussinessMsgConfigQueryDTO = TenantAccountBussinessMsgQueryDTO.builder()
                    .availableStatus(TenantAccountConfig.AvailableStatusEnum.YES.getCode())
                    .pushHour(pushHour)
                    .tenantId(tenantId)
                    .tenantAccountIds(tenantAccountIds)
                    .bussinessType(TenantAccountConfig.BussinessMsgTypeEnum.WAIT_DELIVERY_TOTAL.getCode()).build();
            List<TenantAccountBussinessMsgConfig> configList = tenantAccountMsgService.queryTenantAccountBussinessMsgConfig(tenantAccountBussinessMsgConfigQueryDTO);
            if (CollectionUtil.isEmpty(configList)) {
                continue;
            }

            tenantAccountIds = configList.stream().map(TenantAccountBussinessMsgConfig::getTenantAccountId).collect(Collectors.toSet());
            // 查询符合账号消息通知开启状态
            TenantAccountReceiveMsgQueryDTO tenantAccountReceiveMsgQueryDTO = TenantAccountReceiveMsgQueryDTO.builder()
                    .tenantId(tenantId).tenantAccountIds(tenantAccountIds)
                    .channelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode)
                    .availableStatus(TenantAccountConfig.AvailableStatusEnum.YES.getCode())
                    .channelType(ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT.getValue()).build();
            List<TenantAccountReceiveMsgSwitch> switchList = tenantAccountMsgService.queryTenantAccountReceiveMsgSwitch(tenantAccountReceiveMsgQueryDTO);
            if (CollectionUtil.isEmpty(switchList)) {
                continue;
            }

            // 过滤出满足条件的租户账号数据
            tenantAccountIds = switchList.stream().map(TenantAccountReceiveMsgSwitch::getTenantAccountId).collect(Collectors.toSet());
            Set<Long> finalTenantAccountIds = tenantAccountIds;
            tenantAccountSupplierMappings = tenantAccountSupplierMappings.stream().filter(tenantAccountSupplierMapping -> finalTenantAccountIds.contains(tenantAccountSupplierMapping.getAccountId())).collect(Collectors.toList());
            // 执行发送逻辑
            sendMessage(tenantId, tenantAccountSupplierMappings);
            TimeUnit.MILLISECONDS.sleep(NumberConstant.TWO_HUNDRED);
        }

        log.info("供应商统计订单发送消息定时任务-end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }

    /**
     * 供应商租户处理发送消息逻辑
     * @param tenantId
     * @param list
     */
    private void sendMessage(Long tenantId, List<TenantAccountSupplierMapping> list) {
        // 查询商城信息
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);

        // 查询供应商配送员账号信息
        List<Long> tenantAccountIdList = list.stream().map(TenantAccountSupplierMapping::getAccountId).distinct().collect(Collectors.toList());
        TenantAccountQueryReq tenantAccountQueryReq = new TenantAccountQueryReq();
        tenantAccountQueryReq.setIdList(tenantAccountIdList);
        tenantAccountQueryReq.setTenantId(tenantId);
        List<TenantAccountResultResp> tenantAccountList = userCenterTenantAccountFacade.getTenantAccounts(tenantAccountQueryReq);
        if (CollectionUtils.isEmpty(tenantAccountList)) {
            log.info("每天待配送整点已发送,租户供应商未配置 供应商配送员信息 tenantId:{}", tenantId);
            return;
        }

        // 查询账号信息对应的openId信息
        List<String> phones = tenantAccountList.stream().map(TenantAccountResultResp::getPhone).distinct().collect(Collectors.toList());
        Map<Long, TenantAccountResultResp> tenantAccountResultRespMap = tenantAccountList.stream().collect(Collectors.toMap(TenantAccountResultResp::getId, Function.identity(), (v1, v2) -> v1));
        Map<String, String> phoneOpenIdMap = wechatFacade.queryAuthMapRespByPhones4FTGYL(phones, tenantId);

        // 查询供应商统计信息
        List<Long> supplierIds = list.stream().map(TenantAccountSupplierMapping::getSupplierId).distinct().collect(Collectors.toList());
        List<SupplierOrderTotalResp> supplierOrderTotalResps = orderStatisticsQueryFacade.querySupplierOrderNeedNotifyByIds(tenantId, supplierIds);
        Map<Long, SupplierOrderTotalResp> supplierOrderTotalMap = supplierOrderTotalResps.stream().collect(Collectors.toMap(SupplierOrderTotalResp::getSupplierId, Functions.identity(), (v1, v2) -> v1));

        Map<Long, List<TenantAccountSupplierMapping>> accountSupplierMappingMap = list.stream().collect(Collectors.groupingBy(TenantAccountSupplierMapping::getAccountId));
        for (Map.Entry<Long, List<TenantAccountSupplierMapping>> entry : accountSupplierMappingMap.entrySet()) {
            // 单配送员执行统计消息发送
            dealSingleSupplierTotalMessage(tenantId, tenant, tenantAccountResultRespMap, phoneOpenIdMap, supplierOrderTotalMap, entry);
            log.info("供应商统计订单,执行统计完成 tenantId:{} accountId:{}，mappingList:{}", tenantId, entry.getKey(), JSON.toJSONString(entry.getValue()));
        }

    }

    private void dealSingleSupplierTotalMessage(Long tenantId, TenantResultResp tenant, Map<Long, TenantAccountResultResp> tenantAccountResultRespMap, Map<String, String> phoneOpenIdMap, Map<Long, SupplierOrderTotalResp> supplierOrderTotalMap, Map.Entry<Long, List<TenantAccountSupplierMapping>> entry) {
        Long accountId = entry.getKey();
        String cacheKey = RedisKeyEnum.CM00005.join(accountId);
        if (Objects.nonNull(redisUtils.get(cacheKey))) {
            log.info("每天待配送整点已发送,tenantId:{},accountId:{}", tenantId, accountId);
            return;
        }
        List<TenantAccountSupplierMapping> tenantAccountSupplierMappings = entry.getValue();
        Set<Long> supplierIds = tenantAccountSupplierMappings.stream().map(TenantAccountSupplierMapping::getSupplierId).collect(Collectors.toSet());

        // 查询供应商配送员账号信息
        TenantAccountResultResp tenantAccountResultResp = tenantAccountResultRespMap.get(accountId);
        if (Objects.isNull(tenantAccountResultResp)) {
            log.info("每天待配送整点 accountId:{},推送过滤，配送员账号信息不存在", accountId);
            return;
        }

        // 目标openId
        String openId = phoneOpenIdMap.get(tenantAccountResultResp.getPhone());
        if (Objects.isNull(openId)) {
            log.info("每天待配送整点 accountId:{},推送过滤，找不到openId信息", accountId);
            return;
        }

        // 获取供应商配送员在该租户下配置的供应商的 待配送订单总数、待配送订单总实付金额
        BigDecimal supplierOrderTotalPrice = BigDecimal.ZERO;
        Set<Long> orderIds = Sets.newHashSet();
        List<SupplierOrderTotalResp> supplierOrderTotalRespList = supplierIds.stream().map(supplierId -> supplierOrderTotalMap.get(supplierId)).collect(Collectors.toList());
        for (SupplierOrderTotalResp supplierOrderTotalResp : supplierOrderTotalRespList) {
            if (Objects.isNull(supplierOrderTotalResp) || CollectionUtil.isEmpty(supplierOrderTotalResp.getOrderIds())) {
                continue;
            }
            orderIds.addAll(supplierOrderTotalResp.getOrderIds());
            supplierOrderTotalPrice = NumberUtil.add(supplierOrderTotalResp.getSupplierOrderTotalPrice(), supplierOrderTotalPrice);
        }

        // 组装参数，调用消息中心发送消息
        Integer supplierOrderNumber = orderIds.size();
        String keyword = TemplateWechatEnum.TemplateCode.SAAS_ORDER_SUPPLIER_TOTAL_CODE.getKeyword();
        String tenantName = Optional.ofNullable(tenant).map(TenantResultResp::getTenantName).orElse(StringUtils.EMPTY);
        String data = String.format(keyword, supplierOrderNumber.toString(), NumberUtil.decimalFormat("0.00", supplierOrderTotalPrice), tenantName);
        MessageBodyReq messageBodyReq = new MessageBodyReq();
        messageBodyReq.setContentType(MessageContentTypeEnum.NORMAL.getType());
        messageBodyReq.setData(data);
        messageBodyReq.setTemplateCode(TemplateWechatEnum.TemplateCode.SAAS_ORDER_SUPPLIER_TOTAL_CODE.getCode());
        messageBodyReq.setJumpUrlTypeEnum(JumpUrlTypeEnum.PAGE);
        messageBodyReq.setJumpUrl(weChatOaConfig.getMessageTotalJumpUrl());
        messageServiceFacade.batchSendFTWechatOaMessage(tenantId, Collections.singletonList(openId), messageBodyReq);
        // 设置已通知缓存
        redisUtils.set(cacheKey, NumberConstant.ONE, TimeUnit.MINUTES.toMillis(30));
        log.info("每天待配送整点发送成功,tenantId:{},accountId:{}", tenantId, accountId);
    }

}

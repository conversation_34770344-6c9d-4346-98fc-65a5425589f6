package com.cosfo.manage.common.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.agentorder.service.PlanOrderService;
import com.cosfo.manage.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/1/3 16:39
 * @Description: 每分钟执行一次，查询待生成的计划单，执行自动创建订单
 */
@Component
@Slf4j
public class PlanOrderCreateOrderTask extends XianMuJavaProcessorV2 {
    @Resource
    private PlanOrderService planOrderService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String instanceParameters = context.getInstanceParameters();
        log.info("PlanOrderCreateOrderTask 执行任务入参：{}", instanceParameters);

        if (!StringUtils.isEmpty(instanceParameters)) {
            List<String> planOrderNos = JSONArray.parseArray(instanceParameters, String.class);
            planOrderService.planOrderCreateOrder(planOrderNos);
        } else {
            planOrderService.planOrderCreateOrder(null);
        }
        return new ProcessResult(true);
    }
}

package com.cosfo.manage.common.task;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.bill.mapper.PaymentItemMapper;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.po.PaymentItem;
import com.cosfo.manage.bill.model.query.PaymentConditionQuery;
import com.cosfo.manage.common.context.OrderStatusEnum;
import com.cosfo.manage.common.context.PaymentEnum.Status;
import com.cosfo.manage.common.context.PaymentTradeTypeEnum;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import com.cosfo.manage.wechat.service.WeixinShippingService;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 微信支付的订单，开通了【发货信息管理】功能，调接口自动上传发货信息
 *
 * https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/order-shipping/order-shipping.html#%E4%B8%80%E3%80%81%E5%8F%91%E8%B4%A7%E4%BF%A1%E6%81%AF%E5%BD%95%E5%85%A5%E6%8E%A5%E5%8F%A3
 * @author: xiaowk
 * @time: 2023/7/7 下午2:05
 */
@Component
@Slf4j
public class WechatPayUploadShippingInfoTask extends XianMuJavaProcessorV2 {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private WeixinShippingService weixinShippingService;
    @Resource
    private OrderQueryFacade orderQueryFacade;

    public static final Integer BATCH_SIZE = 100;


    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("微信支付订单调接口自动上传发货信息 task start, 参数：{}", jobContext.getJobParameters());

        UploadShippingInfoTaskParameter parameter = JSON.parseObject(jobContext.getJobParameters(), UploadShippingInfoTaskParameter.class);
        try {
            runTask(parameter);
        } catch (Exception e) {
            log.error("微信支付订单调接口自动上传发货信息 error.", e);
        }

        log.info("微信支付订单调接口自动上传发货信息 task end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }

    private void runTask(UploadShippingInfoTaskParameter parameter) {
        Integer intervalDays = 5;
        if (parameter.getIntervalDays() != null) {
            intervalDays = parameter.getIntervalDays();
        }

        List<Long> tenantIds  = weixinShippingService.queryOpenShippingTenantIds();
        if(CollectionUtils.isEmpty(tenantIds)){
            return;
        }

        List<TenantAuthConnection> tenantAuthConnectionList = tenantAuthConnectionMapper.selectTenantsByTenantIds(tenantIds);
        Map<Long, String> tenantId2AppIdMap = tenantAuthConnectionList.stream().collect(Collectors.toMap(TenantAuthConnection::getTenantId, TenantAuthConnection::getAppId, (v1, v2) -> v1));

        LocalDateTime currentDate = LocalDateTime.now();
        LocalDateTime todayStartTime = LocalDateTimeUtil.beginOfDay(currentDate);
        LocalDateTime startTime = todayStartTime.minusDays(intervalDays);
        if (parameter.getJobStartTime() != null && startTime.isBefore(parameter.getJobStartTime())) {
            startTime = parameter.getJobStartTime();
        }

        PaymentConditionQuery query = new PaymentConditionQuery();
        query.setMaxId(0L);
        query.setSize(BATCH_SIZE);
        query.setTenantIds(tenantIds);
        query.setStartTime(startTime);
        query.setEndTime(currentDate);
        query.setStatusList(Lists.newArrayList(Status.SUCCESS.getCode()));
        // 支付通道 0、微信 1、汇付
//        query.setOnlinePayChannel(0);
        // 小程序微信直连、小程序汇付微信
        query.setTradeTypeList(Arrays.asList(PaymentTradeTypeEnum.JSAPI.getDesc(), PaymentTradeTypeEnum.T_MINIAPP.getDesc()));
        while (true) {
            List<Payment> paymentList = paymentMapper.queryNeedShippingPaymentByCondition(query);
            if (CollectionUtils.isEmpty(paymentList)) {
                break;
            }

            checkOrderAndUploadShippingInfo(paymentList, tenantId2AppIdMap);

            query.setMaxId(paymentList.get(paymentList.size() - 1).getId());
        }
    }

    private void checkOrderAndUploadShippingInfo(List<Payment> paymentList, Map<Long, String> tenantId2AppIdMap) {

        List<Long> paymentIds = paymentList.stream().map(e -> e.getId()).collect(Collectors.toList());
        List<PaymentItem> paymentItemList = paymentItemMapper.batchQueryByPaymentIds(paymentIds);

        // paymentId到orderIdList映射
        Map<Long, List<Long>> paymentId2OrderIdMap = paymentItemList.stream()
                .collect(Collectors.groupingBy(PaymentItem::getPaymentId, Collectors.mapping(PaymentItem::getOrderId, Collectors.toList())));

        List<Long> orderIdList = paymentItemList.stream().map(e -> e.getOrderId()).distinct().collect(Collectors.toList());

        List<OrderResp> orderResps = orderQueryFacade.queryByIds(orderIdList);
        Map<Long, OrderResp> orderDTOMap = orderResps.stream().collect(Collectors.toMap(OrderResp::getId, Function.identity(), (v1, v2) -> v1));

        for (Payment payment : paymentList) {
            try {
                List<Long> orderIds = paymentId2OrderIdMap.get(payment.getId());
                for (Long orderId : orderIds) {
                    OrderResp orderDTO = orderDTOMap.get(orderId);
                    // 已配送
                    if (OrderStatusEnum.ableApplyDeliveredAfterSale(orderDTO.getStatus())) {
                        // 上传发货信息
                        weixinShippingService.uploadShippingInfo(payment, orderId, tenantId2AppIdMap.get(orderDTO.getTenantId()));
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("微信上传发货信息错误, payment={}", payment, e);
            }
        }
    }

    @Data
    public static class UploadShippingInfoTaskParameter {

        /**
         * 扫描区间时间
         */
        private Integer intervalDays;

        private LocalDateTime jobStartTime;

        private List<Long> tenantIds;
    }
}

package com.cosfo.manage.common.task;


import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.context.ExcelTemplateType;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.SpringContextUtil;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.InputStream;

@Component
@Slf4j
public class ExcelPushQiniuTasks extends XianMuJavaProcessorV2 {
    @Resource
    private CommonService commonService;

    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("分布式调度任务开始------调度任务:{}", jobContext.getJobParameters());

        String instanceParameters = jobContext.getInstanceParameters();
        // {"1":"import_pos_store.xlsx"}
        if (StringUtils.isEmpty(instanceParameters)) {
            return new ProcessResult(false, "instanceParameters is empty");
        }
        ExcelTemplateType excelTemplateType = ExcelTemplateType.getNameByOrder(Integer.valueOf(instanceParameters));
        if (excelTemplateType == null) {
            return new ProcessResult(false, "instanceParameters is error");
        }
        log.info(excelTemplateType.name().toLowerCase() + ".xlsx");
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), excelTemplateType.name().toLowerCase() + ".xlsx");
        String filePath = ExcelUtils.tempExcelFilePath();


        try (Workbook wb = WorkbookFactory.create(templateFileInputStream)) {
            wb.write(new FileOutputStream(filePath));
        }

        String url = excelTemplateType.getUrl();
        String filename =excelTemplateType.getDesc() + ".xlsx";
        if (!SpringContextUtil.isPro()) {
            filename = "dev" + Constants.SLASH + filename;
            url =excelTemplateType.getDevUrl();
        }

        // 同名的url， 先删除，再更新
        QiNiuUtils.deleteFile(new String[]{url});

        String qiNiuFilePath = QiNiuUtils.uploadFile(filePath, filename);
        log.info("qiNiuFilePath导入模板 path={}", qiNiuFilePath);

        //删除临时文件
        commonService.deleteFile(filePath);
        return new ProcessResult(true);
    }
}

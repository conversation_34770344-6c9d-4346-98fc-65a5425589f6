package com.cosfo.manage.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.bill.service.PrepaymentAccountService;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ScheduledPrepaymentAccountSnapshotTask extends XianMuJavaProcessorV2 {

    @Resource
    private PrepaymentAccountService prepaymentAccountService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("每日预付账户快照task-start");
        prepaymentAccountService.snapshotAccount();
        log.info("每日预付账户快照task-end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }
}

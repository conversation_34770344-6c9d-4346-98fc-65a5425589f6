package com.cosfo.manage.common.task.jindie;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.jindie.config.JinDieConfig;
import com.cosfo.manage.jindie.service.JindieSalesReturnService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 销售退货单推送金蝶
 */
@Component
@Slf4j
public class OrderAfterSaleSync extends XianMuJavaProcessorV2 {

    @Resource
    private JinDieConfig jinDieConfig;
    @Resource
    private JindieSalesReturnService jindieSalesReturnService;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    // 包含的售后类型: 配送后售后
    private static final Integer AFTER_SALE_TYPE = OrderAfterSaleTypeEnum.DELIVERED.getType();
    // 包含的售后服务类型: 退货退款(3), 退货退款录入账单(4), 退货退款录入余额(8), 退货退款录入积分(9)
    private static final List<Integer> INCLUDE_SERVICE_TYPES = Arrays.asList(3, 4, 8, 9);

    /**
     * 任务参数.
     */
    private static class JobParams {
        private final LocalDateTime startTime = LocalDateTime.now().minusDays(1);
        private final LocalDateTime endTime = LocalDateTime.now();
    }

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        JobParams jobParams = Optional.ofNullable(context.getInstanceParameters())
                .map(it -> JSON.parseObject(it, JobParams.class)).orElseGet(JobParams::new);

        log.info("开始执行销售退货单推送金蝶任务, jobParams: {}", jobParams);

        try {
            List<OrderAfterSaleResp> aftersaleOrders = this.queryAfterSaleOrders(
                    jinDieConfig.getZcwTenantId(),
                    jobParams.startTime,
                    jobParams.endTime);
            // 批量推送售后单到金蝶
            int successCount = jindieSalesReturnService.batchPushSalesReturnToJindie(aftersaleOrders);

            log.info("销售退货单推送金蝶任务执行完成, 成功推送数量: {}", successCount);
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("销售退货单推送金蝶任务执行异常", e);
            return new ProcessResult(false, "销售退货单推送金蝶任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 查询指定时间范围内符合条件的售后单
     *
     * @param tenantId  租户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 售后单列表
     */
    private List<OrderAfterSaleResp> queryAfterSaleOrders(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        log.info("查询售后单，时间范围: {} - {}", startTime, endTime);

        OrderAfterSaleQueryReq queryReq = new OrderAfterSaleQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setFinishTimeStart(startTime);
        queryReq.setFinishTimeEnd(endTime);
        queryReq.setAfterSaleType(AFTER_SALE_TYPE);
        queryReq.setServiceType(INCLUDE_SERVICE_TYPES);

        List<OrderAfterSaleResp> afterSaleOrders = orderAfterSaleQueryFacade.queryList(queryReq);
        log.info("查询到售后单数量: {}", afterSaleOrders != null ? afterSaleOrders.size() : 0);

        return afterSaleOrders != null ? afterSaleOrders : new ArrayList<>();
    }
}

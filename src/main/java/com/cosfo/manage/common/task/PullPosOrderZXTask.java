package com.cosfo.manage.common.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.context.DocCodeChannelTypeEnum;
import com.cosfo.manage.common.context.DocCodeTargetTypeEnum;
import com.cosfo.manage.common.model.dto.DocCodeMappingDTO;
import com.cosfo.manage.common.service.DocCodeMappingService;
import com.cosfo.manage.keruyun.service.KryServiceZX;
import com.cosfo.manage.keruyun.vo.*;
import com.cosfo.manage.pos.domain.PosOrderDomainService;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.*;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述: 智想
 *  拉取客如云 - zx的pos订单
 */
@Component
@Slf4j
public class PullPosOrderZXTask extends XianMuJavaProcessorV2 {
    /**
     * 入参 ex：
          {
             "startTime": "2021-06-02 04:30:00",
             "endTime": "2021-10-02 04:30:00",
              "tenantId": "2"
     }
     **/

    @Resource
    private KryServiceZX kryServiceZX;
    
    @Resource
    private DocCodeMappingService docCodeMappingService;

    @Resource
    private PosOrderDomainService posOrderDomainService;


    @NacosValue(value = "${kryZX.tenant.ids}", autoRefreshed = true)
    private String kryTenantId;
    @Override
//    public ProcessResult processResult(XmJobInput context) throws Exception {
//        log.info("客如云 - zx订单拉取开始------");
//        //前一天
//        LocalDateTime now = LocalDateTime.now ();
//        LocalDateTime previousDayb = now.minusDays(6);
//
//
//        String startTime = LocalDateTimeUtil.format (previousDayb,"yyyy-MM-dd HH:mm:ss");
//        String endTime = LocalDateTimeUtil.format (now,"yyyy-MM-dd HH:mm:ss");
//        String outStoreCode = null;
//
//        String finalStartTime = startTime;
//        String finalEndTime = endTime;
//        if(StringUtils.isBlank (outStoreCode)){
//            //查询租户对应的客如云 - zx门店映射
//            Long tenantId = 2L;
//            List<DocCodeMappingDTO> storeList = docCodeMappingService.selectByTargetCodeAndType (Collections.emptyList (), DocCodeTargetTypeEnum.STORE.getCode (), tenantId, DocCodeChannelTypeEnum.KE_RU_YUN.getCode ());
//            storeList.forEach (store -> savePosOrder4Store(store, finalStartTime, finalEndTime));
//        }
//        return new ProcessResult(true);
//    }
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("客如云 - zx订单拉取开始------");
        //前一天
        LocalDateTime now = LocalDateTime.now ();
        LocalDateTime previousDay = now.minusDays(1);
        if (ObjectUtil.isNull(context)) {
            return new ProcessResult(true);
        }
        log.info ("kryTenantIds={}",kryTenantId);
        List<Long> tenantIds = Splitter.on(",").splitToStream(kryTenantId).map(Long::valueOf).collect(Collectors.toList());

        String startTime = LocalDateTimeUtil.format (previousDay,"yyyy-MM-dd HH:mm:ss");
        String endTime = LocalDateTimeUtil.format (now,"yyyy-MM-dd HH:mm:ss");

        log.info("客如云 - zx订单拉取开始，tenantIds={}，startTime={},endTime={}",tenantIds,startTime,endTime);

        if(StringUtils.isNotBlank (context.getInstanceParameters ())){
            Map<String, String> map = JSON.parseObject (context.getInstanceParameters (), Map.class);

            String tenantIdKey = "tenantId";
            if(StringUtils.isNotBlank (map.get (tenantIdKey))) {
                tenantIds = Collections.singletonList (Long.valueOf (map.get (tenantIdKey)));
            }

            String startTimeKey = "startTime";
            if(StringUtils.isNotBlank (map.get (startTimeKey))) {
                startTime = map.get (startTimeKey);
            }

            String endTimeKey = "endTime";
            if(StringUtils.isNotBlank (map.get (endTimeKey))) {
                endTime = map.get (endTimeKey);
            }
            log.info("客如云 - zx订单拉取开始 手动输入了参数，tenantIds={}，startTime={},endTime={}",tenantIds,startTime,endTime);
        }

        if(CollectionUtil.isEmpty (tenantIds)){
            log.info("客如云 - zx订单拉取结束 tenantIds = null------");
            return new ProcessResult(true);
        }

        String finalStartTime = startTime;
        String finalEndTime = endTime;
        tenantIds.forEach (tenantId -> {
            //查询租户对应的客如云 - zx门店映射
            List<DocCodeMappingDTO> storeList = docCodeMappingService.selectByTargetCodeAndType (Collections.emptyList (), DocCodeTargetTypeEnum.STORE.getCode (), tenantId, DocCodeChannelTypeEnum.KE_RU_YUN.getCode ());
            storeList.forEach (store -> {
                //1 = 堂食，因为请求参数值不同所以按照类型区分
                savePosOrder4Store(store, finalStartTime, finalEndTime,1);
                //2 = 其他
                savePosOrder4Store(store, finalStartTime, finalEndTime,2);
            });
        });
        return new ProcessResult(true);
    }

    private void savePosOrder4Store(DocCodeMappingDTO store, String startTime, String endTime,Integer type) {
        boolean hasNext = true;
        int pageIndex = 1;
        while(hasNext){
            Long shopId = null;
            try {
                shopId = Long.valueOf (store.getOutCode ());
                KryZXPageDataVO kryZXPageDataVO = kryServiceZX.orderList (shopId, startTime, endTime, pageIndex,type);
                if(ObjectUtil.isNotNull (kryZXPageDataVO)) {
                    batchDeal (kryZXPageDataVO.getList (), store);
                    hasNext = kryZXPageDataVO.hasNext (kryZXPageDataVO.getTotalCount (), 100, pageIndex);
                    pageIndex = pageIndex + 1;
                }else{
                    hasNext = false;
                }
            }catch (Exception e){
                hasNext = false;
                log.error ("请求第{}页pos订单异常，shopid={},startTime={},endTime={}", pageIndex, shopId, startTime, endTime,e);
            }
        }
    }

    private void batchDeal(List<KryZXOrderListVO> items, DocCodeMappingDTO store) {
        if(CollectionUtil.isNotEmpty (items)){
            items.forEach (orderIdList-> {
                try{
                    KryZXOrderDetailDataVO dataVO = kryServiceZX.orderDetail (Long.valueOf (store.getOutCode ()), orderIdList.getOrderId ());
                    posOrderDomainService.savePosOrderFromKryZX (dataVO, store.getTenantId (), store.getTargetCode ());
                } catch (Exception e) {
                    log.error ("请求pos订单详情异常,跳过，shopid={},orderId={}", store.getOutCode (), orderIdList.getOrderId (),e);
                }
            });
        }else{
            log.info ("batchDeal 订单记录为空，不处理");
        }
    }
}
//package com.cosfo.manage.common.task;
//
//
//import com.alibaba.schedulerx.worker.SchedulerxWorker;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class SchedulerxConfig {
//
//    @Value(value = "${spring.schedulerx2.endpoint}")
//    private String endpoint;
//    @Value(value = "${spring.schedulerx2.namespace}")
//    private String namespace;
//    @Value(value = "${spring.schedulerx2.groupId}")
//    private String groupId;
//    @Value(value = "${spring.schedulerx2.appKey}")
//    private String appKey;
//
//
//    @Bean
//    public void initSchedulerXWorker() throws Exception {
//        SchedulerxWorker schedulerxWorker = new SchedulerxWorker();
//        schedulerxWorker.setEndpoint(endpoint);
//        schedulerxWorker.setNamespace(namespace);
//        schedulerxWorker.setGroupId(groupId);
//        schedulerxWorker.setAppKey(appKey);
//        schedulerxWorker.init();
//    }
//}

package com.cosfo.manage.common.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.context.DocCodeChannelTypeEnum;
import com.cosfo.manage.common.context.DocCodeTargetTypeEnum;
import com.cosfo.manage.common.model.dto.DocCodeMappingDTO;
import com.cosfo.manage.common.service.DocCodeMappingService;
import com.cosfo.manage.keruyun.service.KryServiceOnPos;
import com.cosfo.manage.keruyun.vo.KryOrderDetailVO;
import com.cosfo.manage.keruyun.vo.KryOrderListVO;
import com.cosfo.manage.keruyun.vo.KryPageVO;
import com.cosfo.manage.pos.domain.PosOrderDomainService;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.*;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述:ONpos
 *  拉取客如云的pos订单
 */
@Component
@Slf4j
public class PullPosOrderTask extends XianMuJavaProcessorV2 {
    /**
     * 入参 ex：
          {
              "startTime": "2022222",
              "endTime": "2022222",
              "tenantId": "2",
              "outStoreCode": "2022222"
          }
     **/

    @Resource
    private KryServiceOnPos kryServiceOnPos;
    
    @Resource
    private DocCodeMappingService docCodeMappingService;

    @Resource
    private PosOrderDomainService posOrderDomainService;

    @NacosValue(value = "${kry.tenant.ids}", autoRefreshed = true)
    private String kryTenantId;
    @Override
//    public ProcessResult processResult(XmJobInput context) throws Exception {
//        log.info("客如云订单拉取开始------");
//        //前一天
//        LocalDateTime previousDay = LocalDateTime.now ();
//        LocalDateTime previousDayb = LocalDateTime.now ().minusDays(6);
//
//        if (ObjectUtil.isNull(context)) {
//            return new ProcessResult(true);
//        }
//        List<Long> tenantIds = Splitter.on(",").splitToStream(kryTenantId).map(Long::valueOf).collect(Collectors.toList());
//        long startTime = previousDayb.with(LocalTime.MIN).toInstant(ZoneOffset.UTC).toEpochMilli();;
//        long endTime = previousDay.with(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli();
//        String outStoreCode = null;
//        log.info("客如云订单拉取开始，tenantIds={}，startTime={},endTime={}",tenantIds,startTime,endTime);
//
//        if(CollectionUtil.isEmpty (tenantIds)){
//            log.info("客如云订单拉取结束 tenantIds = null------");
//            return new ProcessResult(true);
//        }
//
//        Long finalStartTime = startTime;
//        Long finalEndTime = endTime;
//        tenantIds.forEach (tenantId -> {
//            if(StringUtils.isBlank (outStoreCode)){
//                //查询租户对应的客如云门店映射
//                List<DocCodeMappingDTO> storeList = docCodeMappingService.selectByTargetCodeAndType (Collections.emptyList (), DocCodeTargetTypeEnum.STORE.getCode (), tenantId, DocCodeChannelTypeEnum.KE_RU_YUN.getCode ());
//                storeList.forEach (store -> savePosOrder4Store(store, finalStartTime, finalEndTime));
//            }
//        });
//        return new ProcessResult(true);
//    }
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("客如云订单拉取开始------");
        //前一天
        LocalDateTime previousDay = LocalDateTime.now ().minusDays(1);

        if (ObjectUtil.isNull(context)) {
            return new ProcessResult(true);
        }
        log.info ("kryTenantIds={}",kryTenantId);
        List<Long> tenantIds = Splitter.on(",").splitToStream(kryTenantId).map(Long::valueOf).collect(Collectors.toList());

        ZoneId zoneId = ZoneId.systemDefault();

        long startTime = previousDay.with(LocalTime.MIN).atZone(zoneId).toInstant().toEpochMilli();
        long endTime = previousDay.with(LocalTime.MAX).atZone(zoneId).toInstant().toEpochMilli();
        String outStoreCode;
        log.info("客如云订单拉取开始，tenantIds={}，startTime={},endTime={}",tenantIds,startTime,endTime);

        if(StringUtils.isNotBlank (context.getInstanceParameters ())){
            Map<String, String> map = JSON.parseObject (context.getInstanceParameters (), Map.class);
            String outStoreCodeKey = "outStoreCode";
            outStoreCode = map.get (outStoreCodeKey);

            String tenantIdKey = "tenantId";
            if(StringUtils.isNotBlank (map.get (tenantIdKey))) {
                tenantIds = Collections.singletonList (Long.valueOf (map.get (tenantIdKey)));
            }

            String startTimeKey = "startTime";
            if(StringUtils.isNotBlank (map.get (startTimeKey))) {
                startTime = Long.parseLong (map.get (startTimeKey));
            }

            String endTimeKey = "endTime";
            if(StringUtils.isNotBlank (map.get (endTimeKey))) {
                endTime = Long.parseLong (map.get (endTimeKey));
            }
            log.info("客如云订单拉取开始 手动输入了参数，tenantIds={}，startTime={},endTime={}",tenantIds,startTime,endTime);
        } else {
            outStoreCode = "";
        }

        if(CollectionUtil.isEmpty (tenantIds)){
            log.info("客如云订单拉取结束 tenantIds = null------");
            return new ProcessResult(true);
        }

        Long finalStartTime = startTime;
        Long finalEndTime = endTime;
        tenantIds.forEach (tenantId -> {
            if(StringUtils.isBlank (outStoreCode)){
                //查询租户对应的客如云门店映射
                List<DocCodeMappingDTO> storeList = docCodeMappingService.selectByTargetCodeAndType (Collections.emptyList (), DocCodeTargetTypeEnum.STORE.getCode (), tenantId, DocCodeChannelTypeEnum.KE_RU_YUN.getCode ());
                storeList.forEach (store -> savePosOrder4Store(store, finalStartTime, finalEndTime));
            }else{
                List<DocCodeMappingDTO> storeList = docCodeMappingService.selectByOutCodeAndType (Collections.singleton (outStoreCode), DocCodeTargetTypeEnum.STORE.getCode (), DocCodeChannelTypeEnum.KE_RU_YUN.getCode (), tenantId);
                storeList.forEach (store -> savePosOrder4Store(store, finalStartTime, finalEndTime));
            }
        });
        return new ProcessResult(true);
    }

    private void savePosOrder4Store(DocCodeMappingDTO store, Long startTime, Long endTime) {
        boolean hasNext = true;
        int pageIndex = 1;
        while(hasNext){
            Long shopId = null;
            try {
                shopId = Long.valueOf (store.getOutCode ());
                KryPageVO<KryOrderListVO> pageVO = kryServiceOnPos.orderList (shopId, startTime, endTime, pageIndex);
                if(ObjectUtil.isNotNull (pageVO)) {
                    batchDeal (pageVO.getItems (), store);
                    hasNext = pageVO.hasNext (pageVO.getTotalRows (), 100, pageIndex);
                    pageIndex = pageIndex + 1;
                }else{
                    hasNext = false;
                }
            }catch (Exception e){
                hasNext = false;
                log.error ("请求第{}页pos订单异常，shopid={},startTime={},endTime={}", pageIndex, shopId, startTime, endTime,e);
            }
        }
    }

    private void batchDeal(List<KryOrderListVO> items,DocCodeMappingDTO store) {
        if(CollectionUtil.isNotEmpty (items)){
            //只要已支付 == 4
            List<Long> orderIds = items.stream ().filter (e->e.getTradeStatus () == 4).map (KryOrderListVO::getOrderId).collect (Collectors.toList ());
            List<List<Long>> split = ListUtil.split (orderIds, 20);
            split.forEach (orderIdList-> {
                List<KryOrderDetailVO> kryOrderDetailVOS = kryServiceOnPos.orderDetail (Long.valueOf (store.getOutCode ()), orderIdList);
                posOrderDomainService.savePosOrderFromKry (kryOrderDetailVOS, store.getTenantId (), store.getTargetCode ());
            });
        }else{
            log.info ("batchDeal 订单记录为空，不处理");
        }
    }
}
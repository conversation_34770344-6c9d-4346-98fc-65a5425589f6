package com.cosfo.manage.common.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.good.service.ProductService;
import com.cosfo.manage.product.service.ProductCategoryService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/2
 */
@Component
@Slf4j
public class CategorySynchronizedTask extends XianMuJavaProcessorV2 {
    @Resource
    private ProductCategoryService productCategoryService;
    @Autowired
    private ProductService productService;

    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("类目同步调度任务开始------调度任务请求参数:{}", jobContext.getInstanceParameters());

        String jobParametersStr =jobContext.getInstanceParameters();
        List<Long> categoryIds = new ArrayList<>();
        if (!StringUtils.isBlank(jobParametersStr)) {
            categoryIds = JSONObject.parseArray(jobParametersStr, Long.class);
        }

        productCategoryService.synchronizedXianmuCategory(categoryIds);

        // 类目更新后，刷新自营货品导入模板
        productService.generateImportProductTemplate();

        return new ProcessResult(true);
    }
}

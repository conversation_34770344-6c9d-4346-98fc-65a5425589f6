package com.cosfo.manage.common.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.pos.model.dto.PosBomDTO;
import com.cosfo.manage.pos.model.po.PosBomItem;
import com.cosfo.manage.pos.service.PosBomItemService;
import com.cosfo.manage.pos.service.PosBomService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PosBomTask extends XianMuJavaProcessorV2 {

    @Resource
    private PosBomService posBomService;
    @Resource
    private PosBomItemService posBomItemService;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
//        String instanceParameters = context.getInstanceParameters();
//        LocalDate runDate = LocalDate.now();
//        LocalDate finalRunDate;
//        if (!StringUtils.isEmpty(instanceParameters)) {
//            JSONObject jsonObject = JSON.parseObject(instanceParameters);
//            runDate = LocalDate.parse(jsonObject.getString("runDate"));
//            finalRunDate = LocalDate.parse(jsonObject.getString("finalRunDate"));
//        } else {
//            finalRunDate = runDate.plusDays(1);
//        }
//        List<Long> tenantIds = posBomService.listTenantIds();
//        log.info("PosBomTask tenantIds:{}", tenantIds);
//        for (Long tenantId : tenantIds) {
//            List<PosBomDTO> posBomDTOS = posBomService.listPosBom(tenantId, runDate);
//            if (CollectionUtils.isEmpty(posBomDTOS)) {
//                log.info("PosBomTask posBomDTOS is empty tenantId:{}", tenantId);
//                continue;
//            }
//            List<String> outMenuCodeList = posBomDTOS.stream().map(PosBomDTO::getOutMenuCode).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(outMenuCodeList)) {
//                log.info("PosBomTask outMenuCodeList is empty tenantId:{}", tenantId);
//                continue;
//            }
//            List<PosBomItem> posBomItems = posBomItemService.listByTenantAndOutMenu(tenantId,outMenuCodeList, runDate);
//            if (CollectionUtils.isEmpty(posBomItems)) {
//                log.info("PosBomTask posBomItems is empty tenantId:{}", tenantId);
//                continue;
//            }
//            // 生成下一天的成本卡时间
//
//            posBomDTOS.forEach(posBomDTO -> posBomDTO.setAvailableDate(finalRunDate));
//            posBomItems.forEach(posBomItem -> posBomItem.setAvailableDate(finalRunDate));
//            transactionTemplate.executeWithoutResult(transactionStatus -> {
//                log.info("PosBomTask batchInsert tenantId:{}, total:{}", tenantId, posBomDTOS.size());
//                posBomService.batchInsert(posBomDTOS);
//                posBomItemService.batchInsert(posBomItems);
//            });
//        }
        return new ProcessResult(true);
    }
}

package com.cosfo.manage.common.task;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.merchant.service.impl.MerchantDeliveryInfoService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 霸王茶姬门店配送规则回告 定时任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ChageeMerchantDeliveryRuleJobProcessor extends XianMuJavaProcessorV2 {
    @Autowired
    private MerchantDeliveryInfoService merchantDeliveryInfoService;

    @Override
    public ProcessResult processResult(XmJobInput context) {
        log.info("开始霸王茶姬门店配送规则回告定时任务, 参数:{}", context.getInstanceParameters());
        long startTime = System.currentTimeMillis();
        try {
            merchantDeliveryInfoService.sendChageeMerchantDeliveryInfoAll();
        } catch (Exception e) {
            log.error("霸王茶姬门店配送规则回告定时任务执行失败", e);
            return new ProcessResult(InstanceStatus.FAILED, e.getMessage());
        }
        log.info("结束霸王茶姬门店配送规则回告定时任务，参数:{}，耗时:{} ms", context.getInstanceParameters(), System.currentTimeMillis() - startTime);
        return new ProcessResult(InstanceStatus.SUCCESS, "success");
    }

}
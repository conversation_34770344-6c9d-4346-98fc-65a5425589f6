package com.cosfo.manage.common.task.jindie;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.jindie.config.JinDieConfig;
import com.cosfo.manage.jindie.service.JindiePaymentRefundService;
import com.cosfo.manage.order.mapper.payment.RefundMapper;
import com.cosfo.manage.order.model.po.payment.Refund;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 收款退款单推送金蝶
 */
@Component
@Slf4j
public class PaymentRefundSync extends XianMuJavaProcessorV2 {

    @Resource
    private JinDieConfig jinDieConfig;
    @Resource
    private JindiePaymentRefundService jindiePaymentRefundService;
    @Resource
    private RefundMapper refundMapper;

    private static final List<String> TRADE_TYPE = Arrays.asList("JSAPI", "T_JSAPI", "T_MINIAPP", "HF_WECHAT_PLUGIN");

    /**
     * 任务参数.
     */
    private static class JobParams {
        private final LocalDateTime startTime = LocalDateTime.now().minusDays(1);
        private final LocalDateTime endTime = LocalDateTime.now();
    }

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        JobParams jobParams = Optional.ofNullable(context.getInstanceParameters())
                .map(it -> JSON.parseObject(it, JobParams.class)).orElseGet(JobParams::new);

        log.info("开始执行收款退款单推送金蝶任务, jobParams: {}", jobParams);

        try {
            List<Refund> refunds = refundMapper.selectForJindie(
                    jinDieConfig.getZcwTenantId(),
                    jobParams.startTime,
                    jobParams.endTime,
                    TRADE_TYPE);
            // 批量推送退款单到金蝶
            int successCount = jindiePaymentRefundService.batchPushPaymentRefundToJindie(refunds);

            log.info("收款退款单推送金蝶任务执行完成, 成功推送数量: {}", successCount);
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("收款退款单推送金蝶任务执行异常", e);
            return new ProcessResult(false, "收款退款单推送金蝶任务执行异常: " + e.getMessage());
        }
    }
}

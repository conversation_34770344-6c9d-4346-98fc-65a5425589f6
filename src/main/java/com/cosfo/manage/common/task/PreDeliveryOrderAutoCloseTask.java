package com.cosfo.manage.common.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.shade.org.apache.commons.lang.StringUtils;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.common.util.DateUtil;
import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;
import com.cosfo.manage.bill.service.PaymentCombinedDetailService;
import com.cosfo.manage.common.config.PreDeliveryOrderAutoCloseTenantConfig;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.context.OrderStatusEnum;
import com.cosfo.manage.common.context.PaymentTradeTypeEnum;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.req.event.OrderCloseReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-11-28
 * @Description:截单时间前-三方仓订单自动发起配送前售后-线上灰度租户
 */
@Component
@Slf4j
public class PreDeliveryOrderAutoCloseTask extends XianMuJavaProcessorV2 {

    @Resource
    private PreDeliveryOrderAutoCloseTenantConfig preDeliveryOrderAutoCloseTenantConfig;

    @DubboReference
    private OrderCommandProvider orderCommandProvider;

    @Resource
    private MerchantStoreService merchantStoreService;

    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;

    @Override
    public ProcessResult processResult(XmJobInput context) {
        log.info("PreDeliveryOrderAutoCloseTask 三方仓订单自动发起配送前售后开始");

        List<PreDeliveryOrderAutoCloseTenantConfig.TenantConfig> tenantConfigs = null;
        try {
            String preDeliveryAutoCloseConfig = preDeliveryOrderAutoCloseTenantConfig.getPreDeliveryAutoCloseConfig();
            tenantConfigs = JSON.parseArray(preDeliveryAutoCloseConfig, PreDeliveryOrderAutoCloseTenantConfig.TenantConfig.class);
        } catch (Exception e) {
            log.error("自动发起配送前售后任务,配置转换异常", e);
        }

        log.info("PreDeliveryOrderAutoCloseTask tenantConfigs:{}", JSON.toJSONString(tenantConfigs));
        if (CollectionUtils.isEmpty(tenantConfigs)) {
            return getProcessResult();
        }

        List<Long> tenantIdList = tenantConfigs.stream().map(PreDeliveryOrderAutoCloseTenantConfig.TenantConfig::getTenantId).collect(Collectors.toList());
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByIds(tenantIdList);
        Map<Long, TenantResultResp> tenantIdMap = tenants.stream().collect(Collectors.toMap(TenantResultResp::getId, Function.identity(), (v1, v2) -> v1));

        for (PreDeliveryOrderAutoCloseTenantConfig.TenantConfig tenantConfig : tenantConfigs) {
            closePreDeliveryThirdOrderByTenantConfig(tenantConfig, tenantIdMap);
            log.info("PreDeliveryOrderAutoCloseTask 租户编号:{},租户名称:{} 处理完成", tenantConfig.getTenantId(), getTenantName(tenantIdMap, tenantConfig));
        }

        return getProcessResult();
    }

    private String getTenantName(Map<Long, TenantResultResp> tenantIdMap, PreDeliveryOrderAutoCloseTenantConfig.TenantConfig tenantConfig) {
        return Optional.ofNullable(tenantIdMap.get(tenantConfig.getTenantId())).map(TenantResultResp::getTenantName).orElse(StringUtils.EMPTY);
    }

    private ProcessResult getProcessResult() {
        log.info("PreDeliveryOrderAutoCloseTask 三方仓订单自动发起配送前售后结束");
        return new ProcessResult(true);
    }

    /**
     * 基于单租户自动售后
     *
     * @param tenantConfig
     */
    private void closePreDeliveryThirdOrderByTenantConfig(PreDeliveryOrderAutoCloseTenantConfig.TenantConfig tenantConfig, Map<Long, TenantResultResp> tenantIdMap) {
        Long tenantId = tenantConfig.getTenantId();
        Set<Long> whiteStoreIds = Optional.ofNullable(tenantConfig.getWhiteStoreIds()).orElse(Collections.emptySet());
        // 排除白名单店铺后的店铺列表
        List<MerchantStore> merchantStores = merchantStoreService.selectByTenantId(tenantId);
        List<Long> storeIdList = merchantStores.stream().filter(merchantStore -> !whiteStoreIds.contains(merchantStore.getId())).map(MerchantStore::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeIdList)) {
            return;
        }

        Integer pageNum = NumberConstant.ONE;
        Integer pageSize = NumberConstant.FIVE_HUNDRED;
        List<Integer> waitStatusList = Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode());
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = DateUtil.startOfDay(now);
        LocalDateTime endTime = DateUtil.endOfDay(now);

        // 查询该租户下当日待配送的三方仓订单列表
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setPageNum(pageNum);
        orderQueryReq.setPageSize(pageSize);
        orderQueryReq.setStatusList(waitStatusList);
        orderQueryReq.setTenantId(tenantId);
        orderQueryReq.setCreateStartTime(startTime);
        orderQueryReq.setCreateEndTime(endTime);
        orderQueryReq.setStoreIds(storeIdList);
        orderQueryReq.setWarehouseType(WarehouseTypeEnum.THREE_PARTIES.getCode());

        PageInfo<OrderResp> orderRespPageInfo = orderQueryFacade.queryOrderPage(orderQueryReq);

        List<OrderResp> orderList = orderRespPageInfo.getList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        for (OrderResp orderDTO : orderList) {
            OrderCloseReq req = new OrderCloseReq();
            req.setOrderId(orderDTO.getId());
            req.setTenantId(orderDTO.getTenantId());
            if (Objects.equals(orderDTO.getPayType(), com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode())) {
                List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.querySuccessCombinedByOrderId(orderDTO.getTenantId(), orderDTO.getId());
                List<Integer> combinedPayTypes = combinedDetails.stream().map(detail -> {
                    return PaymentTradeTypeEnum.getPayTypeByTradeType(detail.getTradeType());
                }).collect(Collectors.toList());
                req.setCombinedPayTypes(combinedPayTypes);
            }
            DubboResponse<Boolean> response = orderCommandProvider.close(req);
            if (!response.isSuccess()) {
                throw new BizException(response.getMsg());
            }
            log.info("PreDeliveryOrderAutoCloseTask 租户编号:{},租户名称:{},订单号:{}发起完成", orderDTO.getTenantId(), getTenantName(tenantIdMap, tenantConfig), orderDTO.getOrderNo());
        }
    }
}

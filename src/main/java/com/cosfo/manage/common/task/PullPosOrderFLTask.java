package com.cosfo.manage.common.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.context.DocCodeChannelTypeEnum;
import com.cosfo.manage.common.context.DocCodeTargetTypeEnum;
import com.cosfo.manage.common.model.dto.DocCodeMappingDTO;
import com.cosfo.manage.common.service.DocCodeMappingService;
import com.cosfo.manage.flpos.service.FlPosService;
import com.cosfo.manage.flpos.vo.FlPosOrderDetailVO;
import com.cosfo.manage.flpos.vo.FlPosOrderInfoVO;
import com.cosfo.manage.flpos.vo.PageResult;
import com.cosfo.manage.pos.domain.PosOrderDomainService;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.*;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * FL/POS
 */
@Component
@Slf4j
public class PullPosOrderFLTask extends XianMuJavaProcessorV2 {
    /**
     * 入参 ex：
          {
             "startTime": "2021-06-02 04:30:00",
             "endTime": "2021-10-02 04:30:00",
              "tenantId": "2"
          }
     **/

    @Resource
    private FlPosService flPosService;
    
    @Resource
    private DocCodeMappingService docCodeMappingService;

    @Resource
    private PosOrderDomainService posOrderDomainService;


    @NacosValue(value = "${flpos.tenant.ids}", autoRefreshed = true)
    private String tenantId;
    @Override
//    public ProcessResult processResult(XmJobInput context) throws Exception {
//        //前一天
//        LocalDateTime now = LocalDateTime.now ();
//        LocalDateTime previousDayb = now.minusDays(6);
//
//
//        String startTime = LocalDateTimeUtil.format (previousDayb,"yyyy-MM-dd HH:mm:ss");
//        String endTime = LocalDateTimeUtil.format (now,"yyyy-MM-dd HH:mm:ss");
//        String outStoreCode = null;
//
//        String finalStartTime = startTime;
//        String finalEndTime = endTime;
//        if(StringUtils.isBlank (outStoreCode)){
//            //查询租户对应的客如云 - zx门店映射
//            Long tenantId = 2L;
//            List<DocCodeMappingDTO> storeList = docCodeMappingService.selectByTargetCodeAndType (Collections.emptyList (), DocCodeTargetTypeEnum.STORE.getCode (), tenantId, DocCodeChannelTypeEnum.FL_POS.getCode ());
//            storeList.forEach (store -> savePosOrder4Store(store, finalStartTime, finalEndTime));
//        }
//        return new ProcessResult(true);
//    }
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("flpos订单拉取开始------");
        //前一天
        LocalDateTime now = LocalDateTime.now ();
        LocalDateTime previousDay = now.minusDays(1);
        if (ObjectUtil.isNull(context)) {
            return new ProcessResult(true);
        }
        log.info ("tenantId={}",tenantId);
        List<Long> tenantIds = Splitter.on(",").splitToStream(tenantId).map(Long::valueOf).collect(Collectors.toList());

        String startTime = LocalDateTimeUtil.format (previousDay,"yyyy-MM-dd HH:mm:ss");
        String endTime = LocalDateTimeUtil.format (now,"yyyy-MM-dd HH:mm:ss");

        log.info("flpos订单拉取开始，tenantIds={}，startTime={},endTime={}",tenantIds,startTime,endTime);

        if(StringUtils.isNotBlank (context.getInstanceParameters ())){
            Map<String, String> map = JSON.parseObject (context.getInstanceParameters (), Map.class);

            String tenantIdKey = "tenantId";
            if(StringUtils.isNotBlank (map.get (tenantIdKey))) {
                tenantIds = Collections.singletonList (Long.valueOf (map.get (tenantIdKey)));
            }

            String startTimeKey = "startTime";
            if(StringUtils.isNotBlank (map.get (startTimeKey))) {
                startTime = map.get (startTimeKey);
            }

            String endTimeKey = "endTime";
            if(StringUtils.isNotBlank (map.get (endTimeKey))) {
                endTime = map.get (endTimeKey);
            }
            log.info("flpos订单拉取开始 手动输入了参数，tenantIds={}，startTime={},endTime={}",tenantIds,startTime,endTime);
        }

        if(CollectionUtil.isEmpty (tenantIds)){
            log.info("flpos订单拉取结束 tenantIds = null------");
            return new ProcessResult(true);
        }

        String finalStartTime = startTime;
        String finalEndTime = endTime;
        tenantIds.forEach (tenantId -> {
            //查询租户对应的flpos门店映射
            List<DocCodeMappingDTO> storeList = docCodeMappingService.selectByTargetCodeAndType (Collections.emptyList (), DocCodeTargetTypeEnum.STORE.getCode (), tenantId, DocCodeChannelTypeEnum.FL_POS.getCode ());
            storeList.forEach (store -> savePosOrder4Store(store, finalStartTime, finalEndTime));
        });
        return new ProcessResult(true);
    }

    private void savePosOrder4Store(DocCodeMappingDTO store, String startTime, String endTime) {
        boolean hasNext = true;
        int pageIndex = 1;
        String outCode = store.getOutCode ();
        while(hasNext){
            try {
                PageResult<FlPosOrderInfoVO> pageResult = flPosService.orderList (outCode,startTime, endTime, pageIndex);
                if(ObjectUtil.isNotNull (pageResult)) {
                    batchDeal (pageResult.getData (), store);
                    hasNext = pageResult.getTotalPage () > pageIndex;
                    pageIndex = pageIndex + 1;
                }else{
                    hasNext = false;
                }

            }catch (Exception e){
                hasNext = false;
                log.error ("请求第{}页pos订单异常,startTime={},endTime={},outStoreCode={}", pageIndex, startTime, endTime,outCode,e);
            }
        }
    }

    private void batchDeal(List<FlPosOrderInfoVO> items, DocCodeMappingDTO store) {
        if(CollectionUtil.isNotEmpty (items)){
            items.forEach (order-> {
                try{
                    FlPosOrderDetailVO dataVO = flPosService.orderDetail (order.getBill ());
                    posOrderDomainService.savePosOrderFromFl (dataVO, store.getTenantId (), store.getTargetCode ());
                } catch (Exception e) {
                    log.error ("请求pos订单详情异常,跳过，shopid={},orderId={}", store.getOutCode (),order.getBill (),e);
                }
            });
        }else{
            log.info ("batchDeal 订单记录为空，不处理");
        }
    }
}
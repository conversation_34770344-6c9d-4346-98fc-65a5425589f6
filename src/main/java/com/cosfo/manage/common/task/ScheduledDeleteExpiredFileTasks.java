package com.cosfo.manage.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/17 13:57
 */
@Component
@Slf4j
public class ScheduledDeleteExpiredFileTasks extends XianMuJavaProcessorV2 {

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;

    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("定时任务-删除过期文件开始执行");
        String jobParametersStr = jobContext.getJobParameters();
        if (StringUtils.isBlank(jobParametersStr)) {
            log.error("未找到分布式调度任务------");
            return new ProcessResult(true);
        }
//        fileDownloadRecordService.deleteExpiredFileTask();
        log.info("定时任务-删除过期文件执行完毕");
        return new ProcessResult(true);
    }
}

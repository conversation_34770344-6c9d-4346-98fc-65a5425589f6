package com.cosfo.manage.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.client.merchant.openapi.MerchantStoreOpenProvider;
import com.cosfo.manage.client.merchant.req.MerchantStoreOpenReq;
import com.cosfo.manage.client.merchant.resp.MerchantStoreOpenResp;
import com.cosfo.manage.common.config.OpenApiConfig;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantContactFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.service.MerchantAddressService;
import com.cosfo.manage.merchant.service.MerchantStoreAccountService;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * 柠季门店自动审核任务
 *
 * @author: xiaowk
 * @date: 2024/12/25 下午1:41
 */
@Component
@Slf4j
public class NingjiStoreAutoAuditTask extends XianMuJavaProcessorV2 {

    @Resource
    private MerchantStoreOpenProvider merchantStoreOpenProvider;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private UserCenterMerchantContactFacade userCenterMerchantContactFacade;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;



    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("柠季门店自动审核任务 task start, 参数：{}", context);

        List<Long> storeIds = null;

        String jobParameters = context.getJobParameters();
        String instanceParameters = context.getInstanceParameters();

        if (!StringUtils.isEmpty(jobParameters)) {
            storeIds = Arrays.stream(jobParameters.split(",")).map(String::trim).map(Long::parseLong).distinct().collect(Collectors.toList());
        }

        if (!StringUtils.isEmpty(instanceParameters)) {
            storeIds = Arrays.stream(instanceParameters.split(",")).map(String::trim).map(Long::parseLong).distinct().collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(storeIds)) {
            throw new ProviderException("定时任务参数配置错误");
        }

        for (Long storeId : storeIds) {
            try {
                autoAuditStore(storeId);
            } catch (Exception e) {
                log.warn("自动审核门店异常，storeId={}", storeId, e);
            }
        }


        log.info("柠季门店自动审核任务 task end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }


    public void autoAuditStore(Long storeId){

        MerchantStoreResultResp merchantStore = userCenterMerchantStoreFacade.getMerchantStoreById(storeId);
        if(merchantStore == null){
            return;
        }

        Long tenantId = merchantStore.getTenantId();

        if(!openApiConfig.getOpenApiNingJiTenantIds().contains(tenantId)){
            log.info("不是柠季租户下的门店");
            return;
        }

        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        MerchantAddressResultResp address = merchantAddressService.selectByStoreId(tenantId, storeId);
        if(address == null){
            return;
        }

        MerchantContactQueryReq merchantContactQueryReq = new MerchantContactQueryReq();
        merchantContactQueryReq.setAddressId(address.getId());
        merchantContactQueryReq.setTenantId(tenantId);
//        merchantContactQueryReq.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        List<MerchantContactResultResp> merchantContacts = userCenterMerchantContactFacade.getMerchantContacts(merchantContactQueryReq);
        if(CollectionUtils.isEmpty(merchantContacts)){
            return;
        }

        MerchantStoreAccountDTO storeAccountDTO = merchantStoreAccountService.selectManager(tenantId, storeId);
        if(storeAccountDTO == null){
            return;
        }

        MerchantStoreOpenReq req = new MerchantStoreOpenReq();
        req.setStoreName(merchantStore.getStoreName());
        req.setStoreNo(merchantStore.getStoreNo());
        req.setType(merchantStore.getType());
        req.setBillSwitch(merchantStore.getBillSwitch());
        req.setOnlinePayment(merchantStore.getOnlinePayment());
        req.setBalanceAuthority(merchantStore.getBalanceAuthority());

        // 门店地址、门店联系人
        List<MerchantStoreOpenReq.ContactReq> contactList = Lists.newArrayList();
        MerchantStoreOpenReq.ContactReq contactReq = new MerchantStoreOpenReq.ContactReq();
        contactReq.setContactName(merchantContacts.get(0).getName());
        contactReq.setPhone(merchantContacts.get(0).getPhone());
        contactReq.setDefaultFlag(merchantContacts.get(0).getDefaultFlag());

        MerchantStoreOpenReq.AddressReq addressInfo = new MerchantStoreOpenReq.AddressReq();
        addressInfo.setProvince(address.getProvince());
        addressInfo.setCity(address.getCity());
        addressInfo.setArea(address.getArea());
        addressInfo.setAddress(address.getAddress());
        contactReq.setAddressInfo(addressInfo);
        contactList.add(contactReq);
        req.setContactList(contactList);

        // 门店账号
        List<MerchantStoreOpenReq.AccountReq> accountList = Lists.newArrayList();
        MerchantStoreOpenReq.AccountReq accountReq = new MerchantStoreOpenReq.AccountReq();
        accountReq.setPhone(storeAccountDTO.getPhone());
        accountReq.setAccountName(storeAccountDTO.getAccountName());
        accountReq.setType(storeAccountDTO.getType());
        accountList.add(accountReq);
        req.setAccountList(accountList);

        DubboResponse<MerchantStoreOpenResp> response = merchantStoreOpenProvider.upsertStoreInfo(req);
    }

}

package com.cosfo.manage.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.merchant.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Component
@Slf4j
public class ScheduledSettleInfoTask extends XianMuJavaProcessorV2 {
    @Resource
    private MerchantService merchantService;
    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("同步结算信息任务开始------");

        merchantService.orderSettleInfoTask();

        log.info("同步结算信息任务结束------");
        return new ProcessResult(true);
    }
}
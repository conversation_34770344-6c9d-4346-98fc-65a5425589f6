package com.cosfo.manage.common.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.merchant.service.MerchantStoreTradeSummaryService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @description:
 * @author: George
 * @date: 2023-11-03
 **/
@Component
@Slf4j
public class AuditStoreDimensionTradeSummaryTask extends XianMuJavaProcessorV2 {

    @Resource
    private MerchantStoreTradeSummaryService merchantStoreTradeSummaryService;

    public static final String PARAMETER_TENANT_ID = "tenantId";
    public static final String PARAMETER_START_TIME = "startTime";
    public static final String PARAMETER_END_TIME = "endTime";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        // 获取参数
        JSONObject jsonObject = JSONObject.parseObject(context.getInstanceParameters());
        Long tenantId = Optional.ofNullable(jsonObject).map(o -> o.getLong(PARAMETER_TENANT_ID)).orElse(null);
        String startTime = Optional.ofNullable(jsonObject).map(o -> o.getString(PARAMETER_START_TIME)).orElse(null);
        String endTime = Optional.ofNullable(jsonObject).map(o -> o.getString(PARAMETER_END_TIME)).orElse(null);
        // 执行稽核方法
        merchantStoreTradeSummaryService.auditStoreDimensionTradeSummary(tenantId, startTime, endTime);
        return new ProcessResult(true);
    }
}

package com.cosfo.manage.common.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import com.cosfo.manage.wechat.api.WxaAPI;
import com.cosfo.manage.wechat.bean.wxa.TradeManagedResult;
import com.cosfo.manage.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.service.WeixinShippingService;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  查询开通了发货信息管理的小程序，并保存响应的租户id
 *
 * @author: xiaowk
 * @date: 2023/7/11 下午10:09
 */
@Component
@Slf4j
public class QueryOpenShippingInfoTask extends XianMuJavaProcessorV2 {

    @Resource
    private WechatAuthorizerMapper wechatAuthorizerMapper;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private WeixinShippingService weixinShippingService;


    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("查询开通了发货信息管理的小程序 task start, 参数：{}", jobContext.getJobParameters());

        try {
            runTask();
        } catch (Exception e) {
            log.error("查询开通了发货信息管理的小程序 error.", e);
        }

        log.info("查询开通了发货信息管理的小程序 task end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }

    private void runTask() {
        List<Long> tenantIds = Lists.newArrayList();

        Long maxId = 0L;
        Integer size = 100;
        while (true) {
            List<WechatAuthorizer> list = wechatAuthorizerMapper.getAuthTenantsByPage(maxId, size);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            tenantIds.addAll(getOpenShippingTenantIds(list));

            maxId = list.get(list.size() - 1).getId();
        }

        weixinShippingService.changeOpenShippingTenantIds(tenantIds);

    }

    public List<Long> getOpenShippingTenantIds(List<WechatAuthorizer> list) {
        List<String> openShippingAppidList = Lists.newArrayList();
        for (WechatAuthorizer wechatAuthorizer : list) {
            try {
                String funcInfo = wechatAuthorizer.getFuncInfo();
                // 上传发货信息需要开通142权限
                if(StringUtils.isBlank(funcInfo) || !JSON.parseArray(funcInfo, Integer.class).contains(142)){
                    continue;
                }
                String accessToken = wechatAuthorizer.getAccessToken();
                String appid = wechatAuthorizer.getAppId();
                TradeManagedResult result = WxaAPI.is_trade_managed(accessToken, appid);
                if (result == null || !result.successed()) {
                    log.error("查询小程序是否已开通发货信息管理服务错误， appid={}, result={}", appid, result);
                } else {
                    if (result.getIs_trade_managed() != null && result.getIs_trade_managed()) {
                        openShippingAppidList.add(appid);
                    }
                }
            } catch (Exception e) {
                log.error("查询小程序是否已开通发货信息管理服务错误， wechatAuthorizer={}", wechatAuthorizer, e);
            }
        }

        if (CollectionUtils.isEmpty(openShippingAppidList)) {
            return Collections.emptyList();
        }

        List<TenantAuthConnection> tenantAuthConnectionList = tenantAuthConnectionMapper.selectAllTenants(openShippingAppidList);
        return tenantAuthConnectionList.stream().map(e -> e.getTenantId()).distinct().collect(Collectors.toList());
    }
}

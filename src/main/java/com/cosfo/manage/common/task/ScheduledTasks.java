package com.cosfo.manage.common.task;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.wechat.service.AuthorizerService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
public class ScheduledTasks extends XianMuJavaProcessorV2 {

    @Resource
    AuthorizerService authorizerService;

    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("分布式调度任务开始------调度任务:{}",jobContext.getJobParameters());

        String jobParametersStr=jobContext.getJobParameters();
        if(StringUtils.isBlank(jobParametersStr)){
            log.error("未找到分布式调度任务------");
            return new ProcessResult(false);
        }
        JobParameters jobParameters = JSONObject.parseObject(jobParametersStr, JobParameters.class);
        if(Objects.isNull(jobParameters) || StringUtils.isBlank(jobParameters.getJobName())){
            log.error("未找到分布式调度任务------");
            return new ProcessResult(false);
        }

        // 刷新微信token
        if(JobNameConstant.WECHAT_TOKEN_REFRESH_JOB_NAME.equals(jobParameters.getJobName())){
            authorizerService.refreshAuthorizerToken(Boolean.FALSE,null);
        }

        log.info("分布式调度任务结束------调度任务:{}",jobParameters.getJobName());
        return new ProcessResult(true);
    }
}

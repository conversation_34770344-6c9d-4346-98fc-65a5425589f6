package com.cosfo.manage.common.converter;

import com.cosfo.manage.report.model.dto.ProductAgentShelfLifeQueryDTO;
import net.summerfarm.wms.saleinventory.dto.req.PageQueryPurchaseBatchInventoryReq;
import net.xianmu.common.input.PageSortInput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2023-11-20
 * @Description:
 */
@Mapper
public interface PurchaseBatchInventoryReqMapper {

    PurchaseBatchInventoryReqMapper INSTANCE = Mappers.getMapper(PurchaseBatchInventoryReqMapper.class);

    @Mapping(target = "quantityDateSort", source = "sortInput.orderBy", defaultValue = "asc")
    PageQueryPurchaseBatchInventoryReq queryToReq(ProductAgentShelfLifeQueryDTO dto, PageSortInput sortInput);
}

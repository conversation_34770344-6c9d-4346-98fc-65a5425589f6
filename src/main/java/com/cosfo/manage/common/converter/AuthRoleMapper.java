package com.cosfo.manage.common.converter;

import com.cosfo.manage.tenant.model.dto.RoleDTO;
import com.cosfo.manage.tenant.model.dto.RoleQueryDTO;
import com.cosfo.manage.tenant.model.vo.RoleVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthRoleDTO;
import net.xianmu.authentication.client.dto.AuthRoleDetailsDTO;
import net.xianmu.authentication.client.input.AuthRoleQueryVO;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface AuthRoleMapper {

    AuthRoleMapper INSTANCE = Mappers.getMapper(AuthRoleMapper.class);

    PageInfo<RoleDTO> authRoleToRoleDTOPage(PageInfo<AuthRoleDTO> authRolePageInfo);

    @Mapping(target = "roleName", source = "rolename")
    RoleDTO authRoleDTOToRoleDTO(AuthRoleDTO authRoleDTO);

    PageInfo<RoleVO> roleDTOToRoleVOPage(PageInfo<RoleDTO> authRolePageInfo);

    @Mapping(target = "roleName", source = "rolename")
    RoleDTO authRoleToRoleDTO(AuthRole authRole);

    @Mapping(target = "updateTime", expression = "java(roleDTO.getUpdateTime() == null ? roleDTO.getCreateTime() : roleDTO.getUpdateTime())")
    RoleVO roleDTOToRoleVO(RoleDTO roleDTO);

    @Mapping(target = "menuPurviewIds", expression = "java(roleDTO.getList() == null? com.google.common.collect.Lists.newArrayList() : roleDTO.getList().stream().filter(r->r.getType() != 0).map(net.xianmu.authentication.client.dto.AuthMenuPurview::getId).collect(java.util.stream.Collectors.toList()))")
    @Mapping(target = "roleName", source = "rolename")
    RoleDTO roleDetailDTOToRoleDTO(AuthRoleDetailsDTO roleDTO);

    @Mapping(target = "tenantId", source = "tenantId")
    @Mapping(target = "rolename", source = "roleDTO.roleName")
    AuthRoleUpdateVO roleDTOToRoleUpdateVO(RoleDTO roleDTO, Long tenantId);

    @Mapping(target = "tenantId", source = "tenantId")
    @Mapping(target = "systemOrigin", expression = "java(net.xianmu.authentication.client.input.SystemOriginEnum.COSFO_MANAGE.getType())")
    AuthRoleQueryVO ToAuthRoleQueryVO(RoleQueryDTO roleQueryDTO, Long tenantId);
}

package com.cosfo.manage.common.converter.report;

import com.cosfo.manage.product.model.vo.ProductStockWarnExportVO;
import com.cosfo.manage.product.model.vo.ProductStockWarnVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2023-05-20
 * @Description:
 */
@Mapper
public interface ProductStockWarnMapper {

    ProductStockWarnMapper INSTANCE = Mappers.getMapper(ProductStockWarnMapper.class);

    /**
     * WarnVo转ExportVo
     * @param productStockWarnVO
     * @return
     */
    ProductStockWarnExportVO productStockWarnToExportVo(ProductStockWarnVO productStockWarnVO);

}

package com.cosfo.manage.common.converter;

import com.cosfo.manage.product.model.vo.ProductAgentWarehouseShelfLifeVO;
import net.summerfarm.wms.saleinventory.dto.dto.PurchaseBatchInventoryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ProductAgentWarehouseShelfLifeMapper {

    ProductAgentWarehouseShelfLifeMapper INSTANCE = Mappers.getMapper(ProductAgentWarehouseShelfLifeMapper.class);

    ProductAgentWarehouseShelfLifeVO toVO(PurchaseBatchInventoryDTO dto);
}

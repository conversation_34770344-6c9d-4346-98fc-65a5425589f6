package com.cosfo.manage.common.converter;

/**
 * <AUTHOR>
 */
//@Mapper
//public interface PageQueryPurchaseBatchInventoryReqMapper {
//
//    PageQueryPurchaseBatchInventoryReqMapper INSTANCE = Mappers.getMapper(PageQueryPurchaseBatchInventoryReqMapper.class);
//
//    @Mapping(target = "quantityDateSort", source = "sortInput.orderBy", defaultValue = "asc")
//    PageQueryPurchaseBatchInventoryReq queryToReq(ProductAgentShelfLifeQueryDTO dto, PageSortInput sortInput);
//}

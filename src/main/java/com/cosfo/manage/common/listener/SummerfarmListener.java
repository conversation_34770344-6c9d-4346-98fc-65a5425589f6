package com.cosfo.manage.common.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.good.service.ProductService;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.tenant.service.TenantMeasureService;
import com.cosfo.summerfarm.model.dto.product.SummerfarmProductApplicationDTO;
import com.cosfo.summerfarm.model.dto.product.SummerfarmProductAuditResultDTO;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgType;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-10-14
 * @description mall-list topic监听
 */
//@Slf4j
//@Component
//@RocketMQMessageListener(
//        topic = SummerfarmMQTopic.SAAS_MANAGE,
//        consumerGroup = "GID_saas-manage",
//        consumeMode = ConsumeMode.CONCURRENTLY,
//        selectorExpression = "*",
//        enableMsgTrace = false)
//public class SummerfarmListener implements RocketMQListener<SummerfarmMsgModel> {
@Slf4j
@Component
@MqListener(
        topic = SummerfarmMQTopic.SAAS_MANAGE,
        consumerGroup = "GID_saas-manage",
        tag = "*")
public class SummerfarmListener extends AbstractMqListener<SummerfarmMsgModel> {

    @Resource
    private OrderBusinessService orderBusinessService;
    @Resource
    private ProductService productService;
    @Lazy
    @Resource
    private TenantMeasureService tenantMeasureService;

    @Override
    public void process(SummerfarmMsgModel msg) {
        log.error("rocketmq 收到summerfarm消息，升级mq扩展包版本，消息内容：{}", JSONObject.toJSONString(msg));
        long startTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调执行时间：{}ms", Thread.currentThread().getName(), startTime);
        String msgType = msg.getMsgType();
        String jsonString = JSON.toJSONString(msg.getMsgData());

        switch (msgType) {
            case SummerfarmMsgType.SELF_OUT_STORE_TIME:
                // 已由ofc发送自提出库消息
//                SummerfarmOrderOutDTO summerfarmOrderOutDTO = JSONObject.parseObject(jsonString, SummerfarmOrderOutDTO.class);
//                orderBusinessService.receiveActualSelfLiftingTime(summerfarmOrderOutDTO);
                break;
            case SummerfarmMsgType.PRODUCT_AGENT_APPLY_RESULT:
                SummerfarmProductAuditResultDTO summerfarmProductAuditResultDTO = JSONObject.parseObject(jsonString, SummerfarmProductAuditResultDTO.class);
                productService.receiveSummerfarmProductAuditResult(summerfarmProductAuditResultDTO);
                break;
            case SummerfarmMsgType.PRODUCT_AGENT_APPLY:
                SummerfarmProductApplicationDTO summerfarmProductApplicationDTO = JSONObject.parseObject(jsonString, SummerfarmProductApplicationDTO.class);
                productService.receiveCallBackData(summerfarmProductApplicationDTO);
                break;
//            case MqMessageType.MEASURE:
//                Object msgData = msg.getMsgData();
//                Long measureReportId = Long.valueOf(String.valueOf(msgData));
//                tenantMeasureService.doMeasure(measureReportId);
//                break;
//            case MqMessageType.MEASURE_DELAY_CANCEL:
//                Object data = msg.getMsgData();
//                Long id = Long.valueOf(String.valueOf(data));
//                tenantMeasureService.cancelMeasure(id);
//                break;
            default:
                log.error("消息类型异常，消息内容：{}", JSONObject.toJSONString(msg));
                break;
        }

        long endTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调执行时间：{}ms", Thread.currentThread().getName(), endTime);
    }
}

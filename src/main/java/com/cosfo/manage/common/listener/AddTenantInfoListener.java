package com.cosfo.manage.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.constant.MqTagConstant;
import com.cosfo.manage.common.constant.MqTopicConstant;
import com.cosfo.manage.common.listener.AddTenantInfoListener.AddTenantInfoMessageDTO;
import com.cosfo.manage.facade.SummerFarmInterfaceServiceFacade;
import com.cosfo.manage.good.service.SyncProductService;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.service.TenantService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 新增品牌方租户信息的mq消息监听类
 * 同步鲜沐代仓客户的SKU
 *
 * @author: xiaowk
 * @date: 2023/6/25 下午4:42
 */
@Slf4j
@Component
@MqListener(topic = MqTopicConstant.SAAS_ADD_TENANT_INFO,
        consumerGroup = "GID_saas_manage_tenant_info",
        tag = MqTagConstant.SAAS_ADD_TENANT_INFO_TAG
)
public class AddTenantInfoListener extends AbstractMqListener<AddTenantInfoMessageDTO> {

    @Resource
    private TenantService tenantService;

    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;

    @Resource
    private SyncProductService syncProductService;

    @Override
    public void process(AddTenantInfoMessageDTO msgDTO) {
        log.info("rocketmq 收到添加品牌方租户,消息内容：{}", JSONObject.toJSONString(msgDTO));

        if(msgDTO == null || msgDTO.getTenantId() == null || msgDTO.getAdminId() == null){
            log.warn("rocketmq 收到添加品牌方租户, 消息内容为空");
            return;
        }
        TenantDTO tenantDTO = tenantService.selectByAdminId(msgDTO.getAdminId());
        if(tenantDTO == null){
            log.warn("rocketmq 收到添加品牌方租户, adminId={} 查询关联租户信息为空", msgDTO.getAdminId());
            return;
        }

        Long tenantId = tenantDTO.getId();

        if (!msgDTO.getTenantId().equals(tenantId)) {
            log.error("rocketmq 收到添加品牌方租户, adminId={} tenantId={} 查询关联租户信息不等，tenantDTO={}", msgDTO.getAdminId(), msgDTO.getTenantId(), tenantDTO);
            return;
        }

        List<Long> skuIdList = summerFarmInterfaceServiceFacade.querySkuIdsByAdminId(msgDTO.getAdminId());
        if(CollectionUtils.isEmpty(skuIdList)){
            log.info("rocketmq 收到添加品牌方租户, adminId={} tenantId={} 查询鲜沐代仓SKU为空", msgDTO.getAdminId(), msgDTO.getTenantId());
            return;
        }

        syncProductService.syncAgentProductFromXianmu(tenantId, skuIdList);
    }

    @Data
    public static class AddTenantInfoMessageDTO{
        /**
         * 品牌商城ID
         */
        private Long tenantId;

        /**
         * 鲜沐大客户Id
         */
        private Long adminId;
    }
}

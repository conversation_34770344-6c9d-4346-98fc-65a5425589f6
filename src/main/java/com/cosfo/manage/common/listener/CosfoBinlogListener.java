package com.cosfo.manage.common.listener;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.context.binlog.RocketMqConstant;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.binlog.DbTableDmlFactory;
import com.cosfo.manage.common.util.binlog.DbTableDmlService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@MqOrderlyListener(
        topic = RocketMqConstant.Topic.MYSQL_BINLOG_SAAS,
        consumerGroup = RocketMqConstant.ConsumerGroup.MYSQL_BINLOG_COSFO_MANAGE,
        tag = RocketMqConstant.Tag.COSFO_TABLENAMES_TAG)
public class CosfoBinlogListener extends AbstractMqListener<DtsModelBO> {

    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Override
    public void process(DtsModelBO dtsModel) {
        log.info("rocketmq 收到消息,事件类型:[{}]，recordId/msg-key:[{}]， 表:[{}].[{}]",
                dtsModel.getType(), dtsModel.getMsgKey(), dtsModel.getDatabase(), dtsModel.getTable());
        DbTableDmlService creator = dbTableDmlFactory.creator(dtsModel.getTable());
        if (Objects.nonNull(creator)) {
            log.info("dtsModel=[{}]", JSON.toJSONString(dtsModel));
            creator.tableDml(dtsModel);
        } else {
            log.info("未在DbTableDmlFactory注册的table:[{}],请先注册后再做处理!", dtsModel.getTable());
        }
    }
}

package com.cosfo.manage.common.listener;

import com.cosfo.manage.common.constant.MqTagConstant;
import com.cosfo.manage.common.constant.MqTopicConstant;
import com.cosfo.manage.common.mq.saasmanagedelay.factory.SaasManageDelayFactory;
import com.cosfo.manage.common.mq.saasmanagedelay.factory.SaasManageDelayService;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2024/2/23 09:24
 * @Description:
 */
@Slf4j
@Component
@MqListener(topic = MqTopicConstant.SAAS_MANAGE_DELAY,
    consumerGroup = MqTopicConstant.GROUP_SAAS_MANAGE_DELAY,
    tag = MqTagConstant.AGENT_ORDER_CANCEL
)
public class SaasManageDelayListener extends AbstractMqListener<SummerfarmMsgModel> {

    @Resource
    private SaasManageDelayFactory saasManageDelayFactory;

    @Override
    public void process(SummerfarmMsgModel msgModel) {
        log.info("rocketmq 收到消息,topic:{},消息体：{}", MqTopicConstant.SAAS_MANAGE_DELAY, msgModel);
        SaasManageDelayService creator = saasManageDelayFactory.creator(msgModel.getMsgType());
        if (Objects.nonNull(creator)) {
            creator.handle(msgModel.getMsgData());
        } else {
            log.error("未找到msgType所对应的实现类！,msgType:{}", msgModel.getMsgType());
        }
    }
}

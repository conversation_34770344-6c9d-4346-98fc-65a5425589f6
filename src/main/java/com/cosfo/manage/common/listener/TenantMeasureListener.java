package com.cosfo.manage.common.listener;

import com.cosfo.manage.common.constant.MqMessageType;
import com.cosfo.manage.common.constant.MqTagConstant;
import com.cosfo.manage.tenant.service.TenantMeasureService;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-17
 **/
@Slf4j
@Component
@MqListener(
        topic = SummerfarmMQTopic.SAAS_MANAGE,
        consumerGroup = "GID_saas_manage_tenant_measure",
        tag = MqTagConstant.TENANT_MEASURE + "||" + MqTagConstant.TENANT_MEASURE_CANCEL)
public class TenantMeasureListener  extends AbstractMqListener<SummerfarmMsgModel> {

    @Resource
    private TenantMeasureService tenantMeasureService;
    @Override
    public void process(SummerfarmMsgModel msg) {
        log.info("rocketmq 消息，接收到度量检测，消息内容：{}", msg);
        String msgType = msg.getMsgType();
        Object msgData = msg.getMsgData();

        switch (msgType) {
            case MqMessageType.MEASURE:
                Long measureReportId = Long.valueOf(String.valueOf(msgData));
                tenantMeasureService.doMeasure(measureReportId);
                break;
            case MqMessageType.MEASURE_DELAY_CANCEL:
                Long reportId = Long.valueOf(String.valueOf(msgData));
                tenantMeasureService.cancelMeasure(reportId);
                break;
        }
    }
}

package com.cosfo.manage.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.constant.MqTagConstant;
import com.cosfo.manage.common.constant.MqTopicConstant;
import com.cosfo.manage.common.model.dto.SupplyMessageDTO;
import com.cosfo.manage.product.mapper.ProductSkuMapper;
import com.cosfo.manage.report.service.ProductAgentStockReportService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: fansongsong
 * @Date: 2023-05-18
 * @Description: 新增报价单同步sku城市库存
 */
@Slf4j
@Component
@MqListener(topic = MqTopicConstant.ADD_PRODUCT_PRICING_SUPPLY,
        consumerGroup = "GID_saas_manage_product_supply",
        tag = MqTagConstant.ADD_PRICING_SUPPLY_TAG
)
public class AddProductPricingSupplyListener extends AbstractMqListener<SupplyMessageDTO> {

    @Resource
    private ProductAgentStockReportService productAgentStockReportService;
    @Resource
    private ProductSkuMapper productSkuMapper;

    @Override
    public void process(SupplyMessageDTO supplyMessageDTO) {
        log.info("rocketmq 收到添加报价单,消息内容：{}", JSONObject.toJSONString(supplyMessageDTO));

//        List<Long> supplyIdList = supplyMessageDTO.getSupplyIdList();
//        if (CollectionUtils.isEmpty(supplyIdList)) {
//            return;
//        }
//
//        // 查询鲜沐报价货品信息
//        AgentTenantSkuQueryDTO agentTenantSkuQueryDTO = AgentTenantSkuQueryDTO.builder().agentTenantId(XianmuSupplyTenant.TENANT_ID).supplyIdList(supplyIdList).build();
//        List<AgentTenantSkuDTO> agentTenantSkuDTOList = productSkuMapper.selectAgentTenantSkuInfoByAgentTenantId(agentTenantSkuQueryDTO);
//        if (CollectionUtils.isEmpty(agentTenantSkuDTOList)) {
//            log.info("报价单不存在相关货品信息 supplyMessageDTO:{},agentTenantSkuQueryDTO：{}", JSON.toJSONString(supplyMessageDTO), JSONObject.toJSONString(agentTenantSkuQueryDTO));
//            return;
//        }
//        // 同步库存信息
//        productAgentStockReportService.syncSkuCityStock(agentTenantSkuDTOList, true);
    }
}

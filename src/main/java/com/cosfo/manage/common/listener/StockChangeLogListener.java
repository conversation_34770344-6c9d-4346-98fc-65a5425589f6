package com.cosfo.manage.common.listener;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.binlog.DbTableDmlFactory;
import com.cosfo.manage.common.util.binlog.DbTableDmlService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-05-18
 * @Description:
 */
@Slf4j
@Component
@MqOrderlyListener(
        topic = RocketMqMessageConstant.MYSQL_BINLOG_ORDERLY,
        consumerGroup = "GID_saas_manage_binlog",
        tag = DBTableName.QUANTITY_CHANGE_RECORD)
public class StockChangeLogListener extends AbstractMqListener<DtsModelBO> {

    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Override
    public void process(DtsModelBO dtsModel) {
        log.info("rocketmq 收到消息,事件类型:[{}]，recordId/msg-key:[{}]， 表:[{}].[{}]",
                dtsModel.getType(), dtsModel.getMsgKey(), dtsModel.getDatabase(), dtsModel.getTable());
        DbTableDmlService creator = dbTableDmlFactory.creator(dtsModel.getTable());
        if (Objects.nonNull(creator)) {
            log.info("dtsModel=[{}]", JSON.toJSONString(dtsModel));
            creator.tableDml(dtsModel);
        } else {
            log.info("未在DbTableDmlFactory注册的table:[{}],请先注册后再做处理!", dtsModel.getTable());
        }
    }

}

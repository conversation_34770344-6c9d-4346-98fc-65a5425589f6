package com.cosfo.manage.common.constant;

import java.util.regex.Pattern;

/**
 * desc: 正则表达式
 *
 * <AUTHOR>
 * @date 2022/8/18 17:53
 */
public class RegularExpressionConstant {

    /**
     * 省份或直辖市正则表达式
     */
    public final static String PROVINCE = ".*省|.*自治区|上海|北京|天津|重庆";

    /**
     * 城市正则表达式
     */
    public final static String CITY = ".*市";

    /**
     * 县区正则表达式
     */
    public static final String AREA = ".*区|.*县";

    /**
     * 数字
     */
    public static final Pattern NUMBER_PATTERN = Pattern.compile("[0-9]*");

    /**
     * 特殊字符
     */
    public static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile("[^a-zA-Z0-9\\u4E00-\\u9FA5-()（）〈〉<>＿―《》——……；!? 。：”“、.__:\"']+");

    public static void main(String[] args) {
        System.out.println(SPECIAL_CHAR_PATTERN.matcher("KRY001").matches());
    }
}

package com.cosfo.manage.common.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/19 18:57
 */
public class NumberConstant {

    /**
     * integer 0
     */
    public static final Integer ZERO = 0;

    /**
     * integer 1
     */
    public static final Integer ONE = 1;

    /**
     * integer 2
     */
    public static final Integer TWO = 2;

    /**
     * integer 3
     */
    public static final Integer THREE = 3;

    /**
     * integer 4
     */
    public static final Integer FOUR = 4;

    /**
     * integer 5
     */
    public static final Integer FIVE = 5;

    /**
     * integer 10
     */
    public static final Integer TEN = 10;

    /**
     * integer 7
     */
    public static final Integer SEVEN = 7;

    /**
     * double 7
     */
    public static final Double SEVEN_DOUBLE = 7.00;

    /**
     * integer 8
     */
    public static final Integer EIGHT = 8;

    /**
     * integer 9
     */
    public static final Integer NINE = 9;

    /**
     * integer 12
     */
    public static final Integer TWELVE = 12;

    /**
     * integer 16
     */
    public static final Integer SIXTEEN = 16;
    /**
     * integer 100
     */
    public static final Integer HUNDRED = 100;
    /**
     * integer 10000
     */
    public static final Integer TEN_THOUSAND = 10000;

    /**
     * integer 1000
     */
    public static final Integer THOUSAND = 1000;
    /**
     * integer 200
     */
    public static final Integer TWO_HUNDRED = 200;
    /**
     * integer 500
     */
    public static final Integer FIVE_HUNDRED = 500;
    /**
     * integer 15
     */
    public static final Integer FIFTEEN = 15;

    /**
     * integer 20
     */
    public static final Integer TWENTY = 20;
    /**
     * integer 30
     */
    public static final Integer THIRTY = 30;
    /**
     * integer 50
     */
    public static final Integer FIFTY = 50;

    /**
     * big decimal 100
     */
    public static BigDecimal DECIMAL_HUNDRED = new BigDecimal(100);
}

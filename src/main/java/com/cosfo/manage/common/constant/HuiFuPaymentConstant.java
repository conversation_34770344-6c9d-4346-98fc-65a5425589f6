package com.cosfo.manage.common.constant;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date : 2022/12/21 15:43
 */
public class HuiFuPaymentConstant implements Serializable {
    /**
     * 延时确认接口地址
     */
    public static final String EXCHANGE_CONFIRM = "/v2/trade/payment/delaytrans/confirm";
    /**
     * 扫码交易查询接口
     */
    public static final String EXCHANGE_QUERY = "/v2/trade/payment/scanpay/query";
    /**
     * 扫码交易退款接口
     */
    public static final String EXCHANGE_CANCEL = "/v2/trade/payment/scanpay/refund";
    /**
     * 扫码交易关单接口
     */
    public static final String EXCHANGE_CLOSE = "/v2/trade/payment/scanpay/close";

    /**
     * 结算记录查询接口
     */
    public static final String SETTLE_QUERY = "/v2/merchant/basicdata/settlement/query";
    /**
     * 商户用户信息查询接口
     */
    public static final String USER_QUERY = "/v2/user/basicdata/query";
    /**
     * 商户详细信息查询接口
     */
    public static final String MERCHANT_QUERY = "/v2/merchant/basicdata/query";

    /**
     * 交易对象名称
     */
    public static final String STORE_NAME = "交易对象名称";

    /**
     * 交易日期
     */
    public static final String EXCHANGE_DATE = "交易日期";
    /**
     * 收支类型
     */
    public static final String TYPE = "收支类型";
    /**
     * 订单编号
     */
    public static final String ORDER_NO = "订单编号";
    /**
     * 外部交易订单号
     */
    public static final String PAYMENT_NO = "外部交易订单号";
    /**
     * 交易类型
     */
    public static final String EXCHANGE_TYPE = "交易类型";
    /**
     * 支付方式
     */
    public static final String PAY_TYPE = "支付方式";
    /**
     * 银行流水号
     */
    public static final String BILL_NO = "银行流水号";
    /**
     * 银行结算状态
     */
    public static final String SETTLE_CONFIG = "银行结算状态";
    /**
     * 结算日期
     */
    public static final String SETTLE_DAY = "结算日期";
    /**
     * 分账日期
     */
    public static final String PROFIT_DAY = "分账日期";
    /**
     * 分账状态
     */
    public static final String PROFIT_STATUS = "分账状态";
    /**
     * 分账接收方主体名称
     */
    public static final String PROFIT_MAIN_NAME = "分账接收方主体名称";
    /**
     * 分账交易号
     */
    public static final String PROFIT_TRANSACTION = "分账交易号";
    /**
     * 原交易银行流水号
     */
    public static final String PROFIT_WEIXIN_ORDER_ID = "原交易银行流水号";
    /**
     * 分账交易订单号
     */
    public static final String PROFIT_ORDER_ID = "分账交易订单号";
    /**
     * 汇付支付收取手续费的临界值
     */
    public static final BigDecimal FEE = new BigDecimal(4.18);
    /**
     * 手续费取负值的值
     */
    public static final BigDecimal NEGATIVE = new BigDecimal(-1);
}

package com.cosfo.manage.common.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: monna.chen
 * @Date: 2023/12/20 16:01
 * @Description: 代仓申请相关常量
 */
public class ProductAgentConstants {
    /**
     * 代仓申请列表，允许排序的字段
     */
    public static final List<String> AGENT_ORDER_FIELD_LIST = Arrays.asList("firstApplyTimeSort", "lastUpdateTimeSort");

    public static final Map<String, String> AGENT_ORDER_FIELD_MAP = new HashMap<>();

    static {
        AGENT_ORDER_FIELD_MAP.put("firstApplyTimeSort", "firstApplyTime");
        AGENT_ORDER_FIELD_MAP.put("lastUpdateTimeSort", "lastUpdateTime");
    }

    public static final List<String> AGENT_ORDER_SORT_LIST = Arrays.asList("asc", "ASC", "desc", "DESC");
}

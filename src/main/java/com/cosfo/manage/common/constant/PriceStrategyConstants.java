package com.cosfo.manage.common.constant;

import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cosfo.manage.common.context.StorePriceTypeEnum;
import net.xianmu.common.exception.ParamsException;

/**
 * @author: monna.chen
 * @Date: 2023/8/4 15:06
 * @Description:
 */
public class PriceStrategyConstants {

    /**
     * 价格类型：1-默认价格 2-指定分组 3-指定门店
     */
    public static final Integer PRICE_TYPE_DEFAULT = 1;
    public static final Integer PRICE_TYPE_STORE_GROUP = 2;
    public static final Integer PRICE_TYPE_STORE = 3;


    /**
     * 由价格类型切换至策略类型
     * pricetype: 1-默认价格 2-指定分组 3-指定门店
     * targetType: 0-品牌方,1-门店,2-运营区域,3-门店分组
     * 对应关系： 1-0 2-3 3-1
     *
     * @param priceType
     * @return
     */
    public static Integer switchTargetType(Integer priceType) {
        if (PriceStrategyConstants.PRICE_TYPE_DEFAULT.equals(priceType)) {
            return PriceTargetTypeEnum.TENANT.getCode();
        }
        if (PriceStrategyConstants.PRICE_TYPE_STORE_GROUP.equals(priceType)) {
            return PriceTargetTypeEnum.STORE_GROUP.getCode();
        }
        if (PriceStrategyConstants.PRICE_TYPE_STORE.equals(priceType)) {
            return PriceTargetTypeEnum.STORE.getCode();
        }
        throw new ParamsException("价格类型错误");
    }


    /**
     * 由策略类型切换至价格类型
     * targetType: 0-品牌方,1-门店,2-运营区域,3-门店分组
     * priceType: 1-默认价格 2-指定分组 3-指定门店
     * 对应关系： 0-1 1-3 3-2
     *
     * @param targetType
     * @return
     */
    public static Integer switchPriceType(Integer targetType) {
        if (PriceTargetTypeEnum.TENANT.getCode().equals(targetType)) {
            return PriceStrategyConstants.PRICE_TYPE_DEFAULT;
        }
        if (PriceTargetTypeEnum.STORE.getCode().equals(targetType)) {
            return PriceStrategyConstants.PRICE_TYPE_STORE;
        }
        if (PriceTargetTypeEnum.STORE_GROUP.getCode().equals(targetType)) {
            return PriceStrategyConstants.PRICE_TYPE_STORE_GROUP;
        }
        throw new ParamsException("价格策略类型为运营区域，暂不支持展示！");
    }

    /**
     * 由策略类型切换至定价方式
     * targetType: 0-品牌方,1-门店,2-运营区域,3-门店分组
     * storePriceType: 0-统一价,1-其他价
     * 对应关系： 0-0 1-1 3-1
     *
     * @param targetType
     * @return
     */
    public static Integer switchStorePriceType(Integer targetType) {
        if (PriceTargetTypeEnum.TENANT.getCode().equals(targetType)) {
            return StorePriceTypeEnum.ALL.getCode();
        } else {
            return StorePriceTypeEnum.OTHER.getCode();
        }
    }
}

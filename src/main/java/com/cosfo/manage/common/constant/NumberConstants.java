package com.cosfo.manage.common.constant;

import java.math.BigDecimal;

/**
 * @description 常用数值常量
 * <AUTHOR>
 * @date 2022/5/12 11:33
 */
public class NumberConstants {

    /**
     * 数字0
     */
    public static final Integer ZERO = 0;

    /**
     * 数字1
     */
    public static final Integer ONE = 1;

    /**
     * 数字2
     */
    public static final Integer TWO = 2;

    /**
     * 数字3
     */
    public static final Integer THREE = 3;

    /**
     * 4
     */
    public static final Integer FOUR = 4;

    /**
     * 5
     */
    public static final Integer FIVE = 5;

    /**
     * 6
     */
    public static final Integer SIX = 6;

    /**
     * 7
     */
    public static final Integer SEVEN = 7;

    /**
     * 8
     */
    public static final Integer EIGHT = 8;

    /**
     * 数字10
     */
    public static final Integer TEN = 10;

    /**
     * 数字11
     */
    public static final Integer ELEVEN = 11;

    /**
     * 16
     */
    public static final Integer SIXTEEN = 16;

    /**
     * 数字-1
     */
    public static final Integer NEGATIVE_ONE = -1;

    /**
     * 数字50
     */
    public static final Integer FIFTY = 50;

    /**
     * 数字20
     */
    public static final Integer TWENTY = 20;

    /**
     * 数字30
     */
    public static final Integer THIRTY = 30;

    /**
     * 数字32
     */
    public static final Integer THIRTY_TWO = 32;

    /**
     * 数字60
     */
    public static final Integer SIXTY = 60;

    /**
     * 数字100
     */
    public static final Integer HUNDRED = 100;

    /**
     *数字255
     */
    public static final Integer TWO_HUNDRED_AND_FIFTY_FIVE = 255;

    /**
     * 数字200
     */
    public static final Integer TWO_HUNDRED = 200;


    /**
     * 数字500
     */
    public static final Integer FIVE_HUNDRED = 500;

    /**
     * big decimal 100
     */
    public static final BigDecimal ONE_HUNDRED = new BigDecimal(100);

    /**
     * 数字1000
     */
    public static final Integer ONE_THOUSAND = 1000;

    /**
     * 9999
     */
    public static final BigDecimal FOUR_NINE = new BigDecimal(9999);

    /**
     * 0.01
     */
    public static final BigDecimal CENTS = BigDecimal.valueOf(0.01);

    /**
     * 不存在的ID值
     */
    public static final Long NOT_EXIST_ID = -1L;

}

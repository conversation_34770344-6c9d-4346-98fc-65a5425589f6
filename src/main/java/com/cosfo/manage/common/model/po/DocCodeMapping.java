package com.cosfo.manage.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 映射关系
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Getter
@Setter
@TableName("doc_code_mapping")
public class DocCodeMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 映射类型 1=门店
     */
    @TableField("target_type")
    private Integer targetType;

    /**
     * 外部系统类型 1=美团
     */
    @TableField("channel_type")
    private Integer channelType;

    /**
     * 外部系统code
     */
    @TableField("out_code")
    private String outCode;

    /**
     * 外部系统名称
     */
    @TableField("out_name")
    private String outName;

    /**
     * 内部系统code
     */
    @TableField("target_code")
    private String targetCode;


}

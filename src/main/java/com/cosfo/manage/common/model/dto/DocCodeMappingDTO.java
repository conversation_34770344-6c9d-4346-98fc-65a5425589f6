package com.cosfo.manage.common.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DocCodeMappingDTO implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 映射类型 1=门店
     */
    private Integer targetType;

    /**
     * 外部系统类型 1=美团
     */
    private Integer channelType;

    /**
     * 外部系统code
     */
    private String outCode;

    /**
     * 外部系统名称
     */
    private String outName;

    /**
     * 内部系统code
     */
    private String targetCode;
}

package com.cosfo.manage.common.service.impl;

import com.cosfo.manage.common.model.converter.DocCodeMappingConverter;
import com.cosfo.manage.common.model.dto.DocCodeMappingDTO;
import com.cosfo.manage.common.model.po.DocCodeMapping;
import com.cosfo.manage.common.repository.DocCodeMappingRepository;
import com.cosfo.manage.common.service.DocCodeMappingService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DocCodeMappingServiceImpl implements DocCodeMappingService {
    @Resource
    private DocCodeMappingRepository docCodeMappingRepository;

    @Override
    public boolean saveCodeMapping(List<DocCodeMappingDTO> mappingDTOS) {
        Map<String, DocCodeMappingDTO> outCodeMap = mappingDTOS.stream().collect(Collectors.toMap(DocCodeMappingDTO::getOutCode, d -> d, (oldV, newV) -> newV));
        List<DocCodeMapping> docCodeMappingList = DocCodeMappingConverter.INSTANCE.toDocCodeMappingList(Lists.newArrayList(outCodeMap.values()));
        docCodeMappingRepository.batchInsert(docCodeMappingList);
        return true;
    }

    @Override
    public List<DocCodeMappingDTO> selectByTargetCodeAndType(List<String> targetCodeList, Integer targetType, Long tenantId, Integer channelType) {
        List<DocCodeMapping> docCodeMappingList = docCodeMappingRepository.selectByTargetCodeAndType(targetCodeList, targetType, channelType, tenantId);

        return DocCodeMappingConverter.INSTANCE.toDocCodeMappingDTOList(docCodeMappingList);
    }

    @Override
    public List<DocCodeMappingDTO> selectByOutCodeAndType(Set<String> outCodeList, Integer targetType, Integer channelType, Long tenantId) {
        List<DocCodeMapping> docCodeMappingList = docCodeMappingRepository.selectByOutCodeAndType(outCodeList, targetType, channelType, tenantId);
        return DocCodeMappingConverter.INSTANCE.toDocCodeMappingDTOList(docCodeMappingList);
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        docCodeMappingRepository.deleteByIds(ids);
        return true;
    }
}

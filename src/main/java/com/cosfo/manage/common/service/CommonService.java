package com.cosfo.manage.common.service;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.cosfo.manage.common.model.po.CommonLocationCity;
import com.cosfo.manage.common.model.vo.CommonLocationProvinceVO;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;

/**
 * 描述: 通用服务类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
public interface CommonService {

    /**
     * 下载文件
     *
     * @param response
     * @param fileName 文件名
     * @param filePath 文件路径
     */
    void downloadFile(HttpServletResponse response, String fileName, String filePath);

    /**
     * 导出数据
     *
     * @param list
     * @param name
     * @return
     */

    /**
     * 删除文件
     * @param filePath
     */
    void deleteFile(String filePath);

    /**
     * 生成excel并上传到云存储 且更新文件下载记录
     * @param dataList
     * @param excelTypeEnum
     * @param fileDownloadRecordId
     */
//    Boolean generateAndUploadExcel(List dataList, ExcelTypeEnum excelTypeEnum, Long fileDownloadRecordId);

    /**
     * 本地已生成的excel临时文件上传到云存储 并更新文件下载记录
     * @param filePath
     * @param fileName
     * @param excelTypeEnum
     * @param fileDownloadRecordId∂
     * @return
     */
//    Boolean uploadExcelAndUpdateDownloadStatus(String filePath, String fileName, ExcelTypeEnum excelTypeEnum, Long fileDownloadRecordId);

    /**
     * 导出文件
     * @param list
     * @param name
     * @return
     */
    String exportExcel(List list, String name);

    /**
     * 指定策略导出文件
     * @param list
     * @param name
     * @param cellWriteHandler
     * @return
     */
    String exportExcel(List list, String name, CellWriteHandler cellWriteHandler);

    /**
     * 查询城市下的城市集合
     * @param provinceId
     * @return
     */
    List<CommonLocationCity> queryCitiesByProvinceId(Long provinceId);

    /**
     * 省份城市信息
     * @return
     */
    List<CommonLocationProvinceVO> cityList();

    /**
     * 文件
     *
     * @param file
     * @param filePath
     */
    void uploadExcelFile(File file, String filePath);
}

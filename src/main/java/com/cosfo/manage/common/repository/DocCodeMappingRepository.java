package com.cosfo.manage.common.repository;

import com.cosfo.manage.common.model.po.DocCodeMapping;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 映射关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface DocCodeMappingRepository extends IService<DocCodeMapping> {

    /**
     * 批量插入
     */
    void batchInsert(List<DocCodeMapping> docCodeMappingList);

    /**
     * 根据 targetCode 和 type 查询映射
     */
    List<DocCodeMapping> selectByTargetCodeAndType(List<String> targetCodeList, Integer targetType, Integer channelType,Long tenantId);

    List<DocCodeMapping> selectByOutCodeAndType(Set<String> outCodeList, Integer targetType, Integer channelType, Long tenantId);

    /**
     * 根据ids批量删除映射
     */
    void deleteByIds(List<Long> ids);
}

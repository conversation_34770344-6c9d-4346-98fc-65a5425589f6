package com.cosfo.manage.common.executor;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.arms.sdk.v1.async.TraceExecutors;
import com.cosfo.manage.common.constant.NumberConstants;

import java.util.concurrent.*;

/**
 * 线程池工厂
 * <AUTHOR>
 */
public class ExecutorFactory {

    private static ExecutorService generateExcelExecutor0 = new ThreadPoolExecutor(NumberConstants.TWO, NumberConstants.TEN,
            60L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), new NamedThreadFactory("excelExecutor", false),
            new ThreadPoolExecutor.AbortPolicy());


    public static ExecutorService generateExcelExecutor = TraceExecutors.wrapExecutorService(generateExcelExecutor0, true);


    private static ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(NumberConstants.EIGHT, NumberConstants.TEN,
            60L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), new NamedThreadFactory("async-normal-service", false),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 异步线程池
     */
    public static ExecutorService ASYNC_EXECUTOR = TraceExecutors.wrapExecutorService(EXECUTOR_SERVICE, true);


    /**
     * 总部代下单 - 创建订单 线程池
     */
    private static ExecutorService AGENT_ORDER_CREATE_EXECUTOR_SERVICE = new ThreadPoolExecutor(NumberConstants.TWO, NumberConstants.THREE,
        60L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(500), new NamedThreadFactory("agent_order_create_executor", false),
        new ThreadPoolExecutor.AbortPolicy());

    public static ExecutorService AGENT_ORDER_CREATE_EXECUTOR = TraceExecutors.wrapExecutorService(AGENT_ORDER_CREATE_EXECUTOR_SERVICE, true);

    /**
     * 总部代下单 - 发送短信 线程池
     */
    private static ExecutorService AGENT_ORDER_SMS_EXECUTOR_SERVICE = new ThreadPoolExecutor(NumberConstants.TWO, NumberConstants.TEN,
        60L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(500), new NamedThreadFactory("agent-order-sms-executor", false),
        new ThreadPoolExecutor.AbortPolicy());

    public static ExecutorService AGENT_ORDER_SMS_EXECUTOR = TraceExecutors.wrapExecutorService(AGENT_ORDER_SMS_EXECUTOR_SERVICE, true);

}

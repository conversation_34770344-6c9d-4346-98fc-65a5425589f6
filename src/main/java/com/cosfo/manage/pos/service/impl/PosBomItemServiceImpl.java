package com.cosfo.manage.pos.service.impl;

import com.cosfo.manage.pos.model.po.PosBomItem;
import com.cosfo.manage.pos.repository.PosBomItemRepository;
import com.cosfo.manage.pos.service.PosBomItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PosBomItemServiceImpl implements PosBomItemService {

    @Resource
    private PosBomItemRepository posBomItemRepository;

    @Override
    public void batchInsert(List<PosBomItem> posBomItemList) {
        posBomItemRepository.batchInsert(posBomItemList);
    }
}

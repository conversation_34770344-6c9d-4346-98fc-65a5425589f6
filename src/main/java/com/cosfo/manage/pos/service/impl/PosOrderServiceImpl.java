package com.cosfo.manage.pos.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.facade.PriceFacade;
import com.cosfo.manage.market.model.vo.MarketItemInfoPageVO;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.pos.model.po.PosOrder;
import com.cosfo.manage.pos.model.po.PosOrderItem;
import com.cosfo.manage.pos.repository.PosOrderItemRepository;
import com.cosfo.manage.pos.repository.PosOrderRepository;
import com.cosfo.manage.pos.service.PosBomService;
import com.cosfo.manage.pos.service.PosOrderService;
import com.cosfo.storeinventory.enums.BillTypeEnum;
import com.cosfo.storeinventory.model.dto.StoreInventoryBillDTO;
import com.cosfo.storeinventory.model.dto.StoreInventoryBillItemDTO;
import com.cosfo.storeinventory.service.StoreInventoryBillCommandService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PosOrderServiceImpl implements PosOrderService {

    @Resource
    private PosOrderItemRepository posOrderItemRepository;
    @Resource
    private PosOrderRepository posOrderRepository;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private PosBomService bomService;
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private PriceFacade priceFacade;
    @Resource
    private StoreInventoryBillCommandService billCommandService;

    /**
     * 保存订单  订单项 优惠信息
     * @param orderItems
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePosOrder(PosOrder order,List<PosOrderItem> orderItems) {
        PosOrder posOrderDb = posOrderRepository.queryByOrderNo(order.getOrderNo (),order.getChannelType (),order.getOutStoreCode (),order.getTenantId ());
        if(ObjectUtil.isNotNull (posOrderDb)){
            order.setId (posOrderDb.getId ());
            posOrderItemRepository.removeByPosOrderId(posOrderDb.getId (), order.getTenantId ());
        }
        posOrderRepository.saveOrUpdate (order);

        orderItems.forEach (item-> {
            item.setPosOrderId (order.getId ());
            item.setOutStoreCode(order.getOutStoreCode ());
            item.setOutStoreName(order.getOutStoreName ());
            item.setMerchantStoreCode(order.getMerchantStoreCode ());
            item.setOrderNo(order.getOrderNo ());
            item.setAvailableDate(order.getAvailableDate ());
            item.setMerchantStoreCode (order.getMerchantStoreCode ());
            item.setChannelType (order.getChannelType ());
            item.setTenantId (order.getTenantId ());
        });
        posOrderItemRepository.saveBatch (orderItems);

//        保存出库单
        storeInventoryOutboundWithBill(order,orderItems);
    }

    @Override
    public void storeInventoryOutboundWithBill(PosOrder posOrder,List<PosOrderItem> orderItems) {
        Long tenantId = posOrder.getTenantId ();
        // 查看租户是否开启门店进销存
        Boolean storeInventorySwitch = userCenterTenantFacade.getStoreInventorySwitch(tenantId);
        if(!storeInventorySwitch){
            log.error("租户未开启门店进销存，不处理，tenantId={}, posOrderNO={}", tenantId, posOrder.getOrderNo ());
            return;
        }
        Map<String, Integer> menuMap = orderItems.stream ().collect (Collectors.toMap (PosOrderItem::getOutMenuCode, PosOrderItem::getQuantity));
        if(CollectionUtil.isEmpty (menuMap)){
            return;
        }

        Map<Long, BigDecimal> itemQuantityMap = bomService.calculateBomItemQuantity (posOrder.getTenantId (), posOrder.getChannelType (), menuMap);
        if(CollectionUtil.isEmpty (itemQuantityMap)){
            return;
        }
        MerchantStorePageResultResp merchantStore = userCenterMerchantStoreFacade.getMerchantStoreByCode (posOrder.getTenantId (), posOrder.getMerchantStoreCode ());
        if(merchantStore == null){
            return;
        }
        Map<Long, BigDecimal> itemPriceMap = priceFacade.queryItemPriceWithDefaultPrice (tenantId, merchantStore.getId (), itemQuantityMap.keySet ());
        if(CollectionUtil.isEmpty (itemPriceMap)){
            return;
        }


        // 查询商品信息，库存单位
        Map<Long, MarketItemInfoPageVO> itemInfoPageVOMap = queryItemMap (posOrder.getTenantId (), itemQuantityMap.keySet ());

        // 更新库存，记录库存变动记录
        StoreInventoryBillDTO reqDTO = new StoreInventoryBillDTO ();
        reqDTO.setBillType(BillTypeEnum.OUT_SALE.getCode ());

        reqDTO.setUserName("系统");
        reqDTO.setUserId (-1L);
        reqDTO.setRelatedDataId(posOrder.getId ());
        reqDTO.setRelatedDataNo(posOrder.getOrderNo ());
        List<StoreInventoryBillItemDTO> billItemDTOS = new ArrayList<> ();
        itemInfoPageVOMap.forEach ((itemId, itemInfoPageVO)->{
            BigDecimal qutity = itemQuantityMap.get (itemId);
            BigDecimal storeInventoryCostUnitMultiple = itemInfoPageVO.getStoreInventoryCostUnitMultiple ();
            BigDecimal storeOrderingInventoryUnitMultiple = itemInfoPageVO.getStoreOrderingInventoryUnitMultiple ();
            if(qutity != null && itemPriceMap.get (itemId)!= null
                    && storeInventoryCostUnitMultiple!= null && storeInventoryCostUnitMultiple.compareTo (BigDecimal.ZERO)!=0
                    && storeOrderingInventoryUnitMultiple!= null && storeOrderingInventoryUnitMultiple.compareTo (BigDecimal.ZERO)!=0 ) {
                StoreInventoryBillItemDTO item = new StoreInventoryBillItemDTO ();
                item.setItemId (itemId);
                item.setTitle (itemInfoPageVO.getTitle ());
                item.setSpecification (itemInfoPageVO.getSpecification ());
                item.setOptQuantity (qutity.divide (itemInfoPageVO.getStoreInventoryCostUnitMultiple ()));
                item.setStoreInventoryUnit (itemInfoPageVO.getStoreInventoryUnit ());
                item.setUnitPrice (itemPriceMap.get (itemId).divide (storeOrderingInventoryUnitMultiple));
                billItemDTOS.add (item);
            }
        });
        reqDTO.setItemList(billItemDTOS);

        billCommandService.billSubmit (reqDTO, tenantId, merchantStore.getId ());
    }

    private Map<Long,MarketItemInfoPageVO> queryItemMap(Long tenantId,Set<Long> itemIds) {
        List<MarketItemInfoPageVO> info = marketItemService.queryMarketItemListWithUnit (tenantId,itemIds);
        if(CollectionUtil.isEmpty (info)){
            return Collections.emptyMap ();
        }
        return info.stream().collect(Collectors.toMap(MarketItemInfoPageVO::getId,x -> x));
    }

}

package com.cosfo.manage.pos.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.pos.model.converter.PosBomConverter;
import com.cosfo.manage.pos.model.dto.PosBomDTO;
import com.cosfo.manage.pos.model.dto.PosBomDelDTO;
import com.cosfo.manage.pos.model.dto.PosBomDetailQueryDTO;
import com.cosfo.manage.pos.model.dto.PosBomQueryDTO;
import com.cosfo.manage.pos.model.vo.PosBomVO;
import com.cosfo.manage.pos.service.PosBomService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * bom成本卡 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@RestController
@RequestMapping("/pos/pos-bom")
public class PosBomController {
    @Resource
    private PosBomService posBomService;

    /**
     * bom成本卡列表
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<PosBomVO>> listPage(@Valid @RequestBody PosBomQueryDTO posBomQueryDTO) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        posBomQueryDTO.setTenantId(tenantId);
        PageInfo<PosBomDTO> pageInfo = posBomService.listPage(posBomQueryDTO);
        return CommonResult.ok(PosBomConverter.INSTANCE.toPageInfoVO(pageInfo));
    }
    /**
     * 删除成本卡/半成品
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/delete")
    public CommonResult<Boolean> delete(@RequestBody PosBomDelDTO posBomDelDTO) {
        return CommonResult.ok(posBomService.delete(posBomDelDTO.getPosBomId()));
    }
    /**
     * 查看成本卡/半成品 详情
     */
    @PostMapping("/query/detail")
    public CommonResult<PosBomVO> detail(@RequestBody PosBomDetailQueryDTO queryDTO) {
        PosBomDTO detail = posBomService.detail(queryDTO.getPosBomId());
        return CommonResult.ok(PosBomConverter.INSTANCE.toPosBomVO(detail));
    }

    /**
     * 导入excel
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/import")
    public CommonResult<ExcelImportResDTO> importExcel(@RequestBody MultipartFile file) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        return CommonResult.ok(posBomService.importExcel(file, tenantId));
    }

    /**
     * 导出半成品
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/exportSemiFinished")
    public CommonResult<Void> exportSemiFinished(@RequestBody PosBomQueryDTO posBomQueryDTO) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        posBomQueryDTO.setTenantId(tenantId);
        posBomService.exportSemiFinished(posBomQueryDTO);
        return CommonResult.ok();
    }

    /**
     * 模版下载
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/download/template")
    public CommonResult<String> downloadTemplate() {
        return CommonResult.ok(posBomService.downloadTemplate());
    }

    /**
     * 保存/修改 成本卡
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/query/upsert")
    public CommonResult<Long> upsert(@Valid @RequestBody PosBomDTO dto) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        dto.setTenantId (tenantId);
        dto.setTargetType (1);
        dto.setTargetValue (String.valueOf (tenantId));
        return CommonResult.ok(posBomService.upsertPosBom(dto));
    }
}


package com.cosfo.manage.pos.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.pos.model.dto.PosOrderQueryDTO;
import com.cosfo.manage.pos.model.vo.PosOrderItemVO;
import com.cosfo.manage.pos.domain.PosOrderDomainService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * pos订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@RestController
@RequestMapping("/pos/pos-order")
public class PosOrderController extends BaseController {

    @Resource
    private PosOrderDomainService posOrderDomainService;
    /**
     * pos订单列表
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<PosOrderItemVO>> listPage(@Valid @RequestBody PosOrderQueryDTO dto) {
        return CommonResult.ok(posOrderDomainService.listPage(dto,getMerchantInfoDTO().getTenantId()));
    }
    /**
     * 导入excel
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/import")
    public CommonResult<ExcelImportResDTO> importExcel(@RequestBody MultipartFile file) {
        return CommonResult.ok(posOrderDomainService.importExcel(file,getMerchantInfoDTO().getTenantId()));
    }
}


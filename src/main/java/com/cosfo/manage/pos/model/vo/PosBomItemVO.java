package com.cosfo.manage.pos.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class PosBomItemVO {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 1=美团
     */
    private Integer channelType;

    /**
     * 租户id
     */
    private Long tenantId;

//    /**
//     * 外部系统门店code
//     */
//    private String outStoreCode;
//
    /**
     * 帆台门店id
     */
    private Long merchantStoreId;

    /**
     * 外部系统物料编码
     */
    private String outItemCode;

    /**
     * 帆台商品id（弃用）
     */
    private Long marketItemId;

    /**
     * 生效日（弃用）
     */
    private LocalDate availableDate;

    /**
     * 单位成本
     */
    private BigDecimal unitCostPrice;

    /**
     * 净料量/净用量
     */
    private BigDecimal netQuantity;

    /**
     * 参考用料成本（弃用）
     */
    private BigDecimal referenceCostPrice;

    /**
     * 备注
     */
    private String remarks;

    /**
     * bom类型 1=品牌方
     */
    private Integer targetType;

    /**
     * 外部系统门店code/品牌方id
     */
    private String targetValue;


    /**
     * 物品名称(规格)
     */
    private String outItemName;

    /**
     * 物品单位
     */
    private String outItemUnit;

    /**
     * 用料成本（弃用）
     */
    private BigDecimal materialCostPrice;

    /**
     * 组成类型1=原料（帆台商品）,2=bom（半成品）
     */
    private Integer compositionType;
    /**
     * 原料id(帆台商品skuid)/pom_bom_id
     */
    private Long compositionId;
    /**
     * 标准单价（成本单价）
     */
    private BigDecimal costUnitPrice;
    /**
     * 成本单位
     */
    private String storeCostUnit;

    /**
     * 库存单位
     */
    private String storeInventoryUnit;

    /**
     * 单位换算比例
     */
    private BigDecimal unitMultiple;
    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 规格
     */
    private String specification;

    /**
     * 商品头图
     */
    private String mainPicture;


    /**
     * 门店订货单位 - 原料时需要
     */
    private String storeOrderingUnit;
    /**
     * 0 下架 1 上架 - 原料时需要
     */
    private Integer onSale;
    /**
     * 分类全名- 原料时需要
     */
    private String marketItemClassificationStr;
}

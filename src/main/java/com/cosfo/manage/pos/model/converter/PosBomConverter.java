package com.cosfo.manage.pos.model.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.pos.model.dto.PosBomDTO;
import com.cosfo.manage.pos.model.dto.PosBomImportDTO;
import com.cosfo.manage.pos.model.po.PosBom;
import com.cosfo.manage.pos.model.vo.PosBomVO;
import com.github.pagehelper.PageInfo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface PosBomConverter {

    PosBomConverter INSTANCE = Mappers.getMapper(PosBomConverter.class);

    @Mapping(target = "list", source = "records")
    @Mapping(target = "hasNextPage", expression = "java(posBomPage.hasNext())")
    PageInfo<PosBomDTO> toPageInfo(Page<PosBom> posBomPage, @Context Map<Long, String> storeNameMap, @Context Map<String, BigDecimal> calcCostUnitPrice, @Context Map<String, Integer> posBomItemCountMap);


    List<PosBomDTO> toListDTO(List<PosBom> posBomPage, @Context Map<Long, String> storeNameMap, @Context Map<Long, BigDecimal> grossProfitRateMap);

    PageInfo<PosBomVO> toPageInfoVO(PageInfo<PosBomDTO> pageInfo);

    PosBomDTO toPosBomDTO(PosBom posBom, @Context Map<Long, String> storeNameMap, @Context Map<Long, BigDecimal> grossProfitRateMap);

    @AfterMapping
    default void setStoreName(@MappingTarget PosBomDTO posBomDTO, @Context Map<Long, String> storeNameMap,@Context Map<String, BigDecimal> calcCostUnitPrice, @Context Map<String, Integer> posBomItemCountMap) {
        if (storeNameMap != null) {
            String storeName = storeNameMap.get(posBomDTO.getMerchantStoreId());
            posBomDTO.setMerchantStoreName(storeName);
        }
        if (calcCostUnitPrice != null) {
            posBomDTO.setCostUnitPrice (calcCostUnitPrice.get(posBomDTO.getOutMenuCode ()));
            if(posBomDTO.getCostUnitPrice () != null && BigDecimal.ZERO.compareTo (posBomDTO.getPrice ())!=0) {
                BigDecimal grossProfitRate = (posBomDTO.getPrice ().subtract (calcCostUnitPrice.get (posBomDTO.getOutMenuCode ())).divide (posBomDTO.getPrice (), 4, BigDecimal.ROUND_HALF_UP).multiply (BigDecimal.valueOf (100)));
                posBomDTO.setGrossProfitRate (grossProfitRate.setScale (2, BigDecimal.ROUND_HALF_UP));
            }
        }
        if (posBomItemCountMap != null) {
            posBomDTO.setPosBomItemCount (posBomItemCountMap.get(posBomDTO.getOutMenuCode ()));
        }
    }

    @Mapping(target = "channelType", expression = "java(com.cosfo.manage.common.context.DocCodeChannelTypeEnum.getByDesc(importDTO.getChannelTypeDesc()).getCode())")
    @Mapping(target = "bomType", defaultValue = "1")
    PosBomDTO toPosBomDTO(PosBomImportDTO importDTO);

    List<PosBom> toPosBomList(List<PosBomDTO> posBomDTOList);

//    List<PosBomDTO> toPosBomDTOList(List<PosBom> posBomList);

    PosBomVO toPosBomVO(PosBomDTO posBomDTO);
    PosBom toPosBom(PosBomDTO posBomDTO);
}

package com.cosfo.manage.pos.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * bom成本卡
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Getter
@Setter
@TableName("pos_bom")
public class PosBom implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 1=美团
     */
    @TableField("channel_type")
    private Integer channelType;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

//    /**
//     * 外部系统门店code
//     */
//    @TableField("out_store_code")
//    private String outStoreCode;

    /**
     * 外部系统规格
     */
    @TableField("out_menu_specification")
    private String outMenuSpecification;

    /**
     * 外部系统商品编码
     */
    @TableField("out_menu_code")
    private String outMenuCode;

    /**
     * 帆台门店id
     */
    @TableField("merchant_store_id")
    private Long merchantStoreId;

    /**
     * 外部系统商品名称
     */
    @TableField("out_menu_name")
    private String outMenuName;

    /**
     * 售卖价
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 生效日
     */
    @TableField("available_date")
    private LocalDate availableDate;


    /**
     * bom类型 1=品牌方
     */
    @TableField("target_type")
    private Integer targetType;

    /**
     * 外部系统门店code/品牌方id
     */
    @TableField("target_value")
    private String targetValue;

    /**
     * 菜单商品类型 1=成品,2=半成品,3=套餐
     */
    @TableField("bom_type")
    private Integer bomType;

    /**
     * 主图
     */
    @TableField("main_picture")
    private String mainPicture;

    /**
     * 成本单位
     */
    @TableField("store_cost_unit")
    private String storeCostUnit;

    /**
     * 库存单位
     */
    @TableField("store_inventory_unit")
    private String storeInventoryUnit;

    /**
     * 单位换算比例
     */
    @TableField("unit_multiple")
    private BigDecimal unitMultiple;
}

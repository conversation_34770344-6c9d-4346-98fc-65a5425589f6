package com.cosfo.manage.pos.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * bom成本卡物料详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Getter
@Setter
@TableName("pos_bom_item")
public class PosBomItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    @TableField("out_menu_code")
    private String outMenuCode;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 1=美团
     */
    @TableField("channel_type")
    private Integer channelType;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

//    /**
//     * 外部系统门店code
//     */
//    @TableField("out_store_code")
//    private String outStoreCode;

    /**
     * 帆台门店id
     */
    @TableField("merchant_store_id")
    private Long merchantStoreId;

    /**
     * 外部系统物料编码
     */
    @TableField("out_item_code")
    private String outItemCode;

    /**
     * 帆台商品id(弃用)
     */
    @TableField("market_item_id")
    private Long marketItemId;

    /**
     * 生效日(弃用)
     */
    @TableField("available_date")
    private LocalDate availableDate;

    /**
     * 单位成本(弃用)
     */
    @TableField("unit_cost_price")
    private BigDecimal unitCostPrice;

    /**
     * 净料量
     */
    @TableField("net_quantity")
    private BigDecimal netQuantity;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;


    /**
     * bom类型 1=品牌方
     */
    @TableField("target_type")
    private Integer targetType;

    /**
     * 外部系统门店code/品牌方id
     */
    @TableField("target_value")
    private String targetValue;

    /**
     * 物品名称(规格)
     */
    @TableField("out_item_name")
    private String outItemName;

    /**
     * 物品单位
     */
    @TableField("out_item_unit")
    private String outItemUnit;
    /**
     * 组成类型1=原料,2=bom
     */
    @TableField("composition_type")
    private Integer compositionType;
    /**
     * 原料id(帆台商品skuid)/pom_bom_id
     */
    @TableField("composition_id")
    private Long compositionId;
}

package com.cosfo.manage.pos.model.dto;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PosBomQueryDTO extends BasePageInput implements Serializable {

    /**
     * 外部门店名称
     */
    private String outStoreName;

    /**
     * 外部货品名称
     */
    private String outMenuName;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 生效时间
     */
    private LocalDate availableDate;
    /**
     * 渠道 1=美团
     */
    private Integer channelType;
    /**
     * bom类型  2=半成品     1,3=bom成本卡
     */
    private List<Integer> bomType;
}

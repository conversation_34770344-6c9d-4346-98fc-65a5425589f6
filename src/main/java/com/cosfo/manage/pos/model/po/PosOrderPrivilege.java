package com.cosfo.manage.pos.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * pos订单优惠信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Getter
@Setter
//@TableName("pos_order_privilege")
public class PosOrderPrivilege implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * pos订单id
     */
    @TableField("pos_order_id")
    private Long posOrderId;

    /**
     * 优惠金额
     */
    @TableField("privilege_amount")
    private BigDecimal privilegeAmount;

    /**
     * 优惠类型：1:DISCOUNT:手动折扣,2:REBATE:折让,3:FREE:免单,4:PROMO:优惠活动或者优惠券,5:INTEGRAL:积分抵现,6:AUTODISCOUNT:自动折扣,7:平台优惠,8:商户优惠,9:物流优惠,10:代理商优惠,11:会员特价,12:附加费,13商品营销,14熟客折扣,15微信卡券,16宴请,17问题菜品,18批量赠送,19分享减免,20微信礼品卡,21服务费,22次卡,23拼团,24秒杀,25砍价,26服务次数
     */
    @TableField("privilege_value")
    private String privilegeValue;

    /**
     * 优惠类别（优惠类型为1、2、3时为商品名称，否则为优惠类型）
     */
    @TableField("type_detail_name")
    private String typeDetailName;

    /**
     * 外部系统商品编码
     */
    @TableField("type_name")
    private String typeName;

    /**
     * 优惠券id
     */
    @TableField("promo_id")
    private Long promoId;

    /**
     * 优惠券模板ID
     */
    @TableField("coupon_id")
    private Long couponId;


}

package com.cosfo.manage.pos.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class PosOrderImportDTO implements Serializable {
    @ExcelProperty("营业日期\n" +
            "(必填)")
    private LocalDate availableDate;

    @ExcelProperty("POS渠道\n" +
            "(必填，如美团、企迈、客如云等)")
    private String channelTypeDesc;

    @ExcelProperty("门店名称\n" +
            "(必填)")
    private String outStoreName;

    @ExcelProperty("门店编码\n" +
            "(必填)")
    private String outStoreCode;

    @ExcelProperty("订单编号\n" +
            "(必填)")
    private String orderNo;

    @ExcelProperty("商品名称\n" +
            "(必填)")
    private String outMenuName;

    @ExcelProperty("商品编码\n" +
            "(必填)")
    private String outMenuCode;

    @ExcelProperty("商品规格\n" +
            "(必填)")
    private String outMenuSpecification;

    @ExcelProperty("销售数量 \n" +
            "(必填)")
    private Integer quantity;

    @ExcelProperty("金额合计(元)\n" +
            "(必填)")
    private BigDecimal totalPrice;

    @ExcelProperty("商品优惠(元)\n" +
            "(必填)")
    private BigDecimal discountPrice;

    @ExcelProperty("商品收入(元)\n" +
            "(必填，等于金额合计-商品优惠)")
    private BigDecimal incomePrice;

    @ExcelProperty("折扣率\n" +
            "(必填，商品优惠/金额合计 *100%)")
    private BigDecimal discountRate;

    @ExcelProperty("税率")
    private BigDecimal taxRate;

    @ExcelProperty("税额(元)")
    private BigDecimal taxPrice;

    @ExcelProperty("不含税金额(元)")
    private BigDecimal excludeTaxPrice;

    @ExcelProperty("备注")
    private String remarks;

    @ExcelProperty("错误信息")
    private String errorMsg;

    public String check() {
        if (availableDate == null) {
            return "营业日期不能为空";
        }
        if (outMenuCode == null || outMenuCode.isEmpty()) {
            return "商品编码不能为空";
        }
        if (outMenuName == null || outMenuName.isEmpty()) {
            return "商品名称不能为空";
        }
        if (channelTypeDesc == null || channelTypeDesc.isEmpty()) {
            return "POS渠道不能为空";
        }
        if (outStoreName == null || outStoreName.isEmpty()) {
            return "门店名称不能为空";
        }
        if (outStoreCode == null || outStoreCode.isEmpty()) {
            return "门店编码不能为空";
        }
        if (outMenuSpecification == null || outMenuSpecification.isEmpty()) {
            return "商品规格不能为空";
        }
        if (quantity == null) {
            return "销售数量不能为空";
        }
        if (totalPrice == null) {
            return "合计金额不能为空";
        }
        if (discountPrice == null) {
            return "商品优惠不能为空";
        }
        if (incomePrice == null) {
            return "商品收入不能为空";
        }
        if (discountRate == null) {
            return "折扣率不能为空";
        }
        return null;
    }
}

package com.cosfo.manage.pos.mapper;

import com.cosfo.manage.pos.model.po.PosBom;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * bom成本卡 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Mapper
public interface PosBomMapper extends BaseMapper<PosBom> {

    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<PosBom> posBomList);


    List<PosBom> listLatestAvailableBom(@Param("tenantId") Long tenantId);

    /**
     * 获取租户列表
     * @return
     */
    List<Long> listTenantIds();
}

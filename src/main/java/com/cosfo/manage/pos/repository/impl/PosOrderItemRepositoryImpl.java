package com.cosfo.manage.pos.repository.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.merchant.model.po.MerchantStoreTradeSummary;
import com.cosfo.manage.pos.model.dto.PosOrderQueryDTO;
import com.cosfo.manage.pos.model.po.PosOrder;
import com.cosfo.manage.pos.model.po.PosOrderItem;
import com.cosfo.manage.pos.mapper.PosOrderItemMapper;
import com.cosfo.manage.pos.repository.PosOrderItemRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * pos子订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Service
public class PosOrderItemRepositoryImpl extends ServiceImpl<PosOrderItemMapper, PosOrderItem> implements PosOrderItemRepository {
    @Override
    public Page<PosOrderItem> listPage(PosOrderQueryDTO dto, Long tenantId) {
        LambdaQueryWrapper<PosOrderItem> queryWrapper = new LambdaQueryWrapper ();
        queryWrapper.eq (PosOrderItem::getTenantId,tenantId);
        queryWrapper.eq (PosOrderItem::getChannelType,dto.getChannelType ());
        queryWrapper.like (StringUtil.isNotBlank (dto.getOutStoreName()),PosOrderItem::getOutStoreName,dto.getOutStoreName ());
        queryWrapper.eq (StringUtil.isNotBlank (dto.getOrderNo()),PosOrderItem::getOrderNo,dto.getOrderNo ());
        queryWrapper.like (StringUtil.isNotBlank (dto.getOutMenuName()),PosOrderItem::getOutMenuName,dto.getOutMenuName ());
        queryWrapper.eq (StringUtil.isNotBlank (dto.getOutMenuCode()),PosOrderItem::getOutMenuCode,dto.getOutMenuCode ());
        queryWrapper.ge (StringUtil.isNotBlank (dto.getBeginTime()),PosOrderItem::getAvailableDate,dto.getBeginTime ());
        queryWrapper.le (StringUtil.isNotBlank (dto.getEndTime()),PosOrderItem::getAvailableDate,dto.getEndTime ());
        return page(new Page<>(dto.getPageIndex(), dto.getPageSize()), queryWrapper);
    }

    @Override
    public void removeByPosOrderId(Long id, Long tenantId) {
        LambdaQueryWrapper<PosOrderItem> wrapper = new LambdaQueryWrapper();
        wrapper.eq (PosOrderItem::getPosOrderId,id);
        wrapper.eq (PosOrderItem::getTenantId,tenantId);
        remove (wrapper);
    }

    @Override
    public void removeByPosOrderIds(List<Long> posOrderIds, Long tenantId) {
        LambdaQueryWrapper<PosOrderItem> wrapper = new LambdaQueryWrapper();
        wrapper.in (PosOrderItem::getPosOrderId,posOrderIds);
        wrapper.eq (PosOrderItem::getTenantId,tenantId);

        remove (wrapper);
    }

    @Override
    public List<PosOrderItem> listByPosOrderId(Long posOrderId, Long tenantId) {
        LambdaQueryWrapper<PosOrderItem> queryWrapper = new LambdaQueryWrapper ();
        queryWrapper.eq (PosOrderItem::getPosOrderId,posOrderId);
        queryWrapper.eq (PosOrderItem::getTenantId,tenantId);
        return list (queryWrapper);
    }
}

package com.cosfo.manage.pos.repository;

import com.cosfo.manage.pos.model.po.PosBomItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * bom成本卡物料详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface PosBomItemRepository extends IService<PosBomItem> {

    void batchInsert(List<PosBomItem> posBomItemList);

    /**
     * 根据posBomId获取
     */
    List<PosBomItem> listBomItem(Long tenantId, String menuCode, Integer targetType, String targetValue, Integer channelType, LocalDate availableDate);
    List<PosBomItem> listBomItem(Long tenantId, List<String> menuCode);

    void deleteByOutInfo(Long tenantId, String menuCode, Integer targetType, String targetValue, Integer channelType, LocalDate availableDate);
    void deleteByOutMenuCodes(Long tenantId, Set<String> keySet);

    List<PosBomItem> listBomItemByCompositionId(Long compositionId,Integer compositionType);

}

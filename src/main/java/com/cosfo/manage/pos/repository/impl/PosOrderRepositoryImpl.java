package com.cosfo.manage.pos.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.pos.model.dto.PosOrderQueryDTO;
import com.cosfo.manage.pos.model.po.PosOrder;
import com.cosfo.manage.pos.mapper.PosOrderMapper;
import com.cosfo.manage.pos.repository.PosOrderRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * pos订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Service
public class PosOrderRepositoryImpl extends ServiceImpl<PosOrderMapper, PosOrder> implements PosOrderRepository {

    @Override
    public PosOrder queryByOrderNo(String orderNo,Integer channelType, String outStoreCode, Long tenantId) {
        LambdaQueryWrapper<PosOrder> queryWrapper = new LambdaQueryWrapper ();
        queryWrapper.eq (PosOrder::getTenantId,tenantId);
        queryWrapper.eq (PosOrder::getOrderNo,orderNo);
        queryWrapper.eq (PosOrder::getChannelType,channelType);
        queryWrapper.eq (StringUtils.isNotBlank (outStoreCode),PosOrder::getOutStoreCode,outStoreCode);
        return getOne (queryWrapper);
    }

    @Override
    public List<PosOrder> queryByOrderNos(List<String> orderNos, Integer channelType, String outStoreCode, Long tenantId) {
        LambdaQueryWrapper<PosOrder> queryWrapper = new LambdaQueryWrapper ();
        queryWrapper.eq (PosOrder::getTenantId,tenantId);
        queryWrapper.in (PosOrder::getOrderNo,orderNos);
        queryWrapper.eq (PosOrder::getChannelType,channelType);
        queryWrapper.eq (StringUtils.isNotBlank (outStoreCode),PosOrder::getOutStoreCode,outStoreCode);
        return list (queryWrapper);
    }
}

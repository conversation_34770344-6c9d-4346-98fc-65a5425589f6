package com.cosfo.manage.pos.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.pos.model.dto.PosOrderQueryDTO;
import com.cosfo.manage.pos.model.po.PosOrder;
import com.cosfo.manage.pos.model.po.PosOrderItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * pos子订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface PosOrderItemRepository extends IService<PosOrderItem> {

    Page<PosOrderItem> listPage(PosOrderQueryDTO dto, Long tenantId);

    void removeByPosOrderId(Long posOrderId, Long tenantId);

    void removeByPosOrderIds(List<Long> posOrderIds, Long tenantId);

    List<PosOrderItem> listByPosOrderId(Long posOrderId, Long tenantId);
}

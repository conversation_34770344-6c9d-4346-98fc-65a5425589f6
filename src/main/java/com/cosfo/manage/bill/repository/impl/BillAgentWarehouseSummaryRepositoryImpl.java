package com.cosfo.manage.bill.repository.impl;

import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.bill.model.po.BillAgentWarehouseSummary;
import com.cosfo.manage.report.mapper.BillAgentWarehouseSummaryMapper;
import com.cosfo.manage.bill.repository.BillAgentWarehouseSummaryRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 代仓账单概要 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Service
public class BillAgentWarehouseSummaryRepositoryImpl extends ServiceImpl<BillAgentWarehouseSummaryMapper, BillAgentWarehouseSummary> implements BillAgentWarehouseSummaryRepository {

    @Resource
    private BillAgentWarehouseSummaryMapper billAgentWarehouseSummaryMapper;

    @Override
    public BillAgentWarehouseSummary querySummary(TenantBillQueryDTO tenantBillQueryDTO) {
        return billAgentWarehouseSummaryMapper.querySummary(tenantBillQueryDTO);
    }
}

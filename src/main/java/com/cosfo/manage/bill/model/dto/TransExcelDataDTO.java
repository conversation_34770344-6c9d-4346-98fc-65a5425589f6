package com.cosfo.manage.bill.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2022/12/27 15:29
 * 结算信息导出DTO
 */
@Data
public class TransExcelDataDTO {
    /**
     * 分账时间
     */
    @ExcelProperty("分账时间")
    private String createTime;

    /**
     * 分账交易号
     */
    @ExcelProperty("分账交易号")
    private String transactionId;
    /**
     * 原交易银行流水号
     */
    @ExcelProperty("原交易银行流水交易号")
    private String wxOrderId;
    /**
     * 原交易订单号
     */
    @ExcelProperty("原交易订单号")
    private String orderNo;
    /**
     * 分账接收方主体名称
     */
    @ExcelProperty("分账接收方主体名称")
    private String tenantName;
    /**
     * 分账金额
     */
    @ExcelProperty("分账金额")
    private String transAmt;
    /**
     * 分账手续费
     */
    @ExcelProperty("分账手续费")
    private String feeAmt;
    /**
     * 分账状态
     */
    @ExcelProperty("分账状态")
    private String transStat;

    /**
     * 分账状态
     */
    @ExcelProperty("外部交易订单号")
    private String paymentNo;


}

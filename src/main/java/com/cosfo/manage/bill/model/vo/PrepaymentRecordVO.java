package com.cosfo.manage.bill.model.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预付明细
 *
 * <AUTHOR>
 */
@Data
@ToString
public class PrepaymentRecordVO implements Serializable {

    /**
     * id
     */
    private String id;

    /**
     * 付款人
     */
    private Long tenantId;

    /**
     * 商城名称
     */
    private String tenantName;

    /**
     * 付款人
     */
    private String tenantCompanyName;

    /**
     * 收款人
     */
    private Long supplierTenantId;

    /**
     * 收款人
     */
    private String supplierTenant;

    /**
     * 付款金额
     */
    private BigDecimal prepaymentAmount;

    /**
     * 资金可用范围 0、供应商直供 1、代仓费用 2、供应商直供和代仓费用
     */
    private Integer type;

    private String payTargetDesc;

    /**
     * 0、待审核 1、审核通过 2、 审核失败
     */
    private Integer auditStatus;

    private String auditStatusDesc;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 付款凭证 ，分割
     */
    private String proof;

    private List<String> proofList;

    /**
     * 付款备注
     */
    private String remark;

    /**
     * 提交人
     */
    private Long creatorId;

    /**
     * 提交人信息
     */
    private String creator;
    private String creatorPhone;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核人id
     */
    private Long auditorId;

    /**
     * 审核人信息
     */
    private String auditor;

    private String auditorPhone;

    /**
     * 提交时间
     */
    private LocalDateTime createTime;

    /**
     * 开户行
     */
    private String openingBank;

    /**
     * 户名
     */
    private String accountName;

    /**
     * 户号
     */
    private String accountNumber;

    /**
     * 交易类型 0、预付 1、预收 2、退款
     */
    private Integer transactionType;

    private String transactionTypeDesc;


}

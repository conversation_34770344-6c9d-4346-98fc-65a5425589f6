package com.cosfo.manage.bill.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * financial_bill_credentials
 * <AUTHOR>
@Data
public class FinancialBillCredentials implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 账单Id
     */
    private Long billId;

    /**
     * 操作人Id
     */
    private Long operatorId;

    /**
     * 账单凭证
     */
    private String credentials;

    /**
     * 上传凭证时间
     */
    private LocalDateTime credentialsTime;

    /**
     * 留言
     */
    private String remark;

    /**
     * 操作人类型0收款人1付款方
     */
    private Integer operatorType;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
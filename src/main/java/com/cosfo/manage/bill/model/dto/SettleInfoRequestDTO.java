package com.cosfo.manage.bill.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/22 11:45
 */
@Data
public class SettleInfoRequestDTO implements Serializable {

    /**
     *请求流水号
     */
    private String req_seq_id;
    /**
     *请求日期
     */
    private String req_date;
    /**
     *汇付客户Id
     */
    private String huifu_id;
    /**
     *结算开始日期
     */
    private String begin_date;
    /**
     *结算结束日期
     */
    private String end_date;
    /**
     *分页条数
     */
    private String page_size;
    /**
     *结算方式
     */
    private String settle_cycle;
    /**
     *分页页码
     */
    private String page_num;
    /**
     *交易状态
     */
    private String trans_stat;
    /**
     *排序字段
     */
    private String sort_column;
    /**
     *排序顺序
     */
    private String sort_order;
}

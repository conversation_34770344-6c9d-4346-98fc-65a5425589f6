package com.cosfo.manage.bill.model.query;

import com.cosfo.manage.common.context.PaymentEnum.Status;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @time: 2023/7/7 下午2:57
 */
@Data
public class PaymentConditionQuery {

    private Long maxId;

    private Integer size;

    /**
     * 租户ids
     */
    private List<Long> tenantIds;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态：0、待支付 1、支付成功 2、支付失败 3、取消支付 4、处理中(锁定)
     *
     * @see Status
     */
    private List<Integer> statusList;

    /**
     * 支付通道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;

    /**
     * 交易类型，枚举值：
     * JSAPI：公众号支付
     * NATIVE：扫码支付
     * APP：APP支付
     * MICROPAY：付款码支付
     * MWEB：H5支付
     * FACEPAY：刷脸支付
     */
    private List<String> tradeTypeList;
}

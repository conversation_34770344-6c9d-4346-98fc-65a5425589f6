package com.cosfo.manage.bill.model.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单分账账户维度明细响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class OrderProfitSharingAccountDetailBO implements Serializable {

    /**
     * 分账标识
     */
    private Boolean hasProfitSharing;

    /**
     * 订单ID或者售后ID
     */
    private Long bizOrderId;

    /**
     * 订单号或者售后NO
     */
    private String bizOrderNo;

    /**
     * 分账方名称
     */
    private String accountName;

    /**
     * 账户类型
     * @see com.cosfo.manage.common.context.AccountTypeEnum
     */
    private Integer accountType;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * @see com.cosfo.manage.common.context.PaymentTradeTypeEnum
     */
    private String tradeType;

    /**
     * 商品分账金额
     */
    private BigDecimal productAmount;

    /**
     * 运费分账金额
     */
    private BigDecimal deliveryAmount;

    /**
     * 手续费分账金额
     */
    private BigDecimal serviceFeeAmount;

    /**
     * 总分账金额
     */
    private BigDecimal totalAmount;

    private static final long serialVersionUID = 1L;
}

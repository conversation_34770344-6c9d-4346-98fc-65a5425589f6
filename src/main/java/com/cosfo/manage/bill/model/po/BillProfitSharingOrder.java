package com.cosfo.manage.bill.model.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * bill_profit_sharing_order
 * <AUTHOR>
@Data
public class BillProfitSharingOrder implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 分账状态0、待分账 1、计算分账金额 2、分账中 3、分账完成 4、初始化
     */
    private Integer status;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * @see ProfitSharingTypeEnum
     */
    private Integer profitSharingType;

    /**
     * 分账单号
     */
    private String profitSharingNo;

    private static final long serialVersionUID = 1L;
}
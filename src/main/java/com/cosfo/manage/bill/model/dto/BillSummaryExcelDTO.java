package com.cosfo.manage.bill.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 供应商直配账单概要
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Data
public class BillSummaryExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 时间标签 yyyyMMdd
     */
    private String timeTag;

    /**
     * 采购金额总计
     */
    private BigDecimal totalPurchaseAmount;

    /**
     * 采购金额总计（微信支付）
     */
    private BigDecimal totalPurchaseAmountWechatPay;

    /**
     * 采购金额总计（账期加余额支付）
     */
    private BigDecimal totalPurchaseAmountBillBalancePay;

    /**
     * 等比换算后采购售后金额合计（微信支付）
     */
    private BigDecimal totalAmountOfPurchaseAndAfterSalesWechatPay;

    /**
     * 等比换算后采购售后金额合计（账期和余额支付）
     */
    private BigDecimal totalAmountOfPurchaseAndAfterSalesBillBalancePay;

    /**
     * 扣除售后的采购金额（微信支付）
     */
    private BigDecimal totalPurchaseAmountRemoveRefundWechatPay;

    /**
     * 扣除售后的采购金额（账期支付+余额支付）
     */
    private BigDecimal totalPurchaseAmountRemoveRefundBillBalancePay;

    /**
     * 剔除售后的供应商配送费
     */
    private BigDecimal goodsDeliveryFeeRemoveRefund;

    /**
     * 销售金额总计
     */
    private BigDecimal totalSalesAmount;

    /**
     * 销售金额总计（微信支付）
     */
    private BigDecimal totalSalesAmountWechatPay;

    /**
     * 销售金额总计（账期加余额支付）
     */
    private BigDecimal totalSalesAmountBillBalancePay;

    /**
     * 销售售后金额（微信支付）
     */
    private BigDecimal afterSaleAmountWechatPay;

    /**
     * 销售售后金额（账期加余额支付）
     */
    private BigDecimal afterSaleAmountBillBalancePay;

    /**
     * 扣除售后销售金额（微信支付）
     */
    private BigDecimal deductAfterSalesAmountWechatPay;

    /**
     * 扣除售后销售金额（账期加余额支付）
     */
    private BigDecimal deductAfterSalesAmountBillBalancePay;

    /**
     * 销售与采购差额（微信支付）
     */
    private BigDecimal salesAndPurchaseDifferenceWechatPay;

    /**
     * 销售与采购差额（账期加余额支付）
     */
    private BigDecimal salesAndPurchaseDifferenceBillBalancePay;

    /**
     * 代仓实付费用合计
     */
    private BigDecimal totalActualWarehouseExpenses;

    /**
     * 代仓应付费用合计
     */
    private BigDecimal totalWarehouseExpenses;

    /**
     * 已到货售后（代仓服务商责任）费用合计
     */
    private BigDecimal totalAfterSaleWarehouseExpenses;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 账单起止日
     */
    private String billStartTime;

    /**
     * 账单起止日
     */
    private String billEndTime;

    /**
     * 订单数合计
     */
    private Integer orderCount;

    /**
     * sku数
     */
    private Integer skuCount;

    /**
     * 下单件数合计
     */
    private Integer orderItemCount;

    /**
     * 账期支付门店数
     */
    private Integer billPayStoreCount;

    /**
     * 余额支付门店数
     */
    private Integer balancePayStoreCount;

    /**
     * 微信支付门店数
     */
    private Integer wechatPayStoreCount;

    /**
     * 剔除售后的订单配送费
     */
    private BigDecimal deliveryFeeDeductAfterSalesAmount;
}

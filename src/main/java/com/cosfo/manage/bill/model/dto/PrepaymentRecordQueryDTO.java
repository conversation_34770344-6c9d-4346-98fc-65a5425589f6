package com.cosfo.manage.bill.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class PrepaymentRecordQueryDTO extends BasePageInput {

    /**
     * 预付时间范围开始
     */
    private LocalDate dateStart;

    /**
     * 预付时间范围结束
     */
    private LocalDate dateEnd;

    /**
     * 收款人
     */
    private Long supplierTenantId;

    /**
     * 提交人
     */
    private Long creatorId;

    private Long tenantId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 类型 0、供应商直供 1、代仓费用 2、供应商直供和代仓费用
     */
    private Integer type;

    /**
     * 交易类型 0、预付 1、预收
     */
    private Integer transactionType;
}

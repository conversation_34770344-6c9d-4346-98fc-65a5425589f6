package com.cosfo.manage.bill.model.po;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
* @description: 组合支付订单明细表
* @author: <PERSON>
* @date: 2025-04-29
**/
@Data
public class PaymentCombinedOrderDetail {
    /**
    * primary key
    */
    private Long id;

    /**
    * 组合明细ID
    */
    private Long combinedDetailId;

    /**
    * 订单ID
    */
    private Long orderId;

    /**
    * 金额
    */
    private BigDecimal totalPrice;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * 订单编号
    */
    private String orderNo;

    /**
    * 租户ID
    */
    private Long tenantId;
}
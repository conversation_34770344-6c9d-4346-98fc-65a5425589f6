package com.cosfo.manage.bill.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
@Data
public class FinancialTenantBillVO {
    /**
     * 品牌方租户Id
     */
    private Long tenantId;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 供应商Id
     */
    private Long supplyTenantId;
    /**
     * 供应商租户Id
     */
    private String supplyTenantName;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 开始时间
     */
    @JSONField(format="yyyy-MM-dd")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @JSONField(format="yyyy-MM-dd")
    private LocalDateTime endTime;
    /**
     * 账单Id
     */
    private Long billId;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 订单数
     */
    private Integer orderNum;
    /**
     * 订单数
     */
    private Integer skuNum;
    /**
     * 下单件数合计
     */
    private Integer amount;
    /**
     * 账期门店数
     */
    private Integer billStoreNum;
    /**
     * 现结门店数
     */
    private Integer onlinePaymentNum;
    /**
     * 应付账单金额合计
     */
    private BigDecimal receivablePrice;
    /**
     * 订单明细表采购总价合计
     */
    private BigDecimal supplyTotalPrice;
    /**
     * 订单明细表供应商配送运费合计
     */
    private BigDecimal supplyDeliveryFeeTotalPrice;
    /**
     * 售后明细表等比换算后采购售后金额合计
     */
    private BigDecimal supplyOrderAfterSaleTotalPrice;
    /**
     * 倒挂总金额
     */
    private BigDecimal invertedTotalPrice;

    /**
     * 鲜沐直供货品售价与采购价差额总计（未剔售后）
     */
    private BigDecimal supplyProductBalancePrice;

    /**
     * 有退款的鲜沐直供货品的售价与采购差额总计
     */
    private BigDecimal refundBalancePrice;

    /**
     * 鲜沐直供货品售价与采购价差额总计
     */
    private BigDecimal totalBalancePrice;

    /**
     * 订单明细表_代仓费用合计
     */
    private BigDecimal agentTotalPrice;

    /**
     * 售后明细表_已到货售后_鲜沐责任_代仓费用合计
     */
    private BigDecimal afterSaleTotalPrice;
}

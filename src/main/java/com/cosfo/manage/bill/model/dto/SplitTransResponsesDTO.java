package com.cosfo.manage.bill.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date : 2022/12/16 14:05
 */
@Data
public class SplitTransResponsesDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 请求流水号
     */
    private String reqSeqId;

    /**
     * 请求日期
     */
    private String reqDate;
    /**
     * 分账完成时间;yyyyMMddHHmmss；示例值：20221023112345
     * 分账日期
     */
    private String transFinishTime;

    /**
     * 账务状态;P：处理中、S：成功、F：失败；示例值：S
     * 分账状态
     */
    private String acctStat;

    /**
     * 分账接收方主体名称
     */
    private String tenantName;
    /**
     *帆台交易号
     */
    private String wxOrderId;
    /**
     * 原银行交易请求流水号
     */
    private String transactionId;
    /**
     * 分账交易订单号
     */
    private String orderNo;

    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 页码大小
     */
    private Integer pageSize;
}

package com.cosfo.manage.bill.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 订单项 金额构成
 */
@Data
public class OrderItemPriceDetailDTO {
    private Long orderId;

    private Long tenantId;

    private Long orderItemId;
    /**
     * 金额构成 Map<支付方式,金额>
     *     支付方式 = 0 - 现结
     *     支付方式 = 1 - 优惠券
     */
    private Map<Integer, BigDecimal> priceMap;

    private Long paymentId;

    private Long paymentItemId;
}

package com.cosfo.manage.bill.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
@Data
public class BillOrderAfterSaleVO {
    /**
     * 售后单Id
     */
    private Long orderAfterSaleId;
    /**
     * 售后订单号
     */
    private String orderAfterSaleNo;
    /**
     * 订单编号
     */
    private Long orderId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 下单时间吗
     */
    private LocalDateTime orderCreateTime;
    /**
     * 售后成功时间
     */
    private LocalDateTime orderAfterSaleFinishedTime;
    /**
     * 商城售卖商品名称
     */
    private String title;
    /**
     * 商城售卖商品sku
     */
    private Long skuId;

    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 商城售卖商品规则
     */
    private String specification;
    /**
     * 商城售卖数量
     */
    private Integer amount;
    /**
     * 商品单价
     */
    private BigDecimal price;
    /**
     * 售后数量
     */
    private Integer orderAfterSaleAmount;
    /**
     * 售后类型
     */
    private Integer orderAfterSaleType;
    /**
     * 售后类型str
     */
    private String orderAfterSaleTypeStr;
    /**
     * 售后服务类型
     */
    private Integer orderAfterSaleServiceType;
    /**
     * 售后服务类型Str
     */
    private String orderAfterSaleServiceTypeStr;
    /**
     * 售后原因
     */
    private String reason;
    /**
     * 售后退款金额
     */
    private BigDecimal orderAfterSalePrice;
    /**
     * 售后退款运费
     */
    private BigDecimal orderAfterSaleDeliveryFee;
    /**
     * 采购商品名称
     */
    private String supplyProductTitle;
    /**
     * 采购商品sku
     */
    private Long supplySkuId;
    /**
     * 采购商品sku
     */
    private String supplySku;
    /**
     * 供应价
     */
    private BigDecimal supplyPrice;
    /**
     * 采购商品规格
     */
    private String supplySkuSpecification;
    /**
     * 采购供应商
     */
    private String supplyTenantName;
    /**
     * 等比换算后采购售后金额
     */
    private BigDecimal supplyOrderAfterSalePrice;
    /**
     * 主图片
     */
    private String mainPicture;

    /**
     * 货源类型
     */
    private Integer warehouseType;

    /**
     * 配送类型
     */
    private Integer deliveryType;

    /**
     * 退款差额
     */
    private BigDecimal refundBalancePrice;
    /**
     * 货品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer GoodsType;

    /**
     * 售后责任方desc
     */
    private String responsibilityTypeDesc;
    private String responsibilityType;

    /**
     * 货品类型desc
     */
    private String productTypeDesc;

    /**
     * 货品类型
     */
    private Integer productType;

    /**
     * 代仓费用
     */
    private BigDecimal refundAgentFee;
}

package com.cosfo.manage.bill.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 供应商直配账单概要
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Getter
@Setter
@TableName("bill_supplier_direct_assign_summary")
public class BillSupplierDirectAssignSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 时间标签 yyyyMMdd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 采购金额总计
     */
    @TableField("total_purchase_amount")
    private BigDecimal totalPurchaseAmount;

    /**
     * 采购金额总计（微信支付）
     */
    @TableField("total_purchase_amount_wechat_pay")
    private BigDecimal totalPurchaseAmountWechatPay;

    /**
     * 采购金额总计（账期加余额支付）
     */
    @TableField("total_purchase_amount_bill_balance_pay")
    private BigDecimal totalPurchaseAmountBillBalancePay;

    /**
     * 等比换算后采购售后金额合计（微信支付）
     */
    @TableField("total_amount_of_purchase_and_after_sales_wechat_pay")
    private BigDecimal totalAmountOfPurchaseAndAfterSalesWechatPay;

    /**
     * 等比换算后采购售后金额合计（账期和余额支付）
     */
    @TableField("total_amount_of_purchase_and_after_sales_bill_balance_pay")
    private BigDecimal totalAmountOfPurchaseAndAfterSalesBillBalancePay;

    /**
     * 扣除售后的采购金额（微信支付）
     */
    @TableField("total_purchase_amount_remove_refund_wechat_pay")
    private BigDecimal totalPurchaseAmountRemoveRefundWechatPay;

    /**
     * 扣除售后的采购金额（账期支付+余额支付）
     */
    @TableField("total_purchase_amount_remove_refund_bill_balance_pay")
    private BigDecimal totalPurchaseAmountRemoveRefundBillBalancePay;

    /**
     * 剔除售后的供应商配送费
     */
    @TableField("goods_delivery_fee_remove_refund")
    private BigDecimal goodsDeliveryFeeRemoveRefund;

    /**
     * 销售金额总计
     */
    @TableField("total_sales_amount")
    private BigDecimal totalSalesAmount;

    /**
     * 销售金额总计（微信支付）
     */
    @TableField("total_sales_amount_wechat_pay")
    private BigDecimal totalSalesAmountWechatPay;

    /**
     * 销售金额总计（账期加余额支付）
     */
    @TableField("total_sales_amount_bill_balance_pay")
    private BigDecimal totalSalesAmountBillBalancePay;

    /**
     * 销售售后金额（微信支付）
     */
    @TableField("after_sale_amount_wechat_pay")
    private BigDecimal afterSaleAmountWechatPay;

    /**
     * 销售售后金额（账期加余额支付）
     */
    @TableField("after_sale_amount_bill_balance_pay")
    private BigDecimal afterSaleAmountBillBalancePay;

    /**
     * 扣除售后销售金额（微信支付）
     */
    @TableField("deduct_after_sales_amount_wechat_pay")
    private BigDecimal deductAfterSalesAmountWechatPay;

    /**
     * 扣除售后销售金额（账期加余额支付）
     */
    @TableField("deduct_after_sales_amount_bill_balance_pay")
    private BigDecimal deductAfterSalesAmountBillBalancePay;

    /**
     * 销售与采购差额（微信支付）
     */
    @TableField("sales_and_purchase_difference_wechat_pay")
    private BigDecimal salesAndPurchaseDifferenceWechatPay;

    /**
     * 销售与采购差额（账期加余额支付）
     */
    @TableField("sales_and_purchase_difference_bill_balance_pay")
    private BigDecimal salesAndPurchaseDifferenceBillBalancePay;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 剔除售后的订单配送费
     */
    @TableField("delivery_fee_deduct_after_sales_amount")
    private BigDecimal deliveryFeeDeductAfterSalesAmount;

    public BillSupplierDirectAssignSummary() {

    }

    public BillSupplierDirectAssignSummary(Boolean init) {
        if (init) {
            this.totalPurchaseAmount = BigDecimal.ZERO;
            this.totalPurchaseAmountWechatPay = BigDecimal.ZERO;
            this.totalPurchaseAmountBillBalancePay = BigDecimal.ZERO;
            this.totalAmountOfPurchaseAndAfterSalesWechatPay = BigDecimal.ZERO;
            this.totalAmountOfPurchaseAndAfterSalesBillBalancePay = BigDecimal.ZERO;
            this.totalPurchaseAmountRemoveRefundWechatPay = BigDecimal.ZERO;
            this.totalPurchaseAmountRemoveRefundBillBalancePay = BigDecimal.ZERO;
            this.goodsDeliveryFeeRemoveRefund = BigDecimal.ZERO;
            this.totalSalesAmount = BigDecimal.ZERO;
            this.totalSalesAmountWechatPay = BigDecimal.ZERO;
            this.totalSalesAmountBillBalancePay = BigDecimal.ZERO;
            this.afterSaleAmountWechatPay = BigDecimal.ZERO;
            this.afterSaleAmountBillBalancePay = BigDecimal.ZERO;
            this.deductAfterSalesAmountWechatPay = BigDecimal.ZERO;
            this.deductAfterSalesAmountBillBalancePay = BigDecimal.ZERO;
            this.salesAndPurchaseDifferenceWechatPay = BigDecimal.ZERO;
            this.salesAndPurchaseDifferenceBillBalancePay = BigDecimal.ZERO;
            this.deliveryFeeDeductAfterSalesAmount = BigDecimal.ZERO;
        }
    }


}

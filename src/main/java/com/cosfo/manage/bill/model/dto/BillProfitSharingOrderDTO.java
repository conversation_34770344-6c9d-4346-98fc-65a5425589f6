package com.cosfo.manage.bill.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/26
 */
@Data
public class BillProfitSharingOrderDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 分账状态0待分账1分账处理中2部分分账3分账完成4分账失败
     */
    private Integer status;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}

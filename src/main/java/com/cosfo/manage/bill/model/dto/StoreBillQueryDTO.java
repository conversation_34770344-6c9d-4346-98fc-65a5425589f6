package com.cosfo.manage.bill.model.dto;

import com.cosfo.manage.common.model.dto.PageQueryDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述: 应收账单查询条件
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/4
 */
@Data
public class StoreBillQueryDTO extends PageQueryDTO {
    /**
     * 账单编号
     */
    private String billNo;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 门店名称
     */
    private String StoreName;
    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 门店注册电话
     */
    private String storePhone;
    /**
     * 账单状态 0待收款1已收款
     */
    private Integer status;
    /**
     * 账单生成开始时间
     */
    private LocalDateTime createBillStartTime;
    /**
     * 账单生成结束时间
     */
    private LocalDateTime createBillEndTime;
    /**
     * 门店确认开始时间
     */
    private LocalDateTime storeUploadCredentialsStartTime;
    /**
     * 门店确认结束时间
     */
    private LocalDateTime storeUploadCredentialsEndTime;
    /**
     * 审核完成开始时间
     */
    private LocalDateTime auditStartTime;
    /**
     * 审核完成结束时间
     */
    private LocalDateTime auditEndTime;
    /**
     * 门店是否提交凭证 0 是 1 否
     */
    private Integer havingCredentials;

    /**
     * 门店编号
     */
    private List<Long> storeIds;

    /**
     * 订单编号
     */
    private List<Long> orderIds;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 账单生成日
     */
    private Integer billCreateTime;

    /**
     * 生成时间排序
     */
    private String createTimeSort;

    /**
     * 门店上传凭证时间排序
     */
    private String storeUploadCredentialsTimeSort;

    /**
     * 收款完成时间排序
     */
    private String auditTimeSort;

    /**
     * 门店分组ids
     */
    private List<Long> merchantStoreGroupIds;
}


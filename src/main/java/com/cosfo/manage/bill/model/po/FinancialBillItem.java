package com.cosfo.manage.bill.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * financial_bill_item
 * <AUTHOR>
@Data
public class FinancialBillItem implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 账单Id
     */
    private Long billId;

    /**
     * 业务Id
     */
    private Long businessId;

    /**
     * 业务类型0订单1售后单
     */
    private Integer businessType;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
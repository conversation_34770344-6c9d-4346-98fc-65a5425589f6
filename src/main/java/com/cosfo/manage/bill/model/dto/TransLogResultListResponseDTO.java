package com.cosfo.manage.bill.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 结算信息请求参数
 * <AUTHOR>
 * @date : 2022/12/20 0:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TransLogResultListResponseDTO implements Serializable {

    /**
     * 结算流水编号
     */
    private String trans_id;

    /**
     * 结算日期
     */
    private String trans_date;
    /**
     * 结算汇付商户号
     */
    private String huifu_id;
    /**
     * 结算方式
     */
    private String settle_cycle;
    /**
     * 银行卡号(掩码)
     */
    private String card_no;
    /**
     * 银行户名
     */
    private String card_name;
    /**
     * 银行编码
     */
    private String bank_code;
    /**
     * 交易金额（元）
     */
    private String trans_amt;
    /**
     * 	结算手续费（元）
     */
    private String fee_amt;
    /**
     * 手续费汇付商户号
     */
    private String fee_cust_id;
    /**
     * 结算状态
     */
    private String trans_stat;

    /**
     * 结算类型
     */
    private String settle_type;
    /**
     * 结算摘要
     */
    private String settle_abstract;
    /**
     * 结算批次号
     */
    private String settle_batch_no;
    /**
     * 租户id
     */
    private Long tenant_id;
}

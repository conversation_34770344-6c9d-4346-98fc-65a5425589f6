package com.cosfo.manage.bill.model.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * bill_profit_sharing_snapshot
 * <AUTHOR>
@Data
public class BillProfitSharingSnapshot implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 账号Id
     */
    private Long accountId;

    /**
     * 账号类型
     */
    private Integer accountType;

    /**
     * 订单配送类型 0自营 1三方
     */
    private Integer deliveryType;

    /**
     * 分账金额类型
     */
    private Integer profitSharingType;

    /**
     * 分账方式0按价格1按比例2按分账比例均摊
     */
    private Byte mappingType;

    /**
     * 对应值
     */
    private BigDecimal number;

    /**
     * 原始金额
     */
    private BigDecimal originPrice;

    /**
     * 分账金额
     */
    private BigDecimal profitSharingPrice;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 分账单号
     */
    private String profitSharingNo;

    private static final long serialVersionUID = 1L;
}
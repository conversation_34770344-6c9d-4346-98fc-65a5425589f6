package com.cosfo.manage.bill.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.bill.model.po.FinancialBillCredentials;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface FinancialBillCredentialsMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(FinancialBillCredentials record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(FinancialBillCredentials record);

    /**
     * 查询
     * @param id
     * @return
     */
    FinancialBillCredentials selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(FinancialBillCredentials record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(FinancialBillCredentials record);

    /**
     * 查询账单凭证信息
     *
     * @param tenantId
     * @Param billId
     * @param operatorType 0收款人 1付款方
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    FinancialBillCredentials selectByBillId(@Param("tenantId") Long tenantId,
                                            @Param("billId") Long billId,
                                            @Param("operatorType") Integer operatorType);
}

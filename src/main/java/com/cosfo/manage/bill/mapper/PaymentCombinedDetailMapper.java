package com.cosfo.manage.bill.mapper;

import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;
import com.cosfo.manage.bill.model.vo.PaymentCombinedDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* @description: 组合支付详情表
* @author: <PERSON>
* @date: 2025-04-21
**/
public interface PaymentCombinedDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaymentCombinedDetail record);

    int insertSelective(PaymentCombinedDetail record);

    PaymentCombinedDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaymentCombinedDetail record);

    int updateByPrimaryKey(PaymentCombinedDetail record);

    List<PaymentCombinedDetailVO> getListByOrderIds(@Param("orderIds") Collection<Long> orderIds);

    List<PaymentCombinedDetail> listByPaymentNo(String paymentNo);

    /**
     * 根据组合支付单号查询支付明细
     *
     * @param combinePaymentNo
     * @return
     */
    List<PaymentCombinedDetail> selectByCombinedPaymentNo(String combinePaymentNo);
}
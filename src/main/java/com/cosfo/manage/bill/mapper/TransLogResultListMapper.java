package com.cosfo.manage.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.bill.model.dto.TransLogResultListDTO;
import com.cosfo.manage.bill.model.dto.TransLogResultListQueryDTO;
import com.cosfo.manage.bill.model.po.TransLogResultList;
import org.apache.ibatis.session.ResultHandler;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 结算信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
public interface TransLogResultListMapper extends BaseMapper<TransLogResultList> {

    /**
     * 查询结账明细
     * @param transLogResultListQueryDTO
     * @return
     */
    List<TransLogResultListDTO> listAll(TransLogResultListQueryDTO transLogResultListQueryDTO);

    /**
     * 流式查询结账明细
     * @param transLogResultListQueryDTO
     * @return
     */
    void exportListAll(TransLogResultListQueryDTO transLogResultListQueryDTO, ResultHandler<?> resultHandler);

    BigDecimal listAllCountMoney(TransLogResultListQueryDTO transLogResultListQueryDTO);

    /**
     * 查询结算手续费
     * @param transLogResultListQueryDTO
     * @return
     */
    BigDecimal listAllCountFeeAmt(TransLogResultListQueryDTO transLogResultListQueryDTO);


}

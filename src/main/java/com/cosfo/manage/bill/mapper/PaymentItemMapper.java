package com.cosfo.manage.bill.mapper;

import com.cosfo.manage.bill.model.po.PaymentItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


@Mapper
public interface PaymentItemMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaymentItem record);

    int insertSelective(PaymentItem record);

    PaymentItem selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaymentItem record);

    int updateByPrimaryKey(PaymentItem record);

    /**
     * 查询支付单明细
     * @param tenantId 租户id
     * @param orderId  订单id
     * @return 支付item
     */
    PaymentItem selectPaySuccessByOrderId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId);

    /**
     * 查询支付明细
     * @param paymentId 支付id
     * @return 明细
     */
    List<PaymentItem> selectByPaymentId(@Param("paymentId") Long paymentId);

    List<PaymentItem> batchQueryByPaymentIds(@Param("paymentIds") List<Long> paymentIds);

    /**
     * 查询支付单明细
     * @param tenantId 租户id
     * @param orderId  订单id
     * @return 支付item
     */
    PaymentItem selectByOrderId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId);
    /**
     * 查询支付单明细
     * @param tenantId 租户id
     * @param orderId  订单id
     * @return 支付item
     */
    PaymentItem selectItemByOrderId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId);

    List<PaymentItem> selectByOrderIds(@Param("tenantId") Long tenantId, @Param("orderIds") Set<Long> orderIds);
}
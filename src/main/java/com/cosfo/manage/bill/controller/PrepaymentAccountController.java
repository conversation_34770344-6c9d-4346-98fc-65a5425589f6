package com.cosfo.manage.bill.controller;

import com.cosfo.manage.bill.model.dto.PrepaymentAccountQueryDTO;
import com.cosfo.manage.bill.model.dto.PrepaymentTrendQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentAccountVO;
import com.cosfo.manage.bill.model.vo.PrepaymentAmountVO;
import com.cosfo.manage.bill.model.vo.PrepaymentTrendVO;
import com.cosfo.manage.bill.service.PrepaymentAccountService;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.icepear.echarts.Line;
import org.icepear.echarts.charts.line.LineSeries;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 预付账号
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/prepayment/account")
public class PrepaymentAccountController extends BaseController {

    @Resource
    private PrepaymentAccountService prepaymentAccountService;

    /**
     * 预付余额
     *
     * @return 余额
     */
    @PostMapping("/query/amount")
    public CommonResult<PrepaymentAmountVO> getTotalAmount() {
        Long tenantId = getMerchantInfoDTO().getTenantId();
        return CommonResult.ok(prepaymentAccountService.getTotalAmount(tenantId));
    }

    /**
     * 余额构成列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/query/page")
    public CommonResult<PageInfo<PrepaymentAccountVO>> getAccount(@RequestBody PrepaymentAccountQueryDTO queryDTO) {
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(prepaymentAccountService.queryAccountPage(queryDTO));
    }

    /**
     * 下载余额构成列表
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/query/account/download")
    public CommonResult<Boolean> downloadAccountPart() {
        PrepaymentAccountQueryDTO queryDTO = new PrepaymentAccountQueryDTO();
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(prepaymentAccountService.downloadAccount(queryDTO));
    }

    /**
     * 预付余额走势
     *
     * @param queryDTO 趋势查询
     * @return 趋势列表
     */
    @PostMapping("/query/trend")
    public CommonResult<Line> queryAmountTrend(@RequestBody PrepaymentTrendQueryDTO queryDTO) {
        List<PrepaymentTrendVO> trendVOS = prepaymentAccountService.queryAmountTrend(queryDTO);
        Line line = new Line()
                .addXAxis(trendVOS.stream().map(data -> data.getDayKey().format(DateTimeFormatter.ISO_LOCAL_DATE)).toArray(String[]::new))
                .addYAxis()
                .addSeries(new LineSeries().setData(trendVOS.stream().map(PrepaymentTrendVO::getAmount).toArray()).setSmooth(true).setName("余额"));
        return CommonResult.ok(line);
    }
}

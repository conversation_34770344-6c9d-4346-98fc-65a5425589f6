package com.cosfo.manage.bill.converter;

import com.cosfo.manage.bill.model.dto.BillSummaryExcelDTO;
import com.cosfo.manage.bill.model.dto.OrderProfitSharingDifferenceExcelDTO;
import com.cosfo.manage.bill.model.po.BillAgentWarehouseSummary;
import com.cosfo.manage.bill.model.po.BillSupplierDirectAssignSummary;
import com.cosfo.manage.bill.model.po.OrderProfitSharingDifference;
import com.cosfo.manage.common.context.PayTypeEnum;

import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-07-10
 **/
public class BillConverter {


    public static BillSummaryExcelDTO convertToExcelDTO(BillSupplierDirectAssignSummary billSupplierDirectAssignSummary) {

        if (billSupplierDirectAssignSummary == null) {
            return null;
        }
        BillSummaryExcelDTO billSummaryExcelDTO = new BillSummaryExcelDTO();
        billSummaryExcelDTO.setId(billSupplierDirectAssignSummary.getId());
        billSummaryExcelDTO.setTenantId(billSupplierDirectAssignSummary.getTenantId());
        billSummaryExcelDTO.setSupplierId(billSupplierDirectAssignSummary.getSupplierId());
        billSummaryExcelDTO.setTimeTag(billSupplierDirectAssignSummary.getTimeTag());
        billSummaryExcelDTO.setTotalPurchaseAmount(billSupplierDirectAssignSummary.getTotalPurchaseAmount());
        billSummaryExcelDTO.setTotalPurchaseAmountWechatPay(billSupplierDirectAssignSummary.getTotalPurchaseAmountWechatPay());
        billSummaryExcelDTO.setTotalPurchaseAmountBillBalancePay(billSupplierDirectAssignSummary.getTotalPurchaseAmountBillBalancePay());
        billSummaryExcelDTO.setTotalAmountOfPurchaseAndAfterSalesWechatPay(billSupplierDirectAssignSummary.getTotalAmountOfPurchaseAndAfterSalesWechatPay());
        billSummaryExcelDTO.setTotalAmountOfPurchaseAndAfterSalesBillBalancePay(billSupplierDirectAssignSummary.getTotalAmountOfPurchaseAndAfterSalesBillBalancePay());
        billSummaryExcelDTO.setTotalPurchaseAmountRemoveRefundWechatPay(billSupplierDirectAssignSummary.getTotalPurchaseAmountRemoveRefundWechatPay());
        billSummaryExcelDTO.setTotalPurchaseAmountRemoveRefundBillBalancePay(billSupplierDirectAssignSummary.getTotalPurchaseAmountRemoveRefundBillBalancePay());
        billSummaryExcelDTO.setGoodsDeliveryFeeRemoveRefund(billSupplierDirectAssignSummary.getGoodsDeliveryFeeRemoveRefund());
        billSummaryExcelDTO.setTotalSalesAmount(billSupplierDirectAssignSummary.getTotalSalesAmount());
        billSummaryExcelDTO.setTotalSalesAmountWechatPay(billSupplierDirectAssignSummary.getTotalSalesAmountWechatPay());
        billSummaryExcelDTO.setTotalSalesAmountBillBalancePay(billSupplierDirectAssignSummary.getTotalSalesAmountBillBalancePay());
        billSummaryExcelDTO.setAfterSaleAmountWechatPay(billSupplierDirectAssignSummary.getAfterSaleAmountWechatPay());
        billSummaryExcelDTO.setAfterSaleAmountBillBalancePay(billSupplierDirectAssignSummary.getAfterSaleAmountBillBalancePay());
        billSummaryExcelDTO.setDeductAfterSalesAmountWechatPay(billSupplierDirectAssignSummary.getDeductAfterSalesAmountWechatPay());
        billSummaryExcelDTO.setDeductAfterSalesAmountBillBalancePay(billSupplierDirectAssignSummary.getDeductAfterSalesAmountBillBalancePay());
        billSummaryExcelDTO.setSalesAndPurchaseDifferenceWechatPay(billSupplierDirectAssignSummary.getSalesAndPurchaseDifferenceWechatPay());
        billSummaryExcelDTO.setSalesAndPurchaseDifferenceBillBalancePay(billSupplierDirectAssignSummary.getSalesAndPurchaseDifferenceBillBalancePay());
        billSummaryExcelDTO.setDeliveryFeeDeductAfterSalesAmount(billSupplierDirectAssignSummary.getDeliveryFeeDeductAfterSalesAmount());
        return billSummaryExcelDTO;
    }

    public static BillSummaryExcelDTO convertToExcelDTO(BillAgentWarehouseSummary billAgentWarehouseSummary) {

        if (billAgentWarehouseSummary == null) {
            return null;
        }
        BillSummaryExcelDTO billSummaryExcelDTO = new BillSummaryExcelDTO();
        billSummaryExcelDTO.setId(billAgentWarehouseSummary.getId());
        billSummaryExcelDTO.setTenantId(billAgentWarehouseSummary.getTenantId());
        billSummaryExcelDTO.setTimeTag(billAgentWarehouseSummary.getTimeTag());
        billSummaryExcelDTO.setTotalSalesAmount(billAgentWarehouseSummary.getTotalSalesAmount());
        billSummaryExcelDTO.setTotalSalesAmountWechatPay(billAgentWarehouseSummary.getTotalSalesAmountWechatPay());
        billSummaryExcelDTO.setTotalSalesAmountBillBalancePay(billAgentWarehouseSummary.getTotalSalesAmountBillBalancePay());
        billSummaryExcelDTO.setAfterSaleAmountBillBalancePay(billAgentWarehouseSummary.getAfterSaleAmountBillBalancePay());
        billSummaryExcelDTO.setAfterSaleAmountWechatPay(billAgentWarehouseSummary.getAfterSaleAmountWechatPay());
        billSummaryExcelDTO.setDeductAfterSalesAmountWechatPay(billAgentWarehouseSummary.getDeductAfterSalesAmountWechatPay());
        billSummaryExcelDTO.setDeductAfterSalesAmountBillBalancePay(billAgentWarehouseSummary.getDeductAfterSalesAmountBillBalancePay());
        billSummaryExcelDTO.setTotalWarehouseExpenses(billAgentWarehouseSummary.getTotalWarehouseExpenses());
        billSummaryExcelDTO.setTotalActualWarehouseExpenses(billAgentWarehouseSummary.getTotalActualWarehouseExpenses());
        billSummaryExcelDTO.setTotalAfterSaleWarehouseExpenses(billAgentWarehouseSummary.getTotalAfterSaleWarehouseExpenses());
        billSummaryExcelDTO.setDeliveryFeeDeductAfterSalesAmount(billAgentWarehouseSummary.getDeliveryFeeDeductAfterSalesAmount());
        return billSummaryExcelDTO;
    }

    public static OrderProfitSharingDifferenceExcelDTO convertToExcelDTO(OrderProfitSharingDifference orderProfitSharingDifference) {

        if (orderProfitSharingDifference == null) {
            return null;
        }
        OrderProfitSharingDifferenceExcelDTO orderProfitSharingDifferenceExcelDTO = new OrderProfitSharingDifferenceExcelDTO();
        orderProfitSharingDifferenceExcelDTO.setId(orderProfitSharingDifference.getId());
        orderProfitSharingDifferenceExcelDTO.setTenantId(orderProfitSharingDifference.getTenantId());
        orderProfitSharingDifferenceExcelDTO.setTimeTag(orderProfitSharingDifference.getTimeTag());
        orderProfitSharingDifferenceExcelDTO.setOrderId(orderProfitSharingDifference.getOrderId());
        orderProfitSharingDifferenceExcelDTO.setOrderNo(orderProfitSharingDifference.getOrderNo());
        orderProfitSharingDifferenceExcelDTO.setStoreName(orderProfitSharingDifference.getStoreName());
        orderProfitSharingDifferenceExcelDTO.setOrderAddress(orderProfitSharingDifference.getOrderAddress());
        orderProfitSharingDifferenceExcelDTO.setOrderPhone(orderProfitSharingDifference.getOrderPhone());
        orderProfitSharingDifferenceExcelDTO.setOrderTime(orderProfitSharingDifference.getOrderTime());
        orderProfitSharingDifferenceExcelDTO.setPayTime(orderProfitSharingDifference.getPayTime());
        orderProfitSharingDifferenceExcelDTO.setPayType(orderProfitSharingDifference.getPayType());
        orderProfitSharingDifferenceExcelDTO.setProfitSharingSuccessTime(orderProfitSharingDifference.getProfitSharingSuccessTime());
        orderProfitSharingDifferenceExcelDTO.setTotalItemPrice(orderProfitSharingDifference.getTotalItemPrice());
        orderProfitSharingDifferenceExcelDTO.setTotalGoodsPrice(orderProfitSharingDifference.getTotalGoodsPrice());
        orderProfitSharingDifferenceExcelDTO.setDifference(orderProfitSharingDifference.getDifference());
        PayTypeEnum payType = PayTypeEnum.getPayType(orderProfitSharingDifference.getPayType());
        orderProfitSharingDifferenceExcelDTO.setPayTypeDesc(Objects.isNull(payType) ? "" : payType.getDesc());
        return orderProfitSharingDifferenceExcelDTO;
    }
}

package com.cosfo.manage.bill.service.impl;

import com.cosfo.manage.bill.mapper.PaymentCombinedDetailMapper;
import com.cosfo.manage.bill.mapper.PaymentCombinedOrderDetailMapper;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;
import com.cosfo.manage.bill.service.PaymentCombinedDetailService;
import com.cosfo.manage.common.context.PaymentTradeTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2025-05-08
 **/
@Service
public class PaymentCombinedDetailServiceImpl implements PaymentCombinedDetailService {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentCombinedDetailMapper paymentCombinedDetailMapper;
    @Resource
    private PaymentCombinedOrderDetailMapper paymentCombinedOrderDetailMapper;

    @Override
    public List<PaymentCombinedDetail> querySuccessCombinedByOrderId(Long tenantId, Long orderId) {
        Payment payment = paymentMapper.querySuccessByOrderId(tenantId, orderId);
        if (payment == null) {
            return Collections.emptyList();
        }
        if (!Objects.equals(payment.getTradeType(), PaymentTradeTypeEnum.COMBINED_PAY.getDesc())) {
            return Collections.emptyList();
        }
        return paymentCombinedDetailMapper.selectByCombinedPaymentNo(payment.getPaymentNo());
    }

    @Override
    public Map<String, BigDecimal> querySuccessCombinedTradeTypeAmountByOrderId(Long tenantId, Long orderId) {
        // 1. 通过tenantId和orderId找到对应成功的支付单
        Payment payment = paymentMapper.querySuccessByOrderId(tenantId, orderId);
        if (payment == null) {
            return Collections.emptyMap();
        }

        // 2. 检查是否为组合支付
        if (!Objects.equals(payment.getTradeType(), PaymentTradeTypeEnum.COMBINED_PAY.getDesc())) {
            return Collections.emptyMap();
        }

        // 3. 查询payment_combined_detail表获取具体的多个tradeType组合
        List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailMapper.selectByCombinedPaymentNo(payment.getPaymentNo());
        if (combinedDetails == null || combinedDetails.isEmpty()) {
            return Collections.emptyMap();
        }

        // 4. 根据combinedId和orderId关联到payment_combined_order_detail表获取具体的金额
        Map<String, BigDecimal> result = new HashMap<>();
        for (PaymentCombinedDetail detail : combinedDetails) {
            String tradeType = detail.getTradeType();
            if (tradeType == null) {
                continue;
            }

            // 根据combinedDetailId和orderId查询具体金额
            BigDecimal totalPrice = paymentCombinedOrderDetailMapper.getTotalPriceByCombinedDetailIdAndOrderId(detail.getId(), orderId);
            if (totalPrice != null) {
                // 如果同一个tradeType有多个记录，累加金额
                result.merge(tradeType, totalPrice, BigDecimal::add);
            }
        }

        return result;
    }

    @Override
    public List<PaymentCombinedDetail> queryByMasterPaymentNo(String masterPaymentNo) {
        return paymentCombinedDetailMapper.selectByCombinedPaymentNo(masterPaymentNo);
    }
}

package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
public interface FinancialTenantBillService {

    /**
     * 导出应付报表
     *
     * @param tenantBillQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO export(TenantBillQueryDTO tenantBillQueryDTO, LoginContextInfoDTO loginContextInfoDTO);
}

package com.cosfo.manage.bill.service.impl;

import com.cosfo.manage.bill.mapper.FinancialBillRuleMapper;
import com.cosfo.manage.bill.model.dto.FinancialBillRuleDTO;
import com.cosfo.manage.bill.model.po.FinancialBillRule;
import com.cosfo.manage.bill.model.vo.FinancialBillRuleVO;
import com.cosfo.manage.bill.service.FinancialBillRuleService;
import com.cosfo.manage.common.context.BillRuleTypeEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckBiz;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.common.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/4
 */
@Service
@Slf4j
public class FinancialBillRuleServiceImpl implements FinancialBillRuleService {
    @Resource
    private FinancialBillRuleMapper financialBillRuleMapper;

    @Override
    public FinancialBillRuleDTO queryTenantFinancialRule(Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
        FinancialBillRule financialBillRule = financialBillRuleMapper.selectByTenantId(tenantId);
        if(Objects.isNull(financialBillRule)){
            return null;
        }

        FinancialBillRuleDTO financialBillRuleDTO = new FinancialBillRuleDTO();
        BeanUtils.copyProperties(financialBillRule,financialBillRuleDTO);
        return financialBillRuleDTO;
    }

    @Override
    public ResultDTO add(FinancialBillRuleDTO financialBillRuleDTO, LoginContextInfoDTO loginContextInfoDTO) {
        FinancialBillRule financialBillRule = financialBillRuleMapper.selectByTenantId(loginContextInfoDTO.getTenantId());
        AssertCheckBiz.isNull(financialBillRule, ResultDTOEnum.FINANCIAL_RULE_EXISTED.getCode(), ResultDTOEnum.FINANCIAL_RULE_EXISTED.getMessage());
        financialBillRule = new FinancialBillRule();
        financialBillRule.setTenantId(loginContextInfoDTO.getTenantId());
        financialBillRule.setType(financialBillRuleDTO.getType());
        financialBillRule.setDay(financialBillRuleDTO.getDay());
        financialBillRule.setStartTime(TimeUtils.dateConvertLocalDateTime(new Date()));
        LocalDateTime nextBillTime = getNextBillTime(financialBillRuleDTO.getType(), financialBillRuleDTO.getDay());
        financialBillRule.setEndTime(nextBillTime);
        financialBillRuleMapper.insertSelective(financialBillRule);
        return ResultDTO.success();
    }

    @Override
    public LocalDateTime getNextBillTime(Integer type, Integer day){
        Date endTime;
        // 计算账期结束时间
        if(BillRuleTypeEnum.WEEK.getCode().equals(type)){
            // 获取本周第一天
            String weekFirstDate = TimeUtils.getWeekStartDate(TimeUtils.changeDate2String(new Date()));
            // 计算之后多少天还没，计算之后多少天还没
            endTime = TimeUtils.getBeforeTime(TimeUtils.changeString2Date(weekFirstDate), -day + 1);
            // 判断当前时间是否已经超过账期结束时间，如果超过，账期变为下一周
            if(!endTime.after(new Date())){
                endTime = TimeUtils.getBeforeTime(TimeUtils.changeString2Date(weekFirstDate), -day - 6);
            }
        }else {
            // 获取本月第一天
            String[] thisMonthStartAndEndTimeStr = TimeUtils.getThisMonthStartAndEndTimeStr();
            String monthFirstDate = thisMonthStartAndEndTimeStr[0];
            // 计算之后多少天还没，计算之后多少天还没
            endTime = TimeUtils.getBeforeTime(TimeUtils.changeString2Date(monthFirstDate), - day + 1);
            // 判断当前时间是否已经超过账期结束时间，如果超过，账期变为下一月
            if(!endTime.after(new Date())){
                // 获取下月的开始时间
                endTime = TimeUtils.getBeforeTime(TimeUtils.changeString2Date(thisMonthStartAndEndTimeStr[1]), -day);
            }
        }

        return TimeUtils.dateConvertLocalDateTime(endTime);
    }

    @Override
    public ResultDTO update(FinancialBillRuleDTO financialBillRuleDTO, LoginContextInfoDTO loginContextInfoDTO) {
        FinancialBillRule financialBillRule = financialBillRuleMapper.selectByTenantId(loginContextInfoDTO.getTenantId());
        financialBillRule.setType(financialBillRuleDTO.getType());
        financialBillRule.setDay(financialBillRuleDTO.getDay());
        LocalDateTime nextBillTime = getNextBillTime(financialBillRuleDTO.getType(), financialBillRuleDTO.getDay());
        financialBillRule.setEndTime(nextBillTime);
        financialBillRuleMapper.updateByPrimaryKeySelective(financialBillRule);
        return ResultDTO.success();
    }

    @Override
    public ResultDTO<FinancialBillRuleVO> query(LoginContextInfoDTO loginContextInfoDTO) {
        FinancialBillRule financialBillRule = financialBillRuleMapper.selectByTenantId(loginContextInfoDTO.getTenantId());
        if(Objects.isNull(financialBillRule)){
            return ResultDTO.success();
        }

        FinancialBillRuleVO financialBillRuleVo = new FinancialBillRuleVO();
        BeanUtils.copyProperties(financialBillRule,financialBillRuleVo);
        return ResultDTO.success(financialBillRuleVo);
    }

    @Override
    public ResultDTO<FinancialBillRuleVO> calculateNextBillTime(Integer type, Integer day, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(type, ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.getMessage());
        AssertCheckParams.notNull(day,ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PRICING_SUPPLY_FAILURE.getMessage());
        FinancialBillRule financialBillRule = financialBillRuleMapper.selectByTenantId(loginContextInfoDTO.getTenantId());
        LocalDateTime endTime = getNextBillTime(type,day);
        FinancialBillRuleVO financialBillRuleVO = new FinancialBillRuleVO();
        financialBillRuleVO.setStartTime(financialBillRule.getStartTime());
        financialBillRuleVO.setEndTime(endTime);

        return ResultDTO.success(financialBillRuleVO);
    }
}

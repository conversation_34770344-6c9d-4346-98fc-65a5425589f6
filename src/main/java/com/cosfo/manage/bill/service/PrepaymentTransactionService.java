package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.PrepaymentTransactionQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionTotalVO;
import com.cosfo.manage.bill.model.vo.PrepaymentTransactionVO;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 */
public interface PrepaymentTransactionService {

    /**
     * 获取收支明细分页
     * @param queryDTO
     * @return
     */
    PageInfo<PrepaymentTransactionVO> queryPrepaymentTransactionPage(PrepaymentTransactionQueryDTO queryDTO);

    /**
     * 下载收支明细
     * @param queryDTO
     * @return
     */
    Boolean downloadPrepaymentTransaction(PrepaymentTransactionQueryDTO queryDTO);

    /**
     * 获取收支明细
     * @param queryDTO
     * @return
     */
    PrepaymentTransactionTotalVO queryPrepaymentTransactionTotal(PrepaymentTransactionQueryDTO queryDTO);
}

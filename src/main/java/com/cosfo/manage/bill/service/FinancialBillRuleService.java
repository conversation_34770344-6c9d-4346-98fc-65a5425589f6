package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.FinancialBillRuleDTO;
import com.cosfo.manage.bill.model.vo.FinancialBillRuleVO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/4
 */
public interface FinancialBillRuleService {
    /**
     * 查询品牌方账期规则
     *
     * @param tenantId
     * @return
     */
    FinancialBillRuleDTO queryTenantFinancialRule(Long tenantId);

    /**
     * 新增账期规则
     *
     * @param financialBillRuleDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO add(FinancialBillRuleDTO financialBillRuleDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 计算下一次出账时间
     *
     * @param type
     * @param day
     * @return
     */
    LocalDateTime getNextBillTime(Integer type, Integer day);

    /**
     * 修改账期规则
     *
     * @param financialBillRuleDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO update(FinancialBillRuleDTO financialBillRuleDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询账期规则
     *
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO<FinancialBillRuleVO> query(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 计算下一次出账时间
     *
     * @param type
     * @param day
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO<FinancialBillRuleVO> calculateNextBillTime(Integer type,Integer day,LoginContextInfoDTO loginContextInfoDTO);
}

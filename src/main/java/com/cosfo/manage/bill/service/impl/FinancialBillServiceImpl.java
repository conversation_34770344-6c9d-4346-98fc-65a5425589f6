package com.cosfo.manage.bill.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.LargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.bill.mapper.*;
import com.cosfo.manage.bill.model.dto.*;
import com.cosfo.manage.bill.model.po.*;
import com.cosfo.manage.bill.model.vo.*;
import com.cosfo.manage.bill.service.BillProfitSharingOrderService;
import com.cosfo.manage.bill.service.FinancialBillRuleService;
import com.cosfo.manage.bill.service.FinancialBillService;
import com.cosfo.manage.bill.service.PaymentService;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.HuiFuPaymentConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.easy.excel.helper.ImportExcelHelper;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.*;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.file.model.po.FileDownloadRecord;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.service.MerchantStoreGroupMappingService;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.order.model.dto.BillOrderInfoDTO;
import com.cosfo.manage.order.model.dto.OrderDTO;
import com.cosfo.manage.order.service.OrderAfterSaleService;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/4
 */
@Slf4j
@Service
public class FinancialBillServiceImpl implements FinancialBillService {
    @Resource
    private FinancialBillMapper financialBillMapper;
    @Resource
    @Lazy
    private MerchantStoreService merchantStoreService;
    @Resource
    private OrderBusinessService orderBusinessService;
    @Resource
    private FinancialBillCredentialsMapper financialBillCredentialsMapper;
    @Resource
    private FinancialBillItemMapper financialBillItemMapper;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;
    @Resource
    private FinancialBillRuleMapper financialBillRuleMapper;
    @Resource
    private FinancialBillRuleService financialBillRuleService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private TransLogResultListMapper transLogResultListMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;
    @Resource
    private AdministrativeDivisionMapper administrativeDivisionMapper;
    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private MerchantStoreGroupMappingService merchantStoreGroupMappingService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;

    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private PaymentService paymentService;


    @Override
    public ResultDTO<PageInfo<FinancialStoreBillVO>> list(StoreBillQueryDTO storeBillQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        boolean hasResultFlag = dealQueryCondition(storeBillQueryDTO, loginContextInfoDTO);

        Integer pageIndex = storeBillQueryDTO.getPageIndex();
        Integer pageSize = storeBillQueryDTO.getPageSize();
        List<FinancialStoreBillVO> list = new ArrayList<FinancialStoreBillVO>(16);
        if (!hasResultFlag) {
            return ResultDTO.success(PageInfoHelper.createPageInfo(list, pageSize));
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<FinancialStoreBillVO> financialStoreBillVOS = financialBillMapper.list(storeBillQueryDTO);
        if (!CollectionUtils.isEmpty(financialStoreBillVOS)) {
            List<Long> storeIds = financialStoreBillVOS.stream().map(FinancialStoreBillVO::getStoreId).collect(Collectors.toList());
            List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.batchQuery(storeIds, loginContextInfoDTO.getTenantId());
            Map<Long, MerchantStoreDTO> merchantStoreDTOMap = merchantStoreDTOS.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));
            // 查询门店分组
            Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(loginContextInfoDTO.getTenantId(), storeIds);
            financialStoreBillVOS.stream().forEach(item -> {
                MerchantStoreDTO merchantStoreDTO = merchantStoreDTOMap.get(item.getStoreId());
                item.setStoreName(merchantStoreDTO.getStoreName());
                item.setStoreType(merchantStoreDTO.getType());
                item.setStorePhone(merchantStoreDTO.getAccountPhone());
                item.setMerchantStoreGroupName(groupMap.get(item.getStoreId()));
            });
        }

        return ResultDTO.success(PageInfoHelper.createPageInfo(financialStoreBillVOS, pageSize));
    }

    private boolean dealQueryCondition(StoreBillQueryDTO storeBillQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        storeBillQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        if (StringUtils.isEmpty(storeBillQueryDTO.getCreateTimeSort())) {
            storeBillQueryDTO.setCreateTimeSort(Constants.DESC);
        }
        // 查詢条件处理 门店查询条件
        List<Long> merchantStoreIds = new ArrayList<>();
        // 门店类型和门店账号
        if (!CollectionUtils.isEmpty(storeBillQueryDTO.getMerchantStoreGroupIds())) {
            List<MerchantStoreGroupResultResp> mappings = merchantStoreGroupMappingService.selectByGroupId(storeBillQueryDTO.getMerchantStoreGroupIds(), loginContextInfoDTO.getTenantId());
            if (CollectionUtils.isEmpty(mappings)) {
                return false;
            }
            merchantStoreIds = mappings.stream().map(MerchantStoreGroupResultResp::getStoreId).collect(Collectors.toList());
        }
        if (Objects.nonNull(storeBillQueryDTO) && (!StringUtils.isEmpty(storeBillQueryDTO.getStoreName()) || Objects.nonNull(storeBillQueryDTO.getStoreType())
                || !StringUtils.isEmpty(storeBillQueryDTO.getStorePhone())) || !CollectionUtils.isEmpty(merchantStoreIds)) {
            MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder()
                    .tenantId(loginContextInfoDTO.getTenantId())
                    .type(storeBillQueryDTO.getStoreType())
                    .storeName(storeBillQueryDTO.getStoreName())
                    .storeIds(merchantStoreIds)
                    .phone(storeBillQueryDTO.getStorePhone())
                    .build();
            List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.listByConditionNew(query);
            if (CollectionUtils.isEmpty(merchantStoreDTOS)) {
                return false;
            }
            merchantStoreIds = merchantStoreDTOS.stream().map(MerchantStoreDTO::getId).collect(Collectors.toList());
            storeBillQueryDTO.setStoreIds(merchantStoreIds);
        }

        if (!StringUtils.isEmpty(storeBillQueryDTO.getOrderNo())) {
            OrderResp orderResp = orderQueryFacade.queryByNo(storeBillQueryDTO.getOrderNo());
            if(orderResp == null){
                return false;
            }
            storeBillQueryDTO.setOrderIds(Collections.singletonList(orderResp.getId()));
        }
        return true;
    }

    @Override
    public ResultDTO<FinancialStoreBillVO> detail(Long billId, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(billId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "账单编号不能为空");
        FinancialStoreBillVO financialStoreBillVO = financialBillMapper.selectByBillNo(billId, loginContextInfoDTO.getTenantId(), BillTypeEnum.RECEIVABLE_BILL.getCode());
        AssertCheckParams.notNull(financialStoreBillVO, ResultDTOEnum.NOT_FOUND.getCode(), "账单不存在");
        // 查询门店信息
        MerchantStoreDTO merchantStore = merchantStoreService.selectDetail(financialStoreBillVO.getStoreId());
        financialStoreBillVO.setStoreName(merchantStore.getStoreName());
        // 查询门店提交凭证信息
        FinancialBillCredentials storeFinancialBillCredentials = financialBillCredentialsMapper.selectByBillId(loginContextInfoDTO.getTenantId(), financialStoreBillVO.getBillId(), CredentialsOperatorTypeEnum.PAYER.getCode());
        if (Objects.nonNull(storeFinancialBillCredentials)) {
            FinancialBillCredentialsVO storeCredentials = new FinancialBillCredentialsVO();
            BeanUtils.copyProperties(storeFinancialBillCredentials, storeCredentials);
            financialStoreBillVO.setStoreCredentials(storeCredentials);
            financialStoreBillVO.setStoreCredentialsTime(storeFinancialBillCredentials.getCredentialsTime());
        }

        // 查询品牌方提交凭证信息
        FinancialBillCredentials tenantFinancialBillCredentials = financialBillCredentialsMapper.selectByBillId(loginContextInfoDTO.getTenantId(), financialStoreBillVO.getBillId(), CredentialsOperatorTypeEnum.PAYEE.getCode());
        if (Objects.nonNull(tenantFinancialBillCredentials)) {
            FinancialBillCredentialsVO tenantCredentials = new FinancialBillCredentialsVO();
            BeanUtils.copyProperties(tenantFinancialBillCredentials, tenantCredentials);
            financialStoreBillVO.setTenantCredentials(tenantCredentials);
        }

        return ResultDTO.success(financialStoreBillVO);
    }

    @Override
    public ResultDTO addTenantCredentials(TenantBillCredentialsDTO tenantBillCredentialsDTO, LoginContextInfoDTO loginContextInfoDTO) {
        FinancialBillCredentials financialBillCredentials = financialBillCredentialsMapper.selectByBillId(loginContextInfoDTO.getTenantId(), tenantBillCredentialsDTO.getBillId(), CredentialsOperatorTypeEnum.PAYEE.getCode());
        if (Objects.isNull(financialBillCredentials)) {
            financialBillCredentials = new FinancialBillCredentials();
            financialBillCredentials.setCredentials(tenantBillCredentialsDTO.getCredentials());
            financialBillCredentials.setTenantId(loginContextInfoDTO.getTenantId());
            financialBillCredentials.setCredentialsTime(TimeUtils.dateConvertLocalDateTime(new Date()));
            financialBillCredentials.setRemark(tenantBillCredentialsDTO.getRemark());
            financialBillCredentials.setBillId(tenantBillCredentialsDTO.getBillId());
            financialBillCredentials.setOperatorType(CredentialsOperatorTypeEnum.PAYEE.getCode());
            financialBillCredentials.setOperatorId(loginContextInfoDTO.getTenantId());
            financialBillCredentialsMapper.insertSelective(financialBillCredentials);
        } else {
            financialBillCredentials.setCredentials(tenantBillCredentialsDTO.getCredentials());
            financialBillCredentials.setTenantId(loginContextInfoDTO.getTenantId());
            financialBillCredentials.setCredentialsTime(TimeUtils.dateConvertLocalDateTime(new Date()));
            financialBillCredentials.setRemark(tenantBillCredentialsDTO.getRemark());
            financialBillCredentials.setBillId(tenantBillCredentialsDTO.getBillId());
            financialBillCredentials.setOperatorType(CredentialsOperatorTypeEnum.PAYEE.getCode());
            financialBillCredentials.setOperatorId(loginContextInfoDTO.getTenantId());
            financialBillCredentialsMapper.updateByPrimaryKeySelective(financialBillCredentials);
        }

        return ResultDTO.success();
    }

    @Override
    public ResultDTO<FinancialStoreBillVO> info(Long billId, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(billId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "账单编号不能为空");
        FinancialStoreBillVO financialStoreBillVO = financialBillMapper.selectByBillNo(billId, loginContextInfoDTO.getTenantId(), BillTypeEnum.RECEIVABLE_BILL.getCode());
        // 查询门店信息
        MerchantStoreDTO merchantStoreDTO = merchantStoreService.queryStore(financialStoreBillVO.getStoreId(), loginContextInfoDTO.getTenantId());
        financialStoreBillVO.setStoreName(merchantStoreDTO.getStoreName());
        // 订单
        List<FinancialBillItem> orderFinancialBillItems = financialBillItemMapper.selectByBillId(billId, loginContextInfoDTO.getTenantId(), BusinessTypeEnum.ORDER.getCode());
        BigDecimal totalDeliveryFee = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(orderFinancialBillItems)) {
            List<Long> orderIds = orderFinancialBillItems.stream().map(FinancialBillItem::getBusinessId).collect(Collectors.toList());
            List<OrderDTO> orderDTOs = orderBusinessService.batchQuery(orderIds, loginContextInfoDTO.getTenantId());
            for (OrderDTO orderDTO : orderDTOs) {
                totalDeliveryFee = NumberUtil.add(orderDTO.getDeliveryFee(), totalDeliveryFee);
            }

            financialStoreBillVO.setOrderNum(orderFinancialBillItems.size());
            // 查询sku数和sku数量
            BillOrderInfoDTO billOrderInfoDTO = orderBusinessService.queryBillInfo(orderIds, loginContextInfoDTO.getTenantId());
            financialStoreBillVO.setSkuNum(billOrderInfoDTO.getSkuNum());
            financialStoreBillVO.setSkuAmount(billOrderInfoDTO.getSkuAmount());
        }

        financialStoreBillVO.setOrderDeliveryFeeTotalPrice(totalDeliveryFee);
        return ResultDTO.success(financialStoreBillVO);
    }


    @Override
    public ResultDTO<List<BillOrderVO>> orderDetail(Long billId, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(billId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "账单编号不能为空");
        // 订单
        List<FinancialBillItem> orderFinancialBillItems = financialBillItemMapper.selectByBillId(billId, loginContextInfoDTO.getTenantId(), BusinessTypeEnum.ORDER.getCode());
        if (!CollectionUtils.isEmpty(orderFinancialBillItems)) {
            List<Long> orderIds = orderFinancialBillItems.stream().map(FinancialBillItem::getBusinessId).collect(Collectors.toList());
            List<BillOrderVO> billOrderVOS = orderBusinessService.queryBillOrderInfo(orderIds, loginContextInfoDTO.getTenantId());
            return ResultDTO.success(billOrderVOS);
        }

        return ResultDTO.success(new ArrayList<>());
    }

    @Override
    public ResultDTO<List<BillOrderAfterSaleVO>> orderAfterSaleDetail(Long billId, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(billId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "账单编号不能为空");
        // 售后单
        List<FinancialBillItem> orderAfterSaleFinancialBillItems = financialBillItemMapper.selectByBillId(billId, loginContextInfoDTO.getTenantId(), BusinessTypeEnum.ORDER_AFTER_SALE.getCode());
        if (!CollectionUtils.isEmpty(orderAfterSaleFinancialBillItems)) {
            List<Long> orderAfterSaleIds = orderAfterSaleFinancialBillItems.stream().map(FinancialBillItem::getBusinessId).collect(Collectors.toList());
            List<BillOrderAfterSaleVO> billOrderAfterSaleVOS = orderAfterSaleService.batchQueryByOrderAfterSaleIds(orderAfterSaleIds, loginContextInfoDTO.getTenantId());
            return ResultDTO.success(billOrderAfterSaleVOS);
        }

        return ResultDTO.success(new ArrayList<>());
    }

    @Override
    public ResultDTO confirm(Long billId, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(billId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "账单编号不能为空");
        // 查询门店提交凭证信息
        FinancialBillCredentials storeFinancialBillCredentials = financialBillCredentialsMapper.selectByBillId(loginContextInfoDTO.getTenantId(), billId, CredentialsOperatorTypeEnum.PAYER.getCode());
        // 查询品牌方提交凭证信息
        FinancialBillCredentials tenantFinancialBillCredentials = financialBillCredentialsMapper.selectByBillId(loginContextInfoDTO.getTenantId(), billId, CredentialsOperatorTypeEnum.PAYEE.getCode());
        AssertCheckBiz.isTrue(Objects.nonNull(storeFinancialBillCredentials) || Objects.nonNull(tenantFinancialBillCredentials), ResultDTOEnum.NOT_FOUND_CREDENTIALS.getCode(), ResultDTOEnum.NOT_FOUND_CREDENTIALS.getMessage());

        // 修改账单信息
        financialBillMapper.updateStatus(billId, BillStatusEnum.AUDITED_SUCCESS.getCode());
        return ResultDTO.success();
    }

    @Override
    public ResultDTO<FinancialBillTotalDataVO> billTotalData(StoreBillQueryDTO storeBillQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 查詢条件处理 门店查询条件
        boolean havingResult = true;
        if (Objects.nonNull(storeBillQueryDTO) && (Objects.nonNull(storeBillQueryDTO.getStoreName()) || Objects.nonNull(storeBillQueryDTO.getStoreType())
                || Objects.nonNull(storeBillQueryDTO.getStorePhone()))) {
            List<MerchantStoreResultResp> merchantStores = merchantStoreService.queryMerchantStore(storeBillQueryDTO.getStoreType(), storeBillQueryDTO.getStoreName(), storeBillQueryDTO.getStorePhone(), loginContextInfoDTO.getTenantId());
            if (CollectionUtils.isEmpty(merchantStores)) {
                havingResult = false;
            } else {
                List<Long> storeIds = merchantStores.stream().map(MerchantStoreResultResp::getId).collect(Collectors.toList());
                storeBillQueryDTO.setStoreIds(storeIds);
            }
        }

        if (Objects.nonNull(storeBillQueryDTO.getOrderNo())) {
            OrderResp orderResp = orderQueryFacade.queryByNo(storeBillQueryDTO.getOrderNo());
            if(orderResp == null){
                havingResult = false;
            }else {
                storeBillQueryDTO.setOrderIds(Collections.singletonList(orderResp.getId()));
            }
        }

        FinancialBillTotalDataVO financialBillTotalDataVO = new FinancialBillTotalDataVO();
        if (Boolean.FALSE.equals(havingResult)) {
            financialBillTotalDataVO.setBillNum(Constants.ZERO.intValue());
            financialBillTotalDataVO.setReceivablePrice(BigDecimal.ZERO);
            financialBillTotalDataVO.setOrderReceivablePrice(BigDecimal.ZERO);
            financialBillTotalDataVO.setOrderAfterSaleReceivablePrice(BigDecimal.ZERO);
            return ResultDTO.success(financialBillTotalDataVO);
        }

        storeBillQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        List<FinancialStoreBillVO> financialStoreBillVOS = financialBillMapper.list(storeBillQueryDTO);
        financialBillTotalDataVO.setBillNum(financialStoreBillVOS.size());
        BigDecimal receivablePrice = BigDecimal.ZERO;
        BigDecimal orderReceivablePrice = BigDecimal.ZERO;
        BigDecimal orderAfterSaleReceivablePrice = BigDecimal.ZERO;
        for (FinancialStoreBillVO financialStoreBillVO : financialStoreBillVOS) {
            receivablePrice = NumberUtil.add(receivablePrice, financialStoreBillVO.getReceivablePrice());
            orderReceivablePrice = NumberUtil.add(orderReceivablePrice, financialStoreBillVO.getReceivableOrderPrice());
            orderAfterSaleReceivablePrice = NumberUtil.add(orderAfterSaleReceivablePrice, financialStoreBillVO.getReceivableOrderAfterSalePrice());
        }

        financialBillTotalDataVO.setReceivablePrice(receivablePrice);
        financialBillTotalDataVO.setOrderReceivablePrice(orderReceivablePrice);
        financialBillTotalDataVO.setOrderAfterSaleReceivablePrice(orderAfterSaleReceivablePrice);

        return ResultDTO.success(financialBillTotalDataVO);
    }

    @Override
    public void billTask() {
        log.error("开始处理门店账期结算任务...");
        // 查询需要出账的品牌方
        List<FinancialBillRule> financialBillRules = financialBillRuleMapper.queryNeedEnterAccountTenant();
        if (!CollectionUtils.isEmpty(financialBillRules)) {
            financialBillRules.stream().forEach(financialBillRule -> {
                log.error("开始处理品牌方{}门店下账期信息", financialBillRule.getTenantId());
                // 查询门店下账期订单信息
                List<OrderDTO> orderDTOS = orderBusinessService.queryBillOrderByStartTimeAndEndTime(financialBillRule.getTenantId(), financialBillRule.getStartTime(), financialBillRule.getEndTime());
                Map<Long, List<OrderDTO>> orderDTOMap = new HashMap<>(16);
                if (!CollectionUtils.isEmpty(orderDTOS)) {
                    // 根据门店进行分组
                    orderDTOMap = orderDTOS.stream().collect(Collectors.groupingBy(OrderDTO::getStoreId));
                }

                // 查询门店下账期售后单信息
                List<OrderAfterSaleResp> orderAfterSaleDTOS = orderAfterSaleService.queryBillOrderByStartTimeAndEndTime(financialBillRule.getTenantId(), financialBillRule.getStartTime(), financialBillRule.getEndTime());
                Map<Long, List<OrderAfterSaleResp>> orderAfterSaleDTOMap = new HashMap<>(16);
                if (!CollectionUtils.isEmpty(orderAfterSaleDTOS)) {
                    // 根据门店进行分组
                    orderAfterSaleDTOMap = orderAfterSaleDTOS.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getStoreId));
                }

                Set<Long> storeIds = new HashSet<>();
                storeIds.addAll(orderDTOMap.keySet());
                storeIds.addAll(orderAfterSaleDTOMap.keySet());
                Map<Long, List<OrderDTO>> finalOrderDTOMap = orderDTOMap;
                Map<Long, List<OrderAfterSaleResp>> finalOrderAfterSaleDTOMap = orderAfterSaleDTOMap;
                boolean match = true;
                if (!CollectionUtils.isEmpty(storeIds)) {
                    // 是否全部执行
                    match = storeIds.stream().map(storeId -> {
                        List<OrderDTO> orderDTOList = finalOrderDTOMap.get(storeId);
                        List<OrderAfterSaleResp> orderAfterSaleDTOList = finalOrderAfterSaleDTOMap.get(storeId);
                        if (CollectionUtils.isEmpty(orderDTOList) && CollectionUtils.isEmpty(orderAfterSaleDTOList)) {
                            return true;
                        }

                        // 生成门店周期账单
                        Boolean executeResult = storeBillCreate(storeId, orderDTOList, orderAfterSaleDTOList, financialBillRule);
                        return executeResult;
                    }).allMatch(e -> e);
                }

                // 全部执行完成修改品牌方下次出账周期
                if (match) {
                    financialBillRule.setStartTime(financialBillRule.getEndTime());
                    LocalDateTime endTime = financialBillRuleService.getNextBillTime(financialBillRule.getType(), financialBillRule.getDay());
                    financialBillRule.setEndTime(endTime);
                    financialBillRuleMapper.updateByPrimaryKeySelective(financialBillRule);
                }

                log.error("处理品牌方{}门店下账期信息结束，更新下次账期时间", financialBillRule.getTenantId());
            });
        }

        log.error("处理门店账期结算任务结束...");
    }

    @Override
    public CommonResult<FileDownloadRecord> exportProfitList(LoginContextInfoDTO requestContextInfoDTO, BillProfitSharingQueryDTO billProfitSharingQueryDTO) {
        Map<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.EIGHT);
        // 分账日期
        if (Objects.nonNull(billProfitSharingQueryDTO.getStartTime()) && Objects.nonNull(billProfitSharingQueryDTO.getEndTime())) {
            queryParamsMap.put(HuiFuPaymentConstant.PROFIT_DAY, LocalDateTimeUtil.localDateTimeToString3(billProfitSharingQueryDTO.getStartTime()) + StringConstants.SEPARATING_IN_LINE + LocalDateTimeUtil.localDateTimeToString3(billProfitSharingQueryDTO.getEndTime()));
        }
//        // 分账状态
//        if (Objects.nonNull(billProfitSharingQueryDTO.getStatus())) {
//            queryParamsMap.put(HuiFuPaymentConstant.PROFIT_STATUS, BillProfitEnum.getDesc(billProfitSharingQueryDTO.getStatus()));
//        }
        // 分账接收方主体名称
        if (Objects.nonNull(billProfitSharingQueryDTO.getTenantName())) {
            queryParamsMap.put(HuiFuPaymentConstant.PROFIT_MAIN_NAME, billProfitSharingQueryDTO.getTenantName());
        }
        // 分账交易号
        if (Objects.nonNull(billProfitSharingQueryDTO.getTransactionId())) {
            queryParamsMap.put(HuiFuPaymentConstant.PROFIT_TRANSACTION, billProfitSharingQueryDTO.getTransactionId());
        }
        // 原交易银行流水号
        if (Objects.nonNull(billProfitSharingQueryDTO.getBankOrderId())) {
            queryParamsMap.put(HuiFuPaymentConstant.PROFIT_WEIXIN_ORDER_ID, billProfitSharingQueryDTO.getBankOrderId());
        }
        // 分账交易订单号
        if (Objects.nonNull(billProfitSharingQueryDTO.getRecordNo())) {
            queryParamsMap.put(HuiFuPaymentConstant.ORDER_NO, billProfitSharingQueryDTO.getRecordNo());
        }
        if(StringUtils.isNotEmpty(billProfitSharingQueryDTO.getPaymentNo())){
            queryParamsMap.put(HuiFuPaymentConstant.PAYMENT_NO, billProfitSharingQueryDTO.getPaymentNo());
        }
        billProfitSharingQueryDTO.setTenantId(requestContextInfoDTO.getTenantId());
        billProfitSharingQueryDTO.setStatus(ProfitSharingResultEnum.FINISHED.getStatus());

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.PROFIT_INFO.getType());
        recordDTO.setTenantId(requestContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.PROFIT_EXPORT.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(billProfitSharingQueryDTO, e -> writeDownloadCenter(e, recordDTO.getFileName()));

//        //存储文件下载记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(requestContextInfoDTO.getTenantId());
//        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        // 设置导出类型为分账记录
//        record.setType(FileDownloadTypeEnum.PROFIT_INFO.getType());
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                generateProfitExportFileStream(billProfitSharingQueryDTO, record.getId());
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(record.getId());
//                log.error("分账明细导出失败", e);
//            }
//        });
        return CommonResult.ok();
    }


    public DownloadCenterOssRespDTO writeDownloadCenter(BillProfitSharingQueryDTO billProfitSharingQueryDTO, String fileName) {
        // 1、表格处理
        String filePath = generateProfitExportFileStream(billProfitSharingQueryDTO);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }


    @Override
    public CommonResult exportSettlementList(LoginContextInfoDTO requestContextInfoDTO, TransLogResultListQueryDTO transLogResultListQueryDTO) {
        Map<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.TWO);
        // 银行结算
        if (Objects.nonNull(transLogResultListQueryDTO.getAcctStat())) {
            queryParamsMap.put(HuiFuPaymentConstant.SETTLE_CONFIG, BillPayStatusEnum.getDesc(transLogResultListQueryDTO.getAcctStat()));
        }
        // 结算日期
        if (Objects.nonNull(transLogResultListQueryDTO.getStartTime()) && Objects.nonNull(transLogResultListQueryDTO.getEndTime())) {
            queryParamsMap.put(HuiFuPaymentConstant.SETTLE_DAY, LocalDateTimeUtil.localTimeFormat(transLogResultListQueryDTO.getStartTime()) + StringConstants.SEPARATING_IN_LINE + LocalDateTimeUtil.localTimeFormat(transLogResultListQueryDTO.getEndTime()));
            // 封装导出条件
            transLogResultListQueryDTO.setStartTimeStr(TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(transLogResultListQueryDTO.getStartTime()), TimeUtils.FORMAT_STRING));
            transLogResultListQueryDTO.setEndTimeStr(TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(transLogResultListQueryDTO.getEndTime()), TimeUtils.FORMAT_STRING));
        }
        transLogResultListQueryDTO.setTenantId(requestContextInfoDTO.getTenantId());


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.SETTLE_INFO.getType());
        recordDTO.setTenantId(requestContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.SETTLE_INFO.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(transLogResultListQueryDTO, e -> writeDownloadCenter(e, recordDTO.getFileName()));


//        //存储文件下载记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(requestContextInfoDTO.getTenantId());
//        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        // 设置导出类型为结算单
//        record.setType(FileDownloadTypeEnum.SETTLE_INFO.getType());
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//
//        transLogResultListQueryDTO.setTenantId(requestContextInfoDTO.getTenantId());
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                generateSettleExportFileStream(transLogResultListQueryDTO, record.getId());
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(record.getId());
//                log.error("分账明细导出失败", e);
//            }
//        });
        return CommonResult.ok();
    }


    public DownloadCenterOssRespDTO writeDownloadCenter(TransLogResultListQueryDTO transLogResultListQueryDTO, String fileName) {
        // 1、表格处理
        String filePath = generateSettleExportFileStream(transLogResultListQueryDTO);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }

    @Override
    public ExcelImportResDTO importAdministrativeDivision(MultipartFile file, LoginContextInfoDTO loginContextInfoDTO) throws IOException {
        Long tenantId = loginContextInfoDTO.getTenantId();
        // 读excel
        ImportExcelHelper<AdministrativeDivisionExcelDataDTO> helper = new ImportExcelHelper<>();
        List<AdministrativeDivisionExcelDataDTO> list = helper.getList(file.getInputStream(), AdministrativeDivisionExcelDataDTO.class, 0, 1);
        if (CollectionUtils.isEmpty(list)) {
            throw new DefaultServiceException("导入行政区划数据不能为空");
        }
        List<AdministrativeDivisionExcelDataDTO> errorList = new ArrayList<>();
        for (AdministrativeDivisionExcelDataDTO excelDataDTO : list) {
            // 校验excel数据
            checkAdministrativeDivisionImportData(excelDataDTO);
            if (!StringUtils.isBlank(excelDataDTO.getErrorMessage())) {
                errorList.add(excelDataDTO);
                continue;
            }
            // 写入区域表
            AdministrativeDivision administrativeDivision = new AdministrativeDivision();
            administrativeDivision.setAreaNo(excelDataDTO.getAreaNo());
            administrativeDivision.setAreaName(excelDataDTO.getAreaName());
            administrativeDivision.setCityNo(excelDataDTO.getCityNo());
            administrativeDivision.setCityName(excelDataDTO.getCityName());
            administrativeDivision.setProNo(excelDataDTO.getProNo());
            administrativeDivision.setProName(excelDataDTO.getProName());
            administrativeDivisionMapper.insert(administrativeDivision);
        }

        // 异常数据写入excel
        String qiNiuFilePath = null;
        if (!CollectionUtils.isEmpty(errorList)) {
            String filePath = commonService.exportExcel(errorList, ExcelTypeEnum.ERROR_AREA.getName());
            qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "行政区划错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
            commonService.deleteFile(filePath);
        }

        // 返回导入结果
        ExcelImportResDTO excelImportResDTO = new ExcelImportResDTO();
        excelImportResDTO.setFailRow(CollectionUtils.isEmpty(errorList) ? NumberConstants.ZERO : errorList.size());
        excelImportResDTO.setSuccessRow(list.size() - excelImportResDTO.getFailRow());
        excelImportResDTO.setErrorFilePath(qiNiuFilePath);
        return excelImportResDTO;
    }

    private void checkAdministrativeDivisionImportData(AdministrativeDivisionExcelDataDTO excelDataDTO) {

        if (StringUtils.isBlank(excelDataDTO.getAreaNo())) {
            excelDataDTO.setErrorMessage("区县编码不能为空");
            return;
        }
        if (StringUtils.isBlank(excelDataDTO.getAreaName())) {
            excelDataDTO.setErrorMessage("区县名称不能为空");
            return;
        }
        if (StringUtils.isBlank(excelDataDTO.getCityNo())) {
            excelDataDTO.setErrorMessage("城市编码不能为空");
            return;
        }
        if (StringUtils.isBlank(excelDataDTO.getCityName())) {
            excelDataDTO.setErrorMessage("城市名称不能为空");
            return;
        }
        if (StringUtils.isBlank(excelDataDTO.getProNo())) {
            excelDataDTO.setErrorMessage("省份编码不能为空");
            return;
        }
        if (StringUtils.isBlank(excelDataDTO.getProName())) {
            excelDataDTO.setErrorMessage("省份名称不能为空");
            return;
        }
        AdministrativeDivision administrativeDivision = administrativeDivisionMapper.selectOne(new LambdaQueryWrapper<AdministrativeDivision>().eq(AdministrativeDivision::getAreaNo, excelDataDTO.getAreaNo()));
        if (ObjectUtil.isNotNull(administrativeDivision)) {
            excelDataDTO.setErrorMessage("该地区已存在，请勿重复导入");
            return;
        }
    }


    private String generateSettleExportFileStream(TransLogResultListQueryDTO transLogResultListQueryDTO) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.SETTLE_INFO.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();

        LargeDataSetExporter<TransLogResultListDTO, SettleInfoExcelDataDTO> handler = new LargeDataSetExporter<TransLogResultListDTO, SettleInfoExcelDataDTO>() {
            @Override
            protected List<SettleInfoExcelDataDTO> convert(TransLogResultListDTO transLogResultListDTO) {
                SettleInfoExcelDataDTO settleInfoExcelDataDTO = new SettleInfoExcelDataDTO();
                settleInfoExcelDataDTO.setTransStat(BillPayStatusEnum.getDesc(transLogResultListDTO.getAcctStat()));
                settleInfoExcelDataDTO.setCreateTime(transLogResultListDTO.getTransDate());
                settleInfoExcelDataDTO.setReason(transLogResultListDTO.getReason());
                settleInfoExcelDataDTO.setFeeAmt(transLogResultListDTO.getTransFee());
                settleInfoExcelDataDTO.setTransAmt(transLogResultListDTO.getTransMoney());
                return Lists.newArrayList(settleInfoExcelDataDTO);
            }

            @Override
            protected void flushData(List<SettleInfoExcelDataDTO> dataList) {
                excelWriter.fill(dataList, fillConfig, writeSheet);
                log.info("导出数据 size=" + dataList.size());
            }
        };
        transLogResultListMapper.exportListAll(transLogResultListQueryDTO, handler);
        handler.clearData();
        excelWriter.finish();

        return filePath;
        // 上传数据到七牛云
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, ExcelTypeEnum.SETTLE_INFO, fileDownloadRecordId);
    }


//    private void generateSettleExportFile(TransLogResultListQueryDTO transLogResultListQueryDTO, Long fileDownloadRecordId) {
//        List<SettleInfoExcelDataDTO> list = new ArrayList<>();
//        List<TransLogResultListDTO> transLogResultListDTOList = transLogResultListMapper.listAll(transLogResultListQueryDTO);
//        for (TransLogResultListDTO transLogResultListDTO : transLogResultListDTOList) {
//            SettleInfoExcelDataDTO settleInfoExcelDataDTO = new SettleInfoExcelDataDTO();
//            settleInfoExcelDataDTO.setTransStat(BillPayStatusEnum.getDesc(transLogResultListDTO.getAcctStat()));
//            settleInfoExcelDataDTO.setCreateTime(transLogResultListDTO.getTransDate());
//            settleInfoExcelDataDTO.setReason(transLogResultListDTO.getReason());
//            settleInfoExcelDataDTO.setFeeAmt(transLogResultListDTO.getTransFee());
//            settleInfoExcelDataDTO.setTransAmt(transLogResultListDTO.getTransMoney());
//            list.add(settleInfoExcelDataDTO);
//        }
//        // 写入excel
//        String filePath = commonService.exportExcel(list, ExcelTypeEnum.SETTLE_INFO.getName());
//        // 上传七牛云
//        String qiNiuPath = null;
//        try {
//            qiNiuPath = QiNiuUtils.uploadFile(filePath, "结算明细导出信息" + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss") + ".xlsx");
//        } catch (Exception e) {
//            throw new DefaultServiceException("结算导出信息上传七牛云失败", e);
//        }
//
//        // 删除临时文件
//        commonService.deleteFile(filePath);
//
//        // 更新文件下载记录
//        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
//        fileDownloadRecord.setUrl(qiNiuPath);
//        fileDownloadRecord.setId(fileDownloadRecordId);
//        if (com.cosfo.manage.common.util.StringUtils.isBlank(qiNiuPath)) {
//            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
//        } else {
//            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FINISHED.getStatus());
//        }
//        fileDownloadRecordService.updateSelectiveByPrimaryKey(fileDownloadRecord);
//    }

    private String generateProfitExportFileStream(BillProfitSharingQueryDTO billProfitSharingQueryDTO) {
//        if (ObjectUtil.isNotNull(billProfitSharingQueryDTO.getEndTime())) {
//            // 由于between查询有边界问题，前端可能也处理过了；此处判定一下是否为 23:59:59
//            LocalDateTime endTime = billProfitSharingQueryDTO.getEndTime(), endTimePlusOneSecond = endTime.plusSeconds(1L);
//            String endTimeYyyyMmDd = endTime.format(DateTimeFormatter.ISO_DATE), endTimePlusOneSecondYyyyMmDd = endTimePlusOneSecond.format(DateTimeFormatter.ISO_DATE);
//            if(endTimeYyyyMmDd.equalsIgnoreCase(endTimePlusOneSecondYyyyMmDd)){
//                // 如果加了一秒钟后，日期相同，则需要处理EndTime，使之变成 23:59:59;
//                // 否则不需要处理
//                endTime = endTime.plusDays(1);
//            }
//            billProfitSharingQueryDTO.setEndTime(endTime);
////            Date endTime = TimeUtils.getBeforeTime(TimeUtils.localDateTimeConvertDate(billProfitSharingQueryDTO.getEndTime()), -1);
////            billProfitSharingQueryDTO.setEndTime(TimeUtils.dateConvertLocalDateTime(endTime));
//        } else {
//            billProfitSharingQueryDTO.setEndTime(LocalDateTime.now());
//        }
        if (ObjectUtil.isNull(billProfitSharingQueryDTO.getEndTime())) {
            billProfitSharingQueryDTO.setEndTime(LocalDateTime.now());
        }
        // 默认最多3个月
        if (ObjectUtil.isNull(billProfitSharingQueryDTO.getStartTime())) {
            billProfitSharingQueryDTO.setStartTime(LocalDateTime.now().plusMonths(3L));
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(billProfitSharingQueryDTO.getRecordNo())) {
            OrderResp orderResp = orderQueryFacade.queryByNo(billProfitSharingQueryDTO.getRecordNo());
            if (null == orderResp) {
                billProfitSharingQueryDTO.setOrderIds(Collections.emptyList());
            } else {
                billProfitSharingQueryDTO.setOrderIds(Lists.newArrayList(orderResp.getId()));
            }
        }
        if (StringUtils.isNotEmpty(billProfitSharingQueryDTO.getPaymentNo())) {
            List<Long> orderIds = paymentService.getOrderIdsByPaymentNo(billProfitSharingQueryDTO.getPaymentNo());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(orderIds)) {
                billProfitSharingQueryDTO.setOrderIds(Collections.emptyList());
            } else {
                List<Long> alreadyExistedOrderIds = billProfitSharingQueryDTO.getOrderIds();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(alreadyExistedOrderIds)) {
                    orderIds.retainAll(alreadyExistedOrderIds);
                }
                billProfitSharingQueryDTO.setOrderIds(orderIds);
            }
        }

        ExcelLargeDataSetExporter<BillProfitSharingDTO, TransExcelDataDTO> exporterHandler = new ExcelLargeDataSetExporter<BillProfitSharingDTO, TransExcelDataDTO>(ExcelTypeEnum.PROFIT_EXPORT.getName()) {
            @Override
            protected List<TransExcelDataDTO> convert(BillProfitSharingDTO data) {
                // 改用批量转化接口；
                return null;
                //return convertTransExcelDataDTO(data, billProfitSharingQueryDTO);
            }

            @Override
            protected List<TransExcelDataDTO> convertBatch(Collection<BillProfitSharingDTO> dataCollection) {
                billProfitSharingOrderService.fulfillBillProfitSharingInfo(dataCollection);
                return dataCollection.stream().map(FinancialBillServiceImpl::convertToTransExcelDataDTO).collect(Collectors.toList());
            }
        };

        billProfitSharingMapper.exportListAll(billProfitSharingQueryDTO, exporterHandler);
        String filePath = exporterHandler.finish(true);

        return filePath;
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, ExcelTypeEnum.PROFIT_EXPORT, fileDownloadRecordId);
    }

    private static TransExcelDataDTO convertToTransExcelDataDTO(BillProfitSharingDTO billProfitSharingDTO){
        TransExcelDataDTO transExcelDataDTO = new TransExcelDataDTO();
        transExcelDataDTO.setTransAmt(String.valueOf(billProfitSharingDTO.getPrice()));
        transExcelDataDTO.setFeeAmt(String.valueOf(billProfitSharingDTO.getSplitFeeAmt()));
        transExcelDataDTO.setTransStat(BillProfitEnum.getDesc(billProfitSharingDTO.getStatus()));
        transExcelDataDTO.setCreateTime(TimeUtils.changeDate2String(billProfitSharingDTO.getSuccessTime(), TimeUtils.FORMAT));
        transExcelDataDTO.setTransactionId(billProfitSharingDTO.getTransactionId());
        transExcelDataDTO.setWxOrderId(billProfitSharingDTO.getBankOrderId());
        transExcelDataDTO.setOrderNo(billProfitSharingDTO.getRecordNo());
        transExcelDataDTO.setTenantName(billProfitSharingDTO.getTenantName());
        transExcelDataDTO.setPaymentNo(billProfitSharingDTO.getPaymentNo());
        return transExcelDataDTO;
    }

//    private void generateProfitExportFile(BillProfitSharingQueryDTO billProfitSharingQueryDTO, Long fileDownloadRecordId) {
//        List<TransExcelDataDTO> list = new ArrayList<>();
//
//        if (ObjectUtil.isNotNull(billProfitSharingQueryDTO.getEndTime())) {
//            // 由于between查询有边界问题，所以这里截止时间自动+1天
//            Date endTime = TimeUtils.getBeforeTime(TimeUtils.localDateTimeConvertDate(billProfitSharingQueryDTO.getEndTime()), -1);
//            billProfitSharingQueryDTO.setEndTime(TimeUtils.dateConvertLocalDateTime(endTime));
//        }
//        List<BillProfitSharingDTO> billProfitSharingDTOList = billProfitSharingMapper.listAll(billProfitSharingQueryDTO);
//        for (BillProfitSharingDTO billProfitSharingDTO : billProfitSharingDTOList) {
//            // 分账手续费
//            BillProfitSharingSnapshot billProfitSharingSnapshot = billProfitSharingSnapshotMapper.selectByOrderIdAndReceiverTenantId(billProfitSharingDTO.getOrderId(), billProfitSharingDTO.getReceiverTenantId());
//            if (ObjectUtil.isNotNull(billProfitSharingSnapshot)) {
//                // 前端手续费显示为正数
//                billProfitSharingDTO.setSplitFeeAmt(billProfitSharingSnapshot.getProfitSharingPrice().abs());
//            }
//            Long orderId = billProfitSharingDTO.getOrderId();
//            OrderVO orderVO = orderBusinessService.queryOrderVOByOrderId(orderId);
//            // 如果分帐单对应订单不为空
//            if (ObjectUtil.isNotNull(orderVO)) {
//                billProfitSharingDTO.setRecordNo(orderVO.getOrderNo());
//            }
//            TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByHuiFuId(billProfitSharingDTO.getAccount());
//            // 对应租户认证信息不为null
//            if (ObjectUtil.isNotNull(tenantAuthConnection)) {
//                TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantAuthConnection.getTenantId());
////                Tenant tenant = tenantMapper.selectByPrimaryKey(tenantAuthConnection.getTenantId());
//                // 如果对应租户不为空
//                if (ObjectUtil.isNotNull(tenant)) {
//                    billProfitSharingDTO.setTenantName(tenant.getTenantName());
//                }
//            } else {
//                Long tenantReceiverId = billProfitSharingDTO.getReceiverTenantId();
//                if (ObjectUtil.isNotNull(tenantReceiverId)) {
//                    TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantAuthConnection.getTenantId());
////                    Tenant tenant = tenantMapper.selectByPrimaryKey(tenantReceiverId);
//                    billProfitSharingDTO.setTenantName(tenant.getTenantName());
//                } else {
//                    billProfitSharingDTO.setTenantName(StringConstants.EMPTY);
//                }
//            }
//
//        }
//        // 查询条件含有订单号，筛选出所有含目标订单号的分帐单
//        if (ObjectUtil.isNotNull(billProfitSharingQueryDTO.getRecordNo())) {
//            List<BillProfitSharingDTO> billProfitSharingDTOS = billProfitSharingDTOList.stream().filter(billProfitSharingDTO ->
//                    billProfitSharingDTO.getRecordNo().contains(billProfitSharingQueryDTO.getRecordNo())
//            ).collect(Collectors.toList());
//            billProfitSharingDTOList = billProfitSharingDTOS;
//        }
//        // 查询条件含分账方接收方主体名称，筛选出所有含该分账接收方的分帐单
//        if (ObjectUtil.isNotNull(billProfitSharingQueryDTO.getTenantName())) {
//            List<BillProfitSharingDTO> billProfitSharingDTOS = billProfitSharingDTOList.stream().filter(billProfitSharingDTO ->
//                    billProfitSharingDTO.getTenantName().contains(billProfitSharingQueryDTO.getTenantName())
//            ).collect(Collectors.toList());
//            billProfitSharingDTOList = billProfitSharingDTOS;
//        }
//        for (BillProfitSharingDTO billProfitSharingDTO : billProfitSharingDTOList) {
//            TransExcelDataDTO transExcelDataDTO = new TransExcelDataDTO();
//            transExcelDataDTO.setTransAmt(String.valueOf(billProfitSharingDTO.getPrice()));
//            transExcelDataDTO.setFeeAmt(String.valueOf(billProfitSharingDTO.getSplitFeeAmt()));
//            transExcelDataDTO.setTransStat(BillProfitEnum.getDesc(billProfitSharingDTO.getStatus()));
//            transExcelDataDTO.setCreateTime(TimeUtils.changeDate2String(billProfitSharingDTO.getSuccessTime(), TimeUtils.FORMAT));
//            transExcelDataDTO.setTransactionId(billProfitSharingDTO.getTransactionId());
//            transExcelDataDTO.setWxOrderId(billProfitSharingDTO.getBankOrderId());
//            transExcelDataDTO.setOrderNo(billProfitSharingDTO.getRecordNo());
//            transExcelDataDTO.setTenantName(billProfitSharingDTO.getTenantName());
//            list.add(transExcelDataDTO);
//        }
//        // 写入excel
//        String filePath = commonService.exportExcel(list, ExcelTypeEnum.PROFIT_EXPORT.getName());
//        // 上传七牛云
//        String qiNiuPath = null;
//        try {
//            qiNiuPath = QiNiuUtils.uploadFile(filePath, "分账明细导出信息" + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss") + ".xlsx");
//        } catch (Exception e) {
//            throw new DefaultServiceException("分账导出信息信息上传七牛云失败", e);
//        }
//
//        // 删除临时文件
//        commonService.deleteFile(filePath);
//
//        // 更新文件下载记录
//        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
//        fileDownloadRecord.setId(fileDownloadRecordId);
//        fileDownloadRecord.setUrl(qiNiuPath);
//        if (com.cosfo.manage.common.util.StringUtils.isBlank(qiNiuPath)) {
//            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
//        } else {
//            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FINISHED.getStatus());
//        }
//        fileDownloadRecordService.updateSelectiveByPrimaryKey(fileDownloadRecord);
//
//    }

    /**
     * 生成门店周期账单
     *
     * @param storeId
     * @param orderDTOList
     * @param orderAfterSaleDTOList
     * @param financialBillRule
     */
    private Boolean storeBillCreate(Long storeId, List<OrderDTO> orderDTOList, List<OrderAfterSaleResp> orderAfterSaleDTOList, FinancialBillRule financialBillRule) {
        Boolean executeResult = transactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus status) {
                Boolean result = true;
                try {
                    // 计算概要
                    FinancialStoreBillVO financialStoreBillVO = new FinancialStoreBillVO();
                    financialStoreBillVO.setStoreId(storeId);
                    // 查询门店信息
                    MerchantStoreDTO merchantStoreDTO = merchantStoreService.queryStore(storeId, financialBillRule.getTenantId());
                    financialStoreBillVO.setStoreName(merchantStoreDTO.getStoreName());
                    List<BillOrderVO> billOrderVOS = new ArrayList<>();
                    // 剔除已经生成过的订单
                    List<Long> orderIdList = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(orderDTOList)) {
                        orderIdList = orderDTOList.stream().map(OrderDTO::getId).collect(Collectors.toList());
                        List<Long> createdOrderIds = financialBillItemMapper.queryCreatedBillItemInfo(financialBillRule.getTenantId(), BusinessTypeEnum.ORDER.getCode(), orderIdList);
                        orderIdList.removeAll(createdOrderIds);
                    }

                    // 剔除已经生产过的售后单
                    List<BillOrderAfterSaleVO> billOrderAfterSaleVOS = new ArrayList<>();
                    List<Long> orderAfterSaleIdList = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(orderAfterSaleDTOList)) {
                        orderAfterSaleIdList = orderAfterSaleDTOList.stream().map(OrderAfterSaleResp::getId).collect(Collectors.toList());
                        List<Long> createdOrderAfterSaleIds = financialBillItemMapper.queryCreatedBillItemInfo(financialBillRule.getTenantId(), BusinessTypeEnum.ORDER_AFTER_SALE.getCode(), orderAfterSaleIdList);
                        orderAfterSaleIdList.removeAll(createdOrderAfterSaleIds);
                    }


                    if (!CollectionUtils.isEmpty(orderIdList) || !CollectionUtils.isEmpty(orderAfterSaleIdList)) {
                        BigDecimal totalDeliveryFee = BigDecimal.ZERO, receivablePrice = BigDecimal.ZERO, receivableOrderPrice = BigDecimal.ZERO, receivableOrderAfterSalePrice = BigDecimal.ZERO;
                        Set<Long> orderIdSet = new HashSet<>();
                        Set<Long> skuIdSet = new HashSet<>();
                        Integer skuAmount = 0;
                        if (!CollectionUtils.isEmpty(orderIdList)) {
                            billOrderVOS = orderBusinessService.queryBillOrderInfo(orderIdList, financialBillRule.getTenantId());
                            List<OrderDTO> orderDTOs = orderBusinessService.batchQuery(orderIdList, financialBillRule.getTenantId());
                            for (OrderDTO orderDTO : orderDTOs) {
                                totalDeliveryFee = NumberUtil.add(orderDTO.getDeliveryFee(), totalDeliveryFee);
                                receivableOrderPrice = NumberUtil.add(receivableOrderPrice, orderDTO.getTotalPrice());
                            }

                            orderIdSet.addAll(billOrderVOS.stream().map(BillOrderVO::getOrderId).collect(Collectors.toSet()));
                            skuIdSet.addAll(billOrderVOS.stream().map(BillOrderVO::getSkuId).collect(Collectors.toSet()));
                            skuAmount = billOrderVOS.stream().mapToInt(BillOrderVO::getAmount).sum();
                        }

                        if (!CollectionUtils.isEmpty(orderAfterSaleIdList)) {
                            // 售后单数据
                            billOrderAfterSaleVOS = orderAfterSaleService.batchQueryByOrderAfterSaleIds(orderAfterSaleIdList, financialBillRule.getTenantId());
                            for (BillOrderAfterSaleVO billOrderAfterSaleVO : billOrderAfterSaleVOS) {
                                receivableOrderAfterSalePrice = NumberUtil.add(receivableOrderAfterSalePrice, billOrderAfterSaleVO.getOrderAfterSalePrice());
                            }

                            orderIdSet.addAll(billOrderAfterSaleVOS.stream().map(BillOrderAfterSaleVO::getOrderId).collect(Collectors.toSet()));
                            skuIdSet.addAll(billOrderAfterSaleVOS.stream().map(BillOrderAfterSaleVO::getSkuId).collect(Collectors.toSet()));
                            Integer skuAfterSaleAmount = billOrderAfterSaleVOS.stream().mapToInt(BillOrderAfterSaleVO::getOrderAfterSaleAmount).sum();
                            skuAmount += skuAfterSaleAmount;
                        }

                        financialStoreBillVO.setBillStartTime(financialBillRule.getStartTime());
                        financialStoreBillVO.setBillEndTime(financialBillRule.getEndTime());
                        financialStoreBillVO.setSkuNum(skuIdSet.size());
                        financialStoreBillVO.setOrderNum(orderIdSet.size());
                        financialStoreBillVO.setSkuAmount(skuAmount);
                        financialStoreBillVO.setOrderDeliveryFeeTotalPrice(totalDeliveryFee);
                        receivablePrice = NumberUtil.sub(receivableOrderPrice, receivableOrderAfterSalePrice);
                        financialStoreBillVO.setReceivablePrice(receivablePrice);
                        financialStoreBillVO.setReceivableOrderPrice(receivableOrderPrice);
                        financialStoreBillVO.setReceivableOrderAfterSalePrice(receivableOrderAfterSalePrice);

                        // 生成文件
                        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.STORE_BILL.getName());
                        String filePath = "应收账单导出信息" + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss_") + UUID.randomUUID().toString() + ".xlsx";
                        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
                        WriteSheet writeSheet0 = EasyExcel.writerSheet(0).build();
                        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                        excelWriter.fill(Arrays.asList(financialStoreBillVO), fillConfig, writeSheet0);
                        WriteSheet writeSheet1 = EasyExcel.writerSheet(1).build();
                        FillConfig fillConfig1 = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
                        excelWriter.fill(billOrderVOS, fillConfig1, writeSheet1);
                        WriteSheet writeSheet2 = EasyExcel.writerSheet(2).build();
                        excelWriter.fill(billOrderAfterSaleVOS, fillConfig1, writeSheet2);
                        excelWriter.finish();
                        // 上传文件
                        File file = new File(filePath.trim());
                        uploadExcelFile(file, filePath);
                        // 记录数据
                        financialStoreBillVO.setFilePath("bill/" + file.getName());
                        // 记录
                        saveBill(financialStoreBillVO, orderIdList, orderAfterSaleIdList, financialBillRule);
                    }
                } catch (Exception e) {
                    log.error("{}门店{}到{}账期生成失败", storeId, financialBillRule.getCreateTime(), financialBillRule.getEndTime(), e);
                    status.setRollbackOnly();
                    result = false;
                }

                return result;
            }
        });

        return executeResult;
    }

    /**
     * 保存账单记录
     *
     * @param financialStoreBillVO
     * @param orderIds
     * @param orderAfterSaleIds
     * @param financialBillRule
     */
    public void saveBill(FinancialStoreBillVO financialStoreBillVO, List<Long> orderIds, List<Long> orderAfterSaleIds, FinancialBillRule financialBillRule) {
        FinancialBill financialBill = new FinancialBill();
        String billNo = Global.createOrderNo(Global.BILL);
        financialBill.setBillNo(billNo);
        financialBill.setTenantId(financialBillRule.getTenantId());
        financialBill.setPayeeId(financialBillRule.getTenantId());
        financialBill.setPayerId(financialStoreBillVO.getStoreId());
        financialBill.setBillType(financialBillRule.getType());
        financialBill.setReceivablePrice(financialStoreBillVO.getReceivablePrice());
        financialBill.setOrderReceivablePrice(financialStoreBillVO.getReceivableOrderPrice());
        financialBill.setOrderAfterSaleTotalPrice(financialStoreBillVO.getReceivableOrderAfterSalePrice());
        financialBill.setStartTime(financialBillRule.getStartTime());
        financialBill.setEndTime(financialBillRule.getEndTime());
        financialBill.setType(BillTypeEnum.RECEIVABLE_BILL.getCode());
        financialBill.setStatus(BillStatusEnum.UNAUDITED.getCode());
        financialBill.setFilePath(financialStoreBillVO.getFilePath());
        financialBillMapper.insertSelective(financialBill);
        // 批量插入账单项
        List<FinancialBillItem> financialBillItems = new ArrayList<>();
        orderIds.stream().forEach(orderId -> {
            FinancialBillItem financialBillItem = new FinancialBillItem();
            financialBillItem.setBillId(financialBill.getId());
            financialBillItem.setTenantId(financialBill.getTenantId());
            financialBillItem.setBusinessId(orderId);
            financialBillItem.setBusinessType(BusinessTypeEnum.ORDER.getCode());
            financialBillItems.add(financialBillItem);
        });

        orderAfterSaleIds.stream().forEach(orderAfterSaleId -> {
            FinancialBillItem financialBillItem = new FinancialBillItem();
            financialBillItem.setBillId(financialBill.getId());
            financialBillItem.setTenantId(financialBill.getTenantId());
            financialBillItem.setBusinessId(orderAfterSaleId);
            financialBillItem.setBusinessType(BusinessTypeEnum.ORDER_AFTER_SALE.getCode());
            financialBillItems.add(financialBillItem);
        });

        if (!CollectionUtils.isEmpty(financialBillItems)) {
            financialBillItemMapper.batchInsert(financialBillItems);
        }
    }

    /**
     * 上传文件
     *
     * @param file
     * @param filePath
     */
    private void uploadExcelFile(File file, String filePath) {
        try {
            MultipartFile multipartFile = FileUtil.fileToMultipartFile(file);
            String s = QiNiuUtils.uploadFile(multipartFile, "bill/" + file.getName());
            FileUtil.deleteFile(filePath);
        } catch (IOException e) {
            log.error("上传excel文件失败");
            throw new DefaultServiceException("导出报表失败");
        }
    }

    @Override
    public void dealHistoryBill() {
        // 查询excel命名重复的账单
        List<FinancialBill> financialBills = financialBillMapper.queryErrorHistoryBill();
        financialBills.forEach(financialBill -> {
            Long storeId = financialBill.getPayerId();
            Long tenantId = financialBill.getTenantId();
            // 查询订单
            List<FinancialBillItem> orderFinancialBillItems = financialBillItemMapper.selectByBillId(financialBill.getId(), financialBill.getTenantId(), BusinessTypeEnum.ORDER.getCode());
            List<Long> orderIdList = orderFinancialBillItems.stream().map(FinancialBillItem::getBusinessId).collect(Collectors.toList());
            // 查询售后单
            List<FinancialBillItem> afterSaleFinancialBillItems = financialBillItemMapper.selectByBillId(financialBill.getId(), financialBill.getTenantId(), BusinessTypeEnum.ORDER_AFTER_SALE.getCode());
            List<Long> orderAfterSaleIdList = afterSaleFinancialBillItems.stream().map(FinancialBillItem::getBusinessId).collect(Collectors.toList());

            // 计算概要
            FinancialStoreBillVO financialStoreBillVO = new FinancialStoreBillVO();
            financialStoreBillVO.setStoreId(storeId);
            // 查询门店信息
            MerchantStoreDTO merchantStoreDTO = merchantStoreService.queryStore(storeId, tenantId);
            financialStoreBillVO.setStoreName(merchantStoreDTO.getStoreName());
            List<BillOrderVO> billOrderVOS = new ArrayList<>();
            List<BillOrderAfterSaleVO> billOrderAfterSaleVOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(orderIdList) || !CollectionUtils.isEmpty(orderAfterSaleIdList)) {
                BigDecimal totalDeliveryFee = BigDecimal.ZERO, receivablePrice = BigDecimal.ZERO, receivableOrderPrice = BigDecimal.ZERO, receivableOrderAfterSalePrice = BigDecimal.ZERO;
                Set<Long> orderIdSet = new HashSet<>();
                Set<Long> skuIdSet = new HashSet<>();
                Integer skuAmount = 0;
                if (!CollectionUtils.isEmpty(orderIdList)) {
                    billOrderVOS = orderBusinessService.queryBillOrderInfo(orderIdList, tenantId);
                    List<OrderDTO> orderDTOs = orderBusinessService.batchQuery(orderIdList, tenantId);
                    for (OrderDTO orderDTO : orderDTOs) {
                        totalDeliveryFee = NumberUtil.add(orderDTO.getDeliveryFee(), totalDeliveryFee);
                        receivableOrderPrice = NumberUtil.add(receivableOrderPrice, orderDTO.getTotalPrice());
                    }

                    orderIdSet.addAll(billOrderVOS.stream().map(BillOrderVO::getOrderId).collect(Collectors.toSet()));
                    skuIdSet.addAll(billOrderVOS.stream().map(BillOrderVO::getSkuId).collect(Collectors.toSet()));
                    skuAmount = billOrderVOS.stream().mapToInt(BillOrderVO::getAmount).sum();
                }

                if (!CollectionUtils.isEmpty(orderAfterSaleIdList)) {
                    // 售后单数据
                    billOrderAfterSaleVOS = orderAfterSaleService.batchQueryByOrderAfterSaleIds(orderAfterSaleIdList, tenantId);
                    for (BillOrderAfterSaleVO billOrderAfterSaleVO : billOrderAfterSaleVOS) {
                        receivableOrderAfterSalePrice = NumberUtil.add(receivableOrderAfterSalePrice, billOrderAfterSaleVO.getOrderAfterSalePrice());
                    }

                    orderIdSet.addAll(billOrderAfterSaleVOS.stream().map(BillOrderAfterSaleVO::getOrderId).collect(Collectors.toSet()));
                    skuIdSet.addAll(billOrderAfterSaleVOS.stream().map(BillOrderAfterSaleVO::getSkuId).collect(Collectors.toSet()));
                    Integer skuAfterSaleAmount = billOrderAfterSaleVOS.stream().mapToInt(BillOrderAfterSaleVO::getOrderAfterSaleAmount).sum();
                    skuAmount += skuAfterSaleAmount;
                }

                financialStoreBillVO.setBillStartTime(financialBill.getStartTime());
                financialStoreBillVO.setBillEndTime(financialBill.getEndTime());
                financialStoreBillVO.setSkuNum(skuIdSet.size());
                financialStoreBillVO.setOrderNum(orderIdSet.size());
                financialStoreBillVO.setSkuAmount(skuAmount);
                financialStoreBillVO.setOrderDeliveryFeeTotalPrice(totalDeliveryFee);
                receivablePrice = NumberUtil.sub(receivableOrderPrice, receivableOrderAfterSalePrice);
                financialStoreBillVO.setReceivablePrice(receivablePrice);
                financialStoreBillVO.setReceivableOrderPrice(receivableOrderPrice);
                financialStoreBillVO.setReceivableOrderAfterSalePrice(receivableOrderAfterSalePrice);

                // 生成文件
                InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.STORE_BILL.getName());
                String filePath = "应收账单导出信息" + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss_") + UUID.randomUUID().toString() + ".xlsx";
                ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
                WriteSheet writeSheet0 = EasyExcel.writerSheet(0).build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                excelWriter.fill(Arrays.asList(financialStoreBillVO), fillConfig, writeSheet0);
                WriteSheet writeSheet1 = EasyExcel.writerSheet(1).build();
                FillConfig fillConfig1 = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
                excelWriter.fill(billOrderVOS, fillConfig1, writeSheet1);
                WriteSheet writeSheet2 = EasyExcel.writerSheet(2).build();
                excelWriter.fill(billOrderAfterSaleVOS, fillConfig1, writeSheet2);
                excelWriter.finish();
                // 上传文件
                File file = new File(filePath.trim());
                uploadExcelFile(file, filePath);
                // 更新excel名称
                // 记录数据
                financialBillMapper.updateFilePath(financialBill.getId(), "bill/" + file.getName());
            }
        });
    }

}

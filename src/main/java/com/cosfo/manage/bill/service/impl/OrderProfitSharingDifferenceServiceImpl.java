package com.cosfo.manage.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.bill.model.dto.OrderProfitSharingDifferenceExcelDTO;
import com.cosfo.manage.bill.model.po.OrderProfitSharingDifference;
import com.cosfo.manage.bill.repository.OrderProfitSharingDifferenceRepository;
import com.cosfo.manage.bill.service.OrderProfitSharingDifferenceService;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-08-16
 **/
@Service
public class OrderProfitSharingDifferenceServiceImpl implements OrderProfitSharingDifferenceService {

    @Resource
    private OrderProfitSharingDifferenceRepository orderProfitSharingDifferenceRepository;

    /**
     * 查询
     *
     * @param tenantId                            租户id
     * @param startTime                           开始时间
     * @param endTime                             结束时间
     * @param orderProfitSharingDifferenceHandler
     */
    @Override
    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ExcelLargeDataSetExporter<OrderProfitSharingDifference, OrderProfitSharingDifferenceExcelDTO> orderProfitSharingDifferenceHandler) {
        orderProfitSharingDifferenceRepository.queryByConditionWithHandler(tenantId, supplierId, startTime, endTime, orderProfitSharingDifferenceHandler);
    }

    @Override
    public long count(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<OrderProfitSharingDifference> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderProfitSharingDifference::getTenantId, tenantId);
        queryWrapper.between(OrderProfitSharingDifference::getTimeTag, startTime, endTime);
        queryWrapper.eq(Objects.nonNull(supplierId), OrderProfitSharingDifference::getSupplierId, supplierId);
        return orderProfitSharingDifferenceRepository.count(queryWrapper);
    }
}

package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.PrepaymentAccountQueryDTO;
import com.cosfo.manage.bill.model.dto.PrepaymentTrendQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentAccountVO;
import com.cosfo.manage.bill.model.vo.PrepaymentAmountVO;
import com.cosfo.manage.bill.model.vo.PrepaymentTrendVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PrepaymentAccountService {

    /**
     * 获取当前余额构成
     * @param tenantId
     * @return
     */
    PrepaymentAmountVO getTotalAmount(Long tenantId);


    /**
     * 分页查询余额构成
     * @param queryDTO
     * @return
     */
    PageInfo<PrepaymentAccountVO> queryAccountPage(PrepaymentAccountQueryDTO queryDTO);

    /**
     * 下载余额构成
     * @param queryDTO
     * @return
     */
    Boolean downloadAccount(PrepaymentAccountQueryDTO queryDTO);


    /**
     * 查询余额走势
     * @param queryDTO
     * @return
     */
    List<PrepaymentTrendVO> queryAmountTrend(PrepaymentTrendQueryDTO queryDTO);

    /**
     * 定时任务，创建账户余额快照
     */
    void snapshotAccount();
}

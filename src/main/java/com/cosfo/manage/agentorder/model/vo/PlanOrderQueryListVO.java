package com.cosfo.manage.agentorder.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 13:52
 * @Description: 计划单列表出参对象
 */
@Data
public class PlanOrderQueryListVO implements Serializable {
    private static final long serialVersionUID = -9061812591590177917L;


    /**
     * 代下单编号
     */
    private String agentOrderNo;

    /**
     * 计划单ID
     */
    private Long planOrderId;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 店长手机号
     */
    private String storeManagerPhone;

    /**
     * 商品数量（不同商品的个数）
     */
    private Integer itemCount;

    /**
     * 商品件数（不同商品个数*每件商品下单件数）
     */
    private Integer itemTotalAmount;

    /**
     * 商品总价（不含运费）
     */
    private BigDecimal itemTotalPrice;

    /**
     * 计划单创建单
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planOrderCreateTime;

    /**
     * 下单人ID
     */
    private Long agentOperatorAuthId;

    /**
     * 下单人名称
     */
    private String agentOperatorName;

    /**
     * 下单人手机号
     */
    private String agentOperatorPhone;

    /**
     * 是否可提醒
     */
    private boolean enableNotify;

    /**
     * 最后一次提醒时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastNotifyTime;

    /**
     * 计划下单方式 create_plan_order-生成计划单(配货单) create_order-创建订单 create_force_plan_order-计划单强制下单(铺货单)
     */
    private String planType;
}

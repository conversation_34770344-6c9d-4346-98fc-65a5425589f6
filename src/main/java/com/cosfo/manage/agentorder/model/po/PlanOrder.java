package com.cosfo.manage.agentorder.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 计划订单表(PlanOrder)实体类
 *
 * <AUTHOR>
 * @since 2024-02-06 10:50:42
 */
@Data
@TableName("plan_order")
public class PlanOrder implements Serializable {
    private static final long serialVersionUID = -98834618147986329L;
    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 门店Id
     */
    private Long storeId;
    /**
     * 计划单编号
     */
    private String planOrderNo;
    /**
     * 代下单编号
     */
    private String agentOrderNo;
    /**
     * 状态 100-待门店确认; 105-下单中; 200-下单成功; 300-下单失败; 400-已取消
     */
    private Integer status;
    /**
     * 计划下单方式 create_plan_order-生成计划单(配货单) create_order-创建订单 create_force_plan_order-计划单强制下单(铺货单)
     */
    private String planType;
    /**
     * 代下单商品信息快照，包含其他信息
     */
    private String itemInfoSnapshot;
    /**
     * 计划单完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 成功创建订单编号
     */
    private String successCreateOrderNo;
    /**
     * 计划单创建订单失败原因，按照商品id或orderNo展示失败原因
     */
    private String failReason;
    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * 取消备注
     */
    private String cancelRemark;
    /**
     * 取消人
     */
    private Long cancelUserId;
    /**
     * 取消类型 store-门店 tenant-品牌总部 system-系统自动取消
     */
    private String cancelType;
    /**
     * 系统自动取消超时时间 单位：分钟 72*60
     */
    private Integer autoCancelTimeout;
    /**
     * 系统自动取消截止时间
     */
    private LocalDateTime autoCancelTime;
    /**
     * 计划单最后提醒通知时间
     */
    private LocalDateTime planConfirmNotifyTime;
    /**
     * 推荐理由
     */
    private String recommendReason;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 创建人
     */
    private Long creatorUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}


package com.cosfo.manage.agentorder.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cofso.item.client.resp.MarketItemClassificationResp;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.client.planorder.provider.PlanOrderCheckProvider;
import com.cosfo.mall.client.planorder.req.PlanOrderCheckReq;
import com.cosfo.mall.client.planorder.resp.PlanOrderCheckResp;
import com.cosfo.manage.agentorder.convert.AgentOrderConvert;
import com.cosfo.manage.agentorder.dao.AgentOrderDao;
import com.cosfo.manage.agentorder.dao.AgentOrderItemDao;
import com.cosfo.manage.agentorder.model.dto.AddPlanOrderReturnDto;
import com.cosfo.manage.agentorder.model.dto.AgentOrderStoreInfoDto;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderAddInput;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderItemInput;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderStoreInput;
import com.cosfo.manage.agentorder.model.po.AgentOrder;
import com.cosfo.manage.agentorder.model.po.AgentOrderItem;
import com.cosfo.manage.agentorder.model.vo.AgentOrderAgainDetailVO;
import com.cosfo.manage.agentorder.model.vo.AgentOrderCheckVO;
import com.cosfo.manage.agentorder.service.AgentOrderInnerService;
import com.cosfo.manage.agentorder.service.AgentOrderService;
import com.cosfo.manage.agentorder.service.PlanOrderService;
import com.cosfo.manage.common.constant.AgentOrderConstant;
import com.cosfo.manage.common.context.AgentOrderEnum;
import com.cosfo.manage.common.context.MarketDeleteFlagEnum;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.MarketClassificationFacade;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.facade.PriceFacade;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.market.model.dto.MarketItemInfoDTO;
import com.cosfo.manage.market.model.dto.MarketItemInfoQueryFlagDTO;
import com.cosfo.manage.market.model.dto.MarketItemQueryDTO;
import com.cosfo.manage.market.model.vo.MarketItemInfoPageVO;
import com.cosfo.manage.market.model.vo.PriceDetailVO;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.manage.marketing.service.ItemSaleLimitConfigService;
import com.cosfo.manage.merchant.service.MerchantAddressService;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2024/2/6 11:53
 * @Description:
 */
@Service
@Slf4j
public class AgentOrderServiceImpl implements AgentOrderService {
    @Resource
    private UserCenterMerchantStoreFacade storeFacade;
    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    private MarketClassificationFacade marketClassificationFacade;
    @Resource
    private PriceFacade priceFacade;

    @Resource
    private AgentOrderInnerService agentOrderInnerService;
    @Resource
    private PlanOrderService planOrderService;
    @Resource
    private AgentOrderDao agentOrderDao;
    @Resource
    private AgentOrderItemDao agentOrderItemDao;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private ItemSaleLimitConfigService itemSaleLimitConfigService;
    @Resource
    private MarketItemService marketItemService;

    @DubboReference
    private PlanOrderCheckProvider planOrderCheckProvider;


    /**
     * 代下单提交检查 门店*商品 的最大数量限制，默认5000
     */
    @NacosValue(value = "${agentorder.check.totalsize.limit:5000}", autoRefreshed = true)
    private Integer agentOrderCheckTotalsizeLimit;


    /**
     * 代门店下单
     * 新增代下单+计划单
     *
     * @param agentOrderAddInput
     * @param tenantId
     * @return 代下单编号
     */
    @Override
    public String addPlanOrder(AgentOrderAddInput agentOrderAddInput, Long tenantId) {
        // 校验参数
        checkPlanOrderStoreValid(agentOrderAddInput, tenantId);
        checkPlanOrderItemValid(agentOrderAddInput, tenantId);
        // 新增代下单+计划单
        AddPlanOrderReturnDto addPlanOrderReturnDto = agentOrderInnerService.saveAgentPlanOrder(agentOrderAddInput);
        // 异步发送短信
        asyncSendNotify(addPlanOrderReturnDto.getPlanOrderIds());
        return addPlanOrderReturnDto.getAgentOrderNo();
    }

    @Override
    public List<AgentOrderCheckVO> checkPlanOrder(AgentOrderAddInput input, Long tenantId) {

        int totalSize = input.getStoreInputs().size() * input.getItemInputs().size();
        if(totalSize > agentOrderCheckTotalsizeLimit){
            throw new BizException(String.format("商品*门店的数量超过%s，不能提交。请减少数量后重新提交", agentOrderCheckTotalsizeLimit));
        }

        Set<Long> storeIds = input.getStoreInputs().stream().map(AgentOrderStoreInput::getStoreId).collect(Collectors.toSet());

        List<PlanOrderCheckReq.AgentOrderItem> agentOrderItemList = input.getItemInputs().stream().map(e -> {
            PlanOrderCheckReq.AgentOrderItem agentOrderItem = new PlanOrderCheckReq.AgentOrderItem();
            agentOrderItem.setItemId(e.getItemId());
            agentOrderItem.setItemAmount(e.getItemAmount());
            return agentOrderItem;
        }).collect(Collectors.toList());

        // 查询门店
        List<MerchantStoreResultResp> merchantStoreList = storeFacade.getMerchantStoreList(new ArrayList<>(storeIds));
        Map<Long, MerchantStoreResultResp> storeMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, Function.identity(), (k1, k2) -> k2));

        // 查询门店地址
        List<MerchantAddressResultResp> merchantAddressResultRespList = merchantAddressService.selectAddressListByStoreIds(tenantId, new ArrayList<>(storeIds));
        Map<Long, MerchantAddressResultResp> addressResultRespMap = merchantAddressResultRespList.stream().collect(Collectors.toMap(MerchantAddressResultResp::getStoreId, Function.identity(), (k1, k2) -> k2));

        List<AgentOrderCheckVO> resultList = new ArrayList<>();
        for (AgentOrderStoreInput storeInput : input.getStoreInputs()) {
            MerchantStoreResultResp merchantStore = storeMap.get(storeInput.getStoreId());
            MerchantAddressResultResp merchantAddresses = addressResultRespMap.get(storeInput.getStoreId());

            // 先校验门店
            try {
                checkAgentOrderStore(tenantId, storeInput, merchantStore, merchantAddresses);
            } catch (Exception e) {
                AgentOrderCheckVO agentOrderCheckVO = new AgentOrderCheckVO();
                agentOrderCheckVO.setStoreId(storeInput.getStoreId());
                agentOrderCheckVO.setStoreName(merchantStore.getStoreName());
                agentOrderCheckVO.setStoreCheckFailMsg(e.getMessage());
                resultList.add(agentOrderCheckVO);
                continue;
            }

            // 门店校验通过，校验商品
            try {
                PlanOrderCheckReq req = new PlanOrderCheckReq();
                req.setStoreIdList(Lists.newArrayList(storeInput.getStoreId()));
                req.setTenantId(tenantId);
                req.setAgentOrderItemList(agentOrderItemList);
                List<PlanOrderCheckResp> planOrderCheckResps = RpcResponseUtil.handler(planOrderCheckProvider.checkPlanOrder(req));
                if(!CollectionUtils.isEmpty(planOrderCheckResps) && !CollectionUtils.isEmpty(planOrderCheckResps.get(0).getItemCheckResultList())){
                    List<AgentOrderCheckVO.ItemCheckResult> itemCheckResultList = planOrderCheckResps.get(0).getItemCheckResultList().stream().map(e -> {
                        AgentOrderCheckVO.ItemCheckResult itemCheckResult = new AgentOrderCheckVO.ItemCheckResult();
                        itemCheckResult.setItemId(e.getItemId());
                        itemCheckResult.setItemQuantity(e.getItemQuantity());
                        itemCheckResult.setCheckFlag(e.isCheckFlag());
                        itemCheckResult.setFailMsg(e.getFailMsg());
                        return itemCheckResult;
                    }).collect(Collectors.toList());
                    AgentOrderCheckVO agentOrderCheckVO = new AgentOrderCheckVO();
                    agentOrderCheckVO.setStoreId(storeInput.getStoreId());
                    agentOrderCheckVO.setStoreName(merchantStore.getStoreName());
                    agentOrderCheckVO.setItemCheckResultList(itemCheckResultList);
                    resultList.add(agentOrderCheckVO);
                }
            } catch (Exception e) {
                log.error("代下单校验商品失败，storeId={}", storeInput.getStoreId(), e);
                AgentOrderCheckVO agentOrderCheckVO = new AgentOrderCheckVO();
                agentOrderCheckVO.setStoreId(storeInput.getStoreId());
                agentOrderCheckVO.setStoreName(merchantStore.getStoreName());
                agentOrderCheckVO.setStoreCheckFailMsg(e.getMessage());
                resultList.add(agentOrderCheckVO);
                continue;
            }
        }

        // 填充带itemId的marketId
        fillMarketId4AgentOrderCheckVO(resultList, tenantId);

        return resultList;
    }


    private void fillMarketId4AgentOrderCheckVO(List<AgentOrderCheckVO> agentOrderCheckVOList, Long tenantId){
        if(CollectionUtils.isEmpty(agentOrderCheckVOList)){
            return;
        }

        Set<Long> itemIds = agentOrderCheckVOList.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getItemCheckResultList()))
                .flatMap(e -> e.getItemCheckResultList().stream())
                .map(AgentOrderCheckVO.ItemCheckResult::getItemId) // 获取 id
                .collect(Collectors.toSet());

        if(CollectionUtils.isEmpty(itemIds)){
            return;
        }

        MarketItemQueryDTO queryDTO = new MarketItemQueryDTO ();
        queryDTO.setTenantId (tenantId);
        queryDTO.setCombineFlag(Boolean.FALSE);
        queryDTO.setItemIds (itemIds);
        queryDTO.setPageNum (1);
        queryDTO.setPageSize(1000);
        MarketItemInfoQueryFlagDTO flagDTO = new MarketItemInfoQueryFlagDTO();
        PageInfo<MarketItemInfoPageVO> info = marketItemService.queryMarketItemList (queryDTO, flagDTO);
        if(info == null || CollectionUtils.isEmpty(info.getList())){
            return;
        }

        Map<Long, MarketItemInfoPageVO> itemIdMap = info.getList().stream().collect(Collectors.toMap(MarketItemInfoPageVO::getId, Function.identity(), (k1, k2) -> k2));

        for (AgentOrderCheckVO agentOrderCheckVO : agentOrderCheckVOList) {
            if(CollectionUtils.isEmpty(agentOrderCheckVO.getItemCheckResultList())){
                continue;
            }

            for (AgentOrderCheckVO.ItemCheckResult itemCheckResult : agentOrderCheckVO.getItemCheckResultList()) {
                itemCheckResult.setMarketId(itemIdMap.getOrDefault(itemCheckResult.getItemId(), new MarketItemInfoPageVO()).getMarketId());
            }
        }

    }
    /**
     * 快速下单 根据代下单编号查询详情
     *
     * @param agentOrderNo 代下单编号
     * @return
     */
    @Override
    public AgentOrderAgainDetailVO agentOrderAgain(String agentOrderNo) {
        // 校验数据正确性
        checkValidAgentOrderAgain(agentOrderNo);
        Long tenantId = UserLoginContextUtils.getTenantId();

        AgentOrder agentOrder = agentOrderDao.getByAgentOrderNo(agentOrderNo);
        List<AgentOrderItem> agentOrderItems = agentOrderItemDao.listByAgentOrderNo(agentOrderNo);

        List<Long> itemIds = agentOrderItems.stream().map(AgentOrderItem::getItemId).collect(Collectors.toList());
        // 查询商品详情
        Map<Long, MarketItemInfoDTO> itemMap = marketItemFacade.queryMarketItemMap(MarketItemQueryDTO.builder()
            .tenantId(tenantId)
            .itemIds(itemIds)
            .deleteFlag(MarketDeleteFlagEnum.NORMAL.getFlag())
            .build());
        // 查询分类信息
        Set<Long> categoryIds = itemMap.values().stream()
            .map(MarketItemInfoDTO::getCategoryId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        Map<Long, ProductCategoryDTO> categoryDTOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            categoryDTOMap = categoryServiceFacade.selectWholeCategoryNewBatch(categoryIds);
        }

        // 查询类目信息
        Set<Long> marketIds = itemMap.values().stream().map(MarketItemInfoDTO::getMarketId).collect(Collectors.toSet());
        Map<Long, MarketItemClassificationResp> classificationRespMap = marketClassificationFacade.queryClassificationByMarketIds(tenantId, new ArrayList<>(marketIds));

        // 查询限售信息
        Map<Long, ItemSaleLimitConfigDTO>  itemSaleLimitConfigMap = itemSaleLimitConfigService.queryItemSaleLimitConfigMap(tenantId, itemIds);


        // 查询门店详情
        List<AgentOrderStoreInfoDto> agentOrderStoreList = JSONArray.parseArray(agentOrder.getStoreInfo(), AgentOrderStoreInfoDto.class);
        List<Long> storeIds = agentOrderStoreList.stream().map(AgentOrderStoreInfoDto::getStoreId).collect(Collectors.toList());
        List<MerchantStoreResultResp> merchantStoreList = storeFacade.getMerchantStoreList(storeIds);

        // 拼接参数
        AgentOrderAgainDetailVO againDetailVO = new AgentOrderAgainDetailVO();
        againDetailVO.setRecommendReason(agentOrder.getRecommendReason());
        againDetailVO.setItemVOList(AgentOrderConvert.convert2AgentItemVos(agentOrderItems, itemMap, classificationRespMap, categoryDTOMap, itemSaleLimitConfigMap));
        againDetailVO.setStoreVOList(AgentOrderConvert.convert2AgentStoreVos(agentOrderStoreList, merchantStoreList));
        return againDetailVO;
    }


    /**
     * 校验代下单号是否正常
     *
     * @param agentOrderNo
     */
    private void checkValidAgentOrderAgain(String agentOrderNo) {
        // 查询代下单信息
        AgentOrder agentOrder = agentOrderDao.getByAgentOrderNo(agentOrderNo);
        if (Objects.isNull(agentOrder)) {
            throw new BizException("未找到代下单编号信息！");
        }
        Long tenantId = UserLoginContextUtils.getTenantId();
        if (Objects.isNull(tenantId)) {
            throw new BizException("请重新登录!");
        }
        if (!tenantId.equals(agentOrder.getTenantId())) {
            log.error("代下单不属于当前租户！当前登录租户:{} 代下单号：{} 代下单租户:{}", tenantId, agentOrderNo, agentOrder.getTenantId());
            throw new BizException("代下单不属于当前租户！");
        }
        // 查询代下单商品信息
        List<AgentOrderItem> agentOrderItems = agentOrderItemDao.listByAgentOrderNo(agentOrderNo);
        if (CollectionUtils.isEmpty(agentOrderItems)) {
            log.error("代下单编号异常！未找到商品记录！,代下单编号:{}", agentOrder);
            throw new BizException("代下单编号异常！未找到商品记录！");
        }
        if (agentOrderItems.stream().anyMatch(i -> !tenantId.equals(i.getTenantId()))) {
            log.error("代下单不属于当前租户！当前登录租户:{} 代下单号：{}", tenantId, agentOrderNo);
            throw new BizException("代下单商品信息不属于当前租户！");
        }
    }

    @Override
    public boolean checkAgentOrderAgain(String agentOrderNo) {
        checkValidAgentOrderAgain(agentOrderNo);
        return true;
    }

    /**
     * 新增代下单 - 校验门店
     *
     * @param input
     */
    private void checkPlanOrderStoreValid(AgentOrderAddInput input, Long tenantId) {
        Set<Long> storeIds = input.getStoreInputs().stream().map(AgentOrderStoreInput::getStoreId).collect(Collectors.toSet());
        if (storeIds.size() != input.getStoreInputs().size()) {
            throw new BizException("门店不可重复，请重新选择!");
        }
        List<MerchantStoreResultResp> merchantStoreList = storeFacade.getMerchantStoreList(new ArrayList<>(storeIds));
        Map<Long, MerchantStoreResultResp> storeMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, Function.identity(), (k1, k2) -> k2));

        // 查询门店地址
        List<MerchantAddressResultResp> merchantAddressResultRespList = merchantAddressService.selectAddressListByStoreIds(tenantId, new ArrayList<>(storeIds));
        Map<Long, MerchantAddressResultResp> addressResultRespMap = merchantAddressResultRespList.stream().collect(Collectors.toMap(MerchantAddressResultResp::getStoreId, Function.identity(), (k1, k2) -> k2));


        List<String> planTypeNames = AgentOrderEnum.PlanTypeEnum.getAllName();
        for (AgentOrderStoreInput storeInput : input.getStoreInputs()) {
            MerchantStoreResultResp merchantStore = storeMap.get(storeInput.getStoreId());
            MerchantAddressResultResp merchantAddresses = addressResultRespMap.get(storeInput.getStoreId());
            if (!planTypeNames.contains(storeInput.getPlanType())) {
                throw new BizException("门店ID:" + storeInput.getStoreId() + "请选择正确的下单方式！");
            }

            checkAgentOrderStore(tenantId, storeInput, merchantStore, merchantAddresses);

        }
    }

    private void checkAgentOrderStore(Long tenantId, AgentOrderStoreInput storeInput, MerchantStoreResultResp merchantStore, MerchantAddressResultResp merchantAddresses){

        if (Objects.isNull(merchantStore)) {
            throw new BizException("门店ID:" + storeInput.getStoreId() + "不存在！");
        }
        if (!tenantId.equals(merchantStore.getTenantId())) {
            throw new BizException("门店ID:" + storeInput.getStoreId() + "非本租户门店！");
        }
        if (!MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode().equals(merchantStore.getStatus())) {
            throw new BizException("门店ID:" + storeInput.getStoreId() + "非审核成功状态！");
        }

        // 未开账期的门店，不可选择创建订单
        if (AgentOrderEnum.PlanTypeEnum.CREATE_ORDER.name().equals(storeInput.getPlanType()) && !AgentOrderConstant.STORE_BILL_OPEN.equals(merchantStore.getBillSwitch())) {
            throw new BizException("门店ID:" + storeInput.getStoreId() + "未开启账期权限，只能选择创建计划单！");
        }

        if(merchantAddresses == null){
            throw new BizException("门店ID:" + storeInput.getStoreId() + "门店地址不存在！");
        }

        if(StringUtils.isBlank(merchantAddresses.getPoiNote())){
            throw new BizException("门店ID:" + storeInput.getStoreId() + "门店地址poi为空，不能下单");
        }
    }

    /**
     * 新增代下单 - 校验商品
     *
     * @param input
     */
    private void checkPlanOrderItemValid(AgentOrderAddInput input, Long tenantId) {
        Set<Long> itemIds = input.getItemInputs().stream().map(AgentOrderItemInput::getItemId).collect(Collectors.toSet());
        if (itemIds.size() != input.getItemInputs().size()) {
            throw new BizException("有重复商品，请重新选择！");
        }
        Map<Long, MarketItemInfoDTO> itemMap = marketItemFacade.queryMarketItemMap(MarketItemQueryDTO.builder()
            .tenantId(tenantId)
            .itemIds(itemIds)
            .deleteFlag(MarketDeleteFlagEnum.NORMAL.getFlag())
            .build());
        for (AgentOrderItemInput itemInput : input.getItemInputs()) {
            MarketItemInfoDTO itemInfo = itemMap.get(itemInput.getItemId());
            if (Objects.isNull(itemInfo)) {
                throw new BizException("商品编码：" + itemInput.getItemId() + "不存在！");
            }
            if (OnSaleTypeEnum.SOLD_OUT.getCode().equals(itemInfo.getOnSale())) {
                throw new BizException("商品编码：" + itemInput.getItemId() + "已下架！");
            }
        }
        for (AgentOrderStoreInput storeInput : input.getStoreInputs()) {
            Map<Long, PriceDetailVO> priceDetailMap = priceFacade.listItemPriceDetailByItemIds(tenantId, storeInput.getStoreId(), new ArrayList<>(itemIds));
            for (AgentOrderItemInput itemInput : input.getItemInputs()) {
                PriceDetailVO itemPrice = priceDetailMap.get(itemInput.getItemId());
                if (Objects.isNull(itemPrice)) {
                    throw new BizException("门店ID：" + storeInput.getStoreId() + ",商品编码：" + itemInput.getItemId() + "未设置价格！");
                }
            }
        }
    }


    /**
     * 计划单发送短信提醒
     *
     * @param planOrderIds
     */
    private void asyncSendNotify(List<Long> planOrderIds) {
        LoginContextInfoDTO loginInfo = UserLoginContextUtils.getMerchantInfoDTO();
        try {
            ExecutorFactory.ASYNC_EXECUTOR.execute(() -> {
                if (CollectionUtils.isEmpty(planOrderIds)) {
                    log.info("创建完计划单后，未收到有效ID!有可能全部是自动创建订单");
                } else {
                    for (Long planOrderId : planOrderIds) {
                        log.info("正在为计划单ID：{}发送短信", planOrderId);
                        try {
                            planOrderService.notifyStore(planOrderId, loginInfo);
                        } catch (BizException e) {
                            log.warn("计划单ID：{}发送短信失败！", planOrderId, e);
                        } catch (Exception e){
                            log.error("计划单ID：{}发送短信失败！", planOrderId, e);
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("创建计划单发送我相信提醒失败！", e);
        }

    }
}

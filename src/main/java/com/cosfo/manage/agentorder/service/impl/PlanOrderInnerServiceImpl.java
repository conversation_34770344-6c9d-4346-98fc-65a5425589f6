package com.cosfo.manage.agentorder.service.impl;

/**
 * @author: monna.chen
 * @Date: 2024/2/20 16:10
 * @Description:
 */
//@Service
//@Slf4j
//public class PlanOrderInnerServiceImpl implements PlanOrderInnerService {
//    @Resource
//    private PlanOrderDao planOrderDao;
//
//
//    /**
//     * 批量更新计划单提醒时间
//     * @param tenantId
//     * @param planOrderIds
//     * @return
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean batchUpdateNotifyTime(Long tenantId, List<Long> planOrderIds) {
//        return planOrderDao.batchUpdateNotifyTime(tenantId, planOrderIds);
//    }
//}

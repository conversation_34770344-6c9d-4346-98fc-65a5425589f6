package com.cosfo.manage.order.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.order.model.dto.OrderDeliveryDTO;
import com.cosfo.manage.order.model.dto.OrderItemQueryDTO;
import com.cosfo.manage.order.model.po.OrderItem;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderItemMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(OrderItem record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(OrderItem record);

    /**
     * 查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    OrderItem selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderItem record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderItem record);

    /**
     * 查询订单项详情
     *
     * @param orderId
     * @return
     */
    List<OrderItemVO> queryOrderItemVOByOrderId(@Param("orderId") Long orderId);

    /**
     * 查询订单项
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<OrderItem> selectByOrderId(@Param("tenantId") Long tenantId,
                                    @Param("orderId") Long orderId);

    /**
     * 查询下单sku数
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    Integer querySkuNum(@Param("tenantId") Long tenantId,
                        @Param("orderIds") List<Long> orderIds);

    /**
     * 查询下单件数
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    Integer querySkuAmount(@Param("tenantId") Long tenantId,
                           @Param("orderIds") List<Long> orderIds);

    /**
     * 批量查询商品
     * @param orderIds
     * @return
     */
    List<OrderItemVO> batchQueryByOrderId(List<Long> orderIds);

    /**
     * 批量查询商品包含sku
     * @param orderItemQueryDTO
     * @return
     */
    List<OrderItemVO> batchQueryDetailVOList(OrderItemQueryDTO orderItemQueryDTO);

    /**
     * 更新可申请售后时间
     *
     * @param id
     * @param afterSaleExpiryTime
     */
    void updateSharingTime(@Param("id")Long id,
                           @Param("afterSaleExpiryTime") LocalDateTime afterSaleExpiryTime);
    /**
     * 查询订单项包含sku详情
     *
     * @param orderId
     * @return
     */
    List<OrderItemVO> queryOrderItemDetailVOByOrderId(@Param("orderId") Long orderId);

    /**
     * 修改订单商品项已配数量
     * @param orderItemDeliveryDTO
     * @param orderId
     */
    void updateDeliveryQuantity(@Param("orderItemDeliveryDTO") OrderDeliveryDTO.OrderItemDeliveryDTO orderItemDeliveryDTO, @Param("orderId") Long orderId);
}

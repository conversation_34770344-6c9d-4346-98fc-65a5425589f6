package com.cosfo.manage.order.mapper;

import com.cosfo.manage.order.model.dto.OrderSelfLiftingDTO;
import com.cosfo.manage.order.model.po.OrderSelfLifting;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface OrderSelfLiftingMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(OrderSelfLifting record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(OrderSelfLifting record);

    /**
     * 查询
     * @param id
     * @return
     */
    OrderSelfLifting selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderSelfLifting record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderSelfLifting record);

    /**
     * 自提信息
     * @param orderNo
     * @return
     */
    List<OrderSelfLiftingDTO> selectByOrderNo(String orderNo);

    /**
     * 更新实际自提时间
     * @param orderSelfLifting
     */
    void updateActualTime(OrderSelfLifting orderSelfLifting);
}

package com.cosfo.manage.order.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.order.model.po.OrderAddress;
import com.cosfo.manage.order.model.vo.OrderAddressVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
public interface OrderAddressMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(OrderAddress record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(OrderAddress record);

    /**
     * 查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    OrderAddress selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderAddress record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderAddress record);

    /**
     * 查询订单地址信息
     *
     * @param orderId
     * @return
     */
    OrderAddressVO selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 批量查询
     *
     * @param orderIds
     * @return
     */
    List<OrderAddress> batchQuery(@Param("orderIds") List<Long> orderIds);
}

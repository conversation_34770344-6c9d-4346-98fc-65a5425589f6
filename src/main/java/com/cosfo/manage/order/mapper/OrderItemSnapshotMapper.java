package com.cosfo.manage.order.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.manage.order.model.dto.OrderItemSnapshotDTO;
import com.cosfo.manage.order.model.dto.OrderItemSnapshotQueryDTO;
import com.cosfo.manage.order.model.po.OrderItemSnapshot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderItemSnapshotMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(OrderItemSnapshot record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(OrderItemSnapshot record);

    /**
     * 查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    OrderItemSnapshot selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderItemSnapshot record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderItemSnapshot record);

    /**
     * 根据orderNo 和 供应商SKUId查询
     * @param orderNo
     * @param supplierSkuId
     * @return
     */
    List<OrderItemSnapshotDTO> selectByOrderNoAndSupplierSkuId(@Param("orderNo") String orderNo, @Param("supplierSkuId") Long supplierSkuId);

    /**
     * 批量查询
     *
     * @param tenantId
     * @param orderItemIds
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<OrderItemSnapshot> batchQuery(@Param("tenantId") Long tenantId, @Param("orderItemIds") List<Long> orderItemIds);


    /**
     * 根据itemId查询
     *
     * @param tenantId
     * @param orderItemId
     * @return
     */
    OrderItemSnapshot selectByItemId(@Param("tenantId") Long tenantId, @Param("orderItemId") Long orderItemId);

    /**
     * 查询
     * @param orderItemSnapshotQueryDTO
     * @return
     */
    List<OrderItemSnapshot> selectList(OrderItemSnapshotQueryDTO orderItemSnapshotQueryDTO);

}

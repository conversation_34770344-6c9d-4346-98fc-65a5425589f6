package com.cosfo.manage.order.controller.rolestrategy;

import com.cosfo.manage.common.context.OrderStatusEnum;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.common.context.order.DeliveryButtonStatusEnum;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderItemSnapshotQueryFacade;
import com.cosfo.manage.order.model.dto.OrderInfoVo;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleAuditDTO;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleBizDTO;
import com.cosfo.manage.order.model.vo.OrderDeliveryVO;
import com.cosfo.manage.order.model.vo.OrderDetailDeliveryVO;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.cosfo.manage.tenant.service.TenantAccountSupplierMappingService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SupplierRoleRuleStrategy implements OrderRoleRuleStrategy {

    @Resource
    private TenantAccountSupplierMappingService tenantAccountSupplierMappingService;
    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    @Override
    public String role() {
        return "supplier";
    }

    @Override
    public PageInfo<OrderVO> handlePage(PageInfo<OrderVO> pageInfo, Long authUserId) {
        List<Long> supplierIds = tenantAccountSupplierMappingService.queryByAccountId(authUserId);

        log.info("match supplier rule strategy");
        List<OrderVO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        for (OrderVO orderVO : list) {
            // 处理展示字段
            orderVO.setTotalPrice(null);
            orderVO.setPayablePrice(null);
            List<OrderItemVO> orderItemVOS = orderVO.getOrderItemVOS();
            for (OrderItemVO orderItemVO : orderItemVOS) {
                orderItemVO.setPrice(null);
                orderItemVO.setTotalPrice(null);
            }
            // 当供应商直发且订单状态是待发货和发货中
            // 判断当前供应商货品是否已发完
            if (!WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(orderVO.getWarehouseType())) {
                continue;
            }
            if (!OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderVO.getStatus())) {
                continue;
            }
            Optional<OrderItemVO> needDeliveryOptional = orderVO.getOrderItemVOS().stream().filter(item -> !item.getAmount().equals(item.getDeliveryQuantity())).findAny();
            //未配送
            Optional<OrderItemVO> neverDeliveryOptional = orderVO.getOrderItemVOS().stream().filter(item -> item.getDeliveryQuantity() != 0).findAny();
            // 当前供应商还未配送则展示立即配送
            if (!neverDeliveryOptional.isPresent()) {
                orderVO.setDeliveryStatus(DeliveryButtonStatusEnum.GO_DELIVERY.getType());
                continue;
            }
            if (needDeliveryOptional.isPresent()) {
                //当前供应商部分配送则展示继续配送
                orderVO.setDeliveryStatus(DeliveryButtonStatusEnum.CONTINUE_DELIVERY.getType());
            } else {
                //当前供应商已全部配送则展示待收货
                orderVO.setStatus(OrderStatusEnum.DELIVERING.getCode());
                orderVO.setDeliveryStatus(DeliveryButtonStatusEnum.DELIVERY_FINISH.getType());
            }
        }
        return pageInfo;
    }

    @Override
    public List<OrderAfterSaleProductVO> handleAfterSaleVO(List<OrderAfterSaleProductVO> afterSaleProductVOList, Long authUserId) {
        log.info("match supplier rule strategy");
        for (OrderAfterSaleProductVO vo : afterSaleProductVOList) {
            List<OrderAfterSaleBizDTO> afterSaleOrders = vo.getAfterSaleOrders();
            List<Long> supplierIds = tenantAccountSupplierMappingService.queryByAuthUserId(authUserId);
            List<Long> orderItemIds = afterSaleOrders.stream().map(OrderAfterSaleBizDTO::getOrderItemId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderItemIds)) {
                return afterSaleProductVOList;
            }
            List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryByOrderItemIds(orderItemIds);
            Map<Long, Long> orderItemSupplierMap = orderItemSnapshotResps.stream().collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, OrderItemSnapshotResp::getSupplierTenantId));
            List<OrderAfterSaleBizDTO> supplierAfterSaleOrderList = afterSaleOrders.stream().filter(order -> supplierIds.contains(orderItemSupplierMap.get(order.getOrderItemId()))).collect(Collectors.toList());
            for (OrderAfterSaleBizDTO afterSaleOrder : supplierAfterSaleOrderList) {
                afterSaleOrder.setApplyPrice(null);
                afterSaleOrder.setTotalPrice(null);
            }
            vo.setAfterSaleOrders(supplierAfterSaleOrderList);
        }
        return afterSaleProductVOList.stream().filter(vo -> !CollectionUtils.isEmpty(vo.getAfterSaleOrders())).collect(Collectors.toList());
    }

    @Override
    public OrderInfoVo handleOrderCommodity(OrderInfoVo orderInfoVo, Long authUserId) {
        List<OrderItemVO> orderItemVOS = orderInfoVo.getOrderItemVOS();
        List<Long> supplierIds = tenantAccountSupplierMappingService.queryByAuthUserId(authUserId);
        List<OrderItemVO> supplierOrderItemList = orderItemVOS.stream().filter(orderItem -> supplierIds.contains(orderItem.getSupplierTenantId())).collect(Collectors.toList());
        orderInfoVo.setOrderItemVOS(supplierOrderItemList);
        return orderInfoVo;
    }

    @Override
    public OrderAfterSaleAuditDTO handleAfterSaleAudit(OrderAfterSaleAuditDTO auditDTO) {
        if (auditDTO.getSupplierTotalRefundPrice() == null)  {
            return auditDTO;
        }

        OrderAfterSaleResp orderAfterSaleByNo = orderAfterSaleQueryFacade.getOrderAfterSaleByNo(auditDTO.getAfterSaleOrderNo());
        OrderItemAndSnapshotResp orderItemAndSnapshotResp = orderItemQueryFacade.queryDetailById(orderAfterSaleByNo.getOrderItemId());
        if (orderItemAndSnapshotResp == null) {
            throw new BizException("订单明细不存在");
        }
        BigDecimal supplyPrice = orderItemAndSnapshotResp.getSupplyPrice();
        BigDecimal itemPrice = orderItemAndSnapshotResp.getPayablePrice();
        BigDecimal supplyRatio = supplyPrice.divide(itemPrice, 4, RoundingMode.HALF_UP);
        BigDecimal refundPrice = auditDTO.getSupplierTotalRefundPrice().divide(supplyRatio,2, RoundingMode.HALF_UP);
        auditDTO.setTotalPrice(refundPrice);
        return auditDTO;
    }

    @Override
    public OrderDetailDeliveryVO handleDeliveryDetails(OrderDetailDeliveryVO deliveryVO, Long authUserId) {
        if(deliveryVO == null || CollectionUtils.isEmpty(deliveryVO.getWaitDeliveryItemList())){
            return deliveryVO;
        }
        List<Long> supplierIds = tenantAccountSupplierMappingService.queryByAuthUserId(authUserId);
        List<Long> orderItemIds = deliveryVO.getWaitDeliveryItemList().stream().map(OrderDeliveryVO.OrderDeliveryItemVO::getOrderItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return deliveryVO;
        }
        List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryByOrderItemIds(orderItemIds);
        Map<Long, Long> orderItemSupplierMap = orderItemSnapshotResps.stream().collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, OrderItemSnapshotResp::getSupplierTenantId));
        List<OrderDeliveryVO.OrderDeliveryItemVO> supplierDeliveryItemList = deliveryVO.getWaitDeliveryItemList().stream().filter(delivery -> supplierIds.contains(orderItemSupplierMap.get(delivery.getOrderItemId()))).collect(Collectors.toList());
        deliveryVO.setWaitDeliveryItemList(supplierDeliveryItemList);
        return deliveryVO;
    }
}

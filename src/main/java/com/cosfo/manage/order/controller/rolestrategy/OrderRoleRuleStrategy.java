package com.cosfo.manage.order.controller.rolestrategy;

import com.cosfo.manage.order.model.dto.OrderInfoVo;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleAuditDTO;
import com.cosfo.manage.order.model.vo.OrderDetailDeliveryVO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderRoleRuleStrategy {

    default String role() {
        return "";
    }

    /**
     * 处理不同角色订单列表展示
     *
     * @param pageInfo
     * @return
     */
    PageInfo<OrderVO> handlePage(PageInfo<OrderVO> pageInfo, Long authUserId);


    /**
     * 处理售后不同角色展示
     * @param afterSaleProductVOList
     * @return
     */
    List<OrderAfterSaleProductVO> handleAfterSaleVO(List<OrderAfterSaleProductVO> afterSaleProductVOList, Long authUserId);

    /**
     * 处理售后订单明细
     * @param orderInfoVo
     * @param authUserId
     * @return
     */
    OrderInfoVo handleOrderCommodity(OrderInfoVo orderInfoVo, Long authUserId);


    /**
     * 处理售后审核
     * @param auditDTO
     * @return
     */
    OrderAfterSaleAuditDTO handleAfterSaleAudit(OrderAfterSaleAuditDTO auditDTO);

    /**
     * 供应商配送信息
     * @param deliveryVO
     * @param authUserId
     * @return
     */
    OrderDetailDeliveryVO handleDeliveryDetails(OrderDetailDeliveryVO deliveryVO, Long authUserId);
}

package com.cosfo.manage.order.controller.custom;

import com.cosfo.manage.common.config.CustomConfig;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.order.model.dto.OrderQueryDTO;
import com.cosfo.manage.order.service.OrderBusinessService;
import net.xianmu.common.exception.BizException;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: fansongsong
 * @Date: 2023-12-26
 * @Description:绝配定制功能
 */
@RestController
@RequestMapping("juepei/order")
public class JuepeiOrderController extends BaseController {

    @Resource
    private OrderBusinessService orderBusinessService;

    @Resource
    private CustomConfig customConfig;

    /**
     * 绝配订单导出
     *
     * @param orderQueryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody OrderQueryDTO orderQueryDTO) {
        if (!customConfig.getJuepeiTenantIds().contains(UserLoginContextUtils.getTenantId())) {
            throw new BizException("当前租户未开放绝配订单导出功能");
        }
        orderBusinessService.exportForJuepei(orderQueryDTO, getMerchantInfoDTO());
    }
}

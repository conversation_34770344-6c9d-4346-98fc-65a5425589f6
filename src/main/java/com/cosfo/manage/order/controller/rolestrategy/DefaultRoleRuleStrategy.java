package com.cosfo.manage.order.controller.rolestrategy;

import com.cosfo.manage.order.model.dto.OrderInfoVo;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleAuditDTO;
import com.cosfo.manage.order.model.vo.OrderDetailDeliveryVO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DefaultRoleRuleStrategy implements OrderRoleRuleStrategy {

    @Override
    public String role() {
        return "default";
    }

    @Override
    public PageInfo<OrderVO> handlePage(PageInfo<OrderVO> pageInfo, Long authUserId) {
        log.info("match default rule strategy");
        return pageInfo;
    }

    @Override
    public List<OrderAfterSaleProductVO> handleAfterSaleVO(List<OrderAfterSaleProductVO> afterSaleProductVOList, Long authUserId) {
        log.info("match default rule strategy");
        return afterSaleProductVOList;
    }

    @Override
    public OrderInfoVo handleOrderCommodity(OrderInfoVo orderInfoVo, Long authUserId) {
        return orderInfoVo;
    }

    @Override
    public OrderAfterSaleAuditDTO handleAfterSaleAudit(OrderAfterSaleAuditDTO auditDTO) {
        return auditDTO;
    }

    @Override
    public OrderDetailDeliveryVO handleDeliveryDetails(OrderDetailDeliveryVO deliveryVO, Long authUserId) {
        return deliveryVO;
    }
}

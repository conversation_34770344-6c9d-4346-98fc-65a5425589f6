package com.cosfo.manage.order.model.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-10
 * @Description:
 */
@Data
public class OrderDeliveryDTO {

    /**
     * 订单Id
     */
    @NotNull(message = "orderId 不能为空")
    private Long orderId;

    /**
     * 配送方式 0其他 1物流快递
     */
    @NotNull(message = "deliveryType 不能为空")
    private Integer deliveryType;

    /**
     * 物流公司
     */
    private String deliveryCompany;

    /**
     * 配送单号
     */
    private String deliveryNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 配送订单项
     */
    @NotEmpty(message = "deliveryDTOList列表 不能为空")
    private List<OrderItemDeliveryDTO> deliveryDTOList;

    @Data
    public class OrderItemDeliveryDTO {
        /**
         * orderItemId
         */
        @NotNull(message = "orderItemId 不能为空")
        private Long orderItemId;
        /**
         * 配送数量
         */
        @Min(value = 1,message = "quantity 不能小于0")
        private Integer quantity;
    }
}

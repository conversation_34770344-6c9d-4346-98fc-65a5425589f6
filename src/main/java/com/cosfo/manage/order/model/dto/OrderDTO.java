package com.cosfo.manage.order.model.dto;


import com.cosfo.manage.order.model.po.Order;
import com.cosfo.manage.order.model.po.OrderAddress;
import com.cosfo.manage.order.model.po.OrderSelfLifting;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/19  09:43
 */
@Data
public class OrderDTO extends Order {
    /**
     * 下单地址
     */
    private OrderAddress orderAddress;

    /**
     * 供应商租户id
     */
    private Long supplyTenantId;

    /**
     * 订单商品明细
     */
    private List<OrderItemDTO> orderItemDTOList;

    /**
     * 自提列表
     */
    private List<OrderSelfLiftingDTO> orderSelfLiftingDTOS;
}

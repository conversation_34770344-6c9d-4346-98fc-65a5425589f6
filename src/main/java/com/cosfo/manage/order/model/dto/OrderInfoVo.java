package com.cosfo.manage.order.model.dto;

import com.cosfo.manage.order.model.vo.OrderItemVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderInfoVo {
    /**
     * 商品总额
     */
    private BigDecimal productTotalPrice;
    /**
     * 应付金额
     */
    private BigDecimal payablePrice;
    /**
     * 配送费
     */
    private BigDecimal deliveryFee;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    List<OrderItemVO> orderItemVOS;
}

package com.cosfo.manage.order.model.dto;

import com.cosfo.manage.order.model.po.OrderItem;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/19  09:45
 */
@Data
public class OrderItemDTO extends OrderItem {
    /**
     * 供应商skuId
     */
    private Long supplySkuId;
    /**
     * 下单商品Id
     */
    private Long itemId;
    /**
     * 下单商品数量
     */
    private Integer amount;

    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 名称
     */
    private String title;

    /**
     * sku编号
     */
    private Long skuId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 供应商sku编码
     */
    private String supplySku;
}

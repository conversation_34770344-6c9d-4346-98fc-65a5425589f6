package com.cosfo.manage.order.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * @author: xiaowk
 * @time: 2023/5/24 下午1:10
 */
@Data
public class ReturnAddressVO {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 门牌号码
     */
    private String houseNo;

    /**
     * poi地址
     */
    private String poiNote;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

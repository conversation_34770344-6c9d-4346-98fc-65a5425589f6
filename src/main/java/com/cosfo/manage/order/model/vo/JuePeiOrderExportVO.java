package com.cosfo.manage.order.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述: 订单导出数据
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JuePeiOrderExportVO {
    /**
     * 订单编码
     */
    private String orderNO;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * sku
     */
    private Long itemId;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 绝配商品编码
     */
    private String itemCodeDetail;
    /**
     * 商店名称
     */
    private String title;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 仓库名
     */
    private String warehouseName;
    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 订单出库类型
     */
    private String orderSaleTypeDesc;
}

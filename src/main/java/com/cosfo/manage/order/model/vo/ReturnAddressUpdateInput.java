package com.cosfo.manage.order.model.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 * @author: xiaowk
 * @time: 2023/5/24 下午1:10
 */
@Data
public class ReturnAddressUpdateInput {
    /**
     * 主键、自增
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 门牌号码
     */
    private String houseNo;

    /**
     * poi地址
     */
    private String poiNote;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;
}

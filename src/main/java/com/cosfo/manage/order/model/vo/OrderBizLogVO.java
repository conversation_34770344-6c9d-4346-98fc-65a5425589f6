package com.cosfo.manage.order.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/12/29 下午2:36
 */
@Data
public class OrderBizLogVO implements Serializable {
    private static final long serialVersionUID = 1546722176169515687L;

    /**
     * 日志ID
     */
    private Long logId;

    /**
     * 租户
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 操作人ID
     */
    private Long operatorAuthUserId;

    /**
     * 操作人名称
     */
    private String operatorUserName;

    /**
     * 操作人手机号
     */
    private String operatorPhone;

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 业务时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bizCreateTime;


    /**
     * 业务日志内容
     */
    private String logContent;
}

package com.cosfo.manage.order.model.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * order
 * <AUTHOR>
@Data
public class Order implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 店铺Id
     */
    private Long storeId;

    /**
     * 下单账号Id
     */
    private Long accountId;

    /**
     * 供应商租户Id
     */
    private Long supplierTenantId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 配送仓类型
     */
    private Integer warehouseType;

    /**
     * 应付价格
     */
    private BigDecimal payablePrice;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 支付方式 1,线上支付 2,账期
     */
    private Integer payType;

    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 可申请售后时间
     */
    private Integer applyEndTime;

    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 组合订单id
     */
    private Long combineOrderId;

    /**
     * 0-普通订单,1=组合订单,2=预售订单
     */
    private Integer orderType;

    /**
     * 分账完成时间
     */
    private LocalDateTime profitSharingFinishTime;

    private static final long serialVersionUID = 1L;
}
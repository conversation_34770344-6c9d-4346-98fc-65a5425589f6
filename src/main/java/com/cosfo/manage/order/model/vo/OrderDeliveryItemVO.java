package com.cosfo.manage.order.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/28
 */
@Data
public class OrderDeliveryItemVO {

    /**
     * 主键Id
     */
    private Long id;
    /**
     * sku编码
     */
    private Long itemId;

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * skuId
     */
    private Long skuId;

    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 供应商商品skuId
     */
    private Long supplierSkuId;
    /**
     * 区域itemId
     */
    private Long areaItemId;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 供应商Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 配送数量
     */
    private Integer deliveryQuantity;
}

package com.cosfo.manage.order.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 预售订单批量设置配送时间DTO
 */
@Data
public class OrderPresaleBatchDeliveryTimeDTO {

    /**
     * 开始时间
     */
    @NotNull(message = "startTime 不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "endTime 不能为空")
    private String endTime;

    /**
     * 商品Id
     */
    @NotNull(message = "itemIds 不能为空")
    private List<Long> itemIds;


    /**
     * 配送时间
     */
    @NotNull(message = "deliveryTime 不能为空")
    private String deliveryTime;
}

package com.cosfo.manage.order.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/17
 */
@Data
public class OrderItemVO {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * sku编码
     */
    private Long itemId;

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * item的skuId
     */
    private Long itemSkuId;

    /**
     * sku
     */
    private String sku;

    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 供应商商品skuId
     */
    private Long supplierSkuId;
    /**
     * 区域itemId
     */
    private Long areaItemId;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 供应商Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 仓库类型 0 自营 1三方
     */
    private Integer warehouseType;


    /**
     * 可售后件数
     */
    private Integer enableApplyAmount;

    /**
     * 可售后数量(最小颗粒度)
     */
    private Integer enableApplyQuantity;
    /**
     * 配送方式 0 品牌方配送 1三方配送
     */
    private Integer deliveryType;

    /**
     * 是否可申请售后
     */
    private Boolean enableApplyAfterSale;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 售后次数
     */
    private Integer afterSaleAmount;

    /**
     * 售后数量
     */
    private Integer afterSaleCount;

    /**
     * 售后金额
     */
    private BigDecimal afterSalePrice;


    /**
     * afterSaleExpiryTime
     */
    private LocalDateTime afterSaleExpiryTime;

    /**
     * 已配送数量
     */
    private Integer deliveryQuantity;

    /**
     * 商品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 供应价
     */
    private BigDecimal supplyPrice;


    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;


    /**
     * 商品重量
     */
    private BigDecimal weight;

    /**
     * 下单重量 (下单数量*商品重量)
     */
    private BigDecimal totalWeight;

    /**
     * 发货数量
     */
    private Integer deliveryNum;

    /**
     * 发货重量 (发货数量*商品重量)
     */
    private BigDecimal deliveryTotalWeight;
}

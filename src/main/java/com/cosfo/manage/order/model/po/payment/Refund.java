package com.cosfo.manage.order.model.po.payment;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * refund
 * <AUTHOR>
@Data
public class Refund implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 售后id
     */
    private Long afterSaleId;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 状态： 100, 创建退款,待发起 1、退款中 2、退款成功 3、退款失败
     */
    private Integer refundStatus;

    /**
     * 子商户号
     */
    private String subMchid;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 原支付金额
     */
    private BigDecimal paymentPrice;

    /**
     * 微信支付退款单号
     */
    private String refundId;

    /**
     * 退款渠道：
ORIGINAL：原路退款
BALANCE：退回到余额
OTHER_BALANCE：原账户异常退到其他余额账户
OTHER_BANKCARD：原银行卡异常退到其他银行卡
     */
    private String channel;

    /**
     * 退款入账账户
     */
    private String userReceivedAccount;

    /**
     * 退款成功时间
     */
    private LocalDateTime successTime;

    /**
     * 退款状态
    SUCCESS：退款成功
    CLOSED：退款关闭
    PROCESSING：退款处理中
    ABNORMAL：退款异常
     */
    private String status;

    /**
     * 重试次数
     */
    private Integer retryNum;

    /**
     * 汇付请求退款流水号唯一标识
     */
    private String reqSeqId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 交易退款请求Id
     */
    private String confirmRefundReqId;

    /**
     * 支付单id
     */
    private Long paymentId;

    private static final long serialVersionUID = 1L;
}
package com.cosfo.manage.order.convert;

import com.cosfo.manage.order.model.vo.OrderAddressVO;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderAddressConvert {
    OrderAddressConvert INSTANCE = Mappers.getMapper(OrderAddressConvert.class);

    OrderAddressVO toVO(OrderAddressDTO dto);

    OrderAddressVO respToVO(OrderAddressResp dto);
}

package com.cosfo.manage.order.convert;

import com.cosfo.manage.order.model.dto.BillOrderDTO;
import com.cosfo.manage.order.model.po.OrderAddress;
import com.cosfo.manage.order.model.vo.OrderAddressVO;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.OrderDetailDTO;
import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.order.OrderDetailResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 *  order对象转换
 *
 * @author: xiaowk
 * @date: 2023/6/6 下午1:45
 */
@Mapper
public interface OrderConvert {
    OrderConvert INSTANCE = Mappers.getMapper(OrderConvert.class);

    List<OrderVO> convert2VOs(List<OrderDTO> orderDTOS);
    List<OrderVO> convertResp2VOs(List<OrderResp> orderDTOS);

    @Mapping(target = "totalPrice", defaultValue = "0")
    @Mapping(target = "orderTime", source = "createTime")
    @Mapping(source = "id", target = "orderId")
    OrderVO convert2VO(OrderDTO orderDTO);

    @Mapping(target = "totalPrice", defaultValue = "0")
    @Mapping(target = "orderTime", source = "createTime")
    @Mapping(source = "id", target = "orderId")
    OrderVO convertResp2VO(OrderResp orderResp);

    List<OrderItemVO> convertOrderItem2VOs(List<OrderItemAndSnapshotDTO> dtos);
    List<OrderItemVO> convertOrderItemResp2VOs(List<OrderItemAndSnapshotResp> dtos);

    @Mapping(target = "skuId", expression = "java((orderItemAndSnapshotDTO.getSkuId() != null && orderItemAndSnapshotDTO.getSkuId() == -1) ? null : orderItemAndSnapshotDTO.getSkuId())")
    @Mapping(target = "itemSkuId", source = "itemId")
    @Mapping(source = "payablePrice", target = "price")
    @Mapping(source = "orderItemId", target = "id")
    OrderItemVO convertOrderItem2VO(OrderItemAndSnapshotDTO orderItemAndSnapshotDTO);

    @Mapping(target = "skuId", expression = "java((orderItemAndSnapshotDTO.getSkuId() != null && orderItemAndSnapshotDTO.getSkuId() == -1) ? null : orderItemAndSnapshotDTO.getSkuId())")
    @Mapping(target = "itemSkuId", source = "itemId")
    @Mapping(source = "payablePrice", target = "price")
    @Mapping(source = "orderItemId", target = "id")
    OrderItemVO convertOrderItemResp2VO(OrderItemAndSnapshotResp orderItemAndSnapshotDTO);

    OrderAddressVO convert2VO(OrderAddress orderAddress);

    List<BillOrderDTO> convert2BillDTOs(List<OrderDetailDTO> orderDetailDTOs);

    List<BillOrderDTO> convertResp2BillDTOs(List<OrderDetailResp> resps);

    BillOrderDTO convert2BillDTO(OrderDetailDTO orderDetailDTO);

}

package com.cosfo.manage.order.dao;

import com.cosfo.manage.common.context.OrderItemFeeEnum;
import com.cosfo.manage.order.model.po.OrderItemFeeTransaction;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单项费用明细流水 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface OrderItemFeeTransactionDao extends IService<OrderItemFeeTransaction> {

    /**
     * @param orderIds
     * @param feeType
     * @param transactionType
     * @param tenantId
     * @return
     */
    BigDecimal calTotalFeeBy(List<Long> orderIds, OrderItemFeeEnum.FeeType feeType, OrderItemFeeEnum.TransactionType transactionType, Long tenantId);

    /**
     * 查询订单明细项代仓费用
     *
     * @param orderItems
     * @param feeType
     * @param transactionType
     * @param tenantId
     * @return
     */
    Map<Long, BigDecimal> calOrderItemFeeMap(List<Long> orderItems, OrderItemFeeEnum.FeeType feeType, OrderItemFeeEnum.TransactionType transactionType, Long tenantId);


    /**
     * 查询售后费用明细项
     * @param afterSaleIds
     * @param feeType
     * @param tenantId
     * @return
     */
    Map<Long, BigDecimal> calAfterSaleFeeMap(List<Long> afterSaleIds, OrderItemFeeEnum.FeeType feeType, Long tenantId);

}

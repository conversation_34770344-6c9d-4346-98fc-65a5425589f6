package com.cosfo.manage.order.service.impl;

import com.cosfo.manage.bill.mapper.BillProfitSharingOrderMapper;
import com.cosfo.manage.bill.model.dto.BillProfitSharingOrderQuery;
import com.cosfo.manage.bill.model.po.BillProfitSharingOrder;
import com.cosfo.manage.order.service.OrderTaskService;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.ProfitSharingFinishTimeReq;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderTaskServiceImpl implements OrderTaskService {

    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;

    @DubboReference
    private OrderCommandProvider orderCommandProvider;

    @Override
    @Async
    public Boolean syncProfitSharingFinishTime(List<Long> orderIds) {
        log.info("订单同步分账完成时间历史数据开始");
        // 获取已分账任务
        BillProfitSharingOrderQuery query = new BillProfitSharingOrderQuery();
        query.setBatchSize(100);
        query.setStatus(3);
        query.setOrderIds(orderIds);
        List<BillProfitSharingOrder> billProfitSharingOrders;
        while (true) {
            billProfitSharingOrders = billProfitSharingOrderMapper.batchQueryByStatus(query);
            if (CollectionUtils.isEmpty(billProfitSharingOrders)) {
                log.info("分账完成");
                return true;
            }
            // 更新订单完成时间
            updateProfitSharingFinishTime(billProfitSharingOrders);
            query.setMaxId(billProfitSharingOrders.get(billProfitSharingOrders.size() - 1).getId());
        }
    }

    private void updateProfitSharingFinishTime(List<BillProfitSharingOrder> billProfitSharingOrders) {
        ProfitSharingFinishTimeReq req = new ProfitSharingFinishTimeReq();
        List<ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder> orderList = new ArrayList<>();
        for (BillProfitSharingOrder billProfitSharingOrder : billProfitSharingOrders) {
            ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder order = new ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder();
            order.setOrderId(billProfitSharingOrder.getOrderId());
            order.setProfitSharingFinishTime(billProfitSharingOrder.getUpdateTime());
            orderList.add(order);
        }
        req.setProfitSharingFinishTimeOrderList(orderList);
        DubboResponse<Boolean> response = orderCommandProvider.batchUpdateProfitSharingFinishTime(req);
        if (!response.isSuccess()) {
            throw new BizException("订单历史分账完成时间同步失败");
        }
    }
}

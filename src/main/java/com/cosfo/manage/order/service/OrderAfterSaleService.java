package com.cosfo.manage.order.service;

import com.cosfo.manage.bill.model.vo.BillOrderAfterSaleVO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.order.model.dto.OrderAfterSaleDeliveryDTO;
import com.cosfo.manage.order.model.dto.OrderAfterSaleRemarkDTO;
import com.cosfo.manage.order.model.dto.OrderAfterSaleRuleUpdateDTO;
import com.cosfo.manage.order.model.dto.OrderAfterSaleUnAuditModifyQuantityDTO;
import com.cosfo.manage.order.model.dto.OrderInfoVo;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleAuditDTO;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleBizDTO;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleQueryDTO;
import com.cosfo.manage.order.model.vo.OrderAfterSaleRuleResultVO;
import com.cosfo.manage.order.model.vo.OrderSimpleDTO;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleInfoVO;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述: 售后订单服务处理类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/20
 */
public interface OrderAfterSaleService {

    /**
     *
     * 售后订单列表
     *
     * @return
     */
    CommonResult<PageInfo<OrderAfterSaleInfoVO>> getOrderListAfterSale(OrderAfterSaleQueryDTO afterSaleQueryDto, LoginContextInfoDTO loginContextInfoDTO);

    /**
     *
     * 售后订单列表导出订单
     *
     * @return
     */
    void exportOrdersAfterSale(OrderAfterSaleQueryDTO afterSaleQueryDto, LoginContextInfoDTO loginContextInfoDTO);

    /**
     *
     * 售后订单详情售后商品列表
     *
     * @return
     */
    CommonResult<List<OrderAfterSaleProductVO>> getOrderAfterSaleCommodity(Long orderId);

    /**
     *
     * 售后订单详情原订单列表
     *
     * @return
     */
    CommonResult<OrderInfoVo> getOrderCommodity(Long orderId);

    /**
     *
     * 售后订单审核提交
     * @param orderAfterSaleAuditDTO
     * @param loginContextInfoDTO
     *
     * @return
     */
    CommonResult reviewSubmissions(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 售后待审核订单修改数量
     * @param orderAfterSaleUnAuditModifyQuantityDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult modifyQuantity(OrderAfterSaleUnAuditModifyQuantityDTO orderAfterSaleUnAuditModifyQuantityDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 修改售后备注
     * @param orderAfterSaleRemarkDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult updateAdminRemark(OrderAfterSaleRemarkDTO orderAfterSaleRemarkDTO, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 修改物流信息
     * @param orderAfterSaleDeliveryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult updateDeliveryInfo(OrderAfterSaleDeliveryDTO orderAfterSaleDeliveryDTO, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 批量查询售后单信息
     *
     * @param orderAfterSaleIds
     * @param tenantId
     * @return
     */
    List<BillOrderAfterSaleVO> batchQueryByOrderAfterSaleIds(List<Long> orderAfterSaleIds, Long tenantId);

    /**
     * 查询品牌指定账单周期内门店账期订单
     *
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderAfterSaleResp> queryBillOrderByStartTimeAndEndTime(Long tenantId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 管理员发起售后
     * @param orderAfterSaleBizDTO
     * @return
     */
    CommonResult save(OrderAfterSaleBizDTO orderAfterSaleBizDTO);

    /**
     * 校验可售后的金额的数量
     * @param id
     * @param applyPrice
     * @param applyAmount
     */
//    void verifyApplyPriceAndAmount(Long id, BigDecimal applyPrice, Integer applyAmount);

    /**
     * 查询售后规则
     * @param tenantId
     * @return
     */
    OrderAfterSaleRuleResultVO queryRule(Long tenantId);

    /**
     * 新增/修改
     * @param afterSaleRuleDTO
     * @return
     */
    Boolean updateRule(OrderAfterSaleRuleUpdateDTO afterSaleRuleDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 计算退款金额
     * @param orderItemId
     * @param amount
     * @return
     */
    BigDecimal calculateRefundPrice(Long orderItemId, Integer amount);

    /**
     * 根据售后单查询订单简要信息
     * @param afterSaleOrderNo
     * @return
     */
    OrderSimpleDTO queryOrderSimpleDto(String afterSaleOrderNo);
}

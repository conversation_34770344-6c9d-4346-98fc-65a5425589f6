package com.cosfo.manage.order.service;

import com.cosfo.manage.order.model.po.OrderAgentSkuFeeRuleSnapshot;

import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/27
 */
public interface OrderAgentSkuFeeRuleSnapshotService {

    /**
     * 查询品牌方自己代仓品分账金额
     *
     * @param orderId
     * @param tenantId
     * @return
     */
    OrderAgentSkuFeeRuleSnapshot queryTenantProfitSharingDetail(Long orderId, Long tenantId);


    /**
     * 查询订单命中代仓费用规则
     * @param orderId
     * @param tenantId
     */
    String queryOrderHitAgentRule(Long orderId, Long tenantId);

    /**
     * 批量查询订单命中代仓费用规则
     * @param orderIds
     * @param tenantId
     * @return orderId,hitRule
     */
    Map<Long, String> batchQueryOrderHitAgentRule(List<Long> orderIds, Long tenantId);
}

package com.cosfo.manage.order.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.order.model.dto.OrderPresaleBatchDeliveryTimeDTO;
import com.cosfo.manage.order.model.dto.OrderPresaleDeliveryTimeDTO;

public interface OrderPresaleService {

    /**
     * 预售订单批量设置配送日期
     *
     * @param orderPresaleBatchDeliveryTimeDTO orderPresaleDeliveryTimeDTO
     * @param loginContextInfoDTO              loginContextInfoDTO
     * @return orderPresaleDeliveryTimeDTO
     */
    Long batchSetDeliveryTime(OrderPresaleBatchDeliveryTimeDTO orderPresaleBatchDeliveryTimeDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 设置配送日期
     *
     * @param orderPresaleDeliveryTimeDTO orderPresaleDeliveryTimeDTO
     */
    void setDeliveryTime(OrderPresaleDeliveryTimeDTO orderPresaleDeliveryTimeDTO);
}

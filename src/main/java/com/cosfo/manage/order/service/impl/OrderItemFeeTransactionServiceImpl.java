package com.cosfo.manage.order.service.impl;

import com.cosfo.manage.common.context.OrderItemFeeEnum;
import com.cosfo.manage.order.dao.OrderItemFeeTransactionDao;
import com.cosfo.manage.order.service.OrderItemFeeTransactionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单项费用明细流水 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Service
public class OrderItemFeeTransactionServiceImpl implements OrderItemFeeTransactionService {

    @Resource
    private OrderItemFeeTransactionDao orderItemFeeTransactionDao;

    @Override
    public BigDecimal calTotalFeeBy(List<Long> orderIds, OrderItemFeeEnum.FeeType feeType, OrderItemFeeEnum.TransactionType transactionType, Long tenantId) {
        return orderItemFeeTransactionDao.calTotalFeeBy(orderIds, feeType, transactionType, tenantId);
    }

    @Override
    public Map<Long, BigDecimal> calOrderItemFeeMap(List<Long> orderItems, OrderItemFeeEnum.FeeType feeType, OrderItemFeeEnum.TransactionType transactionType, Long tenantId) {
        return orderItemFeeTransactionDao.calOrderItemFeeMap(orderItems, feeType, transactionType, tenantId);
    }

    @Override
    public Map<Long, BigDecimal> calAfterSaleItemFeeMap(List<Long> afterSaleIds, OrderItemFeeEnum.FeeType feeType, Long tenantId) {
        return orderItemFeeTransactionDao.calAfterSaleFeeMap(afterSaleIds, feeType, tenantId);
    }
}

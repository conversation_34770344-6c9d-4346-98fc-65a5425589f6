//package com.cosfo.manage.order.executor;
//
//
//import cn.hutool.core.util.NumberUtil;
//import com.cosfo.aftersale.common.context.OrderAfterSaleStatusEnum;
//import com.cosfo.aftersale.common.context.OrderAfterSaleTypeEnum;
//import com.cosfo.aftersale.model.dto.OrderAfterSaleAuditDTO;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import com.cosfo.manage.common.exception.DefaultServiceException;
//import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
//import com.cosfo.manage.common.result.ResultDTO;
//import com.cosfo.manage.order.service.OrderAfterSaleService;
//import com.cosfo.manage.order.service.OrderAsyncService;
//import com.cosfo.ordercenter.client.resp.OrderDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 11:11
// */
//@Slf4j
//@Component
//public class RefundAfterSaleExecutor extends AfterSaleAbstractExecutor {
//
//    @Resource
//    @Lazy
//    private OrderAfterSaleService orderAfterSaleService;
//    @Resource
//    private com.cosfo.aftersale.service.OrderAfterSaleService afterSaleService;
//    @Resource
//    private OrderAsyncService orderAsyncService;
//    @Value("${saasmall.api-host}")
//    private String apiHost;
//
//    @Override
//    public ResultDTO reviewSuccess(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO requestContextInfoDTO, OrderAfterSale afterSale, OrderDTO order) {
//        // 配送仓类型
//        Integer warehouseType = afterSale.getWarehouseType();
//        // 售后类型
//        Integer afterSaleType = afterSale.getAfterSaleType();
//
//        // 退款处理 不区分无仓有仓，只区分配送前还是配送后
//        // 有仓 【配送前退款】创建售后单有【冻结正向履约单】，完成售后单有【释放冻结库存】操作
//
//        // 退款 配送前售后
//        if (OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(afterSaleType)) {
//            // 未到货退款
//            // 是否退运费
//            BigDecimal deliveryFee = null;
//            if (orderAfterSaleService.validationIsNeedRefundDeliveryFee(afterSale)) {
//                deliveryFee = order.getDeliveryFee();
//            }
//            BigDecimal totalPrice = NumberUtil.add(deliveryFee, orderAfterSaleAuditDTO.getTotalPrice());
//
//            OrderAfterSale updateObj = new OrderAfterSale();
//            updateObj.setTotalPrice(totalPrice);
//            updateObj.setDeliveryFee(deliveryFee);
//            updateObj.setStatus(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getCode());
//            updateObj.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
//            updateObj.setOperatorName(requestContextInfoDTO.getTenantName());
//            updateObj.setHandleTime(LocalDateTime.now());
//            afterSaleService.updateRecordByIdAndSourceStatus(afterSale.getId(), OrderAfterSaleStatusEnum.UNAUDITED.getCode(), updateObj);
//
//
//            // ofc履约中心binlog监听处理中状态，后续mq发消息通知saas消费者执行释放冻结库存和退款操作
//
//        } else if (OrderAfterSaleTypeEnum.DELIVERED.getType().equals(afterSaleType)) {
//            // 退款 配送后售后
//
//            // 已到货退款 更新售后订单状态为退款中
//            OrderAfterSale update = new OrderAfterSale();
//            update.setId(afterSale.getId());
//            update.setStatus(OrderAfterSaleStatusEnum.REFUNDING.getCode());
//            update.setTotalPrice(orderAfterSaleAuditDTO.getTotalPrice());
//            update.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
//            update.setOperatorName(requestContextInfoDTO.getTenantName());
//            update.setHandleTime(LocalDateTime.now());
//            afterSaleService.updateRecordByIdAndSourceStatus(afterSale.getId(), OrderAfterSaleStatusEnum.UNAUDITED.getCode(), update);
//
//            // 请求退款
//            orderAsyncService.payRefund(afterSale.getId(), afterSale.getOrderId(), afterSale.getTenantId(), orderAfterSaleAuditDTO.getTotalPrice());
//        } else {
//            throw new DefaultServiceException("不支持的售后类型：" + afterSaleType);
//
//        }
//
//        return ResultDTO.success();
//    }
//}

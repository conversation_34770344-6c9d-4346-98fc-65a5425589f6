//package com.cosfo.manage.order.executor;
//
//import com.cosfo.aftersale.common.context.OrderAfterSaleStatusEnum;
//import com.cosfo.aftersale.mapper.OrderAfterSaleMapper;
//import com.cosfo.aftersale.model.dto.OrderAfterSaleAuditDTO;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import com.cosfo.aftersale.service.OrderAfterSaleService;
//import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
//import com.cosfo.manage.common.result.ResultDTO;
//import com.cosfo.ordercenter.client.resp.OrderDTO;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 11:13
// */
//@Component
//public class ExchangeAfterSaleExecutor extends AfterSaleAbstractExecutor {
//
//    @Resource
//    private OrderAfterSaleService afterSaleService;
//    @Resource
//    private OrderAfterSaleMapper orderAfterSaleMapper;
//
//    @Override
//    public ResultDTO reviewSuccess(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO requestContextInfoDTO, OrderAfterSale afterSale, OrderDTO order) {
//        // 变更状态
//        OrderAfterSale update = new OrderAfterSale();
//        update.setId(afterSale.getId());
//        update.setStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode());
//        update.setHandleTime(LocalDateTime.now());
//        update.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
//        update.setOperatorName(requestContextInfoDTO.getTenantName());
//        update.setRecycleTime(orderAfterSaleAuditDTO.getRecycleTime());
//        update.setAmount(orderAfterSaleAuditDTO.getAmount());
//        orderAfterSaleMapper.updateByPrimaryKeySelective(update);
//        return ResultDTO.success();
//    }
//
//}

//package com.cosfo.manage.order.executor;
//
//import com.cosfo.aftersale.common.context.OrderAfterSaleStatusEnum;
//import com.cosfo.aftersale.common.context.OrderAfterSaleTypeEnum;
//import com.cosfo.aftersale.model.dto.OrderAfterSaleAuditDTO;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import com.cosfo.aftersale.service.OrderAfterSaleService;
//import com.cosfo.manage.common.context.WarehouseTypeEnum;
//import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
//import com.cosfo.manage.common.result.ResultDTO;
//import com.cosfo.manage.common.result.ResultDTOEnum;
//import com.cosfo.manage.common.util.AssertCheckParams;
//import com.cosfo.manage.order.service.OrderAsyncService;
//import com.cosfo.manage.tenant.dao.TenantReturnAddressDao;
//import com.cosfo.ordercenter.client.resp.OrderDTO;
//import net.xianmu.common.exception.BizException;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 11:11
// */
//@Component
//public class ReturnRefundAfterSaleExecutor extends AfterSaleAbstractExecutor {
//
//    @Resource
//    private OrderAsyncService orderAsyncService;
//    @Resource
//    private OrderAfterSaleService afterSaleService;
//
//    @Override
//    public ResultDTO reviewSuccess(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO requestContextInfoDTO, OrderAfterSale afterSale, OrderDTO order) {
//        // 配送仓类型
//        Integer warehouseType = afterSale.getWarehouseType();
//        // 售后类型
//        Integer afterSaleType = afterSale.getAfterSaleType();
//        // 售后状态
//        Integer afterSaleStatus = afterSale.getStatus();
//
//        if(!WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType) && !WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
//            throw new BizException("不支持的配送仓类型：" + warehouseType);
//        }
//
//        // 退货退款售后只有【配送后售后】能发起
//        if (OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(afterSaleType)) {
//            throw new BizException("配送前售后不支持退货退款");
//        }
//
//        // 如果是【待退款】状态，说明是第二次审核
//        boolean secondAuditFlag = OrderAfterSaleStatusEnum.WAIT_REFUND.getCode().equals(afterSaleStatus);
//
//        // 退货退款两次审核，第一次审核仅更新状态为【待退货】，二次审核，通过直接退款，二次审核前状态为【待退款】（mq消息监听等待退货单入库成功，状态更新为【待退款】）
//        if(!secondAuditFlag) {
//            if(WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType) && orderAfterSaleAuditDTO.getReturnAddressId() == null){
//                throw new BizException("退回地址必填");
//            }
//
//            // 更新售后单状态
//            OrderAfterSale update = new OrderAfterSale();
//            update.setId(afterSale.getId());
//            // 更新为【待退货】
//            update.setStatus(OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getCode());
//            update.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
//            update.setHandleTime(LocalDateTime.now());
//            update.setRecycleTime(orderAfterSaleAuditDTO.getRecycleTime());
//            update.setTotalPrice(orderAfterSaleAuditDTO.getTotalPrice());
//            update.setAmount(orderAfterSaleAuditDTO.getAmount());
//            update.setReturnAddressId(orderAfterSaleAuditDTO.getReturnAddressId());
//            update.setReturnWarehouseNo(orderAfterSaleAuditDTO.getReturnWarehouseNo());
//            afterSaleService.updateRecordByIdAndSourceStatus(afterSale.getId(), OrderAfterSaleStatusEnum.UNAUDITED.getCode(), update);
//        }else {
//            if(WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)){
//                // 自营仓校验入库数量参数
//                AssertCheckParams.notNull(orderAfterSaleAuditDTO.getQuantity(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "入库数量不能为空");
//                AssertCheckParams.notNull(orderAfterSaleAuditDTO.getActualQuantity(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "入库数量不能为空");
//            }
//            // 无论无仓有仓，第二次审核，统一发起退款
//            OrderAfterSale update = new OrderAfterSale();
//            update.setId(afterSale.getId());
//            update.setStatus(OrderAfterSaleStatusEnum.REFUNDING.getCode());
//            update.setSecondHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
//            update.setAmount(orderAfterSaleAuditDTO.getActualQuantity());
//            update.setApplyQuantity(orderAfterSaleAuditDTO.getQuantity());
//            update.setUpdateTime(LocalDateTime.now());
//            afterSaleService.updateRecordByIdAndSourceStatus(afterSale.getId(), OrderAfterSaleStatusEnum.WAIT_REFUND.getCode(), update);
//
//            orderAsyncService.payRefund(afterSale.getId(), afterSale.getOrderId(), afterSale.getTenantId(), orderAfterSaleAuditDTO.getTotalPrice());
//        }
//
//        return ResultDTO.success();
//    }
//}

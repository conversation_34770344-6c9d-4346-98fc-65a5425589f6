package com.cosfo.manage.jindie.service.impl;

import com.cosfo.manage.jindie.service.JindieUnitService;
import com.cosfo.manage.jindie.util.JindieApiClientUtil;
import com.kingdee.service.ApiClient;
import com.kingdee.service.ApiException;
import com.kingdee.service.data.api.UnitApi;
import com.kingdee.service.data.entity.MaterialUnitDetailRes;
import com.kingdee.service.data.entity.MaterialUnitListRes;
import com.kingdee.service.data.entity.UnitMaterialUnitListReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 金蝶计量单位服务实现
 */
@Service
@Slf4j
public class JindieUnitServiceImpl implements JindieUnitService {

    @Resource
    private JindieApiClientUtil jindieApiClientUtil;

    @Override
    public MaterialUnitDetailRes getMaterialUnitDetail(String id) {
        try {
            // 获取配置好的API客户端
            ApiClient apiClient = jindieApiClientUtil.getApiClient();
            UnitApi unitApi = new UnitApi(apiClient);

            // 调用API获取物料计量单位详情
            MaterialUnitDetailRes detailRes = unitApi.unitMaterialUnitDetail(id);
            log.info("获取物料计量单位详情成功, id: {}", id);
            return detailRes;
        } catch (ApiException e) {
            log.error("获取物料计量单位详情异常, id: {}, 错误码: {}, 错误信息: {}",
                    id, e.getCode(), e.getMessage(), e);
            throw new RuntimeException("获取物料计量单位详情异常: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取物料计量单位详情异常, id: {}", id, e);
            throw new RuntimeException("获取物料计量单位详情异常: " + e.getMessage(), e);
        }
    }

    @Override
    public MaterialUnitListRes getMaterialUnitList(UnitMaterialUnitListReq request) {
        try {
            // 获取配置好的API客户端
            ApiClient apiClient = jindieApiClientUtil.getApiClient();
            UnitApi unitApi = new UnitApi(apiClient);

            // 调用API获取物料计量单位列表
            MaterialUnitListRes listRes = unitApi.unitMaterialUnitList(request);
            log.info("获取物料计量单位列表成功");
            return listRes;
        } catch (ApiException e) {
            log.error("获取物料计量单位列表异常, 错误码: {}, 错误信息: {}",
                    e.getCode(), e.getMessage(), e);
            throw new RuntimeException("获取物料计量单位列表异常: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取物料计量单位列表异常", e);
            throw new RuntimeException("获取物料计量单位列表异常: " + e.getMessage(), e);
        }
    }
}

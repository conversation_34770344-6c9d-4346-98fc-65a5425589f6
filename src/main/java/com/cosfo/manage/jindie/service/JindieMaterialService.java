package com.cosfo.manage.jindie.service;

import com.kingdee.service.data.entity.MaterialDetailReply;
import com.kingdee.service.data.entity.MaterialListReply;
import com.kingdee.service.data.entity.MaterialMaterialListReq;

/**
 * 金蝶物料服务接口
 */
public interface JindieMaterialService {


    /**
     * 获取物料详情
     *
     * @param id 物料ID
     * @param number 物料编码
     * @return 物料详情
     */
    MaterialDetailReply getMaterialDetail(String id, String number);

    /**
     * 获取物料列表
     *
     * @param request 查询请求
     * @return 物料列表
     */
    MaterialListReply getMaterialList(MaterialMaterialListReq request);

}

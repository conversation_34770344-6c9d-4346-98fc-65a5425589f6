package com.cosfo.manage.jindie.service.impl;

import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.jindie.facade.JindieFacade;
import com.cosfo.manage.jindie.service.JindieMaterialService;
import com.cosfo.manage.jindie.service.JindieSalesReturnService;
import com.cosfo.manage.jindie.util.JindieApiClientUtil;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.kingdee.service.ApiClient;
import com.kingdee.service.ApiException;
import com.kingdee.service.data.api.SalInBoundApi;
import com.kingdee.service.data.entity.*;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 金蝶销售退货单服务实现
 */
@Service
@Slf4j
public class JindieSalesReturnServiceImpl implements JindieSalesReturnService {

    @Resource
    private JindieApiClientUtil jindieApiClientUtil;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private JindieMaterialService jindieMaterialService;
    @Resource
    private JindieFacade jindieFacade;

    /**
     * 推送售后单到金蝶
     *
     * @param afterSaleResp 售后单
     * @return 推送结果
     */
    @Override
    public SaveReply pushSalesReturnToJindie(OrderAfterSaleResp afterSaleResp) {
        try {
            // 查询订单信息
            OrderResp orderResp = orderQueryFacade.queryById(afterSaleResp.getOrderId());
            if (orderResp == null) {
                log.error("订单不存在, orderId: {}", afterSaleResp.getOrderId());
                throw new ParamsException("订单不存在");
            }

            // 查询订单项信息
            List<OrderItemAndSnapshotResp> orderItems = orderItemQueryFacade.queryByOrderId(afterSaleResp.getOrderId());
            if (CollectionUtils.isEmpty(orderItems)) {
                log.error("订单项不存在, orderId: {}", afterSaleResp.getOrderId());
                throw new ParamsException("订单项不存在");
            }

            // 查询售后单对应的订单项
            OrderItemAndSnapshotResp orderItem = orderItems.stream()
                    .filter(item -> item.getOrderItemId().equals(afterSaleResp.getOrderItemId()))
                    .findFirst()
                    .orElseThrow(() -> {
                        log.error("售后单对应的订单项不存在, orderItemId: {}", afterSaleResp.getOrderItemId());
                        return new ParamsException("售后单对应的订单项不存在");
                    });

            // 构建金蝶销售退货单请求
            SalInBoundSaveReq request = buildSalInBoundSaveReq(afterSaleResp, orderResp, orderItem);

            // 获取配置好的API客户端
            ApiClient apiClient = jindieApiClientUtil.getApiClient();
            SalInBoundApi salInBoundApi = new SalInBoundApi(apiClient);

            // 推送到金蝶
            SaveReply saveReply = salInBoundApi.salInBoundSave(request);
            log.info("推送售后单到金蝶成功, afterSaleOrderNo: {}, response: {}", afterSaleResp.getAfterSaleOrderNo(), saveReply);
            return saveReply;
        } catch (ApiException e) {
            log.error("推送售后单到金蝶异常, afterSaleOrderNo: {}, 错误码: {}, 错误信息: {}",
                    afterSaleResp.getAfterSaleOrderNo(), e.getCode(), e.getMessage(), e);
            throw new RuntimeException("推送售后单到金蝶异常: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("推送售后单到金蝶异常, afterSaleOrderNo: {}", afterSaleResp.getAfterSaleOrderNo(), e);
            throw new RuntimeException("推送售后单到金蝶异常: " + e.getMessage(), e);
        }
    }


    @Override
    public int batchPushSalesReturnToJindie(List<OrderAfterSaleResp> afterSaleOrders) {
        if (CollectionUtils.isEmpty(afterSaleOrders)){
            return 0;
        }

        int successCount = 0;
        for (OrderAfterSaleResp afterSaleResp : afterSaleOrders) {
            try {
                SaveReply response = pushSalesReturnToJindie(afterSaleResp);
                if (response != null && response.getIds() != null && !response.getIds().isEmpty()) {
                    successCount++;
                    log.info("推送售后单到金蝶成功, afterSaleOrderNo: {}", afterSaleResp.getAfterSaleOrderNo());
                } else {
                    log.error("推送售后单到金蝶失败, afterSaleOrderNo: {}, response: {}",
                            afterSaleResp.getAfterSaleOrderNo(), response);
                }
            } catch (Exception e) {
                log.error("推送售后单到金蝶异常, afterSaleOrderNo: {}", afterSaleResp.getAfterSaleOrderNo(), e);
            }
        }

        log.info("批量推送售后单到金蝶完成, 总数: {}, 成功数: {}", afterSaleOrders.size(), successCount);
        return successCount;
    }

    /**
     * 构建金蝶销售退货单请求
     *
     * @param afterSaleResp 售后单
     * @param orderResp     订单
     * @param orderItem     订单项
     * @return 金蝶销售退货单请求
     */
    private SalInBoundSaveReq buildSalInBoundSaveReq(OrderAfterSaleResp afterSaleResp, OrderResp orderResp, OrderItemAndSnapshotResp orderItem) throws ApiException {
        SalInBoundSaveReq request = new SalInBoundSaveReq();

        // 设置基本信息
//        request.setBillDate(afterSaleResp.getFinishedTime());
        request.setCustomerId(jindieFacade.customerCustomerDetail(String.valueOf(afterSaleResp.getStoreNo())));

        // 设置商品分录
        MaterialDetailReply materialDetail = jindieMaterialService.getMaterialDetail (null, orderItem.getItemCode ());

        List<SalInBoundSaveReqMaterialEntity> materialEntities = new ArrayList<>();
        SalInBoundSaveReqMaterialEntity materialEntity = new SalInBoundSaveReqMaterialEntity();
        materialEntity.setMaterialNumber(materialDetail == null ? orderItem.getItemCode () : materialDetail.getNumber ());
        materialEntity.setQty(orderItem.getAmount().doubleValue());
        materialEntity.setPrice(orderItem.getPayablePrice().doubleValue());
        materialEntity.setStockId(materialDetail == null?"":materialDetail.getStockId());
        materialEntity.setUnitId(jindieFacade.unitMeasureUnitMap().getOrDefault(orderItem.getSpecificationUnit(), orderItem.getSpecificationUnit()));
        materialEntities.add(materialEntity);
        request.setMaterialEntity(materialEntities);

        // 设置退款账户
        List<SalInBoundSaveReqPaymentEntry> paymentEntries = new ArrayList<>();
        SalInBoundSaveReqPaymentEntry paymentEntry = new SalInBoundSaveReqPaymentEntry();
        paymentEntry.setPaidAmount(afterSaleResp.getTotalPrice().doubleValue());
        paymentEntry.setPayTypeId("9999999"); // TODO 默认支付方式
        paymentEntry.setSettleAccountId("2203092555199972352"); // TODO 收款账户. 金蝶内部的
        paymentEntries.add(paymentEntry);
        request.setPaymentEntry(paymentEntries);

        return request;
    }

}

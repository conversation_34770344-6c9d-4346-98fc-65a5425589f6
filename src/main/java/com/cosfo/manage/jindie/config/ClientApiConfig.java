package com.cosfo.manage.jindie.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.kingdee.service.ApiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class ClientApiConfig {
    @NacosValue(value = "${jindie.zhenchawu.clientId:310973}", autoRefreshed = true)
    public String clientId;

    @NacosValue(value = "${jindie.zhenchawu.clientSecret:dedf4950057a504efa30e7c4437fec25}", autoRefreshed = true)
    public String clientSecret;

    @PostConstruct
    public void init()   {
        ApiClient defaultApiClient = com.kingdee.service.Configuration.getDefaultApiClient();
        defaultApiClient.setClientId(clientId);
        defaultApiClient.setClientSecret(clientSecret);
    }
}
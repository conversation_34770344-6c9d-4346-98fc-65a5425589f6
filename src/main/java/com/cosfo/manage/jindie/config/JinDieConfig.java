package com.cosfo.manage.jindie.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;


@Slf4j
@Configuration
@Data
public class JinDieConfig {

    @NacosValue(value = "${jindie.zhenchawu.appKey:CzZTCZkB}", autoRefreshed = true)
    public String zcwAppKey;

    @NacosValue(value = "${jindie.zhenchawu.tenantId:2}", autoRefreshed = true)
    public Long zcwTenantId;

    @NacosValue(value = "${jindie.zhenchawu.appsecret:a1a4d396c075c2cf567e66b79b18a6ce89de0b85}", autoRefreshed = true)
    public String zcwAppSecret;
}
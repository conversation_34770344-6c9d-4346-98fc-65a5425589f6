/**
  * Copyright 2024 bejson.com 
  */
package com.cosfo.manage.keruyun.vo;
import lombok.Data;


@Data
public class OrderItemVoList {

    private String itemType;
    private boolean giftFlag;
    private boolean weighFlag;
    private boolean tempFlag;
    private boolean promoFlag;
    private String bigTypeName;
    private String midTypeName;
    private String itemCode;
    private String itemName;
    private String saleStatusType;
    private String saleStatusTypeCode;
    private String id;
    private String parentId;
    private String productionDeptId;
    private String unitName;
    private String specName;
    private String specNameConcat;
    private String itemPrice;
    private String salePrice;
//    private List<PracticeVoList> practiceVoList; 做法
    private String itemSubject;
    private String quantity;
    private String itemSaleAmt;
    private String extraFeeApportionAmt;
    private String itemPromoApportionAmt;
    private String itemReceivedAmt;
//    private Children children;
    private String itemId;
    private String itemSkuId;
}
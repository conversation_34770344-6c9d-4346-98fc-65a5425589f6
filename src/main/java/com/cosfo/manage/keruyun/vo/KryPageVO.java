package com.cosfo.manage.keruyun.vo;
import java.util.List;

/**
 * Auto-generated: 2023-11-30 13:45:53
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class KryPageVO<T> {

    private int totalRows;
    private int pageSize;
    private List<T> items;
    public void setTotalRows(int totalRows) {
         this.totalRows = totalRows;
     }
     public int getTotalRows() {
         return totalRows;
     }

    public void setPageSize(int pageSize) {
         this.pageSize = pageSize;
     }
     public int getPageSize() {
         return pageSize;
     }

    public void setItems(List<T> items) {
         this.items = items;
     }
     public List<T> getItems() {
         return items;
     }

    public boolean hasNext(int totalRows,int pageSize,int pageIndex) {
        int totalPages = (totalRows / pageSize);
        // 处理总页数的余数
        if (totalRows % pageSize != 0) {
            totalPages++;
        }
        // 判断当前页是否小于总页数
        return pageIndex < totalPages;
    }
}
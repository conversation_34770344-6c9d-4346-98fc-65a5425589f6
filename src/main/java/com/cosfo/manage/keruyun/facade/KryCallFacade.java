package com.cosfo.manage.keruyun.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.manage.keruyun.sign.SignDTO;
import com.cosfo.manage.keruyun.sign.SignUtil;
import com.cosfo.manage.keruyun.vo.KryResponse;
import com.cosfo.manage.keruyun.vo.ShopTokenVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KryCallFacade {

    //读取超时为60s
    private static final long READ_TIMEOUT = 30000;
    //写入超时为60s
    private static final long WRITE_TIMEOUT = 30000;
    //连接超时为60s
    private static final long CONNECT_TIMEOUT = 30000;

    private static OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .readTimeout(READ_TIMEOUT, TimeUnit.MILLISECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.MILLISECONDS)
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.MILLISECONDS).build ();

    public static String projectVersion = "";
    public static String projectName = "test-cosfo";

    public static String url = "https://openapi.keruyun.com";

    @NacosValue(value = "${kry.appKey:316ab3fdf4d8453eb7762c48acd58b60}", autoRefreshed = true)
    private String appKey;

    @NacosValue(value = "${kry.appSecret:3620ee3d80f78de72a845ff9ac3d8b02}", autoRefreshed = true)
    private String appSecret;


    private final LoadingCache<Long, String> TOKEN_CACHE = CacheBuilder.newBuilder()
            .maximumSize(20)
            .recordStats()
            .expireAfterWrite(Duration.ofSeconds(600L))//10 分钟
            .build(new CacheLoader<Long, String> () {
                @Override
                public String load(Long orgId) throws Exception {
                    return getTokenInternal(orgId);
                }
            });

    private String getTokenInternal(Long orgId) {
        String responseStr = getCall(KryAPI.GET_SHOP_TOKEN, AuthType.SHOP, orgId, appSecret);
        KryResponse response = JSON.parseObject (responseStr,KryResponse.class);
        if (0 == response.getCode()) {
            ShopTokenVO result = JSON.parseObject(JSON.toJSONString (response.getResult ()),ShopTokenVO.class);
            return result.getToken();
        } else {
            log.info ("获取门店token异常，orgid={}，response={}",orgId,response);
            throw new RuntimeException(response.getMessage());
        }
    }


    public String getToken(Long orgId) {
        try {
            log.info("从缓存中获取门店token:{}", orgId);
            return TOKEN_CACHE.get(orgId);
        } catch (Exception e) {
            log.warn ("从缓存中获取门店token:{}", orgId, e);
            return getTokenInternal(orgId);
        }
    }

    public String getCall(KryAPI api, AuthType authType, Long orgId, String appSecret) {
        SignDTO dto = getSignDto(Method.GET, authType, orgId);
        Request request = new Request.Builder()
                .url(getUrlAndParamStr(api, dto, appSecret))
                .addHeader(projectName, projectVersion)
                .build();
        return execCall(request);
    }

    public String postCall(KryAPI api, AuthType authType, Long orgId, String token, Object body) {
        SignDTO dto = getSignDto(Method.POST, authType, orgId, body);

        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), Objects.nonNull(body) ? JSON.toJSONString (body) : "");
        Request request = new Request.Builder()
                .url(getUrlAndParamStr(api, dto, token))
                .addHeader(projectName, projectVersion)
                .post(requestBody)
                .build();
        return execCall(request);
    }

    private String getUrlAndParamStr(KryAPI api, SignDTO dto, String token) {
        StringBuilder sb = new StringBuilder(url)
                .append(api.getUri())
                .append("?appKey=").append(dto.getAppKey());
        switch (dto.getAuthType()) {
            case BRAND:
                sb.append("&brandId=").append(dto.getOrgId());
                break;
            default:
                sb.append("&shopIdenty=").append(dto.getOrgId());
        }
        sb.append("&version=2.0&timestamp=").append(dto.getTimestamp())
                .append("&sign=").append(SignUtil.sign(dto, token));
        return sb.toString();
    }

    private String execCall(Request request) {
        Response response = null;
        try {
            log.info("kry_request:{}", JSON.toJSONString (request));
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseStr = response.body().string();
                log.info("kry_response:{}", responseStr);
                return responseStr;
            }
        } catch (Exception e) {
            log.error("OkHttpClient Error:{}", e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("OkHttpClient Error:", e.getCause());
        } finally {
            if (response != null) {
                response.close();
            }
        }
        throw new RuntimeException("OkHttpClient Error:" + response.message());
    }

    private SignDTO getSignDto(Method method, AuthType authType, Long orgId) {
        return getSignDto(method, authType, orgId, null);
    }

    private SignDTO getSignDto(Method method, AuthType authType, Long orgId, Object requestBody) {
        return SignDTO.builder()
                .method(method)
                .authType(authType)
                .orgId(orgId)
                .appKey(appKey)
                .timestamp(System.currentTimeMillis() / 1000)
                .version("2.0")
                .requestBody(requestBody)
                .build();
    }
}

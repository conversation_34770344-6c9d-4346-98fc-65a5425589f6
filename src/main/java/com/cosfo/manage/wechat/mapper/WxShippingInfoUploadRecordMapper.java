package com.cosfo.manage.wechat.mapper;

import com.cosfo.manage.wechat.model.po.WxShippingInfoUploadRecord;

public interface WxShippingInfoUploadRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WxShippingInfoUploadRecord record);

    int insertSelective(WxShippingInfoUploadRecord record);

    WxShippingInfoUploadRecord selectByPrimaryKey(Long id);

    WxShippingInfoUploadRecord selectByTransactionId(String transactionId);

    int updateByPrimaryKeySelective(WxShippingInfoUploadRecord record);

    int updateByPrimaryKey(WxShippingInfoUploadRecord record);
}
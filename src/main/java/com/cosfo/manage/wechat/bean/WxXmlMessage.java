package com.cosfo.manage.wechat.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.Data;

import java.util.List;

@Data
@XStreamAlias("xml")
public class WxXmlMessage {

    @XStreamAlias("Encrypt")
    private String encrypt;
    @XStreamAlias("ToUserName")
    private String toUserName;
    @XStreamAlias("FromUserName")
    private String fromUserName;
    @XStreamAlias("CreateTime")
    private long createTime;
    @XStreamAlias("MsgType")
    private String msgType;
    @XStreamAlias("MsgId")
    private Long msgId;
    @XStreamAlias("Status")
    private String status;
    @XStreamAlias("Content")
    private String content;
    @XStreamAlias("PicUrl")
    private String picUrl;
    @XStreamAlias("MediaId")
    private String mediaId;
    @XStreamAlias("Format")
    private String format;
    @XStreamAlias("Recognition")
    private String recognition;
    @XStreamAlias("ThumbMediaId")
    private String thumbMediaId;
    @XStreamAlias("Location_X")
    private Double locationX;
    @XStreamAlias("Location_Y")
    private Double locationY;
    @XStreamAlias("Scale")
    private Integer scale;
    @XStreamAlias("Label")
    private String label;
    @XStreamAlias("Title")
    private String title;
    @XStreamAlias("Description")
    private String description;
    @XStreamAlias("Url")
    private String url;
    @XStreamAlias("Event")
    private String event;
    @XStreamAlias("EventKey")
    private String eventKey;
    @XStreamAlias("MenuId")
    private String menuId;
    @XStreamAlias("Ticket")
    private String ticket;
    @XStreamAlias("Latitude")
    private Double latitude;
    @XStreamAlias("Longitude")
    private Double longitude;
    @XStreamAlias("Precision")
    private Double precision;
    @XStreamAlias("SessionFrom")
    private String sessionFrom;
    @XStreamAlias("Reason")
    private String reason;
    @XStreamAlias("ScreenShot")
    private String screenShot;
    @XStreamAlias("SuccTime")
    private Long succTime;
    @XStreamAlias("FailTime")
    private Long failTime;
    @XStreamAlias("DelayTime")
    private Long delayTime;
    //订阅消息事件
    @XStreamAlias("SubscribeMsgPopupEvent")
    private SubscribeMsgPopupEventList subscribeMsgPopupEvent;
    @XStreamAlias("SubscribeMsgChangeEvent")
    private SubscribeMsgPopupEventList subscribeMsgChangeEvent;
    //以下消息字段参考微信交易组件回调消息定义
    @XStreamAlias("AccountAuditResult")
    private AccountAuditResult accountAuditResult;
    @XStreamAlias("OpenProductSpuAudit")
    private OpenProductSpuAudit openProductSpuAudit;
    @XStreamAlias("QualificationAuditResult")
    private QualificationAuditResult qualificationAuditResult;
    @XStreamAlias("OrderUpdateNotification")
    private OrderUpdateNotification orderUpdateNotification;
    //商家接入结果
    @Data
    public static class AccountAuditResult{
        @XStreamAlias("status")
        private Integer status;
        @XStreamAlias("reject_reason")
        private String rejectReason;
    }

    //商品审核结果
    @Data
    public static class OpenProductSpuAudit{
        @XStreamAlias("out_product_id")
        private String outProductId;
        @XStreamAlias("product_id")
        private String productId;
        @XStreamAlias("status")
        private Integer status;
        @XStreamAlias("handler_type")
        private Integer handlerType;
        @XStreamAlias("seq_qc_id")
        private Long seqQcId;
        @XStreamAlias("reject_reason")
        private String rejectReason;
    }

    //类目、品牌审核结果
    @Data
    public static class QualificationAuditResult{
        @XStreamAlias("audit_id")
        private String audit_id;
        @XStreamAlias("status")
        private Integer status;
        @XStreamAlias("audit_type")
        private Integer audit_type;
        @XStreamAlias("reject_reason")
        private String rejectReason;
        @XStreamAlias("brand_id")
        private Integer brandId;
    }

    @Data
    public static class OrderUpdateNotification{
        @XStreamAlias("update_type")
        private Integer updateType;
        @XStreamAlias("out_order_id")
        private String outOrderId;
        @XStreamAlias("openid")
        private String openid;
    }

    @Data
    public static class SubscribeMsgPopupEvent {
        @XStreamAlias("TemplateId")
        private String templateId;
        @XStreamAlias("SubscribeStatusString")
        private String subscribeStatusString;
        @XStreamAlias("PopupScene")
        private String popupScene;
    }

    @Data
    public static class SubscribeMsgPopupEventList {
        @XStreamImplicit(itemFieldName="List")
        private List<SubscribeMsgPopupEvent> list;
    }
}

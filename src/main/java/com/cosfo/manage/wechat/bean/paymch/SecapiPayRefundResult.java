package com.cosfo.manage.wechat.bean.paymch;

import com.cosfo.manage.wechat.bean.DynamicField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class SecapiPayRefundResult extends MchBase implements DynamicField {

    private static Logger logger = LoggerFactory.getLogger(SecapiPayRefundResult.class);

    private String device_info;

    private String transaction_id;

    private String out_trade_no;

    private String out_refund_no;

    private String refund_id;

    private String refund_channel;

    private Integer refund_fee;

    private Integer total_fee;

    private String fee_type;

    private Integer settlement_refund_fee; // 应结退款金额

    private Integer settlement_total_fee; // 应结订单金额

    private Integer cash_fee;

    private Integer cash_refund_fee;

    private Integer coupon_refund_fee;

    private Integer coupon_refund_count;

    // 代金券或立减优惠
    // @since 2.8.5
    // 使用 getCoupons() 获取 List.
    // List.size() = coupon_count
    @XmlTransient
    private List<Coupon> coupons;

    @Override
    public void buildDynamicField(Map<String, String> dataMap) {
        if(dataMap != null){
            String coupon_countStr = dataMap.get("coupon_refund_count");
            if(coupon_countStr != null){
                List<Coupon> list = new ArrayList<Coupon>();
                for (int i = 0; i < Integer.parseInt(coupon_countStr); i++) {
                    Coupon coupon = new Coupon(
                            dataMap.get("coupon_type_"+i),
                            dataMap.get("coupon_refund_id_"+i),
                            Integer.parseInt(dataMap.get("coupon_refund_fee_"+i)),
                            i);
                    list.add(coupon);
                }
                this.coupons = list;
            }
        }
    }
}

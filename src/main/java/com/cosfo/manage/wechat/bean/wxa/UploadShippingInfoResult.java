package com.cosfo.manage.wechat.bean.wxa;

import com.cosfo.manage.wechat.bean.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 *
 * @author: xiaowk
 * @time: 2023/7/8 下午7:37
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class UploadShippingInfoResult  extends BaseResult {

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}

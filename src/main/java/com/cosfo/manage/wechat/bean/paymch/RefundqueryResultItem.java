package com.cosfo.manage.wechat.bean.paymch;

import lombok.Data;

import java.util.List;

@Data
public class RefundqueryResultItem {

    private String out_refund_no;
    private String refund_id;
    private String refund_channel;
    private Integer refund_fee;
    private Integer settlement_refund_fee;

    private String coupon_type;
    private Integer coupon_refund_fee;
    private Integer coupon_refund_count;
    private String coupon_refund;

    private String refund_status;
    private String refund_recv_accout;

    private Integer n;

    private List<Coupon> coupons;
}

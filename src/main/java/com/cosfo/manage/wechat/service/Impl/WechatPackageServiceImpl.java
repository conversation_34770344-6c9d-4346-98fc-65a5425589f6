package com.cosfo.manage.wechat.service.Impl;

import com.cosfo.manage.common.context.EventType;
import com.cosfo.manage.common.context.VersionStatus;
import com.cosfo.manage.wechat.bean.WxXmlMessage;
import com.cosfo.manage.wechat.mapper.WechatLiteConfigMapper;
import com.cosfo.manage.wechat.mapper.WechatTemplatePackageMapper;
import com.cosfo.manage.wechat.model.po.WechatLiteConfig;
import com.cosfo.manage.wechat.model.po.WechatTemplatePackage;
import com.cosfo.manage.wechat.service.WechatPackageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class WechatPackageServiceImpl implements WechatPackageService {

    @Resource
    WechatLiteConfigMapper wechatLiteConfigMapper;
    @Resource
    WechatTemplatePackageMapper wechatTemplatePackageMapper;

    @Override
    public void changePackageStateByAuditMsg(WxXmlMessage message) {
        //事件相关消息
        WechatLiteConfig wxLiteConfig = wechatLiteConfigMapper.selectByOriginalId(message.getToUserName());
        if(null != wxLiteConfig) {
            WechatTemplatePackage wechatTemplatePackage = wechatTemplatePackageMapper.selectPackageByStatus(wxLiteConfig.getAppid(), VersionStatus.AUDIT_ING.getCode());
            if(null != wechatTemplatePackage){
                switch (message.getEvent()){
                    case EventType.WEAPP_AUDIT_SUCCESS:{
                        wechatTemplatePackage.setPkgStatus(VersionStatus.AUDIT_PASS.getCode());
                        wechatTemplatePackageMapper.updateByPrimaryKeySelective(wechatTemplatePackage);
                        break;
                    }
                    case EventType.WEAPP_AUDIT_FAIL:{
                        wechatTemplatePackage.setPkgStatus(VersionStatus.AUDIT_UNPASS.getCode());
                        wechatTemplatePackage.setRemark(message.getReason());
                        wechatTemplatePackageMapper.updateByPrimaryKeySelective(wechatTemplatePackage);
                        break;
                    }
                    case EventType.WEAPP_AUDIT_DELAY:
                        wechatTemplatePackage.setPkgStatus(VersionStatus.AUDIT_DELAY.getCode());
                        if(null != message.getReason()) {
                            wechatTemplatePackage.setRemark(message.getReason());
                        }
                        wechatTemplatePackageMapper.updateByPrimaryKeySelective(wechatTemplatePackage);
                        break;
                    default:
                        break;
                }
            }
        }
    }
}

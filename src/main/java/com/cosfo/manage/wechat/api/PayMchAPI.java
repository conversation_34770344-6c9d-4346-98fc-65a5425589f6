package com.cosfo.manage.wechat.api;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.bill.model.dto.SettleInfoRequestDTO;
import com.cosfo.manage.bill.model.dto.SettleInfoResponseDTO;
import com.cosfo.manage.common.config.HuiFuConfig;
import com.cosfo.manage.common.constant.HuiFuPaymentConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.model.dto.HuiFuDTO;
import com.cosfo.manage.common.util.Global;
import com.cosfo.manage.common.util.MapUtil;
import com.cosfo.manage.common.util.SignatureUtil;
import com.cosfo.manage.common.util.XMLConverUtil;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import com.cosfo.manage.wechat.bean.paymch.*;
import com.cosfo.manage.wechat.client.LocalHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.zip.GZIPInputStream;

@Slf4j
public class PayMchAPI extends BaseAPI{

    private static ThreadLocal<Boolean> sandboxnew = new ThreadLocal<Boolean>();

    /**
     * 仿真测试 开始
     * @since 2.8.6
     */
    public static void sandboxnewStart(){
        sandboxnew.set(true);
    }

    /**
     * 仿真测试 结束
     * @since 2.8.6
     */
    public static void sandboxnewEnd(){
        sandboxnew.remove();
    }

    /**
     * 获取支付base URI路径
     * @return baseURI
     */
    private static String baseURI(){
        if(sandboxnew.get() == null){
            return MCH_URI;
        }else{
            return MCH_URI + "/sandboxnew";
        }
    }

    /**
     * 获取支付汇付天下base URI路径
     *
     * @return baseURI
     */
    public static String huiFuBaseURI() {
        return HUIFU_URI;
    }

    /**
     * 获取仿真测试验签秘钥
     * @param mch_id mch_id
     * @param key key
     * @return sandbox_signkey
     * @since 2.8.13
     */
    public static SandboxSignkey sandboxnewPayGetsignkey(String mch_id, String key){
        MchBaseResult mchBaseResult = new MchBaseResult();
        mchBaseResult.setMch_id(mch_id);
        mchBaseResult.setNonce_str(UUID.randomUUID().toString().replace("-", ""));
        Map<String,String> map = MapUtil.objectToMap(mchBaseResult);
        String sign = SignatureUtil.generateSign(map,mchBaseResult.getSign_type(),key);
        mchBaseResult.setSign(sign);
        String closeorderXML = XMLConverUtil.convertToXML(mchBaseResult);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(MCH_URI + "/sandboxnew/pay/getsignkey")
                .setEntity(new StringEntity(closeorderXML, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeXmlResult(httpUriRequest, SandboxSignkey.class, mchBaseResult.getSign_type(), key);
    }

    /**
     * 统一下单
     * @param unifiedorder unifiedorder
     * @param key key
     * @return UnifiedorderResult
     */
    public static UnifiedorderResult payUnifiedorder(UnifiedOrder unifiedorder, String key){
        Map<String,String> map = MapUtil.objectToMap(unifiedorder, "detail", "scene_info");
        //@since 2.8.8 detail 字段签名处理
        if(unifiedorder.getDetail() != null){
            map.put("detail", JSON.toJSONString(unifiedorder.getDetail()));
        }
        //@since 2.8.21 scene_info 字段签名处理
        if(unifiedorder.getScene_info() != null){
            map.put("scene_info", JSON.toJSONString(unifiedorder.getScene_info()));
        }
        if(key != null){
            String sign = SignatureUtil.generateSign(map,unifiedorder.getSign_type(),key);
            unifiedorder.setSign(sign);
        }
        String unifiedorderXML = XMLConverUtil.convertToXML(unifiedorder);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(baseURI()+ "/pay/unifiedorder")
                .setEntity(new StringEntity(unifiedorderXML,Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeXmlResult(httpUriRequest,UnifiedorderResult.class,unifiedorder.getSign_type(),key);
    }


    /**
     * 查询订单
     * @param mchOrderquery mchOrderquery
     * @param key key
     * @return MchOrderInfoResult
     */
    public static MchOrderInfoResult payOrderQuery(MchOrderQuery mchOrderquery, String key){
        Map<String,String> map = MapUtil.objectToMap(mchOrderquery);
        String sign = SignatureUtil.generateSign(map,mchOrderquery.getSign_type(),key);
        mchOrderquery.setSign(sign);
        String closeorderXML = XMLConverUtil.convertToXML(mchOrderquery);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(baseURI()+ "/pay/orderquery")
                .setEntity(new StringEntity(closeorderXML,Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeXmlResult(httpUriRequest,MchOrderInfoResult.class,mchOrderquery.getSign_type(),key);
    }



    /**
     * 关闭订单
     * @param closeorder closeorder
     * @param key 商户支付密钥
     * @return MchBaseResult
     */
    public static MchBaseResult payCloseorder(Closeorder closeorder,String key){
        Map<String,String> map = MapUtil.objectToMap(closeorder);
        String sign = SignatureUtil.generateSign(map,closeorder.getSign_type(),key);
        closeorder.setSign(sign);
        String closeorderXML = XMLConverUtil.convertToXML(closeorder);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(baseURI()+ "/pay/closeorder")
                .setEntity(new StringEntity(closeorderXML,Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeXmlResult(httpUriRequest,MchBaseResult.class,closeorder.getSign_type(),key);
    }


    /**
     * 申请退款
     *
     * 注意：
     *	1.交易时间超过半年的订单无法提交退款；
     *	2.微信支付退款支持单笔交易分多次退款，多次退款需要提交原支付订单的商户订单号和设置不同的退款单号。一笔退款失败后重新提交，要采用原来的退款单号。总退款金额不能超过用户实际支付金额。
     * @param secapiPayRefund secapiPayRefund
     * @param key 商户支付密钥
     * @return SecapiPayRefundResult
     */
    public static SecapiPayRefundResult secapiPayRefund(SecapiPayRefund secapiPayRefund,String key){
        Map<String,String> map = MapUtil.objectToMap( secapiPayRefund);
        String sign = SignatureUtil.generateSign(map,secapiPayRefund.getSign_type(),key);
        secapiPayRefund.setSign(sign);
        String secapiPayRefundXML = XMLConverUtil.convertToXML( secapiPayRefund);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(baseURI()+ "/secapi/pay/refund")
                .setEntity(new StringEntity(secapiPayRefundXML,Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.keyStoreExecuteXmlResult(secapiPayRefund.getMch_id(),httpUriRequest,SecapiPayRefundResult.class,secapiPayRefund.getSign_type(),key);
    }



    /**
     * 查询退款
     *
     * 提交退款申请后，通过调用该接口查询退款状态。退款有一定延时，用零钱支付的退款
     * 20 分钟内到账，银行卡支付的退款3 个工作日后重新查询退款状态。
     * @param refundquery refundquery
     * @param key 商户支付密钥
     * @return RefundqueryResult
     */
    public static RefundqueryResult payRefundquery(Refundquery refundquery,String key){
        Map<String,String> map = MapUtil.objectToMap(refundquery);
        String sign = SignatureUtil.generateSign(map,refundquery.getSign_type(),key);
        refundquery.setSign(sign);
        String refundqueryXML = XMLConverUtil.convertToXML(refundquery);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(baseURI()+ "/pay/refundquery")
                .setEntity(new StringEntity(refundqueryXML,Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeXmlResult(httpUriRequest,RefundqueryResult.class,refundquery.getSign_type(),key);
    }

    /**
     * 下载对账单
     * @param downloadbill downloadbill
     * @param key key
     * @return DownloadbillResult
     */
    public static DownloadbillResult payDownloadbill(MchDownloadbill downloadbill,String key){
        Map<String,String> map = MapUtil.objectToMap(downloadbill);
        String sign = SignatureUtil.generateSign(map,downloadbill.getSign_type(),key);
        downloadbill.setSign(sign);
        String closeorderXML = XMLConverUtil.convertToXML(downloadbill);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(baseURI()+ "/pay/downloadbill")
                .setEntity(new StringEntity(closeorderXML,Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.execute(httpUriRequest,new ResponseHandler<DownloadbillResult>() {

            @Override
            public DownloadbillResult handleResponse(HttpResponse response)
                    throws ClientProtocolException, IOException {
                int status = response.getStatusLine().getStatusCode();
                if (status >= 200 && status < 300) {
                    HttpEntity entity = response.getEntity();
                    String str;
                    //GZIP
                    if (entity.getContentType().getValue().matches("(?i).*gzip.*")) {
                        str = StreamUtils.copyToString(new GZIPInputStream(entity.getContent()),
                                Charset.forName("UTF-8"));
                    } else {
                        str = EntityUtils.toString(entity, "utf-8");
                    }
                    EntityUtils.consume(entity);

                    // 失败
                    if(str.startsWith("<xml>")){
                        return XMLConverUtil.convertToObject(DownloadbillResult.class,str);
                    }else{
                        DownloadbillResult dr = new DownloadbillResult();
                        dr.setData(str);
                        // 获取返回头数据 签名信息
                        Header headerDigest = response.getFirstHeader("Digest");
                        if (headerDigest != null) {
                            String[] hkv = headerDigest.getValue().split("=");
                            dr.setSign_type(hkv[0]);
                            dr.setSign(hkv[1]);
                        }
                        return dr;
                    }
                } else {
                    throw new ClientProtocolException("Unexpected response status: " + status);
                }
            }
        });
    }





    /**
     * 交易保障 <br>
     * 测速上报
     * @param report report
     * @param key key
     * @return MchBaseResult
     */
    public static MchBaseResult payitilReport(Report report,String key){
        Map<String,String> map = MapUtil.objectToMap(report);
        String sign = SignatureUtil.generateSign(map,report.getSign_type(),key);
        report.setSign(sign);
        String shorturlXML = XMLConverUtil.convertToXML(report);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(baseURI()+ "/payitil/report")
                .setEntity(new StringEntity(shorturlXML,Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeXmlResult(httpUriRequest,MchBaseResult.class);
    }






    /**
     * 企业付款 <br>
     * 接口调用规则：<br>
     * 给同一个实名用户付款，单笔单日限额2W/2W<br>
     * 给同一个非实名用户付款，单笔单日限额2000/2000<br>
     * 一个商户同一日付款总额限额100W<br>
     * 单笔最小金额默认为1元<br>
     * 每个用户每天最多可付款10次，可以在商户平台--API安全进行设置<br>
     * 给同一个用户付款时间间隔不得低于15秒<br>
     *
     * @param transfers
     *            transfers
     * @param key
     *            key
     * @return TransfersResult
     */
    public static TransfersResult mmpaymkttransfersPromotionTransfers(Transfers transfers,String key){
        Map<String,String> map = MapUtil.objectToMap( transfers);
        String sign = SignatureUtil.generateSign(map,transfers.getSign_type(),key);
        transfers.setSign(sign);
        String secapiPayRefundXML = XMLConverUtil.convertToXML( transfers);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(baseURI()+ "/mmpaymkttransfers/promotion/transfers")
                .setEntity(new StringEntity(secapiPayRefundXML,Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.keyStoreExecuteXmlResult(transfers.getMchid(),httpUriRequest,TransfersResult.class,transfers.getSign_type(),key);
    }


    /**
     * 汇付通用接口调用类 <br>
     *
     * @return TransfersResult
     */
    public static HuiFuDTO huiFuInterface(HuiFuDTO jsapiPayInfo, String url) {
        String json = JSON.toJSONString(jsapiPayInfo);
        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());
        HttpGlobalConfig.setTimeout(30000);
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + url)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(json).execute();
        String body = response.body();
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, url+"请求失败：" + body);
        }
        return JSONObject.parseObject(body,HuiFuDTO.class);
    }

    public static HuiFuDTO getHuiFuDTO(Object data, HuiFuConfig huiFuConfig){
        HuiFuDTO huiFuUserDTO = new HuiFuDTO(data);
        String signUser = SignatureUtil.sign(JSONObject.toJSONString(huiFuUserDTO.getData()),huiFuConfig.getPrivateKey());
//        String signUser = SignatureUtil.sign(JSONObject.toJSONString(huiFuUserDTO.getData()),tenantAuthConnection.getSecretKey());
        huiFuUserDTO.setSign(signUser);
        huiFuUserDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuUserDTO.setSys_id(huiFuConfig.getSysId());
        return huiFuUserDTO;
    }

    /**
     * 省市区地域编码查询类 <br>
     *
     * @return TransfersResult
     */
    public static HuiFuDTO areaInterface(String areaId) {
        StringEntity stringEntity = new StringEntity("", StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());
        HttpGlobalConfig.setTimeout(30000);
        cn.hutool.http.HttpResponse response = HttpUtil.createPost("https://eolink.o.apispace.com/xzqcx/api/v1/administrative_area?code_or_name="+areaId)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .execute();
        String body = response.body();
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, "请求失败：" + body);
        }
        return JSONObject.parseObject(body,HuiFuDTO.class);
    }

    public static SettleInfoResponseDTO HuiFuSettleQuery(TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig){
        String url = HuiFuPaymentConstant.SETTLE_QUERY;
        SettleInfoRequestDTO settleInfoRequestDTO = new SettleInfoRequestDTO();
        Date now = new Date();
        settleInfoRequestDTO.setReq_date(TimeUtils.changeDate2String(now,TimeUtils.FORMAT_STRING));
        settleInfoRequestDTO.setReq_seq_id(Global.createHuiFuNo(Global.HUIFU_PAY_SETTLE));
        settleInfoRequestDTO.setHuifu_id(tenantAuthConnection.getHuifuId());
        String nowTime = TimeUtils.changeDate2String(now,TimeUtils.FORMAT_STRING);
        String beforeTime = TimeUtils.changeDate2String(TimeUtils.getBeforeTime(now, NumberConstants.SEVEN), TimeUtils.FORMAT_STRING);
        String lastTime = StringConstants.EMPTY;
        // 如果没有上次同步时间，那么同步七天内的记录
        if (ObjectUtil.isNull(tenantAuthConnection.getSyncTime())){
            settleInfoRequestDTO.setBegin_date(beforeTime);
            settleInfoRequestDTO.setEnd_date(nowTime);
        }else {
            // 如果在七天以上，同步最近七天内的记录
            lastTime = TimeUtils.changeDate2String(tenantAuthConnection.getSyncTime(), TimeUtils.FORMAT_STRING);
            if (TimeUtils.compareTime(beforeTime,lastTime,TimeUtils.FORMAT_STRING)>0){
                settleInfoRequestDTO.setBegin_date(beforeTime);
                settleInfoRequestDTO.setEnd_date(nowTime);
            }else {
                // 如果有上次同步时间，在七天以内，同步上次同步时间+1到当前时间内的记录
                Date nextTime = TimeUtils.getBeforeTime(tenantAuthConnection.getSyncTime(), -1);
                if (TimeUtils.compareTime(TimeUtils.changeDate2String(nextTime,TimeUtils.FORMAT_STRING),nowTime)>0){
                    settleInfoRequestDTO.setBegin_date(nowTime);
                }else {
                    settleInfoRequestDTO.setBegin_date(TimeUtils.changeDate2String(nextTime,TimeUtils.FORMAT_STRING));
                }
                settleInfoRequestDTO.setEnd_date(nowTime);
            }
        }
        settleInfoRequestDTO.setPage_size(String.valueOf(NumberConstants.FIFTY));
        HuiFuDTO huiFuDTO = new HuiFuDTO(settleInfoRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()),huiFuConfig.getPrivateKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuConfig.getSysId());
        HttpGlobalConfig.setTimeout(30000);
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + HuiFuPaymentConstant.SETTLE_QUERY)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("查询结算信息返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            log.error("查询结算信息返回结果：{}", body);
            throw new DefaultServiceException(500, url + "请求失败：" + body);
        }
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        SettleInfoResponseDTO settleInfoResponseDTO = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), SettleInfoResponseDTO.class);
        settleInfoResponseDTO.setSyncTime(now);
        return settleInfoResponseDTO;
    }
}

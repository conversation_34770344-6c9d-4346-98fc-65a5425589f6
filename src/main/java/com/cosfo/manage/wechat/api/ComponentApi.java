package com.cosfo.manage.wechat.api;


import com.alibaba.fastjson.JSON;
import com.cosfo.manage.wechat.bean.*;
import com.cosfo.manage.wechat.bean.component.ApiQueryAuthResult;
import com.cosfo.manage.wechat.bean.component.AuthorizerAccessToken;
import com.cosfo.manage.wechat.bean.component.PreAuthCode;
import com.cosfo.manage.wechat.client.LocalHttpClient;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;

/**
 * @Package: com.cosfo.api
 * @Description:
 * @author: <EMAIL>
 * @Date: 2022/4/27
 */
@Component
public class ComponentApi extends BaseAPI{

    /**
     * 获取公众号第三方平台 component_access_token
     *
     * @param component_appid         公众号第三方平台appid
     * @param component_appsecret     公众号第三方平台appsecret
     * @param component_verify_ticket 微信后台推送的ticket，此ticket会定时推送，具体请见本页末尾的推送说明
     * @return 公众号第三方平台access_token
     */
    public static AuthorizerTokenResp apiComponentToken(String component_appid, String component_appsecret, String component_verify_ticket) {
        String postJsonData = String.format("{\"component_appid\":\"%1$s\" ,\"component_appsecret\": \"%2$s\",\"component_verify_ticket\": \"%3$s\"}",
                component_appid,
                component_appsecret,
                component_verify_ticket);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(jsonHeader)
                .setUri(BASE_URI + "/cgi-bin/component/api_component_token")
                .setEntity(new StringEntity(postJsonData, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeJsonResult(httpUriRequest, AuthorizerTokenResp.class);
    }

    /**
     * 获取预授权码 pre_auth_code
     *
     * @param component_access_token component_access_token
     * @param component_appid        公众号第三方平台appid
     * @return 预授权码
     */
    public static PreAuthCode apiCreatePreAuthCode(String component_access_token, String component_appid) {
        String postJsonData = String.format("{\"component_appid\":\"%1$s\"}",
                component_appid);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(jsonHeader)
                .setUri(BASE_URI + "/cgi-bin/component/api_create_preauthcode")
                .addParameter("component_access_token", component_access_token)
                .setEntity(new StringEntity(postJsonData, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeJsonResult(httpUriRequest, PreAuthCode.class);
    }

    /**
     * 使用授权码换取公众号的授权信息 authorizer_refresh_token
     *
     * @param component_access_token component_access_token
     * @param component_appid        公众号第三方平台appid
     * @param authorization_code     授权code,会在授权成功时返回给第三方平台，详见第三方平台授权流程说明
     * @return 公众号的授权信息
     */
    public static ApiQueryAuthResult api_query_auth(String component_access_token, String component_appid, String authorization_code) {
        String postJsonData = String.format("{\"component_appid\":\"%1$s\",\"authorization_code\":\"%2$s\"}",
                component_appid,
                authorization_code);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(jsonHeader)
                .setUri(BASE_URI + "/cgi-bin/component/api_query_auth")
                .addParameter("component_access_token", component_access_token)
                .setEntity(new StringEntity(postJsonData, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeJsonResult(httpUriRequest, ApiQueryAuthResult.class);
    }

    /**
     * 获取（刷新）授权公众号的令牌 authorizer_access_token
     *
     * @param component_access_token   component_access_token
     * @param component_appid          公众号第三方平台appid
     * @param authorizer_appid         授权方appid
     * @param authorizer_refresh_token 授权方的刷新令牌，刷新令牌主要用于公众号第三方平台获取和刷新已授权用户的access_token，只会在授权时刻提供，请妥善保存。 一旦丢失，只能让用户重新授权，才能再次拿到新的刷新令牌
     * @return 授权公众号的令牌
     */
    public static AuthorizerTokenResp api_authorizer_token(String component_access_token,
                                                             String component_appid,
                                                             String authorizer_appid,
                                                             String authorizer_refresh_token) {
        String postJsonData = String.format("{\"component_appid\":\"%1$s\",\"authorizer_appid\":\"%2$s\",\"authorizer_refresh_token\":\"%3$s\"}",
                component_appid, authorizer_appid, authorizer_refresh_token);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(jsonHeader)
                .setUri(BASE_URI + "/cgi-bin/component/api_authorizer_token")
                .addParameter("component_access_token", component_access_token)
                .setEntity(new StringEntity(postJsonData, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeJsonResult(httpUriRequest, AuthorizerTokenResp.class);
    }

    public static GetAuthorizerInfoResp api_get_authorizer_info(String component_access_token,
                                                                String component_appid,
                                                                String authorizer_appid) {
        String postJsonData = String.format("{\"component_appid\":\"%1$s\",\"authorizer_appid\":\"%2$s\"}",
                component_appid, authorizer_appid);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(jsonHeader)
                .setUri(BASE_URI + "/cgi-bin/component/api_get_authorizer_info")
                .addParameter("component_access_token", component_access_token)
                .setEntity(new StringEntity(postJsonData, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeJsonResult(httpUriRequest, GetAuthorizerInfoResp.class);
    }

    /**
     * 查询小程序用户隐私保护指引
     *
     * @param authorizer_access_token authorizer_access_token
     * @param privacy_ver        1表示现网版本，即，传1则该接口返回的内容是现网版本的；2表示开发版，
     *                           即，传2则该接口返回的内容是开发版本的。默认是2。
     * @return 预授权码
     */
    public static privateSettingResp getPrivacySetting(String authorizer_access_token, int privacy_ver) {
        String postJsonData = String.format("{\"privacy_ver\":\"%1$s\"}",
                privacy_ver);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(jsonHeader)
                .setUri(BASE_URI + "/cgi-bin/component/getprivacysetting")
                .addParameter(PARAM_ACCESS_TOKEN_DE, authorizer_access_token)
                .setEntity(new StringEntity(postJsonData, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeJsonResult(httpUriRequest, privateSettingResp.class);
    }

    /**
     * 设置用户隐私权限
     *
     * @param authorizer_access_token authorizer_access_token
     * @param wxSetPrivacyReq
     * @return 设置用户隐私权限
     */
    public static BaseResult setPrivacySetting(String authorizer_access_token, WxSetPrivacyReq wxSetPrivacyReq) {
        String json = JSON.toJSONString(wxSetPrivacyReq);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(jsonHeader)
                .setUri(BASE_URI + "/cgi-bin/component/setprivacysetting")
                .addParameter(PARAM_ACCESS_TOKEN_DE, authorizer_access_token)
                .setEntity(new StringEntity(json, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeJsonResult(httpUriRequest, BaseResult.class);
    }


}

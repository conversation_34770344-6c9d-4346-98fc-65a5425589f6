package com.cosfo.manage.open.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.open.model.po.OpenDocCodeMapping;
import com.cosfo.manage.open.mapper.OpenDocCodeMappingMapper;
import com.cosfo.manage.open.repository.OpenDocCodeMappingRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 外部单据映射关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class OpenDocCodeMappingRepositoryImpl extends ServiceImpl<OpenDocCodeMappingMapper, OpenDocCodeMapping> implements OpenDocCodeMappingRepository {

    @Override
    public OpenDocCodeMapping queryByDocCode(String orderNo, Long tenantId, Integer docType, Integer openType) {
        LambdaQueryWrapper<OpenDocCodeMapping> eq = new LambdaQueryWrapper<OpenDocCodeMapping> ()
                .eq (OpenDocCodeMapping::getDocCode, orderNo)
                .eq (OpenDocCodeMapping::getTenantId, tenantId)
                .eq (OpenDocCodeMapping::getDocType, docType)
                .eq (OpenDocCodeMapping::getOpenType, openType);
        return getOne (eq);
    }
}

package com.cosfo.manage.bizlog.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2023/12/19 18:20
 * @Description:
 */
@Data
public class BizLogQueryDTO extends BasePageInput {

    /**
     * 业务模块
     */
    @NotBlank(message = "业务模块不可为空")
    private String bizDomain;

    /**
     * 业务主体
     */
    @NotBlank(message = "业务模块不可为空")
    private String entityType;

    /**
     * 关键字
     */
    @NotBlank(message = "业务字段不可为空")
    private String bizKey;

    /**
     * 业务开始时间
     */
    @JSONField(format = "yyyy-MM-dd")
    @NotNull(message = "开始时间不可为空")
    private LocalDateTime bizStartTime;

    /**
     * 业务结束时间
     */
    @JSONField(format = "yyyy-MM-dd")
    @NotNull(message = "结束时间不可为空")
    private LocalDateTime bizEndTime;

    /**
     * 操作人
     */
    private String operationUserName;

    /**
     * 操作人手机号
     */
    private String operationPhone;

    /**
     * 租户
     */
    private Long tenantId;


}

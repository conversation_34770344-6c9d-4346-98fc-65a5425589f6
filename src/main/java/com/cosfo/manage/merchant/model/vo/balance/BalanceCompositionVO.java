package com.cosfo.manage.merchant.model.vo.balance;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/4/21 15:26
 * @PackageName:com.cosfo.manage.merchant.model.vo.balance
 * @ClassName: BalanceCompositionVO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class BalanceCompositionVO implements Serializable {
    private static final long serialVersionUID = 1818576460513550074L;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 非现金账户id
     */
    private Long fundAccountId;

    /**
     * 子账户名称-非现金账号
     */
    private String fundAccountName;

    /**
     * 账户类型 0=现金余额（默认），1=非现金账户
     * @see com.cosfo.manage.common.context.MerchantStoreBalanceEnums.AccountTypeEnum
     */
    private Integer accountType;

    /**
     * 余额  accountType=0为现金余额 accountType=1为非现金余额
     */
    private BigDecimal balance;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}

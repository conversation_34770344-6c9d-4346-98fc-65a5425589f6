package com.cosfo.manage.merchant.model.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/24 11:07
 * @PackageName:com.cosfo.manage.merchant.model.input
 * @ClassName: MerchantStoreQueryNonCashInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class MerchantStoreBalanceQueryNonCashInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 门店ID
     */
    @NotNull(message = "门店ID不能为空")
    private Long storeId;
}

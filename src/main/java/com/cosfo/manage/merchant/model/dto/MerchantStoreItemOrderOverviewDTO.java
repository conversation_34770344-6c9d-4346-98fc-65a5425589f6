package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 门店商品维度的概况分析
 * @author: George
 * @date: 2023-06-06
 **/
@Data
public class MerchantStoreItemOrderOverviewDTO {

    /**
     * 平均订货周期
     */
    private BigDecimal averageOrderPeriod;

    /**
     * 平均订货周期 上周期
     */
    private BigDecimal lastAverageOrderPeriod;

    /**
     * 平均订货周期 sum
     */
    private BigDecimal averageOrderPeriodSum;

    /**
     * 平均订货周期 上周期 sum
     */
    private BigDecimal lastAverageOrderPeriodSum;

    /**
     * 平均订货周期较上周期
     */
    private String averageOrderPeriodUpperPeriod;

    /**
     * 订货数量
     */
    private Integer orderAmount;

    /**
     * 订货数量 上周期
     */
    private Integer lastOrderAmount;

    /**
     * 订货数量较上周期
     */
    private String orderAmountUpperPeriod;

    /**
     * 订货金额
     */
    private BigDecimal orderPrice;

    /**
     * 订货金额 上周期
     */
    private BigDecimal lastOrderPrice;

    /**
     * 订货金额较上周期
     */
    private String orderPriceUpperPeriod;

    /**
     * 条数
     */
    private Integer recordCounts;

    public MerchantStoreItemOrderOverviewDTO() {}

    public static MerchantStoreItemOrderOverviewDTO getDefaultOverview() {
        MerchantStoreItemOrderOverviewDTO data = new MerchantStoreItemOrderOverviewDTO();
        data.setAverageOrderPeriod(BigDecimal.ZERO);
        data.setLastAverageOrderPeriod(BigDecimal.ZERO);
        data.setAverageOrderPeriodUpperPeriod("-");
        data.setOrderAmount(0);
        data.setLastOrderAmount(0);
        data.setOrderAmountUpperPeriod("-");
        data.setOrderPrice(BigDecimal.ZERO);
        data.setLastOrderPrice(BigDecimal.ZERO);
        data.setOrderPriceUpperPeriod("-");
        return data;
    }


}

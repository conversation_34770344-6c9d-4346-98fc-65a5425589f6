package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 门店手续费配置项DTO
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
public class MerchantServiceFeeConfigItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID（更新时需要）
     */
    private Long id;

    /**
     * 支付方式：wechat、alipay
     */
    @NotBlank(message = "支付方式不能为空")
    private String payType;

    /**
     * 服务费率，如0.6%
     * 最小0，最大100，支持2位小数
     */
    @NotNull(message = "费率不能为空")
    @DecimalMin(value = "0.00", message = "费率不能小于0")
    @DecimalMax(value = "100.00", message = "费率不能大于100")
    @Digits(integer = 3, fraction = 2, message = "费率最多支持2位小数")
    private BigDecimal feeRate;

    /**
     * 状态：1-启用，0-关闭
     */
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值错误")
    @Max(value = 1, message = "状态值错误")
    private Integer status;
}

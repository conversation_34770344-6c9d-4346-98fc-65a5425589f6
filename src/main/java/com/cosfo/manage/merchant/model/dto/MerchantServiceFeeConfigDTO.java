package com.cosfo.manage.merchant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 门店手续费配置VO
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
public class MerchantServiceFeeConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 微信服务费率，如0.6%
     */
    private BigDecimal wechatFeeRate;

    /**
     * 支付宝服务费率，如0.6%
     */
    private BigDecimal alipayFeeRate;

    /**
     * 状态：1-启用，0-关闭
     */
    private Integer status;
}

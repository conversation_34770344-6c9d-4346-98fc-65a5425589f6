package com.cosfo.manage.merchant.model.dto;

import com.cosfo.manage.common.model.dto.PageQueryDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/16 17:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantStoreQueryDTO extends PageQueryDTO {

    private Long id;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private List<Integer> typeList;


    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、关店
     */
    private Integer status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 账期权限 0开启1关闭
     */
    private Integer billSwitch;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 文件下载记录id
     */
    private Long fileDownloadRecordId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 门店分组Id
     */
    private Long groupId;

    /**
     * 分组Id
     */
    private List<Long> groupIds;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 门店Id
     */
    private List<Long> storeIds;

    /**
     * 不需要匹配门店Id
     */
    private List<Long> noMatchingStoreIds;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 是否为分组 -1
     */
    private Integer havingGroup;

    /**
     * 是否供应
     */
    private Integer supplyStatus;

    /**
     * 是否供应门店Id
     */
    private List<Long> supplyStoreIds;

    /**
     * 是否有匹配数据
     */
    private Boolean noMatchData;

    /**
     * 分组ids
     */
    private List<Long> merchantStoreGroupIds;

    /**
     * 门店编号
     */
    private List<String> storeNos;
    /**
     * 门店下单是否可下单
     * true = 可下单
     * false = 不可下单
     */
    private Boolean placeOrderEnableFlag;
    /**
     * 门店下单是否临期
     * true = 临期
     * false = 未临期
     */
    private Boolean placeOrderDeadlineFlag;

    /**
     * 线下支付权限1=开启;0=关闭
     */
    private Integer enableOfflinePayment;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;
}

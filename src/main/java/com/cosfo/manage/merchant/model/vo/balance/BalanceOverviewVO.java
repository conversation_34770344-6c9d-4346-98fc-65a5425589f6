package com.cosfo.manage.merchant.model.vo.balance;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BalanceOverviewVO {

    /**
     * 门店总余额
     */
    private BigDecimal totalBalance;

    /**
     * 有现金余额的门店数
     */
    private Integer hasBalanceStoreCount;

    /**
     * 时间
     */
    private LocalDateTime currentDate;

    /**
     * 门店余额
     */
    private BigDecimal storeBalance;

    /**
     * 门店其他余额（非现金账户）
     */
    private BigDecimal nonCashBalance;

    /**
     * 有非现金余额的门店数
     */
    private Integer hasNoNCashBalanceStoreCount;
}

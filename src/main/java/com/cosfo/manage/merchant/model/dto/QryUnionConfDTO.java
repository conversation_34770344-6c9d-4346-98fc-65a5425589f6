package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/20 17:46
 */
@Data
public class QryUnionConfDTO implements Serializable {

    /**
     *	银联商户号
     */
    private String bank_mer_code;
    /**
     *银联小微二维码
     */
    private String qr_code_info;
    /**
     *交易手续费收取类型
     */
    private String fee_charge_type;
    /**
     *借记卡手续费1000以上（%）
     */
    private String debit_fee_rate_up;
    /**
     *借记卡封顶1000以上
     */
    private String debit_fee_limit_up;
    /**
     *银联二维码业务贷记卡手续费1000以上（%）
     */
    private String credit_fee_rate_up;
    /**
     *	借记卡手续费1000以下（%）
     */
    private String debit_fee_rate_down;
    /**
     *借记卡封顶1000以下
     */
    private String debit_fee_limit_down;
    /**
     *银联二维码业务贷记卡手续费1000以下（%）
     */
    private String credit_fee_rate_down;
}

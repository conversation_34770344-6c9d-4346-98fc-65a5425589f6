package com.cosfo.manage.merchant.model.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 门店手续费配置保存DTO
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
public class MerchantServiceFeeConfigSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手续费配置列表
     */
    @NotEmpty(message = "配置列表不能为空")
    @Valid
    private List<MerchantServiceFeeConfigItemDTO> configList;
}

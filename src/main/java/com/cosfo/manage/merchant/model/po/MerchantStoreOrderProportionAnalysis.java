package com.cosfo.manage.merchant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 门店订货占比分析
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Getter
@Setter
@TableName("merchant_store_order_proportion_analysis")
public class MerchantStoreOrderProportionAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签 yyyyMMdd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 1、周 2、月 3、季度
     */
    @TableField("type")
    private Integer type;

    /**
     * 商品id
     */
    @TableField("title")
    private String title;

    /**
     * 门店id
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 订货数量
     */
    @TableField("order_amount")
    private Integer orderAmount;

    /**
     * 订货数量占比
     */
    @TableField("order_amount_proportion")
    private BigDecimal orderAmountProportion;

    /**
     * 上周期订货数量占比
     */
    @TableField("order_amount_proportion_upper_period")
    private BigDecimal orderAmountProportionUpperPeriod;

    /**
     * 订货金额
     */
    @TableField("order_price")
    private BigDecimal orderPrice;

    /**
     * 订货金额占比
     */
    @TableField("order_price_proportion")
    private BigDecimal orderPriceProportion;

    /**
     * 上周期订货金额占比
     */
    @TableField("order_price_proportion_upper_period")
    private BigDecimal orderPriceProportionUpperPeriod;

    /**
     * 门店类型
     */
    @TableField("store_type")
    private Integer storeType;

    /**
     * 门店分组
     */
    @TableField("store_group_name")
    private String storeGroupName;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


    /**
     * 当前周期的总订货数量
     */
    @TableField("total_order_amount")
    private Integer totalOrderAmount;

    /**
     * 上周期的订货数量
     */
    @TableField("order_amount_upper_period")
    private Integer orderAmountUpperPeriod;

    /**
     * 上周期的总订货数量
     */
    @TableField("total_order_amount_upper_period")
    private Integer totalOrderAmountUpperPeriod;

    /**
     * 当前周期的总订货金额
     */
    @TableField("total_order_price")
    private BigDecimal totalOrderPrice;

    /**
     * 上周期的订货金额
     */
    @TableField("order_price_upper_period")
    private BigDecimal orderPriceUpperPeriod;

    /**
     * 上周期的总订货金额
     */
    @TableField("total_order_price_upper_period")
    private BigDecimal totalOrderPriceUpperPeriod;
}

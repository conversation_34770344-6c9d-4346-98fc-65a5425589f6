package com.cosfo.manage.merchant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cosfo.manage.common.config.SetLongHandler;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <p>
 * 起订量规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Getter
@Setter
@TableName(value = "merchant_order_quantity_rule", autoResultMap = true)
public class MerchantOrderQuantityRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 0,全商城,其他值表示具体仓
     */
    @TableField("rule_target")
    private Long ruleTarget;

    /**
     * 0,无仓1三方仓 2自营仓
     */
    @TableField("warehouse_type")
    private Integer warehouseType;

    /**
     * 规则json
     */
    @TableField("rule")
    private String rule;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("rule_group")
    private Long ruleGroup;

    /**
     * 选中商品ID列表
     */
    @TableField(value = "hit_item_ids", typeHandler= SetLongHandler.class)
    private Set<Long> hitItemIds;

    /**
     * 货品类型,和goodsType不同, 0:全部，1:自营，2代仓，3供应商
     */
    @TableField("hit_goods_source")
    private Integer hitGoodsSource;

    @TableField("include_new_flag")
    private Boolean includeNewFlag;

    @TableField("rule_sort")
    private Integer ruleSort;
}

package com.cosfo.manage.merchant.model.dto;

import com.cosfo.manage.merchant.model.po.CardInfo;
import com.cosfo.manage.merchant.model.po.QryCashConfig;
import com.cosfo.manage.merchant.model.po.SettleConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 结算信息返回数据
 * <AUTHOR>
 * @date : 2022/12/16 4:02
 */
@Data
public class SettleMentInfoDTO implements Serializable {
    /**
     * 微信配置信息
     */
    private QryWxConfDTO qryWxConfDTO;
    /**
     * 支付宝配置信息
     */
    private QryAliConfDTO qryAliConfDTO;
    /**
     * 结算配置信息
     */
    private SettleConfig settleConfig;
    /**
     * 银行账户配置信息
     */
    private CardInfo cardInfo;
}

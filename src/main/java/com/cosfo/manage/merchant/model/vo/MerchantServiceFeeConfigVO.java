package com.cosfo.manage.merchant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 门店手续费配置VO
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
public class MerchantServiceFeeConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 支付方式：wechat、alipay
     */
    private String payType;

    /**
     * 支付方式名称
     */
    private String payTypeName;

    /**
     * 服务费率，如0.6%
     */
    private BigDecimal feeRate;

    /**
     * 状态：1-启用，0-关闭
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;
}

package com.cosfo.manage.merchant.model.dto;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * @description: 门店订货占比分析VO
 * @author: George
 * @date: 2023-06-06
 **/
@Data
public class MerchantStoreOrderProportionQueryDTO extends BasePageInput {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 1、门店类型维度 2、门店分组维度
     */
    private Integer dimension;

    /**
     * 时间标签
     */
    private String timeTag;

    /**
     * 类型 1、周 2、月 3、季度
     */
    private Integer type;

    /**
     * 门店类型
     * @see com.cosfo.manage.common.context.MerchantStoreEnum.Type
     */
    private Integer storeType;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 分组id
     */
    private Long storeGroupId;

    /**
     * 订货数量
     */
    private String orderAmountSort;

    /**
     * 订货金额
     */
    private String orderPriceSort;

    /**
     * 门店ids
     */
    private List<Long> storeIds;
}

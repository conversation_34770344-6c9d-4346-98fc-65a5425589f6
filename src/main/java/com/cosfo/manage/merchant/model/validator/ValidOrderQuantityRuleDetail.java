package com.cosfo.manage.merchant.model.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ ElementType.FIELD, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = OrderQuantityRuleDetailValidator.class)
public @interface ValidOrderQuantityRuleDetail {

    String message() default "请确认规则金额或数量是否正确";

    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

package com.cosfo.manage.merchant.repository;

import com.cosfo.manage.merchant.model.po.MerchantStoreTradeSummary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 门店交易汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
public interface MerchantStoreTradeSummaryRepository extends IService<MerchantStoreTradeSummary> {

    /**
     * 查询门店交易汇总
     * @param tenantId
     * @param startTime
     * @param endTime
     * @param type
     * @return
     */
    List<MerchantStoreTradeSummary> queryAll(Long tenantId, String startTime, String endTime, Integer type);

    /**
     * 查询成功支付的总金额
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal selectSuccessPaymentPrice(Long tenantId, String startTime, String endTime, Integer type);


    /**
     * 根据唯一索引查询
     * @param businessNo
     * @return
     */
    MerchantStoreTradeSummary selectByUniqueKey(String businessNo);
}

package com.cosfo.manage.merchant.repository.impl;

import com.cosfo.manage.merchant.repository.MerchantStoreGroupRepository;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 门店分组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Deprecated
@Service
public class MerchantStoreGroupRepositoryImpl implements MerchantStoreGroupRepository {
//    @Resource
//    private MerchantStoreGroupMapper merchantStoreGroupMapper;

//    @Override
//    public MerchantStoreGroup getByName(Long tenantId, String name) {
//        LambdaQueryWrapper<MerchantStoreGroup> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MerchantStoreGroup::getTenantId, tenantId);
//        queryWrapper.eq(MerchantStoreGroup::getName, name);
//        MerchantStoreGroup merchantStoreGroup = getOne(queryWrapper);
//        return merchantStoreGroup;
//    }
//
//    @Override
//    public List<MerchantStoreGroup> list(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO, Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroup> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MerchantStoreGroup::getTenantId, tenantId);
//        // 分组Id
//        queryWrapper.eq(!Objects.isNull(merchantStoreGroupQueryDTO.getId()), MerchantStoreGroup::getId, merchantStoreGroupQueryDTO.getId());
//        // 分组名称
//        queryWrapper.like(!StringUtils.isEmpty(merchantStoreGroupQueryDTO.getName()), MerchantStoreGroup::getName, merchantStoreGroupQueryDTO.getName());
//        // 创建时间根据id排序
//        queryWrapper.orderByDesc(MerchantStoreGroup::getType);
//        Boolean sort = StringUtils.isEmpty(merchantStoreGroupQueryDTO.getCreateTimeSort()) ? false : merchantStoreGroupQueryDTO.getCreateTimeSort().equals("asc");
//        queryWrapper.orderBy(true, sort, MerchantStoreGroup::getId);
//        if (!StringUtils.isEmpty(merchantStoreGroupQueryDTO.getUpdateTimeSort())) {
//            queryWrapper.orderBy(true, merchantStoreGroupQueryDTO.getUpdateTimeSort().equals("asc"), MerchantStoreGroup::getUpdateTime);
//        }
//        // 分组ids
//        queryWrapper.in(!CollectionUtils.isEmpty(merchantStoreGroupQueryDTO.getGroupIds()), MerchantStoreGroup::getId, merchantStoreGroupQueryDTO.getGroupIds());
//
//        return list(queryWrapper);
//    }
//
//    @Override
//    public List<MerchantStoreGroupInfoDTO> queryBatchByStoreIds(Long tenantId, List<Long> storeIds) {
//        return merchantStoreGroupMapper.queryBatchByStoreIds(tenantId, storeIds);
//    }
//
//    @Override
//    public MerchantStoreGroup queryDefaultGroup(Long tenantId) {
//        LambdaQueryWrapper<MerchantStoreGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(MerchantStoreGroup::getTenantId, tenantId);
//        lambdaQueryWrapper.eq(MerchantStoreGroup::getType, MerchantStoreGroupTypeEnum.DEFAULT.getType());
//        return merchantStoreGroupMapper.selectOne(lambdaQueryWrapper);
//    }
}

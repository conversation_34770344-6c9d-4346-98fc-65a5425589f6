package com.cosfo.manage.merchant.repository.impl;

import com.cosfo.manage.merchant.model.po.MerchantServiceFeeConfig;
import com.cosfo.manage.merchant.mapper.MerchantServiceFeeConfigMapper;
import com.cosfo.manage.merchant.repository.MerchantServiceFeeConfigRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 门店手续费配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Service
public class MerchantServiceFeeConfigRepositoryImpl extends ServiceImpl<MerchantServiceFeeConfigMapper, MerchantServiceFeeConfig> implements MerchantServiceFeeConfigRepository {

}

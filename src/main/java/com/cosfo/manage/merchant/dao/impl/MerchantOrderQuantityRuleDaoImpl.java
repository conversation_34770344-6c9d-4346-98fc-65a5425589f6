package com.cosfo.manage.merchant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.merchant.model.po.MerchantOrderQuantityRule;
import com.cosfo.manage.merchant.mapper.MerchantOrderQuantityRuleMapper;
import com.cosfo.manage.merchant.dao.MerchantOrderQuantityRuleDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 起订量规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Service
public class MerchantOrderQuantityRuleDaoImpl extends ServiceImpl<MerchantOrderQuantityRuleMapper, MerchantOrderQuantityRule> implements MerchantOrderQuantityRuleDao {

    @Override
    public void delOtherRule(Long tenantId) {
        LambdaQueryWrapper<MerchantOrderQuantityRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantOrderQuantityRule::getTenantId, tenantId).and(wrapper->wrapper.ne(MerchantOrderQuantityRule::getRuleTarget, 0).or().ne(MerchantOrderQuantityRule::getWarehouseType, 0));
        remove(queryWrapper);
    }

    @Override
    public List<MerchantOrderQuantityRule> listOrderQuantityRule(Long tenantId) {
        LambdaQueryWrapper<MerchantOrderQuantityRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantOrderQuantityRule::getTenantId, tenantId);
        return list(queryWrapper);
    }

    @Override
    public List<MerchantOrderQuantityRule> listOrderQuantitySpecialRule(Long tenantId) {
        LambdaQueryWrapper<MerchantOrderQuantityRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantOrderQuantityRule::getTenantId, tenantId);
        queryWrapper.ne(MerchantOrderQuantityRule::getRuleTarget, 0);
        return list(queryWrapper);
    }
}

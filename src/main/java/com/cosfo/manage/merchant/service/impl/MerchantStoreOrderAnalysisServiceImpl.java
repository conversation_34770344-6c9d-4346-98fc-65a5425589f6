package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.excel.easyexcel.LargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadStatusEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.common.context.TimeTagTypeEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.NumberUtils;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.file.model.po.FileDownloadRecord;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.merchant.convert.MerchantStoreAuditConvertUtil;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderAnalysisDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderAnalysisExcelDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderOverviewDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderAnalysis;
import com.cosfo.manage.merchant.repository.MerchantStoreOrderAnalysisRepository;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreOrderAnalysisService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.report.mapper.MerchantStoreOrderAnalysisMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.cosfo.common.util.TimeUtils.FORMAT_STRING;
import static java.math.RoundingMode.HALF_UP;

/**
 * @description: 门店维度的订货分析
 * @author: George
 * @date: 2023-06-09
 **/
@Service
@Slf4j
public class MerchantStoreOrderAnalysisServiceImpl implements MerchantStoreOrderAnalysisService {

//    @Resource
//    private MerchantStoreMapper merchantStoreMapper;
//    @Resource
//    private MerchantStoreGroupMapper merchantStoreGroupMapper;
    @Resource
    private MerchantStoreOrderAnalysisRepository merchantStoreOrderAnalysisRepository;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private CommonService commonService;
    @Resource
    private MerchantStoreOrderAnalysisMapper merchantStoreOrderAnalysisMapper;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;

    @Override
    public MerchantStoreOrderOverviewDTO queryAuditStoreDimensionOverview(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        // 处理查询的参数
        Boolean resultFlag = processQueryParameters(merchantStoreOrderAnalysisQueryDTO);
        if (!resultFlag) {
            return MerchantStoreOrderOverviewDTO.getDefault();
        }

        // 离线数据进行sum
        MerchantStoreOrderOverviewDTO merchantStoreOrderOverviewDTO = merchantStoreOrderAnalysisRepository.queryStoreOrderSum(merchantStoreOrderAnalysisQueryDTO);
        // 查询上周期离线数据进行sum
        String lastTimeTag = TimeUtils.getLastRangeByTimeTagAndType(merchantStoreOrderAnalysisQueryDTO.getTimeTag(), merchantStoreOrderAnalysisQueryDTO.getType(), "yyyyMMdd");
        merchantStoreOrderAnalysisQueryDTO.setTimeTag(lastTimeTag);
        MerchantStoreOrderOverviewDTO lastMerchantStoreOrderOverviewDTO = merchantStoreOrderAnalysisRepository.queryStoreOrderSum(merchantStoreOrderAnalysisQueryDTO);

        // 再填充一些属性
        populateOverview(merchantStoreOrderOverviewDTO, lastMerchantStoreOrderOverviewDTO);

        return merchantStoreOrderOverviewDTO;
    }

    /**
     * 填充下需要计算的值
     * @param dto 概况数据
     */
    private void populateOverview(MerchantStoreOrderOverviewDTO dto, MerchantStoreOrderOverviewDTO lastDto) {
        BigDecimal averageOrderPeriod = dto.getCount() == 0 ? BigDecimal.ZERO : NumberUtil.div(dto.getAverageOrderPeriodSum(), dto.getCount(), 1, HALF_UP);
        BigDecimal lastAverageOrderPeriod = lastDto.getCount() == 0 ? BigDecimal.ZERO : NumberUtil.div(lastDto.getAverageOrderPeriodSum(), lastDto.getCount(), 1, HALF_UP);
        dto.setAverageOrderPeriodUpperPeriod(NumberUtils.calculateChain(lastAverageOrderPeriod, averageOrderPeriod));
        dto.setOrderAmountUpperPeriod(NumberUtils.calculateChain(lastDto.getOrderAmount(), dto.getOrderAmount()));
        dto.setOrderPriceUpperPeriod(NumberUtils.calculateChain(lastDto.getOrderPrice(), dto.getOrderPrice()));
        dto.setAverageOrderPeriod(averageOrderPeriod);
        dto.setLastAverageOrderPeriod(lastAverageOrderPeriod);
        dto.setLastOrderAmount(lastDto.getOrderAmount());
        dto.setLastOrderPrice(lastDto.getOrderPrice());
    }

    /**
     * 处理查询的参数
     * @param dto 查询条件
     */
    private Boolean processQueryParameters(MerchantStoreOrderAnalysisQueryDTO dto) {
        String timeTag = dto.getTimeTag();
        if (StringUtils.isBlank(timeTag) || Objects.isNull(dto.getType())) {
            throw new ParamsException("请选择要查询的时间范围");
        }
        if (Objects.isNull(TimeTagTypeEnum.getDesc(dto.getType()))) {
            throw new ParamsException("选择搜索的时间格式不正确");
        }
        if (StringUtils.isBlank(dto.getOrderAmountSort()) && StringUtils.isBlank(dto.getAverageOrderPeriodSort()) && StringUtils.isBlank(dto.getOrderPriceSort())
                && StringUtils.isBlank(dto.getLastOrderTimeSort()) && StringUtils.isBlank(dto.getLastOrderAmountSort()) && StringUtils.isBlank(dto.getLastOrderPriceSort())) {
            dto.setAverageOrderPeriodSort("asc");
        }

        dto.setTenantId(UserLoginContextUtils.getTenantId());
        // 门店信息查询
        if (StringUtils.isNotBlank(dto.getStoreName()) || Objects.nonNull(dto.getStoreType()) || Objects.nonNull(dto.getStoreGroupId())) {
//            List<Long> storeIds = merchantStoreMapper.selectIdListByParam(UserLoginContextUtils.getTenantId(), dto.getStoreName(), null, dto.getStoreType(), dto.getStoreGroupId());
            List<Long> storeIds = merchantStoreService.selectIdListByParam(UserLoginContextUtils.getTenantId(), null, dto.getStoreName(), dto.getStoreType(), dto.getStoreGroupId());
            if (CollectionUtils.isEmpty(storeIds)) {
                return Boolean.FALSE;
            }
            dto.setStoreIds(storeIds);
        }
        return Boolean.TRUE;
    }

    @Override
    public PageInfo<MerchantStoreOrderAnalysisDTO> listAuditStoreDimensionPage(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        // 处理查询的条件
        Boolean resultFlag = processQueryParameters(merchantStoreOrderAnalysisQueryDTO);
        if (!resultFlag) {
            return PageInfoHelper.createPageInfo(Lists.newArrayList(), merchantStoreOrderAnalysisQueryDTO.getPageSize());
        }

        // 分页查询
        Page<MerchantStoreOrderAnalysis> page = PageHelper.startPage(merchantStoreOrderAnalysisQueryDTO.getPageIndex(), merchantStoreOrderAnalysisQueryDTO.getPageSize());
        merchantStoreOrderAnalysisRepository.listByCondition(merchantStoreOrderAnalysisQueryDTO);

        // 转换对象
        PageInfo<MerchantStoreOrderAnalysisDTO> pageInfo = PageInfoConverter.toPageInfo(page, MerchantStoreAuditConvertUtil::toDTO);

        // 填充门店信息
        populateStoreInfo(pageInfo.getList());
        return pageInfo;
    }

    private void populateStoreInfo(List<MerchantStoreOrderAnalysisDTO> list) {
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return;
        }
        // 查询门店信息
        List<Long> storeIds = list.stream().map(MerchantStoreOrderAnalysisDTO::getStoreId).collect(Collectors.toList());
        Long tenantId = UserLoginContextUtils.getTenantId();
//        MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder().tenantId(tenantId).storeIds(storeIds).build();
//        List<MerchantStoreDTO> merchantStoreList = merchantStoreMapper.selectList(query);
        List<MerchantStoreDTO> merchantStoreList = merchantStoreService.selectList(tenantId, storeIds);
        Map<Long, MerchantStoreDTO> storeInfoMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));

        list.forEach(el -> {
            MerchantStoreDTO merchantStoreDTO = storeInfoMap.get(el.getStoreId());
            if (Objects.isNull(merchantStoreDTO)) {
                return;
            }
            el.setStoreType(merchantStoreDTO.getType());
            el.setStoreName(merchantStoreDTO.getStoreName());
            el.setGroupName(merchantStoreDTO.getGroupName());
        });
    }

    @Override
    public Boolean exportAuditStoreDimension(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        // 处理文件参数
        HashMap<String, String> paramsMap = processFileParameters(merchantStoreOrderAnalysisQueryDTO);

        Boolean resultFlag = processQueryParameters(merchantStoreOrderAnalysisQueryDTO);

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.MERCHANT_STORE_DIMENSION.getType());
        recordDTO.setTenantId(UserLoginContextUtils.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.MERCHANT_STORE_DIMENSION_AUDIT.getFileName());
        recordDTO.setParams(JSONObject.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(merchantStoreOrderAnalysisQueryDTO, ee -> {
            // 1、表格处理
            String filePath = null;
            if (!resultFlag) {
                filePath = commonService.exportExcel(Lists.newArrayList(), ExcelTypeEnum.MERCHANT_STORE_DIMENSION_AUDIT.getName());
            }else {
                filePath = generateAuditStoreItemDimensionExcel(merchantStoreOrderAnalysisQueryDTO);
            }

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        // 生成下载记录
//        Long fileDownloadRecordId = processFileDownloadRecord(paramsMap);
//
//        // 处理查询条件
//        Boolean resultFlag = processQueryParameters(merchantStoreOrderAnalysisQueryDTO);
//        if (!resultFlag) {
//            commonService.generateAndUploadExcel(Lists.newArrayList(), ExcelTypeEnum.MERCHANT_STORE_DIMENSION_AUDIT, fileDownloadRecordId);
//        }
//
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            generateAuditStoreItemDimensionExcel(merchantStoreOrderAnalysisQueryDTO, fileDownloadRecordId);
//        });
        return Boolean.TRUE;
    }

    private String generateAuditStoreItemDimensionExcel(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.MERCHANT_STORE_DIMENSION_AUDIT.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        LargeDataSetExporter<MerchantStoreOrderAnalysis, MerchantStoreOrderAnalysisExcelDTO> handler = new LargeDataSetExporter<MerchantStoreOrderAnalysis, MerchantStoreOrderAnalysisExcelDTO>() {
            @Override
            protected List<MerchantStoreOrderAnalysisExcelDTO> convert(MerchantStoreOrderAnalysis data) {
                return Lists.newArrayList(MerchantStoreAuditConvertUtil.toExcelDTO(data));
            }
            @Override
            protected void flushData(List<MerchantStoreOrderAnalysisExcelDTO> dataList) {
                // 在刷新到excel之前填充一些需要批量查询的信息
                populateExcelDTO(dataList);
                excelWriter.fill(dataList, fillConfig, writeSheet);
            }

            private void populateExcelDTO(List<MerchantStoreOrderAnalysisExcelDTO> dataList) {
                List<Long> storeIds = dataList.stream().map(MerchantStoreOrderAnalysisExcelDTO::getStoreId).collect(Collectors.toList());
//                MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder().storeIds(storeIds).tenantId(merchantStoreOrderAnalysisQueryDTO.getTenantId()).build();
//                List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreMapper.selectList(query);
                List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.selectList(merchantStoreOrderAnalysisQueryDTO.getTenantId(),storeIds);
                Map<Long, MerchantStoreDTO> storeInfoMap = merchantStoreDTOS.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));
                dataList.forEach(el -> {
                    MerchantStoreDTO merchantStoreDTO = storeInfoMap.get(el.getStoreId());
                    if (Objects.isNull(merchantStoreDTO)) {
                        return;
                    }
                    el.setStoreName(merchantStoreDTO.getStoreName());
                    el.setStoreType(MerchantStoreEnum.Type.getDesc(merchantStoreDTO.getType()));
                    el.setStoreGroup(merchantStoreDTO.getGroupName());
                });
            }
        };

        merchantStoreOrderAnalysisMapper.exportByCondition(merchantStoreOrderAnalysisQueryDTO, handler);
        handler.clearData();
        excelWriter.finish();

        return filePath;
        // 上传数据到七牛云
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, ExcelTypeEnum.MERCHANT_STORE_DIMENSION_AUDIT, fileDownloadRecordId);
    }


    private HashMap<String, String> processFileParameters(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        HashMap<String, String> paramsMap = new LinkedHashMap<>(NumberConstants.TEN);
        // 根据时间标签和类型确定搜索的时间范围
        Pair<String, String> timeRangePair = TimeUtils.getRangeByTimeTagAndType(merchantStoreOrderAnalysisQueryDTO.getTimeTag(), merchantStoreOrderAnalysisQueryDTO.getType(), FORMAT_STRING);
        paramsMap.put(Constants.QUERY_TIME, timeRangePair.getKey() + StringConstants.SEPARATING_IN_LINE + timeRangePair.getValue());
        // 门店类型
        if (Objects.nonNull(merchantStoreOrderAnalysisQueryDTO.getStoreType())) {
            String storeType = MerchantStoreEnum.Type.getDesc(merchantStoreOrderAnalysisQueryDTO.getStoreType());
            paramsMap.put(Constants.STORE_TYPE, storeType);
        }
        // 门店名称
        if (StringUtils.isNotBlank(merchantStoreOrderAnalysisQueryDTO.getStoreName())) {
            paramsMap.put(Constants.STORE_NAME, merchantStoreOrderAnalysisQueryDTO.getStoreName());
        }
        // 门店分组
        if (Objects.nonNull(merchantStoreOrderAnalysisQueryDTO.getStoreGroupId())) {
//            MerchantStoreGroup merchantStoreGroup = merchantStoreGroupMapper.selectById(merchantStoreOrderAnalysisQueryDTO.getStoreGroupId());
            MerchantStoreGroupPageResultResp merchantStoreGroup = merchantStoreGroupService.queryById(merchantStoreOrderAnalysisQueryDTO.getStoreGroupId(), UserLoginContextUtils.getTenantId());
            paramsMap.put(Constants.STORE_GROUPS, Objects.isNull(merchantStoreGroup) ? "" : merchantStoreGroup.getMerchantStoreGroupName());
        }
        return paramsMap;
    }
}

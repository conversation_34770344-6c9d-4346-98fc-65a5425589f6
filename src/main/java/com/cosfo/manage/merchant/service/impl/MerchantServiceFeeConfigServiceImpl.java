package com.cosfo.manage.merchant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.merchant.model.po.MerchantServiceFeeConfig;
import com.cosfo.manage.merchant.model.vo.MerchantServiceFeeConfigVO;
import com.cosfo.manage.merchant.repository.MerchantServiceFeeConfigRepository;
import com.cosfo.manage.merchant.service.MerchantServiceFeeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 门店手续费配置服务实现
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
@Slf4j
@Service
public class MerchantServiceFeeConfigServiceImpl implements MerchantServiceFeeConfigService {

    @Resource
    private MerchantServiceFeeConfigRepository merchantServiceFeeConfigRepository;

    @Override
    public List<MerchantServiceFeeConfigVO> queryByTenantId(Long tenantId) {
        log.info("查询租户手续费配置, tenantId: {}", tenantId);

        // 查询数据库
        LambdaQueryWrapper<MerchantServiceFeeConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantServiceFeeConfig::getTenantId, tenantId)
                   .orderByAsc(MerchantServiceFeeConfig::getPayType);

        List<MerchantServiceFeeConfig> configList = merchantServiceFeeConfigRepository.list(queryWrapper);

        if (CollectionUtils.isEmpty(configList)) {
            log.info("租户{}暂无手续费配置", tenantId);
            return new ArrayList<>();
        }

        // 转换为VO
        List<MerchantServiceFeeConfigVO> voList = new ArrayList<>();
        for (MerchantServiceFeeConfig config : configList) {
            MerchantServiceFeeConfigVO vo = convertToVO(config);
            voList.add(vo);
        }

        log.info("查询到租户{}的手续费配置{}条", tenantId, voList.size());
        return voList;
    }

    /**
     * 转换为VO对象
     */
    private MerchantServiceFeeConfigVO convertToVO(MerchantServiceFeeConfig config) {
        MerchantServiceFeeConfigVO vo = new MerchantServiceFeeConfigVO();
        vo.setId(config.getId());
        vo.setPayType(config.getPayType());
        vo.setPayTypeName(getPayTypeName(config.getPayType()));
        vo.setFeeRate(config.getFeeRate());
        vo.setStatus(config.getStatus());
        vo.setStatusName(getStatusName(config.getStatus()));
        return vo;
    }

    /**
     * 获取支付方式名称
     */
    private String getPayTypeName(String payType) {
        if ("wechat".equals(payType)) {
            return "微信支付";
        } else if ("alipay".equals(payType)) {
            return "支付宝";
        }
        return payType;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        return status != null && status == 1 ? "启用" : "关闭";
    }
}

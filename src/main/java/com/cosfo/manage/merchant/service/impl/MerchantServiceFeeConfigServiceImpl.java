package com.cosfo.manage.merchant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.merchant.model.dto.MerchantServiceFeeConfigItemDTO;
import com.cosfo.manage.merchant.model.dto.MerchantServiceFeeConfigSaveDTO;
import com.cosfo.manage.merchant.model.po.MerchantServiceFeeConfig;
import com.cosfo.manage.merchant.repository.MerchantServiceFeeConfigRepository;
import com.cosfo.manage.merchant.service.MerchantServiceFeeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 门店手续费配置服务实现
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
@Slf4j
@Service
public class MerchantServiceFeeConfigServiceImpl implements MerchantServiceFeeConfigService {

    @Resource
    private MerchantServiceFeeConfigRepository merchantServiceFeeConfigRepository;

    @Override
    public List<MerchantServiceFeeConfig> queryByTenantId(Long tenantId) {
        log.info("查询租户手续费配置, tenantId: {}", tenantId);

        // 查询数据库
        LambdaQueryWrapper<MerchantServiceFeeConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantServiceFeeConfig::getTenantId, tenantId)
                   .orderByAsc(MerchantServiceFeeConfig::getPayType);

        List<MerchantServiceFeeConfig> configList = merchantServiceFeeConfigRepository.list(queryWrapper);

        if (CollectionUtils.isEmpty(configList)) {
            log.info("租户{}暂无手续费配置", tenantId);
            return new ArrayList<>();
        }

        log.info("查询到租户{}的手续费配置{}条", tenantId, configList.size());
        return configList;
    }
}

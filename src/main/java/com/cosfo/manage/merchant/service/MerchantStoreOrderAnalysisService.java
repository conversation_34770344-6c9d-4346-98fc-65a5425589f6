package com.cosfo.manage.merchant.service;

import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderAnalysisDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderOverviewDTO;
import com.github.pagehelper.PageInfo;

/**
 * @description: 门店维度的订货分析
 * @author: George
 * @date: 2023-06-09
 **/
public interface MerchantStoreOrderAnalysisService {

    /**
     * 查询门店维度的概况
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return 概况数据
     */
    MerchantStoreOrderOverviewDTO queryAuditStoreDimensionOverview(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO);

    /**
     * 查询门店维度的列表
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return 分页数据
     */
    PageInfo<MerchantStoreOrderAnalysisDTO> listAuditStoreDimensionPage(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO);

    /**
     * 导出门店维度的数据
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return true、false
     */
    Boolean exportAuditStoreDimension(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO);
}

package com.cosfo.manage.merchant.service;

import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderAnalysisDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderOverviewDTO;
import com.github.pagehelper.PageInfo;

/**
 * @description: 门店商品维度的订货分析业务方法
 * @author: George
 * @date: 2023-06-06
 **/
public interface MerchantStoreItemOrderAnalysisService {

    /**
     * 查询稽核-门店商品维度概况
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 概况数据
     */
    MerchantStoreItemOrderOverviewDTO queryAuditStoreItemDimensionOverview(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO);

    /**
     * 稽核-门店商品维度列表
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 列表数据
     */
    PageInfo<MerchantStoreItemOrderAnalysisDTO> listAuditStoreItemDimensionPage(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO);

    Boolean exportAuditStoreItemDimension(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO);
}

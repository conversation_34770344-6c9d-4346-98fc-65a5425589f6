package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.merchant.convert.MerchantServiceFeeConfigConvert;
import com.cosfo.manage.merchant.model.po.MerchantServiceFeeConfig;
import com.cosfo.manage.merchant.model.vo.MerchantServiceFeeConfigVO;
import com.cosfo.manage.merchant.service.MerchantServiceFeeConfigService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 门店手续费配置控制器
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Slf4j
@RestController
@RequestMapping("/merchant/service-fee-config")
public class MerchantServiceFeeConfigController {

    @Resource
    private MerchantServiceFeeConfigService merchantServiceFeeConfigService;

    /**
     * 查询手续费配置
     *
     * @return 手续费配置列表
     */
    @PostMapping("/query")
    public CommonResult<List<MerchantServiceFeeConfigVO>> queryConfig() {
        Long tenantId = UserLoginContextUtils.getTenantId();
        List<MerchantServiceFeeConfig> configList = merchantServiceFeeConfigService.queryByTenantId(tenantId);

        // 使用转换工具转换为VO
        List<MerchantServiceFeeConfigVO> voList = MerchantServiceFeeConfigConvert.convertToVOList(configList);

        return CommonResult.ok(voList);
    }
}


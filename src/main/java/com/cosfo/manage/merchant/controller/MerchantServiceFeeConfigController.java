package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.merchant.convert.MerchantServiceFeeConfigConvert;
import com.cosfo.manage.merchant.model.dto.MerchantServiceFeeConfigSaveDTO;
import com.cosfo.manage.merchant.model.po.MerchantServiceFeeConfig;
import com.cosfo.manage.merchant.model.vo.MerchantServiceFeeConfigVO;
import com.cosfo.manage.merchant.service.MerchantServiceFeeConfigService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 门店手续费配置控制器
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Slf4j
@RestController
@RequestMapping("/merchant/service-fee-config")
public class MerchantServiceFeeConfigController {

    @Resource
    private MerchantServiceFeeConfigService merchantServiceFeeConfigService;

    /**
     * 查询手续费配置
     *
     * @return 手续费配置列表
     */
    @PostMapping("/query")
    public CommonResult<MerchantServiceFeeConfigVO> queryConfig() {
        Long tenantId = UserLoginContextUtils.getTenantId();
        log.info("查询手续费配置, tenantId: {}", tenantId);

        List<MerchantServiceFeeConfig> configList = merchantServiceFeeConfigService.queryByTenantId(tenantId);

        // 使用转换工具转换为VO
        List<MerchantServiceFeeConfigVO> voList = MerchantServiceFeeConfigConvert.convertToVOList(configList);

        return CommonResult.ok(voList);
    }

    /**
     * 保存或更新手续费配置
     *
     * 说明：
     * - 新增：configList中的item不传id或id为null
     * - 更新：configList中的item必须传id
     *
     * @param saveDTO 保存数据
     * @return 保存结果，包含每个配置的ID和操作类型
     */
    @PostMapping("/upsert")
    public CommonResult<Boolean> saveOrUpdateConfig(@Valid @RequestBody MerchantServiceFeeConfigSaveDTO saveDTO) {
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtils.getMerchantInfoDTO();
        Boolean result = merchantServiceFeeConfigService.saveOrUpdateConfig(loginContextInfoDTO, saveDTO);
        return CommonResult.ok(result);
    }
}


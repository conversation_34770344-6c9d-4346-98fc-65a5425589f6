package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.merchant.model.vo.MerchantServiceFeeConfigVO;
import com.cosfo.manage.merchant.service.MerchantServiceFeeConfigService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 门店手续费配置控制器
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Slf4j
@RestController
@RequestMapping("/merchant/service-fee-config")
public class MerchantServiceFeeConfigController {

    @Resource
    private MerchantServiceFeeConfigService merchantServiceFeeConfigService;

    /**
     * 查询手续费配置
     *
     * @return 手续费配置列表
     */
    @GetMapping("/query")
    public CommonResult<List<MerchantServiceFeeConfigVO>> queryConfig() {
        try {
            Long tenantId = UserLoginContextUtils.getTenantId();
            log.info("查询手续费配置, tenantId: {}", tenantId);

            List<MerchantServiceFeeConfigVO> configList = merchantServiceFeeConfigService.queryByTenantId(tenantId);

            return CommonResult.ok(configList);
        } catch (Exception e) {
            log.error("查询手续费配置失败", e);
            return CommonResult.fail("查询手续费配置失败");
        }
    }
}


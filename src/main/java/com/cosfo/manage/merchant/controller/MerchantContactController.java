package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.service.MerchantContactService;
import net.xianmu.log.annation.BizLogRecord;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 16:19
 */
@RestController
@RequestMapping("/merchant/contact")
public class MerchantContactController {

    @Resource
    private MerchantContactService merchantContactService;

    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @BizLogRecord(operationName = "变更门店信息", bizKey = "#storeId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public ResultDTO deleteContact(Long storeId, Long contactId) {
        return merchantContactService.deleteContact(storeId, contactId);
    }

}

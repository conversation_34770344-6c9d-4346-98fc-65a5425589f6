package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.merchant.model.dto.balance.BalanceCompositionDTO;
import com.cosfo.manage.merchant.model.dto.balance.BalanceRecordDTO;
import com.cosfo.manage.merchant.model.dto.balance.MerchantStoreBalanceDTO;
import com.cosfo.manage.merchant.model.input.MerchantStoreBalanceQueryNonCashInput;
import com.cosfo.manage.merchant.model.vo.balance.BalanceChangeRecordVO;
import com.cosfo.manage.merchant.model.vo.balance.BalanceCompositionVO;
import com.cosfo.manage.merchant.model.vo.balance.BalanceOverviewVO;
import com.cosfo.manage.merchant.model.vo.balance.MerchantStoreBalanceVO;
import com.cosfo.manage.merchant.service.MerchantStoreBalanceService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/merchant-store-balance")
public class MerchantStoreBalanceController extends BaseController {

    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;

    /**
     * 调整门店额度
     *
     * @param merchantStoreBalanceDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/adjust-balance", method = RequestMethod.POST)
    public CommonResult<Object> adjustBalance(@Valid @RequestBody MerchantStoreBalanceDTO merchantStoreBalanceDTO) {
        return merchantStoreBalanceService.adjustBalance(merchantStoreBalanceDTO, getMerchantInfoDTO());
    }

    /**
     * 余额概览
     *
     * @return
     */
    @RequestMapping(value = "/query/overview", method = RequestMethod.POST)
    public CommonResult<BalanceOverviewVO> overview() {
        return merchantStoreBalanceService.balanceOverview(getMerchantInfoDTO());
    }

    /**
     * 门店余额明细记录
     *
     * @return
     */
    @RequestMapping(value = "/query/change-record", method = RequestMethod.POST)
    public CommonResult<PageInfo<BalanceChangeRecordVO>> balanceChangeRecord(@RequestBody BalanceRecordDTO balanceRecordDTO) {
        return merchantStoreBalanceService.balanceChangeRecord(getMerchantInfoDTO(), balanceRecordDTO, Boolean.TRUE);
    }

    /**
     * 余额构成
     *
     * @return
     */
    @RequestMapping(value = "/query/composition", method = RequestMethod.POST)
    public CommonResult<PageInfo<BalanceCompositionVO>> balanceComposition(@RequestBody BalanceCompositionDTO balanceCompositionDTO) {
        return merchantStoreBalanceService.balanceComposition(getMerchantInfoDTO(), balanceCompositionDTO);
    }

    /**
     * 余额构成--合计金额
     *
     * @return
     */
    @RequestMapping(value = "/query/total", method = RequestMethod.POST)
    public CommonResult<BalanceOverviewVO> balanceTotal(@RequestBody BalanceCompositionDTO balanceCompositionDTO) {
        return merchantStoreBalanceService.balanceTotal(getMerchantInfoDTO(), balanceCompositionDTO);
    }

    /**
     * 门店余额明细记录导出
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export/change-record", method = RequestMethod.POST)
    public CommonResult<Object> exportBalanceChangeRecord(@RequestBody BalanceRecordDTO balanceRecordDTO) {
        return merchantStoreBalanceService.exportBalanceChangeRecord(getMerchantInfoDTO(), balanceRecordDTO);
    }

    /**
     * 查询当前门店下所有的 非现金账号列表
     * @param input
     * @return
     */
    @RequestMapping(value = "/query/all-non-cash", method = RequestMethod.POST)
    public CommonResult<List<MerchantStoreBalanceVO>> queryAllNonCash(@Valid @RequestBody MerchantStoreBalanceQueryNonCashInput input) {
        return CommonResult.ok(merchantStoreBalanceService.queryAllNonCash(input, getMerchantInfoDTO()));
    }
}

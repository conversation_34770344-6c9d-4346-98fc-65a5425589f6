package com.cosfo.manage.merchant.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.merchant.model.dto.quantityrule.OrderQuantityRuleManageDTO;
import com.cosfo.manage.merchant.model.vo.OrderQuantityRuleManageVO;
import com.cosfo.manage.merchant.service.MerchantOrderQuantityRuleService;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 起订量管理
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@RestController
@RequestMapping("/merchant/merchant-order-quantity-rule")
public class MerchantOrderQuantityRuleController extends BaseController {
    @Resource
    private MerchantOrderQuantityRuleService merchantOrderQuantityRuleService;

    /**
     * 获取起订量规则列表
     *
     * @return
     */
    @PostMapping("/query/list")
    public CommonResult<OrderQuantityRuleManageVO> listQuantityRule() {
        return CommonResult.ok(merchantOrderQuantityRuleService.queryOrderQuantityRule(getMerchantInfoDTO().getTenantId()));
    }

    /**
     * 更新起订量规则
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:order-quantity:update", expireError = true)
    @PostMapping("/upsert/update")
    public CommonResult<Boolean> modifyQuantityRule(@RequestBody @Valid OrderQuantityRuleManageDTO ruleDTO) {
        return CommonResult.ok(merchantOrderQuantityRuleService.modifyOrderQuantityRule(ruleDTO, getMerchantInfoDTO().getTenantId()));
    }

}


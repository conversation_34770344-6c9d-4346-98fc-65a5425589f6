package com.cosfo.manage.merchant.convert;

import com.cosfo.manage.merchant.model.po.MerchantServiceFeeConfig;
import com.cosfo.manage.merchant.model.vo.MerchantServiceFeeConfigVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 门店手续费配置转换工具
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
public class MerchantServiceFeeConfigConvert {

    /**
     * PO转VO
     *
     * @param config PO对象
     * @return VO对象
     */
    public static MerchantServiceFeeConfigVO convertToVO(MerchantServiceFeeConfig config) {
        if (config == null) {
            return null;
        }
        
        MerchantServiceFeeConfigVO vo = new MerchantServiceFeeConfigVO();
        vo.setId(config.getId());
        vo.setPayType(config.getPayType());
        vo.setPayTypeName(getPayTypeName(config.getPayType()));
        vo.setWechatFeeRate(config.getFeeRate());
        vo.setStatus(config.getStatus());
        vo.setStatusName(getStatusName(config.getStatus()));
        return vo;
    }

    /**
     * PO列表转VO列表
     *
     * @param configList PO列表
     * @return VO列表
     */
    public static List<MerchantServiceFeeConfigVO> convertToVOList(List<MerchantServiceFeeConfig> configList) {
        if (configList == null || configList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<MerchantServiceFeeConfigVO> voList = new ArrayList<>();
        for (MerchantServiceFeeConfig config : configList) {
            MerchantServiceFeeConfigVO vo = convertToVO(config);
            if (vo != null) {
                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 获取支付方式名称
     */
    private static String getPayTypeName(String payType) {
        if ("wechat".equals(payType)) {
            return "微信支付";
        } else if ("alipay".equals(payType)) {
            return "支付宝";
        }
        return payType;
    }

    /**
     * 获取状态名称
     */
    private static String getStatusName(Integer status) {
        return status != null && status == 1 ? "启用" : "关闭";
    }
}

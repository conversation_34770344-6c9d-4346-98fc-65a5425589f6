package com.cosfo.manage.merchant.convert;

import cn.hutool.core.date.DateUtil;
import com.cosfo.manage.common.context.wnc.WarehouseLogisticsCenterEnums;
import com.cosfo.manage.merchant.model.dto.openapi.MerchantStoreDeliveryInfoDTO;
import com.cosfo.manage.merchant.model.dto.openapi.MerchantStoreDeliveryRuleDTO;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;

import java.time.LocalDate;
import java.util.Collections;

/**
 * <AUTHOR>
 * @Date 2025/4/16 16:55
 * @Version 1.0
 */
public class MerchantStoreDeliveryInfoConverter {

    public static MerchantStoreDeliveryRuleDTO convert(MerchantStoreResultResp merchantStore, DeliveryDateQueryResp deliveryDateQueryResp) {
        MerchantStoreDeliveryInfoDTO merchantStoreDeliveryInfoDTO = new MerchantStoreDeliveryInfoDTO();
        merchantStoreDeliveryInfoDTO.setStoreCode(merchantStore.getStoreNo());
        merchantStoreDeliveryInfoDTO.setStoreName(merchantStore.getStoreName());
        if (null != deliveryDateQueryResp) {
            merchantStoreDeliveryInfoDTO.setLogisticsMethod(deliveryDateQueryResp.getFulfillmentType());

            if (WarehouseLogisticsCenterEnums.FulfillmentType.EXPRESS_DELIVERY.getValue().equals(deliveryDateQueryResp.getFulfillmentType())) {
                merchantStoreDeliveryInfoDTO.setExpressDeliveryBeforeCloseTimeBuyDispatchDate(getDateTimeString(deliveryDateQueryResp.getFirstMerchantDeliveryTime()));
                merchantStoreDeliveryInfoDTO.setExpressDeliveryAfterCloseTimeBuyDispatchDate(getDateTimeString(deliveryDateQueryResp.getNextMerchantDeliveryTime()));
            }
            if (WarehouseLogisticsCenterEnums.FulfillmentType.CITY_DELIVERY.getValue().equals(deliveryDateQueryResp.getFulfillmentType())) {
                merchantStoreDeliveryInfoDTO.setCityDeliveryBeforeCloseTimeBuyReceiveDate(getDateTimeString(deliveryDateQueryResp.getFirstMerchantDeliveryTime()));
                merchantStoreDeliveryInfoDTO.setCityDeliveryAfterCloseTimeBuyReceiveDate(getDateTimeString(deliveryDateQueryResp.getNextMerchantDeliveryTime()));
            }

            merchantStoreDeliveryInfoDTO.setCloseTime(DateUtil.formatLocalDateTime(deliveryDateQueryResp.getDeliveryCloseTime()));
            merchantStoreDeliveryInfoDTO.setAddOrderHourDuration(deliveryDateQueryResp.getAddOrderHourDuration());
        }
        MerchantStoreDeliveryRuleDTO merchantStoreDeliveryRuleDTO = new MerchantStoreDeliveryRuleDTO();
        merchantStoreDeliveryRuleDTO.setRules(Collections.singletonList(merchantStoreDeliveryInfoDTO));
        return merchantStoreDeliveryRuleDTO;
    }

    public static String getDateTimeString(LocalDate localDate) {
        if (null == localDate) {
            return null;
        }
        return DateUtil.formatLocalDateTime(localDate.atStartOfDay());
    }


}

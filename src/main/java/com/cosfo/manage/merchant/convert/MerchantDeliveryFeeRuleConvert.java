package com.cosfo.manage.merchant.convert;

import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleEditDTO;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleSpecialVO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantDeliveryFeeRuleConvert {

    MerchantDeliveryFeeRuleConvert INSTANCE = Mappers.getMapper(MerchantDeliveryFeeRuleConvert.class);

    @Mapping(target = "allStoreHit", source = "includeAllStoreFlag")
    MerchantDeliveryFeeRuleVO from(MerchantDeliveryFeeRule feeRule);
    MerchantDeliveryFeeRuleSpecialVO toSpecial(MerchantDeliveryFeeRule feeRule);

    @Mapping(target = "includeAllStoreFlag", source = "allStoreHit")
    MerchantDeliveryFeeRule dtoToEntity(MerchantDeliveryFeeRuleEditDTO editDTO);
    MerchantDeliveryFeeRuleEditDTO specialToDTO(MerchantDeliveryFeeRuleSpecialVO editDTO);

    List<MerchantDeliveryFeeRuleEditDTO> convert2EditDTOs(List<MerchantDeliveryFeeRule> feeRules);

    @Mapping(target = "allStoreHit", source = "includeAllStoreFlag")
    MerchantDeliveryFeeRuleEditDTO convert2EditDTO(MerchantDeliveryFeeRule feeRule);

}

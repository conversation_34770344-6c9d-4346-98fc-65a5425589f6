package com.cosfo.manage.merchant.convert;

import com.cosfo.manage.merchant.model.dto.MerchantDeliveryAddressResultDTO;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-26
 * @Description:
 */
@Mapper
public interface MerchantAddressMapperConvert {

    MerchantAddressMapperConvert INSTANCE = Mappers.getMapper(MerchantAddressMapperConvert.class);


    /**
     * resp转dto列表
     * @param merchantAddressList
     * @return
     */
    List<MerchantDeliveryAddressResultDTO> respListToDtoList(List<MerchantAddressResultResp> merchantAddressList);
}

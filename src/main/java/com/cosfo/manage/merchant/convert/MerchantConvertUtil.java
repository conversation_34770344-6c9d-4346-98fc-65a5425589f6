package com.cosfo.manage.merchant.convert;


import com.cosfo.manage.client.merchant.req.MerchantStoreOpenReq;
import com.cosfo.manage.common.context.BillSwitchEnum;
import com.cosfo.manage.common.context.OnlinePaymentEnum;
import com.cosfo.manage.common.context.store.balance.BalanceAuthorityTypeEnum;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroup;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreVO;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/12/5 12:46
 */
public class MerchantConvertUtil {

    public static final MerchantStoreGroupVO convertMerchantStoreGroup2VO(MerchantStoreGroup merchantStoreGroup) {

        if (merchantStoreGroup == null) {
            return null;
        }
        MerchantStoreGroupVO merchantStoreGroupVO = new MerchantStoreGroupVO();
        merchantStoreGroupVO.setId(merchantStoreGroup.getId());
        merchantStoreGroupVO.setName(merchantStoreGroup.getName());
        merchantStoreGroupVO.setCreateTime(merchantStoreGroup.getCreateTime());
        merchantStoreGroupVO.setUpdateTime(merchantStoreGroup.getUpdateTime());
        return merchantStoreGroupVO;
    }

    public static final MerchantStoreQueryDTO storeVO2DTO(MerchantStoreVO storeVO){

        if (storeVO == null) {
            return null;
        }
        MerchantStoreQueryDTO merchantStoreQueryDTO = new MerchantStoreQueryDTO();
        merchantStoreQueryDTO.setId(storeVO.getId());
        merchantStoreQueryDTO.setTenantId(storeVO.getTenantId());
        merchantStoreQueryDTO.setStoreName(storeVO.getStoreName());
        merchantStoreQueryDTO.setType(storeVO.getType());
        merchantStoreQueryDTO.setStatus(storeVO.getStatus());
        merchantStoreQueryDTO.setProvince(storeVO.getProvince());
        merchantStoreQueryDTO.setCity(storeVO.getCity());
        merchantStoreQueryDTO.setArea(storeVO.getArea());
        merchantStoreQueryDTO.setGroupId(storeVO.getGroupId());
        merchantStoreQueryDTO.setGroupIds(storeVO.getGroupIds());
        merchantStoreQueryDTO.setGroupName(storeVO.getGroupName());
        merchantStoreQueryDTO.setHavingGroup(storeVO.getHavingGroup());
        merchantStoreQueryDTO.setMerchantStoreGroupIds(storeVO.getMerchantStoreGroupIds());
        merchantStoreQueryDTO.setPageIndex(storeVO.getPageIndex());
        merchantStoreQueryDTO.setPageSize(storeVO.getPageSize());
        merchantStoreQueryDTO.setStoreNo(storeVO.getStoreNo());
        return merchantStoreQueryDTO;
    }

//    public static final MerchantStoreQueryDTO storeLikeVO2DTO(MerchantStoreLinkedVO storeVO){
//
//
//        if (storeVO == null) {
//            return null;
//        }
//        MerchantStoreQueryDTO merchantStoreQueryDTO = new MerchantStoreQueryDTO();
//        merchantStoreQueryDTO.setStoreNo(storeVO.getStoreNo());
//        merchantStoreQueryDTO.setTenantId(storeVO.getTenantId());
//        merchantStoreQueryDTO.setStoreName(storeVO.getStoreName());
//        merchantStoreQueryDTO.setType(storeVO.getType());
//        merchantStoreQueryDTO.setStatus(storeVO.getStatus());
//        merchantStoreQueryDTO.setProvince(storeVO.getProvince());
//        merchantStoreQueryDTO.setCity(storeVO.getCity());
//        merchantStoreQueryDTO.setArea(storeVO.getArea());
//        merchantStoreQueryDTO.setGroupId(storeVO.getGroupId());
//        return merchantStoreQueryDTO;
//    }

    public static MerchantStoreCommandReq merchantStoreOpenReqToStoreCommandReq(MerchantStoreOpenReq merchantStoreOpenReq, MerchantStoreResultResp merchantStoreResultResp) {
        if (merchantStoreOpenReq == null) {
            return null;
        }

        MerchantStoreCommandReq merchantStoreCommandReq = new MerchantStoreCommandReq();
        merchantStoreCommandReq.setId(merchantStoreResultResp.getId());
        merchantStoreCommandReq.setTenantId(merchantStoreResultResp.getTenantId());
        merchantStoreCommandReq.setStoreName(merchantStoreOpenReq.getStoreName());
        merchantStoreCommandReq.setType(merchantStoreOpenReq.getType());
        merchantStoreCommandReq.setRegisterTime(merchantStoreResultResp.getRegisterTime());
        merchantStoreCommandReq.setRemark(merchantStoreOpenReq.getRemark());
        merchantStoreCommandReq.setBillSwitch(Optional.ofNullable(merchantStoreOpenReq.getBillSwitch()).orElse(BillSwitchEnum.OPEN.getCode()));
        merchantStoreCommandReq.setOnlinePayment(Optional.ofNullable(merchantStoreOpenReq.getOnlinePayment()).orElse(OnlinePaymentEnum.SHUTDOWN.getCode()));
        merchantStoreCommandReq.setStoreNo(merchantStoreOpenReq.getStoreNo());
        merchantStoreCommandReq.setBalanceAuthority(Optional.ofNullable(merchantStoreOpenReq.getBalanceAuthority()).orElse(BalanceAuthorityTypeEnum.CLOSE_BALANCE_AUTH.getType()));
        return merchantStoreCommandReq;
    }

    public static MerchantStoreCommandReq merchantStoreOpenReqToCreateStoreCommandReq(MerchantStoreOpenReq merchantStoreOpenReq, Long tenantId) {
        if (merchantStoreOpenReq == null) {
            return null;
        }

        MerchantStoreCommandReq merchantStoreCommandReq = new MerchantStoreCommandReq();
        merchantStoreCommandReq.setTenantId(tenantId);
        merchantStoreCommandReq.setStoreName(merchantStoreOpenReq.getStoreName());
        merchantStoreCommandReq.setType(merchantStoreOpenReq.getType());
        merchantStoreCommandReq.setRegisterTime(LocalDateTime.now());
        merchantStoreCommandReq.setRemark(merchantStoreOpenReq.getRemark());
        merchantStoreCommandReq.setBillSwitch(Optional.ofNullable(merchantStoreOpenReq.getBillSwitch()).orElse(BillSwitchEnum.OPEN.getCode()));
        merchantStoreCommandReq.setOnlinePayment(Optional.ofNullable(merchantStoreOpenReq.getOnlinePayment()).orElse(OnlinePaymentEnum.SHUTDOWN.getCode()));
        merchantStoreCommandReq.setStoreNo(merchantStoreOpenReq.getStoreNo());
        merchantStoreCommandReq.setBalanceAuthority(Optional.ofNullable(merchantStoreOpenReq.getBalanceAuthority()).orElse(BalanceAuthorityTypeEnum.CLOSE_BALANCE_AUTH.getType()));
        return merchantStoreCommandReq;
    }
}

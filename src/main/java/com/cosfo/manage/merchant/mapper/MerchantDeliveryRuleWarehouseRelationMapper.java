package com.cosfo.manage.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryRuleWarehouseRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 门店运费规则 mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MerchantDeliveryRuleWarehouseRelationMapper extends BaseMapper<MerchantDeliveryRuleWarehouseRelation> {

    /**
     * 根据ruleIds删除数据
     *
     * @param ruleIds
     * @param tenantId
     * @return
     */
    int deleteByRuleIds(@Param("ruleIds") List<Long> ruleIds, @Param("tenantId") Long tenantId);

    /**
     * 根据ruleId、仓库编号删除数据
     *
     * @param ruleId
     * @param tenantId
     * @param warehouseNoList
     * @return
     */
    int deleteByWarehouseNoList(@Param("ruleId") Long ruleId, @Param("tenantId") Long tenantId, @Param("warehouseNoList") List<Integer> warehouseNoList);
}

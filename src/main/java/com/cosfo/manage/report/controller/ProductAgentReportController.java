package com.cosfo.manage.report.controller;

import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.product.model.dto.ProductStockChangeRecordQueryDTO;
import com.cosfo.manage.product.model.vo.ProductAgentWarehouseShelfLifeVO;
import com.cosfo.manage.product.model.vo.ProductAgentWarehouseVO;
import com.cosfo.manage.product.model.vo.ProductStockChangeRecordVO;
import com.cosfo.manage.report.model.dto.ProductAgentShelfLifeQueryDTO;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseDateQueryDTO;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseOverviewQueryDTO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseAggregationVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseOverviewVO;
import com.cosfo.manage.report.service.ProductAgentReportService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 描述: 实时数据-代仓品
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/3
 */
@RestController
@RequestMapping("/product/agent")
public class ProductAgentReportController extends BaseController {

    @Resource
    private ProductAgentReportService productAgentReportService;

    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    @Resource
    private ProductFacade productFacade;
    /**
     * 查询仓库列表
     *
     * @return
     */
    @PostMapping("/query/warehouse-list")
    public CommonResult<List<ProductAgentWarehouseVO>> queryWarehouseList(){
        return productAgentReportService.queryWarehouseList(getMerchantInfoDTO());
    }

    /**
     * 仓库维度-查询仓库数据
     *
     * @return
     */
    @PostMapping("query/warehouse-data-list")
    public CommonResult<PageInfo<ProductAgentWarehouseDataVO>> warehouseDataList(@Valid @RequestBody ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO){
        return productAgentReportService.warehouseDataList(productAgentWarehouseDateQueryDTO, getMerchantInfoDTO());
    }

    /**
     * 货品维度-查询仓库-货品聚合数据
     *
     * @return
     */
    @PostMapping("query/warehouse-aggregation-list")
    public CommonResult<PageInfo<ProductAgentWarehouseAggregationVO>> warehouseAggregationList(@RequestBody ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO){
        return productAgentReportService.warehouseAggregationList(productAgentWarehouseDateQueryDTO, getMerchantInfoDTO());
    }

    /**
     * 查询仓库库存总览信息
     *
     * @return
     */
    @PostMapping("/query/warehouse-overview")
    public CommonResult<ProductAgentWarehouseOverviewVO> warehouseOverview(@RequestBody ProductAgentWarehouseOverviewQueryDTO productAgentWarehouseOverviewQueryDTO){
        return productAgentReportService.warehouseOverview(productAgentWarehouseOverviewQueryDTO, getMerchantInfoDTO());
    }

    /**
     * 导出报表
     *
     * @param productAgentWarehouseDateQueryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export")
    public CommonResult export(@RequestBody ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO){
        return productAgentReportService.export(productAgentWarehouseDateQueryDTO, getMerchantInfoDTO());
    }

    /**
     * 库存保质期查询
     * @param productAgentShelfLifeQueryDTO
     * @return
     */
    @PostMapping("/shelf-life/query/page")
    public CommonResult<PageInfo<ProductAgentWarehouseShelfLifeVO>> queryWarehouseShelfLife(@RequestBody @Valid ProductAgentShelfLifeQueryDTO productAgentShelfLifeQueryDTO) {
        return productAgentReportService.pageQueryBatchInventory(productAgentShelfLifeQueryDTO, getMerchantInfoDTO().getTenantId());
    }

    /**
     * 查询库存变动记录
     * @param productStockChangeRecordQueryVO
     * @return
     */
    @PostMapping("/query/stock-change-record")
    public CommonResult<PageInfo<ProductStockChangeRecordVO>> queryStockChangeRecord(@RequestBody ProductStockChangeRecordQueryDTO productStockChangeRecordQueryVO) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        if (grayReleaseConfig.executeProductCenterGray(loginContextInfoDTO.getTenantId ())) {
            return productAgentReportService.pageQueryStockChangeRecord (productStockChangeRecordQueryVO, loginContextInfoDTO);
        }else {
            return productAgentReportService.pageQueryStockChangeRecordOld (productStockChangeRecordQueryVO, loginContextInfoDTO);
        }
    }
}

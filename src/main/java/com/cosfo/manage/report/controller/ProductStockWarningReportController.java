package com.cosfo.manage.report.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.product.model.dto.ProductStockWarningQueryDTO;
import com.cosfo.manage.product.model.dto.ProductStockWarningSimpleDTO;
import com.cosfo.manage.product.model.vo.ProductStockWarningVO;
import com.cosfo.manage.report.model.dto.ProductStockWarnInput;
import com.cosfo.manage.report.model.dto.StockForWaringConfigDTO;
import com.cosfo.manage.report.service.ProductStockForewarningReportService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 数据-库存预警
 *
 * @author: xiaowk
 * @date: 2023/10/30 下午1:47
 */
@RestController
@RequestMapping("/product/stock-warning")
public class ProductStockWarningReportController extends BaseController {

    @Resource
    private ProductStockForewarningReportService productStockForewarningReportService;

    /**
     * 查询货品库存预警列表
     *
     * @return
     */
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:product_stock_forewaring:query", expireError = true)
    @PostMapping("/query/list")
    public CommonResult<PageInfo<ProductStockWarningVO>> queryProductWarnList(@RequestBody @Valid ProductStockWarnInput productStockWarnInput) {
        productStockWarnInput.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(productStockForewarningReportService.queryProductWarnList(productStockWarnInput));
    }

    /**
     * 导出货品库存预警列表
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export-async/list")
    public CommonResult<Long> exportProductWarnList(@RequestBody ProductStockWarnInput productStockWarnInput) {
        productStockWarnInput.setTenantId(getMerchantInfoDTO().getTenantId());
        Long resId = productStockForewarningReportService.exportProductWarnList(productStockWarnInput);
        return CommonResult.ok(resId);
    }

    /**
     * 获取库存预警配置
     *
     * @return
     */
    @PostMapping("/get/config")
    public CommonResult<StockForWaringConfigDTO> getReportConfig() {

        StockForWaringConfigDTO pendingConfig = productStockForewarningReportService.getReportConfig(getMerchantInfoDTO().getTenantId(), false);
        if (pendingConfig != null) {
            return CommonResult.ok(pendingConfig);
        }
        return CommonResult.ok(productStockForewarningReportService.getReportConfig(getMerchantInfoDTO().getTenantId(), true));
    }

    /**
     * 更新库存预警配置
     *
     * @param stockForWaringConfigDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:product_stock_forewaring:update", expireError = true)
    @PostMapping("/upsert/config")
    public CommonResult<Boolean> updateReportConfig(@Valid @RequestBody StockForWaringConfigDTO stockForWaringConfigDTO) {
        return CommonResult.ok(productStockForewarningReportService.updateReportConfig(stockForWaringConfigDTO, getMerchantInfoDTO().getTenantId()));
    }

    /**
     * 查询库存预警汇总
     * @param productStockWarningQueryDTO
     * @return
     */
    @PostMapping("/query/summary")
    public CommonResult<List<ProductStockWarningSimpleDTO>> queryWarningSummary(@RequestBody ProductStockWarningQueryDTO productStockWarningQueryDTO) {
        productStockWarningQueryDTO.setTenantId(UserLoginContextUtils.getTenantId());
        return CommonResult.ok(productStockForewarningReportService.queryWarningSummary(productStockWarningQueryDTO));
    }
}

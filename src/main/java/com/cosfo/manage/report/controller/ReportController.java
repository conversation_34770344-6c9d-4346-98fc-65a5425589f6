package com.cosfo.manage.report.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStorePurchaseQueryDTO;
import com.cosfo.manage.merchant.model.vo.MerchantStorePurchaseQueryVO;
import com.cosfo.manage.merchant.model.vo.MerchantStorePurchaseVO;
import com.cosfo.manage.product.model.dto.ProductMovementQueryDTO;
import com.cosfo.manage.product.model.dto.ProductSalesOverviewQueryDTO;
import com.cosfo.manage.product.model.vo.ProductMovementQueryVO;
import com.cosfo.manage.product.model.vo.ProductMovementVO;
import com.cosfo.manage.product.model.vo.ProductSalesOverviewQueryVO;
import com.cosfo.manage.product.model.vo.ProductSalesOverviewVO;
import com.cosfo.manage.report.converter.ReportConverter;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.dto.ReportQueryDTO;
import com.cosfo.manage.report.model.vo.DamageDetailReportVO;
import com.cosfo.manage.report.model.vo.DamageSaleRatioDetailReportVO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportResultVO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportVO;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.model.vo.PurchaseDetailReportVO;
import com.cosfo.manage.report.model.vo.PurchasesBackDetailReportVO;
import com.cosfo.manage.report.model.vo.ReportCommonQueryVO;
import com.cosfo.manage.report.model.vo.ReportQueryVO;
import com.cosfo.manage.report.service.MerchantStorePurchaseReportService;
import com.cosfo.manage.report.service.ReportService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 报表
 *
 * <AUTHOR>
 * @date 2022/10/10 9:12
 */
@RestController
@RequestMapping("/report")
public class ReportController extends BaseController {

    @Resource
    private ReportService reportService;
    @Resource
    private MerchantStorePurchaseReportService merchantStorePurchaseReportService;
    /**
     * 销售概况
     *
     * @param productSalesOverviewQueryVO
     * @return
     */
    @PostMapping("/query/product-sales-overview")
    public CommonResult<ProductSalesOverviewVO> queryProductSalesOverview(@RequestBody ProductSalesOverviewQueryVO productSalesOverviewQueryVO){
        ProductSalesOverviewQueryDTO productSalesOverviewQueryDTO = new ProductSalesOverviewQueryDTO();
        BeanUtils.copyProperties(productSalesOverviewQueryVO, productSalesOverviewQueryDTO);
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return reportService.queryProductSalesOverview(productSalesOverviewQueryDTO, loginContextInfoDTO);
    }

    /**
     * 商品动销
     *
     * @param productMovementQueryVO
     * @return
     */
    @PostMapping("/query/product-movement")
    public CommonResult<ProductMovementVO> queryProductMovement(@RequestBody ProductMovementQueryVO productMovementQueryVO){
        ProductMovementQueryDTO productMovementQueryDTO = new ProductMovementQueryDTO();
        BeanUtils.copyProperties(productMovementQueryVO, productMovementQueryDTO);
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return reportService.queryProductMovement(productMovementQueryDTO, loginContextInfoDTO);
    }

    /**
     * 门店采购概况
     *
     * @param merchantStorePurchaseQueryVO
     * @return
     */
    @PostMapping("/query/merchant-store-purchase")
    public CommonResult<MerchantStorePurchaseVO> queryMerchantStorePurchase(@RequestBody MerchantStorePurchaseQueryVO merchantStorePurchaseQueryVO){
        MerchantStorePurchaseQueryDTO merchantStorePurchaseQueryDTO = new MerchantStorePurchaseQueryDTO();
        BeanUtils.copyProperties(merchantStorePurchaseQueryVO, merchantStorePurchaseQueryDTO);
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return reportService.queryMerchantStorePurchase(merchantStorePurchaseQueryDTO, loginContextInfoDTO);
    }

    /**
     * 导出商品销售数据
     *
     * @param reportQueryVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/product-detail-sales")
    public CommonResult exportProductDetailSales(@RequestBody ReportQueryVO reportQueryVO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        ReportQueryDTO reportQueryDTO = new ReportQueryDTO();
        BeanUtils.copyProperties(reportQueryVO, reportQueryDTO);
        reportQueryDTO.setType(FileDownloadTypeEnum.PRODUCT_DETAIL_SALES.getType());
        return reportService.exportReportExcel(reportQueryDTO, loginContextInfoDTO);
    }

    /**
     * 导出门店采购数据
     *
     * @param reportQueryVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/merchant-store-detail-purchase")
    public CommonResult exportMerchantStoreDetailPurchase(@RequestBody ReportQueryVO reportQueryVO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        ReportQueryDTO reportQueryDTO = new ReportQueryDTO();
        BeanUtils.copyProperties(reportQueryVO, reportQueryDTO);
        reportQueryDTO.setType(FileDownloadTypeEnum.MERCHANT_STORE_DETAIL_PURCHASE.getType());
        return reportService.exportReportExcel(reportQueryDTO, loginContextInfoDTO);
    }

    /**
     * 采购明细列表
     * @param reportCommonQueryVO
     * @return
     */
    @PostMapping("/query/purchase/detail")
    public CommonResult<PageInfo<PurchaseDetailReportVO>> queryPurchaseDetail(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO queryDTO = ReportConverter.reportCommonQueryVO2DTO(reportCommonQueryVO);
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return reportService.queryPurchaseDetailReport(queryDTO);
    }

    /**
     * 采购明细导出
     * @param reportCommonQueryVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/purchase/detail")
    public CommonResult exportPurchaseDetailReport(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO queryDTO = ReportConverter.reportCommonQueryVO2DTO(reportCommonQueryVO);
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        reportService.exportPurchaseDetailReport(queryDTO);
        return CommonResult.ok();
    }

    /**
     * 采购退货明细
     * @param reportCommonQueryVO
     * @return
     */
    @PostMapping("/query/purchase/back/detail")
    public CommonResult<PageInfo<PurchasesBackDetailReportVO>> queryPurchaseBackDetail(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO queryDTO = ReportConverter.reportCommonQueryVO2DTO(reportCommonQueryVO);
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        CommonResult<PageInfo<PurchasesBackDetailReportVO>> pageInfoCommonResult = reportService.queryPurchaseBackDetailReport(queryDTO);
        return pageInfoCommonResult;
    }

    /**
     * 采购退货明细导出
     * @param reportCommonQueryVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/purchase/back/detail")
    public CommonResult exportPurchaseBackDetailReport(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO queryDTO = ReportConverter.reportCommonQueryVO2DTO(reportCommonQueryVO);
        queryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        reportService.exportPurchaseBackDetailReport(queryDTO);
        return CommonResult.ok();
    }


    /**
     * 货损明细列表
     * @param reportCommonQueryVO
     * @return
     */
    @PostMapping("/query/damage/detail")
    public CommonResult<PageInfo<DamageDetailReportVO>> queryDamageDetail(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO reportQueryDTO = new ReportCommonQueryDTO();
        BeanUtils.copyProperties(reportCommonQueryVO, reportQueryDTO);
        reportQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        CommonResult<PageInfo<DamageDetailReportVO>> pageInfoCommonResult = reportService.queryDamageDetail(reportQueryDTO);
        return pageInfoCommonResult;
    }

    /**
     * 货损明细导出
     * @param reportCommonQueryVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/damage/detail")
    public CommonResult exportDamageDetailReport(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO reportQueryDTO = new ReportCommonQueryDTO();
        BeanUtils.copyProperties(reportCommonQueryVO, reportQueryDTO);
        reportQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        reportService.exportDamageDetailReport(reportQueryDTO);
        return CommonResult.ok();
    }

    /**
     * 损售比明细列表
     * @param reportCommonQueryVO
     * @return
     */
    @PostMapping("/query/damage-sale-ratio/detail")
    public CommonResult<PageInfo<DamageSaleRatioDetailReportVO>> queryDamageSaleRatioDetail(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO reportQueryDTO = new ReportCommonQueryDTO();
        BeanUtils.copyProperties(reportCommonQueryVO, reportQueryDTO);
        reportQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        CommonResult<PageInfo<DamageSaleRatioDetailReportVO>> pageInfoCommonResult = reportService.queryDamageSaleRatioDetail(reportQueryDTO);
        return pageInfoCommonResult;
    }

    /**
     * 损售比明细导出
     * @param reportCommonQueryVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/damage-sale-ratio/detail")
    public CommonResult exportDamageSaleRatioDetailReport(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO reportQueryDTO = new ReportCommonQueryDTO();
        BeanUtils.copyProperties(reportCommonQueryVO, reportQueryDTO);
        reportQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        reportService.exportDamageSaleRatioDetailReport(reportQueryDTO);
        return CommonResult.ok();
    }

    /**
     * 数据概况
     * @param reportCommonQueryVO
     * @return
     */
    @PostMapping("/query/damage/detail-agg")
    public CommonResult<PurchaseDetailAggVO> queryPurchaseDetailAgg(@RequestBody ReportCommonQueryVO reportCommonQueryVO) {
        ReportCommonQueryDTO reportQueryDTO = new ReportCommonQueryDTO();
        BeanUtils.copyProperties(reportCommonQueryVO, reportQueryDTO);
        reportQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        CommonResult<PurchaseDetailAggVO> purchaseDetailAggVOCommonResult = reportService.queryPurchaseDetailAgg(reportQueryDTO);
        return purchaseDetailAggVOCommonResult;
    }

    /**
     * 查询门店采购明细
     * @param merchantStorePurchaseReportVO
     * @return
     */
    @RequestMapping(value = "/query/list-all", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStorePurchaseReportResultVO>> listAll(@RequestBody MerchantStorePurchaseReportVO merchantStorePurchaseReportVO) {
        merchantStorePurchaseReportVO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(merchantStorePurchaseReportService.listAll(merchantStorePurchaseReportVO));
    }

    /**
     * 导出门店采购明细
     * @param merchantStorePurchaseReportVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export/export-by-condition", method = RequestMethod.POST)
    public CommonResult export(@RequestBody MerchantStorePurchaseReportVO merchantStorePurchaseReportVO) {
        merchantStorePurchaseReportService.export(getMerchantInfoDTO(), merchantStorePurchaseReportVO);
        return CommonResult.ok();
    }
}

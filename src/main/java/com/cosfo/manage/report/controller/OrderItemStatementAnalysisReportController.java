package com.cosfo.manage.report.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.product.model.vo.OrderItemStatementAnalysisVO;
import com.cosfo.manage.report.model.dto.OrderItemStatementAnalysisInput;
import com.cosfo.manage.report.service.OrderItemStatementAnalysisReportService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 数据-经营分析-订单对账表
 *
 * @author: xiaowk
 * @date: 2024/9/24 下午4:52
 */
@RestController
@RequestMapping("/report/order/statement-analysis")
public class OrderItemStatementAnalysisReportController extends BaseController {

    @Resource
    private OrderItemStatementAnalysisReportService orderItemStatementAnalysisReportService;

    /**
     * 查询订单对账表
     *
     * @return
     */
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:order-item-statement-analysis:query", expireError = true)
    @PostMapping("/query/list")
    public CommonResult<PageInfo<OrderItemStatementAnalysisVO>> queryList(@RequestBody @Valid OrderItemStatementAnalysisInput orderItemStatementAnalysisInput) {
        orderItemStatementAnalysisInput.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(orderItemStatementAnalysisReportService.queryList(orderItemStatementAnalysisInput));
    }

    /**
     * 导出订单对账表
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export-async/list")
    public CommonResult<Long> exportList(@RequestBody OrderItemStatementAnalysisInput orderItemStatementAnalysisInput) {
        orderItemStatementAnalysisInput.setTenantId(getMerchantInfoDTO().getTenantId());
        Long resId = orderItemStatementAnalysisReportService.exportList(orderItemStatementAnalysisInput);
        return CommonResult.ok(resId);
    }

}

package com.cosfo.manage.report.model.vo;

import com.cosfo.manage.common.model.dto.PageQueryDTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date : 2023/3/2 18:18
 */
@Data
public class MerchantStorePurchaseReportResultVO extends PageQueryDTO {
    private Long id;
    /**
     * 门店编号
     */
    private Long storeNo;
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private String type;

    /**
     * 门店分组
     */
    private String storeGroup;
    /**
     * 商品编码
     */
    private Long productNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 0自营仓、1三方仓
     */
    private String warehouseType;

    /**
     * 0品牌方配送、1三方配送、-1非法数据
     */
    private Integer deliveryType;

    /**
     * 前台分组
     */
    private String marketClassification;

    /**
     * 后台类目
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 商品总金额
     */
    private BigDecimal amount;

    /**
     * 配送日期
     */
    private String deliveryTime;

    /**
     * 实际收货数量
     */
    private Integer receivedQuantity;

    /**
     * 补发数量
     */
    private Integer reissueQuantity;

    /**
     * 售后总金额
     */
    private BigDecimal afterSaleAmount;

    /**
     * 商品类型 0无货 1报价商品 2自营
     */
    private String goodsType;

}

package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 滞销货品汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Getter
@Setter
@TableName("goods_no_sale_summary")
public class GoodsNoSaleSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签（yyyyMMdd）
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 货品id
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 售价
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}

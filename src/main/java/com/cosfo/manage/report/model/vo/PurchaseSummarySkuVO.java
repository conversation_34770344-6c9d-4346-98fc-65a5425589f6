package com.cosfo.manage.report.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: xiaowk
 * @time: 2024/1/17 下午4:38
 */
@Data
public class PurchaseSummarySkuVO {

    /**
     * sku
     */
    private Long skuId;

    /**
     * spu_id
     */
    private Long spuId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 供应商名称
     */
    private String supplier;

    /**
     * 供应商
     */
    private Integer supplierId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String unit;

    /**
     * 合计金额
     */
    private BigDecimal totalAmount;

    /**
     * 合计数量
     */
    private Integer totalQuantity;

    /**
     * 三级类目
     */
    private Integer categoryId;

    /**
     * 一级类目
     */
    private String firstCategory;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 三级类目
     */
    private String thirdCategory;


}

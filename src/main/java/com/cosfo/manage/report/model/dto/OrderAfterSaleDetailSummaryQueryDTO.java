package com.cosfo.manage.report.model.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleDetailSummaryQueryDTO {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 排除的订单编号
     */
    private Set<String> excludeOrderNoList;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 特殊标记
     */
    private boolean specialFlag;

    /**
     * 门店类型标识 1、月结=直营店 2、微信现结=加盟 不传表示所有
     * 通过支付方式区分（月结账期、余额=直营店，微信、支付宝现结=加盟店）
     */
    private Integer storeType;
}

package com.cosfo.manage.report.model.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class DamageSaleRatioDetailReportVO implements Serializable {

    private Long id;

    /**
     * 时间
     */
    private LocalDate damageDate;

    /**
     * sku
     */
    private Long skuId;


    /**
     * spu_id
     */
    private Long spuId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String unit;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 仓库编号
     */
    private Long warehouseId;

    /**
     * 采购人
     */
    private String purchaser;

    /**
     * 货损金额
     */
    private BigDecimal damageAmount;

    /**
     * 销售出库数量
     */
    private Integer damageQuantity;

    /**
     * 销售出库金额
     */
    private BigDecimal outboundAmount;

    /**
     * 货损数量
     */
    private Integer outboundQuantity;

    /**
     * 退货金额
     */
    private BigDecimal backAmount;

    /**
     * 退货数量
     */
    private Integer backQuantity;

    /**
     * 退货金额
     */
    private BigDecimal damageSaleRatio;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 一级类目
     */
    private Integer categoryId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 仓库供应商
     */
    private String warehouseServiceProvider;
}

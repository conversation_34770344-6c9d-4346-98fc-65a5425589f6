package com.cosfo.manage.report.model.vo;

import com.cosfo.manage.common.model.vo.PageRequestVO;
import lombok.Data;

import java.util.List;

/**
 * @desc 报表公共查询参数 view object
 * <AUTHOR>
 * @date 2023/1/13 10:43
 */
@Data
public class ReportCommonQueryVO extends PageRequestVO {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 货品名称
     */
    private String name;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目ids
     */
    private List<Long> categoryIds;
}

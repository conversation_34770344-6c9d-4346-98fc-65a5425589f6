package com.cosfo.manage.report.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MarketItemSoldOutExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String firstClassification;

    private String secondClassification;

    private String title;

    private String specification;

    private Long itemId;

    private BigDecimal salePrice;

    private String soldOutTime;

    private String onSaleTime;

    private String productSource;

    private String productTitle;

    private String productSpecification;

    private Long skuId;

    private String productFirstCategory;

    private String productSecondCategory;

    private String productThirdCategory;
}

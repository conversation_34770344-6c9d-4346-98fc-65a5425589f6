package com.cosfo.manage.report.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class PurchasesBackDetailReportDTO {

    private Long id;

    /**
     * 退货日期
     */
    private LocalDate backDate;

    /**
     * 退货批次
     */
    private String backNo;

    /**
     * 退货类型
     */
    private String backType;

    /**
     * 发起人
     */
    private String operator;

    /**
     * 采购人
     */
    private String purchaser;

    /**
     * sku
     */
    private Long skuId;

    /**
     * spu_id
     */
    private Long spuId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String unit;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 退货仓库
     */
    private String backWarehouse;

    /**
     * 仓库编号
     */
    private Long backWarehouseId;

    /**
     * 出库状态
     */
    private String outboundStatus;

    /**
     * 退货金额
     */
    private BigDecimal backAmount;

    /**
     * 退货数量
     */
    private Integer backQuantity;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 三级类目
     */
    private Integer categoryId;

    /**
     * 时间标签 yyyyMMdd
     */
    private String timeTag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 仓库服务商
     */
    private String warehouseServiceProvider;
    /**
     * 供应商
     */
    private String supplier;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;
}

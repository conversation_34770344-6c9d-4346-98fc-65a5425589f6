package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 采购退货明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Getter
@Setter
@TableName("purchases_back_detail_report")
public class PurchasesBackDetailReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 退货日期
     */
    @TableField("back_date")
    private LocalDate backDate;

    /**
     * 退货批次
     */
    @TableField("back_no")
    private String backNo;

    /**
     * 退货类型
     */
    @TableField("back_type")
    private Integer backType;

    /**
     * 发起人
     */
    @TableField("operator")
    private String operator;

    /**
     * 采购人
     */
    @TableField("purchaser")
    private String purchaser;

    /**
     * sku
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * spu_id
     */
    @TableField("spu_id")
    private Long spuId;

    /**
     * 商品名称
     */
    @TableField("name")
    private String name;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 规格单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 保质期
     */
    @TableField("quality_date")
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    @TableField("production_date")
    private LocalDate productionDate;

    /**
     * 退货仓库
     */
    @TableField("back_warehouse")
    private String backWarehouse;

    /**
     * 仓库编号
     */
    @TableField("back_warehouse_id")
    private Long backWarehouseId;

    /**
     * 出库状态
     */
    @TableField("outbound_status")
    private String outboundStatus;

    /**
     * 退货金额
     */
    @TableField("back_amount")
    private BigDecimal backAmount;

    /**
     * 退货数量
     */
    @TableField("back_quantity")
    private Integer backQuantity;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 三级类目
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 时间标签 yyyyMMdd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 供应商名称
     */
    @TableField("supplier")
    private String supplier;

    /**
     * 仓库服务商
     */
    @TableField("warehouse_service_provider")
    private String warehouseServiceProvider;

    /**
     * 采购单价
     */
    @TableField("purchase_price")
    private BigDecimal purchasePrice;
}

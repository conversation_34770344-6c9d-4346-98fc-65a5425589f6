package com.cosfo.manage.report.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/10/30 下午2:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductStockWarnInput {
    /**
     * 页大小
     */
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

    /**
     * 页码
     */
    @NotNull(message = "pageIndex不能为空")
    private Integer pageIndex;

    /**
     * 货品名称
     */
    private String title;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 预警状态
     */
    private Integer status;

    private Long tenantId;

    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 类目Id
     */
    private List<Long> categoryIds;

    private List<Long> skuIds;

    private Integer warehouseNo;

    /**
     * 停用状态 0-停用 1-启用
     */
    private Integer useFlag;

    /**
     * 导出查询条件
     */
    private String params;

}

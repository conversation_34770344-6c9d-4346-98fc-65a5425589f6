package com.cosfo.manage.report.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cosfo.manage.product.model.dto.WarehouseSkuFenceAreaDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 */
@Data
public class PosOrderAuditVO implements Serializable {
    /**
     * 1=美团
     */
    private Integer channelType;
    /**
     * 稽核区间
     */
    private String auditTime;
    /**
     * 稽核自然周
     */
    private LocalDate reportWeek;

    /**
     * 外部系统门店code
     */
    private String outStoreCode;

    /**
     * 外部系统门店名称
     */
    private String outStoreName;
    /**
     * 外部系统物料编码
     */
    private String outItemCode;
    /**
     * 外部系统物料名称
     */
    private String outItemName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 销用总量
     */
    private BigDecimal useCount;

    /**
     * 应进货总量
     */
    private BigDecimal needBuyCount;

    /**
     * 实际帆台进货总量
     */
    private BigDecimal realBuyCount;

    /**
     * 差异总量
     */
    private BigDecimal diffCount;

    /**
     * 是否疑似私采状态
     * 正常=0 私采=1
     */
    private Integer status;
    /**
     * 差异比例
     */
    private BigDecimal privateProcurement;
}

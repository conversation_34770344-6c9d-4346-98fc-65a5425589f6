package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单明细对账分析
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@Getter
@Setter
@TableName("order_item_statement_analysis")
public class OrderItemStatementAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单来源:0：门店下单; 1：openapi调用; 2:总部代下单
     */
    @TableField("order_source")
    private Integer orderSource;

    /**
     * 门店id
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 门店编号
     */
    @TableField("store_no")
    private String storeNo;

    /**
     * 门店名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    @TableField("store_type")
    private Integer storeType;

    /**
     * 分组Id
     */
    @TableField("store_group_id")
    private Long storeGroupId;

    /**
     * 门店分组名
     */
    @TableField("store_group_name")
    private String storeGroupName;

    /**
     * 下单时间
     */
    @TableField("order_time")
    private LocalDateTime orderTime;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 配送时间
     */
    @TableField("delivery_time")
    private LocalDateTime deliveryTime;

    /**
     * 完成时间
     */
    @TableField("finished_time")
    private LocalDateTime finishedTime;

    /**
     * 应付价格(应付总额)
     */
    @TableField("payable_price")
    private BigDecimal payablePrice;

    /**
     * 配送费
     */
    @TableField("delivery_fee")
    private BigDecimal deliveryFee;

    /**
     * 总金额（实收金额）
     */
    @TableField("total_price")
    private BigDecimal totalPrice;

    /**
     * 支付方式 1、微信支付 2、账期 3、余额支付 4、支付宝支付 5、无需支付
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 支付渠道 0、微信 1、汇付
     */
    @TableField("online_pay_channel")
    private Integer onlinePayChannel;

    /**
     * 支付单号（支付平台交易订单号）
     */
    @TableField("payment_no")
    private String paymentNo;

    /**
     * 交易流水号（银行流水号）
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 支付金额
     */
    @TableField("pay_total_price")
    private BigDecimal payTotalPrice;

    /**
     * 配送仓类型 0,无仓1三方仓 2自营仓
     */
    @TableField("warehouse_type")
    private Integer warehouseType;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private String warehouseNo;

    /**
     * 配送仓名称
     */
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 库存仓服务商名称
     */
    @TableField("warehouse_service_name")
    private String warehouseServiceName;

    /**
     * 出库批次号
     */
    @TableField("outbound_batch_no")
    private String outboundBatchNo;

    /**
     * 出库时间
     */
    @TableField("outbound_time")
    private LocalDateTime outboundTime;

    /**
     * 供应商Id
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 商品名称
     */
    @TableField("item_title")
    private String itemTitle;

    /**
     * 自有编码
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 商品类型 0无货商品 1报价货品 2自营货品
     */
    @TableField("goods_type")
    private Integer goodsType;

    /**
     * 规格单位
     */
    @TableField("specification_unit")
    private String specificationUnit;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 一级分类名称
     */
    @TableField("first_classification_name")
    private String firstClassificationName;

    /**
     * 二级分类名称
     */
    @TableField("second_classification_name")
    private String secondClassificationName;

    /**
     * 货品id
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 货品sku编码
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * 货品名称
     */
    @TableField("goods_title")
    private String goodsTitle;

    /**
     * 一级类目
     */
    @TableField("first_category")
    private String firstCategory;

    /**
     * 二级类目
     */
    @TableField("second_category")
    private String secondCategory;

    /**
     * 三级类目
     */
    @TableField("third_category")
    private String thirdCategory;

    /**
     * 商品单价
     */
    @TableField("item_payable_price")
    private BigDecimal itemPayablePrice;

    /**
     * 商品数量
     */
    @TableField("item_amount")
    private Integer itemAmount;


    /**
     * 出库数量
     */
    @TableField("outbound_amount")
    private Integer outboundAmount;

    /**
     * 商品总价
     */
    @TableField("item_total_price")
    private BigDecimal itemTotalPrice;

    /**
     * 供应价
     */
    @TableField("supply_price")
    private BigDecimal supplyPrice;

    /**
     * 供应总价
     */
    @TableField("total_supply_price")
    private BigDecimal totalSupplyPrice;

    /**
     * 毛利率，百分位
     */
    @TableField("gross_margin_ratio")
    private BigDecimal grossMarginRatio;

    /**
     * 商品退款总金额
     */
    @TableField("item_refund_price")
    private BigDecimal itemRefundPrice;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}

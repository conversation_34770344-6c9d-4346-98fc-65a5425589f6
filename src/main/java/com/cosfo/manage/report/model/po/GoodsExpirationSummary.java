package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 货品过期汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Getter
@Setter
@TableName("goods_expiration_summary")
public class GoodsExpirationSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签（yyyyMMdd）
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 货品id
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 仓库号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 仓库
     */
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 批次
     */
    @TableField("batch")
    private String batch;

    /**
     * 有效期日期
     */
    @TableField("expiration_date")
    private LocalDate expirationDate;

    /**
     * 过期时批次库存数量
     */
    @TableField("expiration_batch_stock")
    private Integer expirationBatchStock;

    /**
     * 期末库存
     */
    @TableField("ending_batch_stock")
    private Integer endingBatchStock;

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 售价
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}

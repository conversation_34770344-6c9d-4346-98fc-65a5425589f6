package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/25
 */
@Data
@TableName("order_after_sale_inverted_summary")
public class OrderAfterSaleInvertedSummary {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;
    /**
     * 售后订单号
     */
    @TableField("after_sale_order_no")
    private String afterSaleOrderNo;
    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;
    /**
     * 售后申请时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;
    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;
    /**
     * 退款总额
     */
    @TableField("refund_total_amount")
    private BigDecimal refundTotalAmount;
    /**
     * 责任类型 0供应商1品牌方2门店
     */
    @TableField("responsibility_type")
    private Integer responsibilityType;
    /**
     * 品牌方应退金额
     */
    @TableField("brand_refundable_amount")
    private BigDecimal brandRefundableAmount;
    /**
     * 供应商应退金额
     */
    @TableField("supplier_refundable_amount")
    private BigDecimal supplierRefundableAmount;
    /**
     * 品牌方实退金额
     */
    @TableField("brand_actual_amount")
    private BigDecimal brandActualAmount;
    /**
     * 供应商实退金额
     */
    @TableField("supplier_actual_amount")
    private BigDecimal supplierActualAmount;
    /**
     * 品牌方应付给供应商的差额
     */
    @TableField("inverted_amount")
    private BigDecimal invertedAmount;

    @TableField("time_tag")
    private String timeTag;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private Long supplierId;
}

package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 门店进销稽核表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Getter
@Setter
@TableName("pos_order_audit")
public class PosOrderAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 1=美团
     */
    @TableField("channel_type")
    private Integer channelType;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 稽核自然周
     */
    @TableField("report_week")
    private LocalDate reportWeek;

    /**
     * 外部系统门店code
     */
    @TableField("out_store_code")
    private String outStoreCode;

    /**
     * 外部系统门店名称
     */
    @TableField("out_store_name")
    private String outStoreName;

    /**
     * 帆台门店id
     */
    @TableField("merchant_store_id")
    private Long merchantStoreId;

    /**
     * 帆台门店code
     */
    @TableField("merchant_store_code")
    private String merchantStoreCode;

    /**
     * 外部系统物料编码
     */
    @TableField("out_item_code")
    private String outItemCode;

    /**
     * 外部系统物料名称
     */
    @TableField("out_item_name")
    private String outItemName;
    /**
     * 帆台商品id
     */
    @TableField("market_item_id")
    private Long marketItemId;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 规格单位
     */
    @TableField("specification_unit")
    private String specificationUnit;

    /**
     * 销用总量
     */
    @TableField("use_count")
    private BigDecimal useCount;

    /**
     * 应进货总量
     */
    @TableField("need_buy_count")
    private BigDecimal needBuyCount;

    /**
     * 实际帆台进货总量
     */
    @TableField("real_buy_count")
    private BigDecimal realBuyCount;


}

package com.cosfo.manage.report.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/10/30 下午2:16
 */
@Data
public class StoreOrderDelayAnalysisInput {
    /**
     * 页大小
     */
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

    /**
     * 页码
     */
    @NotNull(message = "pageIndex不能为空")
    private Integer pageIndex;

    private Long tenantId;

    /**
     * 门店编号
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private Integer storeType;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店状态
     */
    private Integer storeStatus;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品编码id
     */
    private Long itemId;

    /**
     * 滞叫天数
     */
    private Integer delayDays;
}

package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 门店采购明细报表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MerchantStorePurchaseReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店编号
     */
    private Long storeNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     * 门店分组
     */
    private String storeGroup;

    /**
     * 商品编码
     */
    private Long productNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 0自营仓、1三方仓
     */
    private Integer warehouseType;

    /**
     * 0品牌方配送、1三方配送、-1非法数据
     */
    private Integer deliveryType;

    /**
     * 前台分组
     */
    private String marketClassification;

    /**
     * 后台类目
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 商品总金额
     */
    private BigDecimal amount;

    /**
     * 配送日期
     */
    private LocalDateTime deliveryTime;

    /**
     * 实际收货数量
     */
    private Integer receivedQuantity;

    /**
     * 补发数量
     */
    private Integer reissueQuantity;

    /**
     * 售后总金额
     */
    private BigDecimal afterSaleAmount;


}

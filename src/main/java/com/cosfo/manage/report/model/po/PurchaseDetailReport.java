package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 采购明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Getter
@Setter
@TableName("purchase_detail_report")
public class PurchaseDetailReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 采购日期
     */
    @TableField("purchase_date")
    private LocalDate purchaseDate;

    /**
     * 采购批次
     */
    @TableField("purchase_no")
    private String purchaseNo;

    /**
     * 采购人
     */
    @TableField("purchaser")
    private String purchaser;

    /**
     * 仓库
     */
    @TableField("warehouse")
    private String warehouse;

    /**
     * 仓库编号
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 采购单状态
     */
    @TableField("purchase_status")
    private String purchaseStatus;

    /**
     * sku
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * spu_id
     */
    @TableField("spu_id")
    private Long spuId;

    /**
     * 商品名称
     */
    @TableField("name")
    private String name;

    /**
     * 供应商名称
     */
    @TableField("supplier")
    private String supplier;

    /**
     * 供应商
     */
    @TableField("supplier_id")
    private Integer supplierId;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 规格单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 入库状态
     */
    @TableField("inbound_status")
    private String inboundStatus;

    /**
     * 入库时间
     */
    @TableField("inbound_date")
    private LocalDateTime inboundDate;

    /**
     * 采购金额
     */
    @TableField("purchase_amount")
    private BigDecimal purchaseAmount;

    /**
     * 实际入库金额
     */
    @TableField("inbound_amount")
    private BigDecimal inboundAmount;

    /**
     * 采购数量
     */
    @TableField("purchase_quantity")
    private Integer purchaseQuantity;

    /**
     * 实际入库数量
     */
    @TableField("inbound_quantity")
    private Integer inboundQuantity;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签 yyyyMMdd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 三级类目
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 仓库服务商
     */
    @TableField("warehouse_service_provider")
    private String warehouseServiceProvider;

    /**
     * 退货金额，批次+sku累计退货金额
     */
    @TableField("back_amount")
    private BigDecimal backAmount;

    /**
     * 退货数量，批次+sku累计退货数量
     */
    @TableField("back_quantity")
    private Integer backQuantity;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 采购单价
     */
    @TableField("purchase_price")
    private BigDecimal purchasePrice;
}

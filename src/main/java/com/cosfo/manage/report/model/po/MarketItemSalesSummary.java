package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商品销售汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Getter
@Setter
@TableName("market_item_sales_summary")
public class MarketItemSalesSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签 yyyy-MM-dd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 自有编码
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 商品名称
     */
    @TableField("item_title")
    private String itemTitle;

    /**
     * 商品规格
     */
    @TableField("item_specification")
    private String itemSpecification;

    /**
     * 数量
     */
    @TableField("total_amount")
    private Integer totalAmount;

    /**
     * 平均售卖单价
     */
    @TableField("average_payable_price")
    private BigDecimal averagePayablePrice;

    /**
     * 售卖总价
     */
    @TableField("total_price")
    private BigDecimal totalPrice;

    /**
     * 售后总金额
     */
    @TableField("total_refund_price")
    private BigDecimal totalRefundPrice;

    /**
     * 扣除售后的金额
     */
    @TableField("total_price_deducted_refund")
    private BigDecimal totalPriceDeductedRefund;

    /**
     * 货品平均单价
     */
    @TableField("good_average_supply_price")
    private BigDecimal goodAverageSupplyPrice;

    /**
     * 货品总价
     */
    @TableField("goods_total_supply_price")
    private BigDecimal goodsTotalSupplyPrice;

    /**
     * 销售与采购差额（剔除售后）
     */
    @TableField("sales_and_supply_difference_deducted_price")
    private BigDecimal salesAndSupplyDifferenceDeductedPrice;

    /**
     * 支付方式 1、微信2、账期 3、余额支付
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 等比换算后采购售后金额
     */
    @TableField("goods_refund_price")
    private BigDecimal goodsRefundPrice;

    /**
     * 扣除售后的采购金额
     */
    @TableField("goods_price_deducted_refund")
    private BigDecimal goodsPriceDeductedRefund;

}

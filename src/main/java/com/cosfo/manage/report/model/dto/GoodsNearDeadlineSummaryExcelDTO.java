package com.cosfo.manage.report.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 临期货品汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GoodsNearDeadlineSummaryExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库
     */
    private String warehouseName;

    /**
     * 批次
     */
    private String batch;

    /**
     * 进入临期的日期
     */
    private LocalDate enterDeadlineDate;

    /**
     * 有效期日期
     */
    private LocalDate expirationDate;

    /**
     * 进入临期批次库存
     */
    private Integer enterDeadlineBatchStock;

    /**
     * 期末库存
     */
    private Integer endingBatchStock;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 货品名称
     */
    private String title;

    /**
     * 货品规格
     */
    private String specification;

    /**
     * 一级类目
     */
    private String firstCategory;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 三级类目
     */
    private String thirdCategory;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 商品规则
     */
    private String itemSpecification;


    /**
     * 一级分类
     */
    private String firstClassificationName;

    /**
     * 二级分类
     */
    private String secondClassificationName;

}

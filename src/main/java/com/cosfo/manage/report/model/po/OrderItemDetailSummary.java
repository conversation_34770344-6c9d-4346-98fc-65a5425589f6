package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单明细详情汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Getter
@Setter
@TableName("order_item_detail_summary")
public class OrderItemDetailSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签 yyyy-MM-dd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 门店名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    @TableField("store_type")
    private Integer storeType;

    /**
     * 分组名称
     */
    @TableField("group_name")
    private String groupName;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 送货地址
     */
    @TableField("address")
    private String address;

    /**
     * 收货手机号
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 下单时间
     */
    @TableField("order_time")
    private LocalDateTime orderTime;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 支付方式 1、微信 2、账期 3、余额支付
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 配送时间
     */
    @TableField("delivery_time")
    private LocalDateTime deliveryTime;

    /**
     * 确认收货时间
     */
    @TableField("finished_time")
    private LocalDateTime finishedTime;

    /**
     * 商品名称
     */
    @TableField("item_title")
    private String itemTitle;

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 自有编码
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 商品规格
     */
    @TableField("item_specification")
    private String itemSpecification;

    /**
     * 定价方式0、百分比上浮 1、固定价上浮 2、固定价
     */
    @TableField("item_pricing_type")
    private Integer itemPricingType;

    /**
     * 定价数值
     */
    @TableField("item_pricing_number")
    private BigDecimal itemPricingNumber;

    /**
     * 单价
     */
    @TableField("payable_price")
    private BigDecimal payablePrice;

    /**
     * 数量
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 总价
     */
    @TableField("total_price")
    private BigDecimal totalPrice;

    /**
     * 运费
     */
    @TableField("delivery_fee")
    private BigDecimal deliveryFee;

    /**
     * 商品退款金额
     */
    @TableField("item_refund_price")
    private BigDecimal itemRefundPrice;

    /**
     * 运费退款金额
     */
    @TableField("delivery_refund_fee")
    private BigDecimal deliveryRefundFee;

    /**
     * 剔除售后的订单商品金额
     */
    @TableField("total_price_deducted_refund")
    private BigDecimal totalPriceDeductedRefund;

    /**
     * 订单备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 销售与采购差额
     */
    @TableField("sales_and_supply_difference")
    private BigDecimal salesAndSupplyDifference;

    /**
     * 销售与采购差额（剔除售后）
     */
    @TableField("sales_and_supply_difference_deducted_refund")
    private BigDecimal salesAndSupplyDifferenceDeductedRefund;

    /**
     * 货品名称
     */
    @TableField("goods_title")
    private String goodsTitle;

    /**
     * 货品sku
     */
    @TableField("goods_sku")
    private String goodsSku;

    /**
     * 货品规格
     */
    @TableField("goods_specification")
    private String goodsSpecification;

    /**
     * 货品供应商名称
     */
    @TableField("goods_supplier_name")
    private String goodsSupplierName;

    /**
     * 货品类型 1、鲜沐直供 2、代仓
     */
    @TableField("goods_type")
    private Integer goodsType;

    /**
     * 货品代仓计费规则
     */
    @TableField("goods_agent_rule")
    private String goodsAgentRule;

    /**
     * 货品代仓费用
     */
    @TableField("goods_agent_fee")
    private BigDecimal goodsAgentFee;

    /**
     * 货品单价费用
     */
    @TableField("goods_supply_price")
    private BigDecimal goodsSupplyPrice;

    /**
     * 货品总价
     */
    @TableField("goods_supply_total_price")
    private BigDecimal goodsSupplyTotalPrice;

    /**
     * 货品配送费
     */
    @TableField("goods_delivery_fee")
    private BigDecimal goodsDeliveryFee;

    /**
     * 货品退款金额
     */
    @TableField("goods_refund_price")
    private BigDecimal goodsRefundPrice;

    /**
     * 货品总计
     */
    @TableField("goods_total_price")
    private BigDecimal goodsTotalPrice;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 门店编码
     */
    @TableField("store_no")
    private String storeNo;

    /**
     * 收货人
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 一级分类
     */
    @TableField("first_classification")
    private String firstClassification;

    /**
     * 二级分类
     */
    @TableField("second_classification")
    private String secondClassification;

    /**
     * 货品配送费售后金额
     */
    @TableField("goods_delivery_fee_refund")
    private BigDecimal goodsDeliveryFeeRefund;

    /**
     * 订单完成节点
     */
    @TableField("order_finished_node")
    private String orderFinishedNode;
}

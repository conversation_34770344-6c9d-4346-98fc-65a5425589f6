package com.cosfo.manage.report.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cosfo.manage.common.model.dto.PageDTO;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 */
@Data
public class PosOrderAuditQueryDTO  extends BasePageInput implements Serializable {

    private Long tenantId;
    /**
     * 外部系统门店名称
     */
    private String outStoreName;
    /**
     * 1=美团
     */
    @NotNull(message = "pos渠道不能空")
    private Integer channelType;

    /**
     * 开始时间 yyyy-mm-dd 周一
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "开始时间不能空")
    private LocalDate beginWeek;
    /**
     * 结束时间 yyyy-mm-dd 周一
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "结束时间不能空")
    private LocalDate endWeek;
    /**
     * 外部系统物料名称
     */
    private String outItemName;
    /**
     * 状态 正常=0 私采=1
     */
    private Integer status;
    /**
     * 私采率
     */
    private BigDecimal privateProcurement;
}

package com.cosfo.manage.report.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 货损明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Getter
@Setter
@TableName("damage_detail_report")
public class DamageDetailReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出库日期
     */
    @TableField("outbound_date")
    private LocalDate outboundDate;

    /**
     * 批次
     */
    @TableField("damage_no")
    private String damageNo;

    /**
     * 仓库
     */
    @TableField("warehouse")
    private String warehouse;

    /**
     * 仓库编号
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * sku
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * spu_id
     */
    @TableField("spu_id")
    private Long spuId;

    /**
     * 商品名称
     */
    @TableField("name")
    private String name;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 规格单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 采购人
     */
    @TableField("purchaser")
    private String purchaser;

    /**
     * 货损类型
     */
    @TableField("damage_type")
    private String damageType;

    /**
     * 货损凭证
     */
    @TableField("credentials")
    private String credentials;

    /**
     * 货损金额
     */
    @TableField("damage_amount")
    private BigDecimal damageAmount;

    /**
     * 货损数量
     */
    @TableField("damage_quantity")
    private Integer damageQuantity;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 一级类目
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 时间标签 yyyyMMdd
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 仓库服务商
     */
    @TableField("warehouse_service_provider")
    private String warehouseServiceProvider;

    /**
     * 采购单价
     */
    @TableField("purchase_price")
    private BigDecimal purchasePrice;
}

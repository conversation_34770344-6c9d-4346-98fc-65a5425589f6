package com.cosfo.manage.report.repository;

import com.cosfo.manage.report.model.po.GoodsNearDeadlineSummary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 临期货品汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface GoodsNearDeadlineSummaryRepository extends IService<GoodsNearDeadlineSummary> {

    /**
     * 按时间标签查询
     * @param tenantId
     * @param timeTag
     * @return
     */
    List<GoodsNearDeadlineSummary> listByTenantAndTimeTag(Long tenantId, String timeTag);
}

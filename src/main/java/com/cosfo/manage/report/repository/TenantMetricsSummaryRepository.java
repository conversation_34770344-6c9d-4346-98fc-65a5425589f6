package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;

import java.util.List;

/**
 * <p>
 * 租户指标汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
public interface TenantMetricsSummaryRepository extends IService<TenantMetricsSummary> {

    /**
     * 根据租户id和时间标签查询
     * @param tenantId
     * @param timeTag
     * @return
     */
    TenantMetricsSummary queryByTenantAndTimeTag(Long tenantId, String timeTag);

    List<TenantMetricsSummary> selectByTenantIdAndDate(Long tenantId, String startDate, String endDate);
}

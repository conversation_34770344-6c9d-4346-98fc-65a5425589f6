package com.cosfo.manage.report.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.market.model.po.MarketItemNoSaleDetail;
import com.cosfo.manage.report.mapper.MarketItemNoSaleDetailMapper;
import com.cosfo.manage.report.repository.MarketItemNoSaleDetailRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商品滞销明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Service
public class MarketItemNoSaleDetailRepositoryImpl extends ServiceImpl<MarketItemNoSaleDetailMapper, MarketItemNoSaleDetail> implements MarketItemNoSaleDetailRepository {

    @Override
    public List<MarketItemNoSaleDetail> selectByTenantIdAndTypeAndTag(Long tenantId, Integer type, String timeTag) {

        return this.lambdaQuery()
                .eq(MarketItemNoSaleDetail::getTenantId, tenantId)
                .eq(MarketItemNoSaleDetail::getType, type)
                .eq(MarketItemNoSaleDetail::getTimeTag, timeTag)
                .list();
    }
}

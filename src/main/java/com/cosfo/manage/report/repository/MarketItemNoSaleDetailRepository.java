package com.cosfo.manage.report.repository;

import com.cosfo.manage.market.model.po.MarketItemNoSaleDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品滞销明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
public interface MarketItemNoSaleDetailRepository extends IService<MarketItemNoSaleDetail> {

    List<MarketItemNoSaleDetail> selectByTenantIdAndTypeAndTag(Long tenantId, Integer type, String timeTag);

}

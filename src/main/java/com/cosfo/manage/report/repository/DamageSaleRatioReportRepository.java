package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.po.DamageSaleRatioReport;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 损售比 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
public interface DamageSaleRatioReportRepository extends IService<DamageSaleRatioReport> {

    /**
     * 损售比分页查询
     * @param purchaseDetailReportQueryDTO
     * @return
     */
    Page<DamageSaleRatioReport> queryDamageSaleRatioDetailPage(ReportCommonQueryDTO purchaseDetailReportQueryDTO);

    /**
     * 获取损售比导出用列表，限制3000条
     * @param purchaseDetailReportQueryDTO
     * @return
     */
    void listDamageSaleRatioReportForExport(ReportCommonQueryDTO purchaseDetailReportQueryDTO, ResultHandler<?> resultHandler);

    /**
     * 获取损售比报表汇总信息
     * @param queryDTO
     * @return
     */
    PurchaseDetailAggVO getDamageSaleRatioDetailAgg(ReportCommonQueryDTO queryDTO);
}

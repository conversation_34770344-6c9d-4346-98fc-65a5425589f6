package com.cosfo.manage.report.repository;

import com.cosfo.manage.report.model.po.StockTurnoverSummary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 近30天库存周转天数汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public interface StockTurnoverSummaryRepository extends IService<StockTurnoverSummary> {

    /**
     * 根据租户和时间标签查询
     * @param tenantId 租户id
     * @param timeTag 时间标签
     * @return
     */
    List<StockTurnoverSummary> listByTenantAndTimeTag(Long tenantId, String timeTag);
}

package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.po.GoodsExpirationSummary;

import java.util.List;

/**
 * <p>
 * 货品过期汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface GoodsExpirationSummaryRepository extends IService<GoodsExpirationSummary> {

    /**
     * 根据时间标签查询
     * @param tenantId
     * @param timeTag
     * @return
     */
    List<GoodsExpirationSummary> listByTenantAndTimeTag(Long tenantId, String timeTag);

}

package com.cosfo.manage.report.service;

import com.cosfo.manage.report.model.po.OrderStatementSummary;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/4/25 15:28
 */
public interface OrderStatementSummaryService {

    /**
     * 根据条件查询
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    OrderStatementSummary queryByConditionSum(Long tenantId, LocalDateTime startTime, LocalDateTime endTime);
}

package com.cosfo.manage.report.service;

import com.cosfo.manage.report.model.dto.OrderAfterSaleDetailSummaryQueryDTO;
import com.cosfo.manage.report.model.po.OrderAfterSaleDetailSummary;
import org.apache.ibatis.session.ResultHandler;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 9:27
 */
public interface OrderAfterSaleDetailSummaryService {
    /**
     * 根据时间范围查询
     * @param tenantId
     * @param supplierId
     * @param startTime
     * @param endTime
     * @param resultHandler
     */
    void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, Integer goodsType, ResultHandler<?> resultHandler);

    /**
     * 查询统计
     * @param orderAfterSaleDetailSummaryQueryDTO
     * @return
     */
    OrderAfterSaleDetailSummary queryByConditionSum(OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO);

    /**
     * 根据条件查询
     * @param orderAfterSaleDetailSummaryQueryDTO
     * @return
     */
    List<OrderAfterSaleDetailSummary> queryByCondition(OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO);
}

package com.cosfo.manage.report.service;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 9:33
 */
//public interface OrderSoldBelowSupplySummaryService {
//    /**
//     * 根据时间范围查询
//     * @param tenantId
//     * @param supplierId
//     * @param startTime
//     * @param endTime
//     * @param resultHandler
//     */
//    void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ResultHandler<?> resultHandler);
//}

package com.cosfo.manage.report.service.impl;

import com.cosfo.manage.report.model.po.OrderStatementSummary;
import com.cosfo.manage.report.repository.OrderStatementSummaryRepository;
import com.cosfo.manage.report.service.OrderStatementSummaryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/25 15:28
 */
@Service
public class OrderStatementSummaryServiceImpl implements OrderStatementSummaryService {

    @Resource
    private OrderStatementSummaryRepository orderStatementSummaryRepository;

    @Override
    public OrderStatementSummary queryByConditionSum(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return orderStatementSummaryRepository.queryByConditionSum(tenantId, startTime, endTime);
    }
}

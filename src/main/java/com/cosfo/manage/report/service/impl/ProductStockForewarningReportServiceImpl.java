package com.cosfo.manage.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.*;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.ArrangeFacade;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.facade.WarehouseStorageFenceQueryFacade;
import com.cosfo.manage.facade.WarehouseStorageQueryFacade;
import com.cosfo.manage.market.service.MarketItemBusinessService;
import com.cosfo.manage.product.mapper.ProductPricingSupplyMapper;
import com.cosfo.manage.product.mapper.ProductSkuOrderSummaryMapper;
import com.cosfo.manage.product.model.dto.*;
import com.cosfo.manage.product.model.po.ProductStockForewarningReport;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.product.model.vo.ProductStockWarningVO;
import com.cosfo.manage.product.repository.ProductSkuOrderSummaryRepository;
import com.cosfo.manage.product.repository.ProductStockForewarningReportRepository;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.manage.report.model.dto.ProductStockChangeDTO;
import com.cosfo.manage.report.model.dto.ProductStockForewarningReportDTO;
import com.cosfo.manage.report.model.dto.ProductStockWarnInput;
import com.cosfo.manage.report.model.dto.StockForWaringConfigDTO;
import com.cosfo.manage.report.service.ProductStockForewarningReportService;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.model.input.TenantConfigInput;
import com.cosfo.manage.tenant.model.po.TenantCommonConfig;
import com.cosfo.manage.tenant.model.vo.TenantCommonConfigVO;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.cosfo.manage.tenant.service.TenantService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.pms.client.req.ReplenishmentSkuQueryReq;
import net.summerfarm.pms.client.resp.ReplenishmentResultResp;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.req.WarehouseBaseInfoByNoReq;
import net.summerfarm.wnc.client.req.WarehouseSkuFenceReq;
import net.summerfarm.wnc.client.resp.WarehouseBaseInfoByNoResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceAreaResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceStorageResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductStockForewarningReportServiceImpl implements ProductStockForewarningReportService {

    private final static String STOCK_WARNING_CONFIG_KEY = "stock_warning_config";

    @Resource
    private ProductStockForewarningReportRepository productStockForewarningReportRepository;
    @Resource
    private ProductSkuOrderSummaryRepository productSkuOrderSummaryRepository;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private MarketItemBusinessService marketItemBusinessService;
    @Resource
    private WarehouseStorageFenceQueryFacade warehouseStorageFenceQueryFacade;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ArrangeFacade arrangeFacade;
    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;
    @Resource
    private TenantService tenantService;
    @Resource
    private CommonService commonService;
    @Resource
    private ProductSkuOrderSummaryMapper productSkuOrderSummaryMapper;
    @Resource
    private ProductPricingSupplyMapper productPricingSupplyMapper;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ProductFacade productFacade;



    @Override
    public PageInfo<ProductStockWarningVO> queryProductWarnList(ProductStockWarnInput input) {
        // 处理名称和分类查询
        if (!handleQueryInfo(input)) {
            return PageInfo.emptyPageInfo();
        }
        PageInfo<ProductStockForewarningReportDTO> pageInfo = productStockForewarningReportRepository.queryProductWarnList(input);
        if (pageInfo.getTotal() == 0) {
            return PageInfo.emptyPageInfo();
        }
        // 查询品牌方对应adminId
        TenantDTO tenantDTO = tenantService.queryTenantById(input.getTenantId());
        List<ProductStockForewarningReportDTO> list = pageInfo.getList();
        // 获取货品 名称 规格 分类
        List<Long> skuIds = list.stream().map(ProductStockForewarningReportDTO::getSkuId).collect(Collectors.toList());

        // 获取当前生效配置
        StockForWaringConfigDTO reportConfig = getReportConfig(input.getTenantId(), true);

        //按货品类分分类sku
        Map<Integer, List<Long>> skuIdMap = list.stream().collect(Collectors.groupingBy(ProductStockForewarningReportDTO::getGoodsType, Collectors.mapping(ProductStockForewarningReportDTO::getSkuId, Collectors.toList())));

        Map<Long, ProductSkuDTO> skuInfoMap = getReportSkuInfo(skuIdMap, input.getTenantId());

        // 查询仓库信息
        List<Integer> warehouseNos = list.stream().map(ProductStockForewarningReportDTO::getWarehouseNo).collect(Collectors.toList());
        Map<Integer, WarehouseBaseInfoByNoResp> warehouseInfoMap = getWarehouseBaseInfoByNoRespMap(warehouseNos);

        // 查询上下架信息
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> saleSkuIdMap;
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<MarketItemOnSaleSimple4StoreResp> marketItems = marketItemBusinessService.selectSaleStatusBySkuIds(input.getTenantId(), skuIds);
            saleSkuIdMap = marketItems.stream().collect(Collectors.groupingBy(MarketItemOnSaleSimple4StoreResp::getSkuId));
        } else {
            saleSkuIdMap = Maps.newHashMap();
        }

        // 查询补货时间
        List<ProductStockForewarningReportDTO> xmList = list.stream().filter(dto -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(dto.getGoodsType())).collect(Collectors.toList());
        Map<String, ReplenishmentResultResp> uniqueIdReplenishmentMap = new HashMap<>();
        Map<Long, String> uniqueMap = Maps.newHashMap();
        uniqueIdReplenishmentMap = getReplenishmentResultRespMap(xmList, uniqueMap, uniqueIdReplenishmentMap);
        Map<String, ReplenishmentResultResp> finalUniqueIdReplenishmentMap = uniqueIdReplenishmentMap;

        // 售卖区域
        List<WarehouseSkuFenceReq> fenceReqList = list.stream().map(report -> {
            WarehouseSkuFenceReq fenceReq = new WarehouseSkuFenceReq();
            fenceReq.setWarehouseNos(Lists.newArrayList(report.getWarehouseNo()));
            fenceReq.setSku(report.getSkuCode());
            return fenceReq;
        }).collect(Collectors.toList());
        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = warehouseStorageFenceQueryFacade.queryWarehouseSkuFence(fenceReqList);
        Map<String, Map<Integer, WarehouseSkuFenceStorageResp>> skuFenceAreaMap = warehouseSkuFenceResps.stream()
                .collect(Collectors.toMap(WarehouseSkuFenceResp::getSku, warehouseSkuFenceResp -> warehouseSkuFenceResp.getWarehouseSkuFenceStorages().stream()
                        .collect(Collectors.toMap(WarehouseSkuFenceStorageResp::getWarehouseNo, Function.identity(), (v1, v2) -> v1)), (v1, v2) -> v1));

        List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = productPricingSupplyMapper.queryEffectSupplyCityBySkuId(skuIds, tenantDTO.getId());
        Map<Long, List<ProductPricingSupplyCityMappingDTO>> supplyCityMap = productPricingSupplyCityMappingDTOS.stream().collect(Collectors.groupingBy(ProductPricingSupplyCityMappingDTO::getSupplySkuId));
        // 组装数据
        List<ProductStockWarningVO> result = list.stream().map(report -> {
            ProductStockWarningVO warningVO = new ProductStockWarningVO();
            fillCommonInfo(report, warningVO, reportConfig);
            // 货品名称
            fillProductInfo(report, skuInfoMap, warningVO);
            // 补充补货时间
            fillReplenishmentTime(report, uniqueMap, finalUniqueIdReplenishmentMap, warningVO);
            // 上下架状态
            fillOnSaleInfo(report, saleSkuIdMap, warningVO);
            //补充仓和覆盖区域信息
            fillWarehouseAndAreaInfo(report, warehouseInfoMap, warningVO, skuFenceAreaMap, tenantDTO, supplyCityMap);

            if(GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(report.getGoodsType())){
                warningVO.setUseFlag(null);
            }
            warningVO.setUseFlagDesc(Optional.ofNullable(SkuUseFlagEnum.getByCode(warningVO.getUseFlag())).map(SkuUseFlagEnum::getDesc).orElse(null));

            return warningVO;
        }).collect(Collectors.toList());
        PageInfo<ProductStockWarningVO> resultPage = new PageInfo<>();
        resultPage.setList(result);
        resultPage.setPageSize(pageInfo.getPageSize());
        resultPage.setPageNum(pageInfo.getPageNum());
        resultPage.setTotal(pageInfo.getTotal());
        resultPage.setHasNextPage(pageInfo.isHasNextPage());
        resultPage.setIsLastPage(pageInfo.isIsLastPage());
        return resultPage;
    }

    private static void fillCommonInfo(ProductStockForewarningReportDTO report, ProductStockWarningVO warningVO, StockForWaringConfigDTO reportConfig) {
        warningVO.setSkuCode(report.getSkuCode());
        warningVO.setSkuId(report.getSkuId());
        warningVO.setWarehouseNo(report.getWarehouseNo());
        warningVO.setQuantity(report.getQuantity());
        warningVO.setForewarningStatus(report.getForewarningStatus());
        warningVO.setForewarningStatusDesc(ForewarningStatusEnum.getDescByType(report.getForewarningStatus()));
        warningVO.setForewarningMsg(warningVO.getForewarningStatusDesc());
        warningVO.setGoodsType(report.getGoodsType());
        warningVO.setUseFlag(report.getUseFlag());
        if (Objects.equals(reportConfig.getWaringType(), StockForWaringConfigTypeEnum.AVERAGE.getType())) {
            warningVO.setSaleAmount((int) Math.ceil(report.getSaleQuantity()  * 1.00d / report.getSaleDays()));
        } else {
            warningVO.setSaleAmount(report.getSaleQuantity());
        }
        if (ForewarningStatusEnum.FOREWARNING.getType().equals(report.getForewarningStatus())) {
            warningVO.setForewarningMsg(String.format("低于近%s天%s销量", reportConfig.getDay(), reportConfig.getWaringType() == 0 ? "平均" : "累计"));
        }
    }

    private static void fillProductInfo(ProductStockForewarningReportDTO report, Map<Long, ProductSkuDTO> skuInfoMap, ProductStockWarningVO warningVO) {
        ProductSkuDTO productSkuDTO = skuInfoMap.get(report.getSkuId());
        if (productSkuDTO == null) {
            return;
        }
        warningVO.setTitle(productSkuDTO.getTitle());
        warningVO.setMainPicture(productSkuDTO.getMainPicture());
        warningVO.setFirstCategory(productSkuDTO.getFirstCategory());
        warningVO.setSecondCategory(productSkuDTO.getSecondCategory());
        warningVO.setThirdCategory(productSkuDTO.getThirdCategory());
        warningVO.setSpecification(productSkuDTO.getSpecification());
        warningVO.setSpecificationUnit(productSkuDTO.getSpecificationUnit());

    }

    private static void fillReplenishmentTime(ProductStockForewarningReportDTO report, Map<Long, String> uniqueMap, Map<String, ReplenishmentResultResp> finalUniqueIdReplenishmentMap, ProductStockWarningVO warningVO) {
        String uniqueId = uniqueMap.get(report.getId());
        if (!StringUtils.isEmpty(uniqueId)) {
            ReplenishmentResultResp replenishmentResultResp = finalUniqueIdReplenishmentMap.get(uniqueId);
            if (replenishmentResultResp != null && replenishmentResultResp.getReplenishmentTime() != null) {
                warningVO.setReplenishmentTime(TimeUtils.convertToString(replenishmentResultResp.getReplenishmentTime(), TimeUtils.FORMAT_DATE));
            }
        }
    }

    private static void fillOnSaleInfo(ProductStockForewarningReportDTO report, Map<Long, List<MarketItemOnSaleSimple4StoreResp>> saleSkuIdMap, ProductStockWarningVO warningVO) {
        Integer onSale = OnSaleTypeEnum.SOLD_OUT.getCode();
        List<MarketItemOnSaleSimple4StoreResp> marketItems = saleSkuIdMap.get(report.getSkuId());
        if (!CollectionUtils.isEmpty(marketItems)) {
            onSale = marketItems.stream().anyMatch(resp -> OnSaleTypeEnum.ON_SALE.getCode().equals(resp.getOnSale())) ? OnSaleTypeEnum.ON_SALE.getCode() : OnSaleTypeEnum.SOLD_OUT.getCode();
        }
        warningVO.setOnSale(onSale);
        warningVO.setOnSaleDesc(OnSaleTypeEnum.getDesc(onSale));
    }

    private static void fillWarehouseAndAreaInfo(ProductStockForewarningReportDTO report, Map<Integer, WarehouseBaseInfoByNoResp> warehouseInfoMap, ProductStockWarningVO warningVO, Map<String, Map<Integer, WarehouseSkuFenceStorageResp>> skuFenceAreaMap, TenantDTO tenantDTO,Map<Long, List<ProductPricingSupplyCityMappingDTO>> supplyCityMap) {
        // 仓信息
        WarehouseBaseInfoByNoResp warehouseBaseInfoByNoResp = warehouseInfoMap.get(report.getWarehouseNo());
        warningVO.setWarehouseName(warehouseBaseInfoByNoResp.getWarehouseName());

        // 覆盖区域
        Map<Integer, WarehouseSkuFenceStorageResp> fenceAreaMap = skuFenceAreaMap.get(report.getSkuCode());
        if (fenceAreaMap != null && fenceAreaMap.get(report.getWarehouseNo()) != null) {
            WarehouseSkuFenceStorageResp warehouseSkuFenceStorageResp = fenceAreaMap.get(report.getWarehouseNo());
            if (!CollectionUtils.isEmpty(warehouseSkuFenceStorageResp.getWarehouseSkuFenceAreas())) {
                List<WarehouseSkuFenceAreaDTO> warehouseSkuFenceAreaDTOList = warehouseSkuFenceStorageResp.getWarehouseSkuFenceAreas().stream().filter(area->{
                    if (!GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(report.getGoodsType())) {
                        return true;
                    }
                    List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = supplyCityMap.get(report.getSkuId());
                    if (CollectionUtils.isEmpty(productPricingSupplyCityMappingDTOS)) {
                        return false;
                    }
                    return productPricingSupplyCityMappingDTOS.stream().anyMatch(city->city.getCityName().equals(area.getCity()));
                }).map(warehouseSkuFenceAreaResp -> {
                    WarehouseSkuFenceAreaDTO warehouseSkuFenceAreaDTO = new WarehouseSkuFenceAreaDTO();
                    warehouseSkuFenceAreaDTO.setProvince(warehouseSkuFenceAreaResp.getProvince());
                    warehouseSkuFenceAreaDTO.setArea(warehouseSkuFenceAreaResp.getArea());
                    warehouseSkuFenceAreaDTO.setCity(warehouseSkuFenceAreaResp.getCity());
                    return warehouseSkuFenceAreaDTO;
                }).collect(Collectors.toList());
                warningVO.setWarehouseSkuFenceAreaDTOList(warehouseSkuFenceAreaDTOList);
                warningVO.setCityNum(warehouseSkuFenceAreaDTOList.size());
            }
            if (WarehouseSourceEnum.SAAS_WAREHOUSE.equals(warehouseSkuFenceStorageResp.getSourceEnum())) {
                warningVO.setWarehouseProvider(tenantDTO.getCompanyName());
            } else {
                warningVO.setWarehouseProvider("杭州鲜沐科技有限公司");
            }
        }

        boolean isQuotationGoods = GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(report.getGoodsType());
        if (isQuotationGoods) {
            warningVO.setQuantity(null);
            warningVO.setWarehouseName("三方优选仓");
        }
    }

    @Override
    public Long exportProductWarnList(ProductStockWarnInput input) {
        Long tenantId = input.getTenantId();

        //生成对应的查询条件
        Map<String, Object> queryParamsMap = new LinkedHashMap<>(NumberConstants.FIVE);
        if (!StringUtils.isEmpty(input.getTitle())) {
            queryParamsMap.put(Constants.GOODS_NAME, input.getTitle());
        }
        if (input.getSkuId() != null) {
            queryParamsMap.put(Constants.SKU, input.getSkuId());
        }
        if (input.getWarehouseNo() != null) {
            if (input.getWarehouseNo() == -1) {
                queryParamsMap.put(Constants.WAREHOUSE, "三方优选仓");
            } else {
                Map<Integer, WarehouseBaseInfoByNoResp> warehouseBaseInfoByNoRespMap = getWarehouseBaseInfoByNoRespMap(Lists.newArrayList(input.getWarehouseNo()));
                WarehouseBaseInfoByNoResp warehouseBaseInfoByNoResp = warehouseBaseInfoByNoRespMap.get(input.getWarehouseNo());
                if (warehouseBaseInfoByNoResp != null) {
                    queryParamsMap.put(Constants.WAREHOUSE, warehouseBaseInfoByNoResp.getWarehouseName());
                }
            }
        }
        if (input.getStatus() != null) {
            queryParamsMap.put(Constants.COMMON_STATUS, ForewarningStatusEnum.getDescByType(input.getStatus()));
        }
//        if (input.getCategoryId() != null) {
//            queryParamsMap.put(Constants.CATEGORY, input.getCategoryId());
//        }

        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd") + "库存预警数据导出"  + ".xlsx";


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.PRODUCT_STOCK_FOREWARNING_REPORT.getType());
        recordDTO.setTenantId(tenantId);
        recordDTO.setFileName(fileName);
        recordDTO.setParams(input.getParams());
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        return DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(input, ee -> {
            // 1、表格处理
            String filePath = generateProductStockForewarningReport(input);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


    }

    private String generateProductStockForewarningReport(ProductStockWarnInput input) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.PRODUCT_STOCK_FOREWARNING_REPORT.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();

        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        input.setPageSize(500);
        int pageIndex = 1;
        if (!handleQueryInfo(input)) {
            excelWriter.finish();
        } else {
            //查询数据
            PageInfo<ProductStockWarningVO> pageInfo;
            do {
                input.setPageIndex(pageIndex);
                pageInfo = queryProductWarnList(input);
                excelWriter.fill(pageInfo.getList(), fillConfig, writeSheet);
                pageIndex++;
            } while (pageInfo.isHasNextPage());

        }

        StockForWaringConfigDTO reportConfig = getReportConfig(input.getTenantId(), true);
        String saleTitle = String.format("近%s天%s销量", reportConfig.getDay(), reportConfig.getWaringType() == 0 ? "平均" : "累计");

        // 写入list之前的数据
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("salAmountTitle", saleTitle);
        excelWriter.fill(map, writeSheet);
        excelWriter.finish();

        return filePath;
    }

    private boolean handleQueryInfo(ProductStockWarnInput input) {
        if (!StringUtils.isEmpty(input.getTitle()) || input.getCategoryId() != null) {
            // 查询自营
            ProductSkuQueryConditionDTO queryConditionDTO = new ProductSkuQueryConditionDTO();
            queryConditionDTO.setTitle(input.getTitle());
            queryConditionDTO.setCategoryId(input.getCategoryId());
            queryConditionDTO.setTenantId(input.getTenantId());
            List<ProductSkuDTO> productSkuDTOS = productSkuService.queryByCondition(queryConditionDTO);
            //查询鲜沐直供
            queryConditionDTO.setTenantId(XianmuSupplyTenant.TENANT_ID);
            ProductPricingSupplyEffectQueryDTO queryDTO = new ProductPricingSupplyEffectQueryDTO();
            queryDTO.setTenantId(input.getTenantId());
            List<ProductPricingSupplyEffectDTO> productPricingSupplyEffectDTOS = productPricingSupplyMapper.queryEffectSkuInfo(queryDTO);
            List<Long> quoteSkuIds = productPricingSupplyEffectDTOS.stream().map(ProductPricingSupplyEffectDTO::getSupplySkuId).collect(Collectors.toList());
            queryConditionDTO.setSkuIds(quoteSkuIds);
            List<ProductSkuDTO> supplySkuDTOS = productSkuService.queryByCondition(queryConditionDTO);

            Set<Long> skuIds = new HashSet<>();
            if (!CollectionUtils.isEmpty(productSkuDTOS)) {
                skuIds.addAll(productSkuDTOS.stream().map(ProductSkuDTO::getId).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(supplySkuDTOS)) {
                skuIds.addAll(supplySkuDTOS.stream().map(ProductSkuDTO::getId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(skuIds)) {
                return false;
            }
            input.setSkuIds(new ArrayList<>(skuIds));
        }
        return true;
    }

    private Map<Integer, WarehouseBaseInfoByNoResp> getWarehouseBaseInfoByNoRespMap(List<Integer> warehouseNos) {
        Map<Integer, WarehouseBaseInfoByNoResp> warehouseInfoMap;
        WarehouseBaseInfoByNoReq warehouseBaseInfoByNoReq = new WarehouseBaseInfoByNoReq();
        warehouseBaseInfoByNoReq.setWarehouseNos(warehouseNos);
        List<WarehouseBaseInfoByNoResp> warehouseBaseInfoByNoResps = warehouseStorageQueryFacade.queryBaseInfoByWarehouseNo(warehouseBaseInfoByNoReq);
        if (!CollectionUtils.isEmpty(warehouseBaseInfoByNoResps)) {
            warehouseInfoMap = warehouseBaseInfoByNoResps.stream().collect(Collectors.toMap(WarehouseBaseInfoByNoResp::getWarehouseNo, Function.identity(), (v1, v2) -> v1));
        } else {
            warehouseInfoMap = Collections.emptyMap();
        }
        return warehouseInfoMap;
    }

    private Map<String, ReplenishmentResultResp> getReplenishmentResultRespMap(List<ProductStockForewarningReportDTO> xmList, Map<Long, String> uniqueMap, Map<String, ReplenishmentResultResp> uniqueIdReplenishmentMap) {
        if (!CollectionUtils.isEmpty(xmList)) {

            List<ReplenishmentSkuQueryReq> reqList = new ArrayList<>();
            for (int i = 0; i < xmList.size(); i++) {
                ProductStockForewarningReportDTO reportDTO = xmList.get(i);
                ReplenishmentSkuQueryReq req = new ReplenishmentSkuQueryReq();
                req.setSkuCode(reportDTO.getSkuCode());
                req.setWarehouseNoList(Lists.newArrayList(reportDTO.getWarehouseNo()));
                String uniqueId = String.valueOf(i);
                req.setUniqueId(uniqueId);
                reqList.add(req);
                uniqueMap.put(reportDTO.getId(), uniqueId);

            }

            List<ReplenishmentResultResp> replenishmentResultResps = arrangeFacade.queryPurchase(reqList);
            if (!CollectionUtils.isEmpty(replenishmentResultResps)) {
                // 填入补货时间
                uniqueIdReplenishmentMap = replenishmentResultResps.stream().collect(Collectors.toMap(ReplenishmentResultResp::getUniqueId, Function.identity(), (v1, v2) -> v1));
            }

        }
        return uniqueIdReplenishmentMap;
    }

    @Override
    public Boolean updateReportConfig(StockForWaringConfigDTO stockForWaringConfigDTO, Long tenantId) {
        stockForWaringConfigDTO.setUpdateTime(LocalDateTime.now());
        stockForWaringConfigDTO.setIsActive(false);
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantId, STOCK_WARNING_CONFIG_KEY);
        List<StockForWaringConfigDTO> stockForWaringConfigDTOS = new ArrayList<>();
        if (tenantCommonConfig != null && !StringUtils.isEmpty(tenantCommonConfig.getConfigValue())) {
            stockForWaringConfigDTOS = JSON.parseArray(tenantCommonConfig.getConfigValue(), StockForWaringConfigDTO.class);
            Optional<StockForWaringConfigDTO> first = stockForWaringConfigDTOS.stream().max(Comparator.comparing(StockForWaringConfigDTO::getUpdateTime, Comparator.nullsFirst(Comparator.naturalOrder())));
            first.ifPresent(config -> {
                if (config.getUpdateTime() == null) {
                    return;
                }
                long between = Duration.between(LocalDateTime.now(), config.getUpdateTime().plusHours(1)).toMinutes();
                if (between > 0) {
                    throw new BizException(String.format("1小时内只能修改一次，请%s分钟后再试",between));
                }
            });
            if (stockForWaringConfigDTOS.size() > 1) {
                // 删除 active 为 false 的配置
                stockForWaringConfigDTOS.removeIf(config -> !config.getIsActive());
            }
        }
        stockForWaringConfigDTOS.add(stockForWaringConfigDTO);
        TenantConfigInput tenantConfigInput = new TenantConfigInput();
        tenantConfigInput.setConfigCode(STOCK_WARNING_CONFIG_KEY);
        tenantConfigInput.setConfigValue(JSON.toJSONString(stockForWaringConfigDTOS));
        tenantCommonConfigService.updateTenantConfig(tenantId, tenantConfigInput);

        sendMqMsgForUpdateStockWarnConfig(tenantId);
        return Boolean.TRUE;
    }

    public void sendMqMsgForUpdateStockWarnConfig(Long tenantId) {
        Map<String, Object> map = new HashMap<>(1);
        map.put("tenantId", tenantId);
        mqProducer.send(MqTopicConstant.SAAS_ADD_TENANT_INFO, MqTagConstant.UPDATE_TENANT_STOCK_WARN_TAG, map);
    }

    @Override
    public StockForWaringConfigDTO getReportConfig(Long tenantId, Boolean isActive) {

        TenantCommonConfigVO tenantCommonConfig = tenantCommonConfigService.selectTenantConfig(tenantId, STOCK_WARNING_CONFIG_KEY);
        List<StockForWaringConfigDTO> stockForWaringConfigDTOS = JSON.parseArray(tenantCommonConfig.getConfigValue(), StockForWaringConfigDTO.class);
        for (StockForWaringConfigDTO stockForWaringConfigDTO : stockForWaringConfigDTOS) {
            if (isActive.equals(stockForWaringConfigDTO.getIsActive())) {
                return stockForWaringConfigDTO;
            }
        }
        return null;
    }

    @Override
    public StockForWaringConfigDTO getRecentReportConfig(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfig = tenantCommonConfigService.selectTenantConfig(tenantId, STOCK_WARNING_CONFIG_KEY);
        List<StockForWaringConfigDTO> stockForWaringConfigDTOS = JSON.parseArray(tenantCommonConfig.getConfigValue(), StockForWaringConfigDTO.class);
        // updateTime 最近的一条记录
        return stockForWaringConfigDTOS.stream().max(Comparator.comparing(StockForWaringConfigDTO::getUpdateTime, Comparator.nullsFirst(Comparator.naturalOrder()))).orElse(null);
    }

    @Override
    public void updateReportConfigWithDateRefreshed(Long tenantId) {
        StockForWaringConfigDTO reportConfig = getReportConfig(tenantId, false);
        if (reportConfig == null) {
            return;
        }
        reportConfig.setIsActive(true);
        reportConfig.setActiveTime(LocalDateTime.now());
        TenantConfigInput tenantConfigInput = new TenantConfigInput();
        tenantConfigInput.setConfigCode(STOCK_WARNING_CONFIG_KEY);
        tenantConfigInput.setConfigValue(JSON.toJSONString(Lists.newArrayList(reportConfig)));
        tenantCommonConfigService.updateTenantConfig(tenantId, tenantConfigInput);
    }


    @Override
    public void refreshSaleAmountPerDay(Long tenantId) {
        // 读取预警配置
        StockForWaringConfigDTO stockForWaringConfigDTO = getRecentReportConfig(tenantId);

        Integer saleDays = stockForWaringConfigDTO.getDay();

        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(saleDays);

        // 统计X天内的累计销量
        List<ProductSkuOrderSummaryDayDTO> dayDTOS = productSkuOrderSummaryMapper.querySaleAmountByTenantId(tenantId, startDate, endDate);
        dayDTOS.stream().forEach(e -> e.setSaleDays(saleDays));
        Map<String, Map<Integer, List<ProductSkuOrderSummaryDayDTO>>> summaryMap = dayDTOS.stream()
                .collect(Collectors.groupingBy(ProductSkuOrderSummaryDayDTO::getSkuCode,
                        Collectors.groupingBy(ProductSkuOrderSummaryDayDTO::getWarehouseNo)));


        // 遍历租户下的库存预警表记录，更新累计销量、预警状态
        Integer pageSize = 100;
        PageInfo<ProductStockForewarningReportDTO> pageInfo = productStockForewarningReportRepository.queryAllProductWarnPage(tenantId, pageSize, 0L);
        int pages = pageInfo.getPages();
        int index = 1;

        while(index++ <= pages){
            List<ProductStockForewarningReportDTO> list = pageInfo.getList();
            if(CollectionUtils.isEmpty(list)){
                break;
            }

            List<ProductStockForewarningReport> updateList = Lists.newArrayList();
            for (ProductStockForewarningReportDTO productStockForewarningReportDTO : list) {
                String skuCode = productStockForewarningReportDTO.getSkuCode();
                Integer warehouseNo = productStockForewarningReportDTO.getWarehouseNo();
                // 以skuCode+warehouseNo分组
                List<ProductSkuOrderSummaryDayDTO> skuWarehouseNoSummaryList = Optional.ofNullable(summaryMap.get(skuCode))
                        .map(subMap -> subMap.get(warehouseNo))
                        .orElse(null);

                ProductStockForewarningReport updateObj = new ProductStockForewarningReport();
                updateObj.setId(productStockForewarningReportDTO.getId());
                // 统计销量不为空，更新销量
                if(!CollectionUtils.isEmpty(skuWarehouseNoSummaryList)){
                    updateObj.setSaleQuantity(skuWarehouseNoSummaryList.get(0).getSaleQuantity());
                    updateObj.setSaleDays(saleDays);
                    updateObj.setForewarningStatus(ForewarningStatusEnum.getStatus(productStockForewarningReportDTO.getQuantity(), updateObj.getSaleQuantity(), stockForWaringConfigDTO));
                }else{
                    // 为空更新销量为0
                    updateObj.setSaleQuantity(0);
                    updateObj.setSaleDays(saleDays);
                    updateObj.setForewarningStatus(ForewarningStatusEnum.getStatus(productStockForewarningReportDTO.getQuantity(), 0, stockForWaringConfigDTO));
                }
                updateList.add(updateObj);
            }

            productStockForewarningReportRepository.updateBatchById(updateList);

            if (index <= pages) {
                pageInfo = productStockForewarningReportRepository.queryAllProductWarnPage(tenantId, pageSize, list.get(list.size() - 1).getId());
            }
        }


        // 刷新预警配置
        updateReportConfigWithDateRefreshed(tenantId);
    }

    @Override
    public void updateStockWarnBySupplySku(ProductStockChangeDTO productStockChangeDTO) {
        Long skuId = productStockChangeDTO.getSkuId();
        String skuCode = productStockChangeDTO.getSkuCode();
        Integer warehouseNo = productStockChangeDTO.getWarehouseNo();

        List<Long> tenantIdList = queryNeedUpdateTenantIdBySku(skuCode, skuId, warehouseNo);

        for (Long tenantId : tenantIdList) {
            productStockChangeDTO.setTenantId(tenantId);
            updateStockWarn(productStockChangeDTO);
        }

    }

    private List<Long> queryNeedUpdateTenantIdBySku(String skuCode, Long skuId, Integer warehouseNo) {
        WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
        warehouseSkuFenceReq.setSku(skuCode);
        warehouseSkuFenceReq.setWarehouseNos(Lists.newArrayList(warehouseNo));
        // 根据sku+仓库编号获取仓库覆盖城市区域信息
        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = warehouseStorageFenceQueryFacade.queryWarehouseSkuFence(Lists.newArrayList(warehouseSkuFenceReq));
        if (CollectionUtils.isEmpty(warehouseSkuFenceResps)) {
            log.info("sku:{},warehouseNo:{} 没有仓库围栏信息 warehouseSkuFenceResps为空", skuCode, warehouseNo);
            return Collections.emptyList();
        }
        Map<String, List<WarehouseSkuFenceStorageResp>> skuMap = warehouseSkuFenceResps.stream().filter(resp -> CollectionUtil.isNotEmpty(resp.getWarehouseSkuFenceStorages())).collect(Collectors.toMap(WarehouseSkuFenceResp::getSku, WarehouseSkuFenceResp::getWarehouseSkuFenceStorages, (v1, v2) -> v1));
        List<WarehouseSkuFenceStorageResp> warehouseSkuFenceStorageResps = skuMap.get(skuCode);
        if (CollectionUtils.isEmpty(warehouseSkuFenceStorageResps)) {
            log.info("sku:{},warehouseNo:{} 没有仓库围栏信息 warehouseSkuFenceStorageResps为空", skuCode, warehouseNo);
            return Collections.emptyList();
        }
        Map<Integer, List<WarehouseSkuFenceAreaResp>> areaRespMap = warehouseSkuFenceStorageResps.stream().filter(resp -> CollectionUtil.isNotEmpty(resp.getWarehouseSkuFenceAreas())).collect(Collectors.toMap(WarehouseSkuFenceStorageResp::getWarehouseNo, WarehouseSkuFenceStorageResp::getWarehouseSkuFenceAreas, (v1, v2) -> v1));
        List<WarehouseSkuFenceAreaResp> warehouseSkuFenceAreaResps = areaRespMap.get(warehouseNo);
        if (CollectionUtils.isEmpty(warehouseSkuFenceAreaResps)) {
            log.info("sku:{},warehouseNo:{} 没有仓库围栏信息 warehouseSkuFenceAreaResps为空", skuCode, warehouseNo);
            return Collections.emptyList();
        }
        // 得到该库存变动记录的sku+所属仓城市列表
        List<String> cityNames = warehouseSkuFenceAreaResps.stream().map(warehouseSkuFenceAreaResp -> warehouseSkuFenceAreaResp.getCity()).distinct().collect(Collectors.toList());

        // 获取 sku + warehouseNo覆盖城市下的 有效报价单
        List<ProductPricingSupplyCityMappingDTO> pricingSupplyCityMappingDTOS = productPricingSupplyMapper.querySupplyCityBySkuIdAndSupplyTenantId(XianmuSupplyTenant.TENANT_ID, skuId, cityNames);
        if (CollectionUtils.isEmpty(pricingSupplyCityMappingDTOS)) {
            log.info("不存在相应鲜沐报价单, skuId={} cityNames={}", skuId, cityNames);
            return Collections.emptyList();
        }

        return pricingSupplyCityMappingDTOS.stream().map(ProductPricingSupplyCityMappingDTO::getTenantId).distinct().collect(Collectors.toList());
    }

    @Override
    public void updateStockWarn(ProductStockChangeDTO productStockChangeDTO) {
        Long tenantId = productStockChangeDTO.getTenantId();
        String skuCode = productStockChangeDTO.getSkuCode();
        Long skuId = productStockChangeDTO.getSkuId();
        Integer warehouseNo = productStockChangeDTO.getWarehouseNo();
        Integer newOnlineQuantity = productStockChangeDTO.getNewOnlineQuantity();
        Integer goodsType = productStockChangeDTO.getGoodsType();

        // 读取预警配置
        StockForWaringConfigDTO stockForWaringConfigDTO = getRecentReportConfig(tenantId);
        Integer saleDays = stockForWaringConfigDTO.getDay();

        // 更新操作
        ProductStockForewarningReport exist = productStockForewarningReportRepository.selectByTenantAndSkuAndWarehouseNo(tenantId, skuCode, warehouseNo);
        if (exist != null) {
            ProductStockForewarningReport updateObj = new ProductStockForewarningReport();
            updateObj.setId(exist.getId());
            updateObj.setQuantity(newOnlineQuantity);
            updateObj.setForewarningStatus(ForewarningStatusEnum.getStatus(newOnlineQuantity, exist.getSaleQuantity(), stockForWaringConfigDTO));
            if(GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(goodsType)){
                ProductSkuDTO productSkuDTO = productFacade.selectProductSkuDetailById(skuId);
                updateObj.setUseFlag(Optional.ofNullable(productSkuDTO).map(ProductSkuDTO::getUseFlag).orElse(null));
            }

            productStockForewarningReportRepository.updateById(updateObj);
        } else {
            Integer saleQuantity = productSkuOrderSummaryRepository.getSaleAmount(tenantId, skuCode, warehouseNo, saleDays);

            ProductStockForewarningReport saveObj = new ProductStockForewarningReport();
            saveObj.setTenantId(tenantId);
            saveObj.setSkuId(skuId);
            saveObj.setSkuCode(skuCode);
            saveObj.setWarehouseNo(warehouseNo);
            saveObj.setGoodsType(goodsType);
            saveObj.setSaleDays(saleDays);
            saveObj.setSaleQuantity(saleQuantity);
            saveObj.setQuantity(newOnlineQuantity);
            saveObj.setForewarningStatus(ForewarningStatusEnum.getStatus(newOnlineQuantity, saleQuantity, stockForWaringConfigDTO));
            saveObj.setUseFlag(SkuUseFlagEnum.IN_USER.getCode());

            if(GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(goodsType)){
                ProductSkuDTO productSkuDTO = productFacade.selectProductSkuDetailById(skuId);
                saveObj.setUseFlag(Optional.ofNullable(productSkuDTO).map(ProductSkuDTO::getUseFlag).orElse(null));
            }

            productStockForewarningReportRepository.insertOrUpdateBatch(Collections.singletonList(saveObj));
        }
    }


    @Override
    public List<ProductStockWarningSimpleDTO> queryWarningSummary(ProductStockWarningQueryDTO productStockWarningQueryDTO) {
        List<ProductStockForewarningReport> reports = productStockForewarningReportRepository.queryWarningSummary(productStockWarningQueryDTO);
        if (CollectionUtils.isEmpty(reports)) {
            return Collections.emptyList();
        }
        Map<Integer, List<Long>> skuIdMap = reports.stream().collect(Collectors.groupingBy(ProductStockForewarningReport::getGoodsType, Collectors.mapping(ProductStockForewarningReport::getSkuId, Collectors.toList())));
        // 获取sku信息
        Map<Long, ProductSkuDTO> skuInfoMap = getReportSkuInfo(skuIdMap, UserLoginContextUtils.getTenantId());

        // 获取仓库名称
        List<Integer> warehouseNos = reports.stream().map(ProductStockForewarningReport::getWarehouseNo).collect(Collectors.toList());
        Map<Integer, WarehouseBaseInfoByNoResp> warehouseBaseInfoByNoRespMap = getWarehouseBaseInfoByNoRespMap(warehouseNos);

        List<ProductStockWarningSimpleDTO> productStockWarningSimpleDTOS = Lists.newArrayList();
        for (ProductStockForewarningReport report : reports) {
            ProductStockWarningSimpleDTO productStockWarningSimpleDTO = new ProductStockWarningSimpleDTO();
            productStockWarningSimpleDTO.setStock(report.getQuantity());
            ProductSkuDTO productSkuDTO = skuInfoMap.get(report.getSkuId());
            productStockWarningSimpleDTO.setTitle(Objects.isNull(productSkuDTO) ? "" : productSkuDTO.getTitle());
            if (Objects.equals(report.getForewarningStatus(), ForewarningStatusEnum.FOREWARNING.getType()) && Objects.equals(report.getGoodsType(), GoodsTypeEnum.QUOTATION_TYPE.getCode())) {
                StockForWaringConfigDTO reportConfig = getReportConfig(productStockWarningQueryDTO.getTenantId(), true);
                Integer waringType = reportConfig.getWaringType();
                Integer day = reportConfig.getDay();
                String description = String.format("低于前%d天%s", day, (waringType == 0 ? "平均销量" : "累计销量"));
                productStockWarningSimpleDTO.setDesc(description);
            }
            WarehouseBaseInfoByNoResp warehouseBaseInfoByNoResp = warehouseBaseInfoByNoRespMap.get(report.getWarehouseNo());
            productStockWarningSimpleDTO.setWarehouseName(
                    warehouseBaseInfoByNoResp == null ? "" :
                    !Objects.equals(GoodsTypeEnum.QUOTATION_TYPE.getCode(), report.getGoodsType()) ? warehouseBaseInfoByNoResp.getWarehouseName() : "三方优选仓");
            productStockWarningSimpleDTOS.add(productStockWarningSimpleDTO);
        }
        return productStockWarningSimpleDTOS;
    }

    /**
     * 从报警列表中获取sku信息
     * @param skuIdMap
     * @param tenantId
     * @return
     */
    private Map<Long, ProductSkuDTO> getReportSkuInfo(Map<Integer, List<Long>> skuIdMap, Long tenantId) {
        Map<Long, ProductSkuDTO> skuInfoMap = Maps.newHashMap();
        skuIdMap.forEach((key, skuIds) -> {
            ProductSkuQueryConditionDTO queryConditionDTO = new ProductSkuQueryConditionDTO();
            if (Objects.equals(key, GoodsTypeEnum.QUOTATION_TYPE.getCode())) {
                queryConditionDTO.setTenantId(XianmuSupplyTenant.TENANT_ID);
                queryConditionDTO.setSkuIds(skuIds);
            }
            if (Objects.equals(key, GoodsTypeEnum.SELF_GOOD_TYPE.getCode())) {
                queryConditionDTO.setTenantId(tenantId);
                queryConditionDTO.setSkuIds(skuIds);
            }
            List<ProductSkuDTO> productSkuList = productSkuService.queryByCondition(queryConditionDTO);
            skuInfoMap.putAll(productSkuList.stream().collect(Collectors.toMap(ProductSkuDTO::getId, item -> item)));
        });
        return skuInfoMap;
    }

}

package com.cosfo.manage.report.service;

import com.cosfo.manage.report.model.dto.MarketItemSummaryQueryDTO;
import org.apache.ibatis.session.ResultHandler;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 13:43
 */
public interface MarketItemSalesSummaryService {

    /**
     * 根据条件查询
     * @param marketItemSummaryQueryDTO
     * @param resultHandler
     */
    void queryByConditionWithHandler(MarketItemSummaryQueryDTO marketItemSummaryQueryDTO, ResultHandler<?> resultHandler);
}

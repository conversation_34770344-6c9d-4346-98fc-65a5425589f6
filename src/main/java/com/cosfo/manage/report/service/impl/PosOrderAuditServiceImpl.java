package com.cosfo.manage.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.PrivateProcurementStatusEnum;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.LocalDateTimeUtil;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.report.converter.PosOrderAuditConverter;
import com.cosfo.manage.report.model.dto.PosOrderAuditExportDTO;
import com.cosfo.manage.report.model.dto.PosOrderAuditQueryDTO;
import com.cosfo.manage.report.model.vo.PosOrderAuditVO;
import com.cosfo.manage.report.repository.PosOrderAuditRepository;
import com.cosfo.manage.report.service.PosOrderAuditService;
import com.cosfo.manage.tenant.model.vo.TenantCommonConfigVO;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Service
@Slf4j
public class PosOrderAuditServiceImpl implements PosOrderAuditService {

    @Autowired
    private PosOrderAuditRepository posOrderAuditRepository;
    @Autowired
    private TenantCommonConfigService tenantCommonConfigService;
    @Autowired
    private CommonService commonService;

    @Override
    public PageInfo<PosOrderAuditVO> listPage(PosOrderAuditQueryDTO dto) {
//        查询私采集率
        TenantCommonConfigVO tenantCommonConfig = tenantCommonConfigService.selectTenantConfig(dto.getTenantId (), TenantConfigEnum.TenantConfig.PRIVATE_PROCUREMENT.getConfigKey());
        dto.setPrivateProcurement (new BigDecimal (tenantCommonConfig.getConfigValue ()));
        Page<PosOrderAuditVO> posOrderAuditVOIPage = posOrderAuditRepository.listPage (dto);
        return PageInfoConverter.toPageInfo(posOrderAuditVOIPage, (PosOrderAuditVO p) -> {
            p.setAuditTime (buildAutidTime(dto.getBeginWeek (),dto.getEndWeek ()));
            return p;
        });
    }

    private String buildAutidTime(LocalDate beginWeek, LocalDate endWeek) {
        beginWeek = beginWeek.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        endWeek = endWeek.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        LocalDate now = LocalDate.now ();
        if(now.compareTo (endWeek) < 0){
            endWeek = now;
        }

        return LocalDateTimeUtil.localDateFormat (beginWeek) + "至" + LocalDateTimeUtil.localDateFormat (endWeek);
    }

    @Override
    public void generateExportFile(PosOrderAuditQueryDTO dto) {
        Map<String, String> queryParamsMap = new LinkedHashMap<> (NumberConstants.SIX);

        if (ObjectUtil.isNotNull (dto.getBeginWeek ()) && ObjectUtil.isNotNull (dto.getEndWeek ()) ) {
            queryParamsMap.put(Constants.AUDIT_TIME, buildAutidTime(dto.getBeginWeek () , dto.getEndWeek ()));
        }
        if (StringUtils.isNotBlank (dto.getOutStoreName ())) {
            queryParamsMap.put(Constants.OUT_STORE_NAME, dto.getOutStoreName());
        }
        if (StringUtils.isNotBlank (dto.getOutItemName ())) {
            queryParamsMap.put(Constants.OUT_ITEM_NAME, dto.getOutItemName());
        }
        if (ObjectUtil.isNotNull (dto.getStatus ())) {
            queryParamsMap.put(Constants.PRIVATE_PROCUREMENT_STATUS, PrivateProcurementStatusEnum.getDescByCode (dto.getStatus()));
        }

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.POST_ORDER_AUDIT.getType());
        recordDTO.setTenantId(dto.getTenantId ());
        String fileName = ExcelTypeEnum.POST_ORDER_AUDIT.getFileName ();
        recordDTO.setFileName(fileName);
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(dto, e -> writeDownloadCenter(e,fileName));
    }
    public DownloadCenterOssRespDTO writeDownloadCenter(PosOrderAuditQueryDTO dto,String fileName) {
        // 1、表格处理
        String filePath = generateFile(dto);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File (filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException ("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }
    private String generateFile(PosOrderAuditQueryDTO dto) {
        dto.setPageIndex(NumberConstants.ONE);
        dto.setPageSize(NumberConstants.FIVE_HUNDRED);

        PageInfo<PosOrderAuditVO> pageInfo = listPage (dto);
        int pages = (int) Math.max((pageInfo.getTotal() - 1) / pageInfo.getPageSize() + 1, 0);

        // 创建excelWriter
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter ()).registerConverter(new LocalDateTimeConverter ())
                .withTemplate(ExcelUtils.getExcelFileInputStream(this.getClass(),ExcelTypeEnum.POST_ORDER_AUDIT.getName ()))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(Collections.emptyList(), writeSheet);

        // 分页查询
        for (int pageNum = NumberConstants.ONE; pageNum <= pages; pageNum++) {
            List<PosOrderAuditVO> posOrderAuditVOS = pageInfo.getList();
            if (CollectionUtil.isEmpty(posOrderAuditVOS)) {
                break;
            }
            List<PosOrderAuditExportDTO> tempList = PosOrderAuditConverter.INSTANCE.vo2exportDtoList (posOrderAuditVOS);
            excelWriter.fill(tempList, writeSheet);
        }
        excelWriter.finish();
        return filePath;
    }
}

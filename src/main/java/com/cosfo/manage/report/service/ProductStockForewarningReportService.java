package com.cosfo.manage.report.service;

import com.cosfo.manage.product.model.dto.ProductStockWarningQueryDTO;
import com.cosfo.manage.product.model.dto.ProductStockWarningSimpleDTO;
import com.cosfo.manage.product.model.vo.ProductStockWarningVO;
import com.cosfo.manage.report.model.dto.ProductStockChangeDTO;
import com.cosfo.manage.report.model.dto.ProductStockWarnInput;
import com.cosfo.manage.report.model.dto.StockForWaringConfigDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductStockForewarningReportService {

    /**
     * 查询货品库存预警列表
     *
     * @param input
     * @return
     */
    PageInfo<ProductStockWarningVO> queryProductWarnList(ProductStockWarnInput input);

    /**
     * 导出货品库存预警列表
     * @param input
     */
    Long exportProductWarnList(ProductStockWarnInput input);

    /**
     * 更新库存预警配置
     *
     * @param stockForWaringConfigDTO
     * @return
     */
    Boolean updateReportConfig(StockForWaringConfigDTO stockForWaringConfigDTO, Long tenantId);

    /**
     * 获取库存预警配置
     * @return
     */
    StockForWaringConfigDTO getReportConfig(Long tenantId, Boolean isActive);

    /**
     * 获取最新一条库存预警配置
     * @param tenantId
     * @return
     */
    StockForWaringConfigDTO getRecentReportConfig(Long tenantId);

    /**
     * 更新库存预警配置
     * @param tenantId
     */
    void updateReportConfigWithDateRefreshed(Long tenantId);


    /**
     * 每天刷新前X天的累计销量
     * @param tenantId
     */
    void refreshSaleAmountPerDay(Long tenantId);

    /**
     * 实时库存变动 更新报价货品的库存预警记录
     * @param productStockChangeDTO
     */
    void updateStockWarnBySupplySku(ProductStockChangeDTO productStockChangeDTO);

    /**
     * 实时库存变动 更新货品的库存预警记录
     * @param productStockChangeDTO
     */
    void updateStockWarn(ProductStockChangeDTO productStockChangeDTO);


    /**
     * 查询库存预警汇总
     * @param productStockWarningQueryDTO
     * @return
     */
    List<ProductStockWarningSimpleDTO> queryWarningSummary(ProductStockWarningQueryDTO productStockWarningQueryDTO);
}

package com.cosfo.manage.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.report.model.po.OrderAfterSaleInvertedSummary;
import com.cosfo.manage.report.repository.OrderAfterSaleInvertedSummaryRepository;
import com.cosfo.manage.report.service.OrderAfterSaleInvertedSummaryService;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/25
 */
@Service
public class OrderAfterSaleInvertedSummaryServiceImpl implements OrderAfterSaleInvertedSummaryService {
    @Resource
    private OrderAfterSaleInvertedSummaryRepository orderAfterSaleInvertedSummaryRepository;

    @Override
    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ResultHandler<?> resultHandler) {
        orderAfterSaleInvertedSummaryRepository.queryByConditionWithHandler(tenantId, supplierId, startTime, endTime, resultHandler);
    }

    @Override
    public long count(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<OrderAfterSaleInvertedSummary> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderAfterSaleInvertedSummary::getTenantId, tenantId);
        lambdaQueryWrapper.eq(Objects.nonNull(supplierId), OrderAfterSaleInvertedSummary::getSupplierId, supplierId);
        lambdaQueryWrapper.between(OrderAfterSaleInvertedSummary::getTimeTag, startTime, endTime);
        return orderAfterSaleInvertedSummaryRepository.count(lambdaQueryWrapper);
    }
}

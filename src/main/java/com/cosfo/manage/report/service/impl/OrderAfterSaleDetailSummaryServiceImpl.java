package com.cosfo.manage.report.service.impl;

import com.cosfo.manage.report.model.dto.OrderAfterSaleDetailSummaryQueryDTO;
import com.cosfo.manage.report.model.po.OrderAfterSaleDetailSummary;
import com.cosfo.manage.report.repository.OrderAfterSaleDetailSummaryRepository;
import com.cosfo.manage.report.service.OrderAfterSaleDetailSummaryService;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/26 9:27
 */
@Service
public class OrderAfterSaleDetailSummaryServiceImpl implements OrderAfterSaleDetailSummaryService {

    @Resource
    private OrderAfterSaleDetailSummaryRepository orderAfterSaleDetailSummaryRepository;

    @Override
    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, Integer goodsType, ResultHandler<?> resultHandler) {
        orderAfterSaleDetailSummaryRepository.queryByConditionWithHandler(tenantId, supplierId, startTime, endTime, goodsType, resultHandler);
    }

    @Override
    public OrderAfterSaleDetailSummary queryByConditionSum(OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO) {
        return orderAfterSaleDetailSummaryRepository.queryByConditionSum(orderAfterSaleDetailSummaryQueryDTO);
    }

    @Override
    public List<OrderAfterSaleDetailSummary> queryByCondition(OrderAfterSaleDetailSummaryQueryDTO orderAfterSaleDetailSummaryQueryDTO) {
        return orderAfterSaleDetailSummaryRepository.queryByCondition(orderAfterSaleDetailSummaryQueryDTO);
    }
}

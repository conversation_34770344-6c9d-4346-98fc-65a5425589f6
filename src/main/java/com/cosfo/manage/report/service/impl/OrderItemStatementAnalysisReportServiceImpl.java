package com.cosfo.manage.report.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.OrderPayTypeEnum;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.common.context.warehouse.WarehouseQueryEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.product.model.vo.OrderItemStatementAnalysisVO;
import com.cosfo.manage.report.converter.OrderItemStatementAnalysisConverter;
import com.cosfo.manage.report.model.dto.OrderItemStatementAnalysisInput;
import com.cosfo.manage.report.model.po.OrderItemStatementAnalysis;
import com.cosfo.manage.report.repository.OrderItemStatementAnalysisRepository;
import com.cosfo.manage.report.service.OrderItemStatementAnalysisReportService;
import com.cosfo.ordercenter.client.common.OrderSourceEnum;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 订单对账表-服务类
 *
 * @author: xiaowk
 * @time: 2024/9/26 上午11:10
 */
@Slf4j
@Service
public class OrderItemStatementAnalysisReportServiceImpl implements OrderItemStatementAnalysisReportService {

    @Resource
    private CommonService commonService;
    @Resource
    private OrderItemStatementAnalysisRepository orderItemStatementAnalysisRepository;

    /**
     * 订单对账表 当前订单号，如果订单号相同，同一个订单，总金额和支付信息只展示一行
     */
    private static final ThreadLocal<String> orderNoHolder = new ThreadLocal<>();

    /**
     * 当前支付单号，同一支付单，支付方式支付金额展示一行
     */
    private static final ThreadLocal<String> paymentNoHolder = new ThreadLocal<>();


    @Override
    public PageInfo<OrderItemStatementAnalysisVO> queryList(OrderItemStatementAnalysisInput input) {
        try {
            return queryCommonList(input);
        } finally {
            clearThreadLocal();
        }
    }

    private void clearThreadLocal(){
        orderNoHolder.remove();
        paymentNoHolder.remove();
    }

    private PageInfo<OrderItemStatementAnalysisVO> queryCommonList(OrderItemStatementAnalysisInput input){

        // 处理仓库编号
        WarehouseQueryEnum warehouseQueryEnum = WarehouseQueryEnum.getById(input.getWarehouseNo());
        input.setWarehouseType(transferWarehouseType(input, warehouseQueryEnum));
        if (Objects.nonNull(warehouseQueryEnum)) {
            input.setWarehouseNo(null);
        }

        Page<OrderItemStatementAnalysis> page = orderItemStatementAnalysisRepository.queryListByPage(input);
        PageInfo<OrderItemStatementAnalysisVO> pageInfo = OrderItemStatementAnalysisConverter.INSTANCE.fromPage(page);
        pageInfo.getList().forEach(e -> {
            if (e.getSpecification() != null && e.getSpecification().contains(Constants.UNDERLINE)) {
                e.setSpecification(e.getSpecification().substring(e.getSpecification().indexOf(Constants.UNDERLINE) + 1));
            }

            e.setOrderSourceStr(getOrderSourceStr(e.getOrderSource()));
            e.setPayTypeStr(OrderPayTypeEnum.getDesc(e.getPayType()));
            e.setWarehouseTypeStr(WarehouseTypeEnum.getByCode(e.getWarehouseType()).getDesc());
            if(StringUtils.isNotBlank(e.getFirstClassificationName())) {
                e.setClassificationName(e.getFirstClassificationName() + "/" + e.getSecondClassificationName());
            }
            if(StringUtils.isNotBlank(e.getFirstCategory())) {
                e.setCategory(e.getFirstCategory() + "/" + e.getSecondCategory() + "/" + e.getThirdCategory());
            }
            if(e.getGrossMarginRatio() != null) {
                e.setGrossMarginRatioStr(e.getGrossMarginRatio() + "%");
            }
            if(e.getSkuId() != null && e.getSkuId() == -1) {
                e.setSkuId(null);
            }
            if(WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(e.getWarehouseType())){
                e.setGoodsTitle(null);
                e.setOutboundAmount(e.getItemAmount());
            }

            if(e.getOutboundAmount() != null){
                e.setOutboundTotalPrice(NumberUtil.mul(e.getOutboundAmount(), e.getItemPayablePrice()));
                e.setTotalSupplyPrice(NumberUtil.mul(e.getOutboundAmount(), e.getSupplyPrice()));
            }


            if (orderNoHolder.get() == null || !orderNoHolder.get().equals(e.getOrderNo())) {
                orderNoHolder.set(e.getOrderNo());
            } else {
                e.setPayablePrice(null);
                e.setDeliveryFee(null);
                e.setTotalPrice(null);
            }

            if(paymentNoHolder.get() == null || !paymentNoHolder.get().equals(e.getPaymentNo())){
                paymentNoHolder.set(e.getPaymentNo());
            } else {
                e.setPayType(null);
                e.setPayTypeStr(null);
                e.setPayTotalPrice(null);
                e.setPaymentNo(null);
                e.setTransactionId(null);
            }

        });
        return pageInfo;
    }


    private Integer transferWarehouseType(OrderItemStatementAnalysisInput input, WarehouseQueryEnum warehouseQueryEnum) {
        if (Objects.isNull(input.getWarehouseNo())) {
            return null;
        }
        // 获取查询的仓库类型
        Integer warehouseType = WarehouseTypeEnum.PROPRIETARY.getCode();
        if (Objects.nonNull(warehouseQueryEnum)) {
            warehouseType = warehouseQueryEnum == WarehouseQueryEnum.NO_WAREHOUSE ? WarehouseTypeEnum.NO_WAREHOUSE.getCode() : warehouseType;
            warehouseType = warehouseQueryEnum == WarehouseQueryEnum.THIRD_WAREHOUSE ? WarehouseTypeEnum.THREE_PARTIES.getCode() : warehouseType;
        }
        return warehouseType;
    }



    private String getOrderSourceStr(Integer orderSource) {
        if (OrderSourceEnum.INNER_SYSTEM.getValue().equals(orderSource)) {
            return "门店下单";
        }

        if (OrderSourceEnum.AGENT_ORDER.getValue().equals(orderSource)) {
            return "总部代下计划单";
        }

        if (OrderSourceEnum.OPENAPI.getValue().equals(orderSource)) {
            return "开放平台下单";
        }

        return null;
    }


    @Override
    public Long exportList(OrderItemStatementAnalysisInput input) {
        Long tenantId = input.getTenantId();

        String fileName = "订单对账表" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH_mm_ss") + ".xlsx";

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.ORDER_ITEM_STATEMENT_ANALYSIS.getType());
        recordDTO.setTenantId(tenantId);
        recordDTO.setFileName(fileName);
        recordDTO.setParams(input.getParams());
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        return DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(input, ee -> {
            // 1、表格处理
            String filePath = generateReport(input);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });
    }

    public String generateReport(OrderItemStatementAnalysisInput input) {
        try {
            InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.ORDER_ITEM_STATEMENT_ANALYSIS.getName());
            String filePath = ExcelUtils.tempExcelFilePath();
            ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            input.setPageSize(500);
            int pageIndex = 1;
            //查询数据
            PageInfo<OrderItemStatementAnalysisVO> pageInfo;
            do {
                input.setPageIndex(pageIndex);
                pageInfo = queryCommonList(input);
                excelWriter.fill(pageInfo.getList(), fillConfig, writeSheet);
                pageIndex++;
            } while (pageInfo.isHasNextPage());

            excelWriter.finish();
            return filePath;
        } finally {
            clearThreadLocal();
        }
    }

}

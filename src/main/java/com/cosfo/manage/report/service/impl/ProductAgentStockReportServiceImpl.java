package com.cosfo.manage.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.BalanceRecordConstants;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.context.CommonCodeEnum;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.OnSaleTypeEnum;
import com.cosfo.manage.common.converter.report.ProductStockWarnMapper;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.ArrangeFacade;
import com.cosfo.manage.facade.WarehouseInventoryFacade;
import com.cosfo.manage.facade.WarehouseStorageFenceQueryFacade;
import com.cosfo.manage.facade.WarehouseStorageQueryFacade;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.market.service.MarketItemBusinessService;
import com.cosfo.manage.order.model.dto.OrderSkuSaleDTO;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.product.mapper.ProductSkuMapper;
import com.cosfo.manage.product.model.dto.AgentTenantSkuDTO;
import com.cosfo.manage.product.model.dto.AgentTenantSkuQueryDTO;
import com.cosfo.manage.product.model.dto.ProductCityStockDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.vo.ProductStockWarnExportVO;
import com.cosfo.manage.product.model.vo.ProductStockWarnVO;
import com.cosfo.manage.product.service.LocationCityService;
import com.cosfo.manage.product.service.ProductCityStockSyncService;
import com.cosfo.manage.report.model.dto.ProductAgentStockQueryDTO;
import com.cosfo.manage.report.service.ProductAgentStockReportService;
import com.cosfo.manage.system.model.dto.CommonLocationCityDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.pms.client.req.ReplenishmentSkuQueryReq;
import net.summerfarm.pms.client.resp.ReplenishmentResultResp;
import net.summerfarm.wms.inventory.req.WarehouseSkuInventoryPageReq;
import net.summerfarm.wms.inventory.resp.WarehouseSkuInventoryPageResp;
import net.summerfarm.wnc.client.req.WarehouseBySkuCityQueryReq;
import net.summerfarm.wnc.client.req.WarehouseSkuFenceReq;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceAreaResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceStorageResp;
import net.summerfarm.wnc.client.resp.WarehouseSkuResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-05-16
 * @Description:
 */
@Slf4j
@Service
public class ProductAgentStockReportServiceImpl implements ProductAgentStockReportService {

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private OrderBusinessService orderBusinessService;
    @Resource
    private CommonService commonService;
    @Resource
    private MarketItemBusinessService marketItemBusinessService;
    @Resource
    private WarehouseInventoryFacade warehouseInventoryFacade;
    @Resource
    private WarehouseStorageFenceQueryFacade warehouseStorageFenceQueryFacade;
    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;
    @Resource
    private ProductSkuMapper productSkuMapper;
    @Resource
    private LocationCityService locationCityService;
    @Resource
    private ProductCityStockSyncService productCityStockSyncService;
    @Resource
    private ArrangeFacade arrangeFacade;


    @Override
    public void correction(Long skuId) {
        // 查询鲜沐报价货品信息
        AgentTenantSkuQueryDTO agentTenantSkuQueryDTO = AgentTenantSkuQueryDTO.builder().agentTenantId(XianmuSupplyTenant.TENANT_ID).skuId(skuId).build();
        List<AgentTenantSkuDTO> agentTenantSkuDTOList = productSkuMapper.selectAgentTenantSkuInfoByAgentTenantId(agentTenantSkuQueryDTO);
        // 同步库存信息
        syncSkuCityStock(agentTenantSkuDTOList, true);
    }

    @Override
    public void stockChangeSync(ProductAgentSkuMapping productAgentSkuMapping, int warehouseNo) {
        String sku = productAgentSkuMapping.getAgentSkuCode();
        WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
        warehouseSkuFenceReq.setSku(sku);
        warehouseSkuFenceReq.setWarehouseNos(Lists.newArrayList(warehouseNo));
        // 根据sku+仓库编号获取仓库覆盖城市区域信息
        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = warehouseStorageFenceQueryFacade.queryWarehouseSkuFence(Lists.newArrayList(warehouseSkuFenceReq));
        if (CollectionUtils.isEmpty(warehouseSkuFenceResps)) {
            log.info("监听库存变更消息忽略,sku:{}无法找到覆盖城市信息 stockChangeSync warehouseSkuFenceResps 为空,warehouseSkuFenceReq:{}",sku, JSON.toJSONString(warehouseSkuFenceReq));
            return;
        }
        Map<String, List<WarehouseSkuFenceStorageResp>> skuMap = warehouseSkuFenceResps.stream()
            .filter(resp -> CollectionUtil.isNotEmpty(resp.getWarehouseSkuFenceStorages()))
            .collect(Collectors.toMap(WarehouseSkuFenceResp::getSku, WarehouseSkuFenceResp::getWarehouseSkuFenceStorages, (v1, v2) -> v1));
        List<WarehouseSkuFenceStorageResp> warehouseSkuFenceStorageResps = skuMap.get(sku);
        if (CollectionUtils.isEmpty(warehouseSkuFenceStorageResps)) {
            log.info("监听库存变更消息忽略,sku:{}无法找到覆盖城市信息 stockChangeSync skuMap 为空,warehouseSkuFenceStorageResps:{}", sku, JSON.toJSONString(warehouseSkuFenceReq));
            return;
        }
        Map<Integer, List<WarehouseSkuFenceAreaResp>> areaRespMap = warehouseSkuFenceStorageResps.stream()
            .filter(resp -> CollectionUtil.isNotEmpty(resp.getWarehouseSkuFenceAreas()))
            .collect(Collectors.toMap(WarehouseSkuFenceStorageResp::getWarehouseNo, WarehouseSkuFenceStorageResp::getWarehouseSkuFenceAreas, (v1, v2) -> v1));
        List<WarehouseSkuFenceAreaResp> warehouseSkuFenceAreaResps = areaRespMap.get(warehouseNo);
        if (CollectionUtils.isEmpty(warehouseSkuFenceAreaResps)) {
            log.info("监听库存变更消息忽略,sku:{},warehouseNo:{}无法找到覆盖城市信息 stockChangeSync warehouseSkuFenceAreaResps 为空,warehouseSkuFenceStorageResps:{}",
                sku, warehouseNo, JSON.toJSONString(warehouseSkuFenceReq));
            return;
        }
        // 得到该库存变动记录的sku+所属仓城市列表
        List<String> cityNames = warehouseSkuFenceAreaResps.stream().map(warehouseSkuFenceAreaResp -> warehouseSkuFenceAreaResp.getCity()).distinct().collect(Collectors.toList());
        AgentTenantSkuQueryDTO agentTenantSkuQueryDTO = AgentTenantSkuQueryDTO.builder()
            .agentTenantId(XianmuSupplyTenant.TENANT_ID).cityNames(cityNames).skuId(productAgentSkuMapping.getSkuId()).build();
        List<AgentTenantSkuDTO> agentTenantSkuDTOList = productSkuMapper.selectAgentTenantSkuInfoByAgentTenantId(agentTenantSkuQueryDTO);
        // 同步库存信息
        syncSkuCityStock(agentTenantSkuDTOList, true);
    }

    @Override
    public void syncSkuCityStock(List<AgentTenantSkuDTO> agentTenantSkuDTOList, boolean needThrow) {
        if (CollectionUtils.isEmpty(agentTenantSkuDTOList)) {
            return;
        }
        Map<String, List<AgentTenantSkuDTO>> map = agentTenantSkuDTOList.stream().filter(dto -> StringUtils.isNotEmpty(dto.getCityName()))
            .collect(Collectors.groupingBy(AgentTenantSkuDTO::getCityName));
        List<ProductCityStockDTO> updateList = Lists.newArrayList();
        for (Map.Entry<String, List<AgentTenantSkuDTO>> entry : map.entrySet()) {
            String cityName = entry.getKey();
            List<AgentTenantSkuDTO> agentTenantSkuDtos = entry.getValue();
            List<String> skuList = agentTenantSkuDtos.stream().filter(dto -> Objects.nonNull(dto.getAgentSkuCode())).map(AgentTenantSkuDTO::getAgentSkuCode).collect(Collectors.toList());
            WarehouseBySkuCityQueryReq warehouseBySkuCityQueryReq = new WarehouseBySkuCityQueryReq();
            warehouseBySkuCityQueryReq.setCity(cityName);
            warehouseBySkuCityQueryReq.setSkuList(skuList);

            // 以city分组，批量查询库存信息
            List<WarehouseSkuResp> warehouseSkuResps;
            try {
                warehouseSkuResps = warehouseStorageQueryFacade.queryXmWarehouseBySkuCity(warehouseBySkuCityQueryReq);
            } catch (Exception e) {
                if (needThrow) {
                    throw e;
                }
                log.warn("同步sku城市库存异常,批量查询库存信息失败 cityName:{},skuCodeList:{},errorMsg:{},e", cityName, JSON.toJSONString(skuList), e.getMessage(), e);
                continue;
            }
            if (CollectionUtils.isEmpty(warehouseSkuResps)) {
                log.info("同步sku城市库存异常,查询无相关数据,商品所属{}城市无对应仓库信息,skuCodeList:{},agentTenantSkuDTOList:{}", cityName, JSON.toJSONString(skuList), JSON.toJSONString(agentTenantSkuDTOList));
                continue;
            }
            // 填充仓库列表
            builderSkuWarehouseNoInfo(cityName, agentTenantSkuDtos, warehouseSkuResps);

            Map<List<Integer>, List<AgentTenantSkuDTO>> warehouseNoMap = agentTenantSkuDtos.stream()
                .filter(dto -> !CollectionUtils.isEmpty(dto.getWarehouseNos())).collect(Collectors.groupingBy(AgentTenantSkuDTO::getWarehouseNos));
            for (Map.Entry<List<Integer>, List<AgentTenantSkuDTO>> warehouseEntry : warehouseNoMap.entrySet()) {
                List<Integer> warehouseNoList = warehouseEntry.getKey();
                List<AgentTenantSkuDTO> warehouseSkuDtoList = warehouseEntry.getValue();
                List<String> skuCodeList = warehouseEntry.getValue().stream().map(AgentTenantSkuDTO::getAgentSkuCode).collect(Collectors.toList());
                // 查询在同一个仓库列表下sku列表的库存信息
                WarehouseSkuInventoryPageReq req = new WarehouseSkuInventoryPageReq();
                req.setSkuCodeList(skuCodeList);
                req.setWarehouseNo(warehouseNoList);
                req.setPageNum(NumberConstant.ONE);
                req.setPageSize(req.getWarehouseNo().size() * req.getSkuCodeList().size());
                List<WarehouseSkuInventoryPageResp> list;
                try {
                    list = warehouseInventoryFacade.pageWarehouseSkuInventory(req).getList();
                } catch (Exception e) {
                    if (needThrow) {
                        throw e;
                    }
                    log.warn("同步sku城市库存异常,分页查询仓库库存信息失败 cityName:{},skuCodeList:{},errorMsg:{},e", cityName, JSON.toJSONString(req.getSkuCodeList()), e.getMessage(), e);
                    continue;
                }
                Map<String, List<WarehouseSkuInventoryPageResp>> skuCodeInventoryMap = list.stream().collect(Collectors.groupingBy(WarehouseSkuInventoryPageResp::getSkuCode));
                builderUpdateStockList(warehouseSkuDtoList, skuCodeInventoryMap, updateList, cityName);
            }
        }
        // 插入或更新城市库存
        productCityStockSyncService.upsertCityStockList(updateList);
    }

    private void builderSkuWarehouseNoInfo(String cityName, List<AgentTenantSkuDTO> agentTenantSkuDtos, List<WarehouseSkuResp> warehouseSkuResps) {
        // 填充仓库列表
        Map<String, List<WarehouseSkuResp>> skuWarehouseMap = warehouseSkuResps.stream().collect(Collectors.groupingBy(WarehouseSkuResp::getSku));
        for (AgentTenantSkuDTO agentTenantSkuDto : agentTenantSkuDtos) {
            List<WarehouseSkuResp> warehouseStorageFenceRuleRespList = skuWarehouseMap.get(agentTenantSkuDto.getAgentSkuCode());
            if (CollectionUtils.isEmpty(warehouseStorageFenceRuleRespList)) {
                log.info("同步sku城市库存异常,查询无相关数据,商品所属{}城市无对应仓库信息,skuCode:{},agentTenantSkuDto:{}", cityName, agentTenantSkuDto.getAgentSkuCode(), JSON.toJSONString(agentTenantSkuDto));
                continue;
            }
            List<Integer> warehouseNos = warehouseStorageFenceRuleRespList.stream().map(WarehouseSkuResp::getWarehouseNos).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            agentTenantSkuDto.setWarehouseNos(warehouseNos);
        }
    }

    /**
     * 构建sku所属的仓库的最新库存数据信息
     * @param warehouseSkuDtoList
     * @param skuCodeInventoryMap
     * @param updateList
     */
    private void builderUpdateStockList(List<AgentTenantSkuDTO> warehouseSkuDtoList, Map<String, List<WarehouseSkuInventoryPageResp>> skuCodeInventoryMap, List<ProductCityStockDTO> updateList,String cityName) {
        for (AgentTenantSkuDTO agentTenantSkuDto : warehouseSkuDtoList) {
            List<WarehouseSkuInventoryPageResp> warehouseSkuInventoryPageResps = skuCodeInventoryMap.get(agentTenantSkuDto.getAgentSkuCode());
            // 该城市id下的总库存量
            long totalAvailableQuantity = 0;
            if (!CollectionUtils.isEmpty(warehouseSkuInventoryPageResps)) {
                totalAvailableQuantity = warehouseSkuInventoryPageResps.stream()
                    .filter(resp -> agentTenantSkuDto.getWarehouseNos().contains(resp.getWarehouseNo().intValue())).mapToInt(WarehouseSkuInventoryPageResp::getAvailableQuantity).sum();
            }
            log.info("同步sku城市库存成功,商品所属{}城市对应仓库信息{},总库存数:{},agentTenantSkuDto:{}", cityName,
                JSON.toJSONString(agentTenantSkuDto.getWarehouseNos()), totalAvailableQuantity, JSON.toJSONString(agentTenantSkuDto));
            ProductCityStockDTO updateCityStockDto = new ProductCityStockDTO();
            updateCityStockDto.setCityId(agentTenantSkuDto.getCityId());
            updateCityStockDto.setSkuId(agentTenantSkuDto.getId());
            updateCityStockDto.setQuantity(totalAvailableQuantity);
            updateList.add(updateCityStockDto);
        }
    }


    /**
     * 获取sku城市仓库信息
     *
     * @param productSkuDtos
     * @return
     */
    private Map<String, Map<String, List<WarehouseSkuResp>>> getProductSkuWarehouseInfo(List<ProductSkuDTO> productSkuDtos) {
        // city->sku->list
        Map<String, Map<String, List<WarehouseSkuResp>>> citySkuWarehouseMap = Maps.newHashMap();
        Map<String, List<ProductSkuDTO>> citySkuMap = productSkuDtos.stream().collect(Collectors.groupingBy(ProductSkuDTO::getCityName));
        for (Map.Entry<String, List<ProductSkuDTO>> entry : citySkuMap.entrySet()) {
            String cityName = entry.getKey();
            List<ProductSkuDTO> productSkuDTOList = entry.getValue();
            List<String> skuList = productSkuDTOList.stream()
                .filter(dto -> Objects.nonNull(dto.getAgentSkuCode())).map(ProductSkuDTO::getAgentSkuCode).collect(Collectors.toList());
            WarehouseBySkuCityQueryReq warehouseBySkuCityQueryReq = new WarehouseBySkuCityQueryReq();
            warehouseBySkuCityQueryReq.setCity(cityName);
            warehouseBySkuCityQueryReq.setSkuList(skuList);
            List<WarehouseSkuResp> warehouseSkuResps = warehouseStorageQueryFacade.queryXmWarehouseBySkuCity(warehouseBySkuCityQueryReq);
            if (CollectionUtils.isEmpty(warehouseSkuResps)) {
                continue;
            }
            Map<String, List<WarehouseSkuResp>> skuWarehouseMap = warehouseSkuResps.stream().collect(Collectors.groupingBy(WarehouseSkuResp::getSku));
            citySkuWarehouseMap.put(cityName, skuWarehouseMap);
        }
        return citySkuWarehouseMap;
    }

    /**
     * 构建补货时间信息
     *
     * @param productStockWarnVoList
     */
    private void builderProductSkuWarehouseInfo(List<ProductStockWarnVO> productStockWarnVoList) {
        Integer uniqueNum = NumberConstant.ONE;
        List<ReplenishmentSkuQueryReq> reqList = Lists.newArrayList();
        Map<String, ProductStockWarnVO> uniqueMap = Maps.newHashMap();
        for (ProductStockWarnVO productStockWarnVO : productStockWarnVoList) {
            if (CollectionUtils.isEmpty(productStockWarnVO.getWarehouseIdList())) {
                continue;
            }
            ReplenishmentSkuQueryReq req = new ReplenishmentSkuQueryReq();
            req.setSkuCode(productStockWarnVO.getAgentSkuCode());
            req.setWarehouseNoList(productStockWarnVO.getWarehouseIdList());
            String uniqueId = String.valueOf(uniqueNum);
            req.setUniqueId(uniqueId);
            reqList.add(req);
            uniqueMap.put(uniqueId, productStockWarnVO);
            uniqueNum++;
        }

        if (CollectionUtils.isEmpty(reqList)) {
            return;
        }
        // 批量查询补货时间
        List<ReplenishmentResultResp> replenishmentResultResps = arrangeFacade.queryPurchase(reqList);
        if (CollectionUtils.isEmpty(replenishmentResultResps)) {
            return;
        }

        // 填入补货时间
        Map<String, ReplenishmentResultResp> uniqueIdReplenishmentMap = replenishmentResultResps.stream()
            .collect(Collectors.toMap(ReplenishmentResultResp::getUniqueId, Function.identity(), (v1, v2) -> v1));
        for (Map.Entry<String, ReplenishmentResultResp> entry : uniqueIdReplenishmentMap.entrySet()) {
            String uniqueId = entry.getKey();
            ProductStockWarnVO productStockWarnVO = uniqueMap.get(uniqueId);
            LocalDateTime replenishmentTime = entry.getValue().getReplenishmentTime();
            if (Objects.nonNull(replenishmentTime)) {
                productStockWarnVO.setReplenishmentTime(TimeUtils.convertToString(replenishmentTime, TimeUtils.FORMAT_DATE));
            }
        }
    }

    /**
     * 过滤上下架状态后的skuId列表
     *
     * @param skuIds
     * @param onSale
     * @param tenantId
     * @return
     */
    private List<Long> filterOnSaleSku(List<Long> skuIds, Integer onSale, Long tenantId) {
        // 若上下架状态不为空，查询出满足条件的skuId
        if (Objects.nonNull(onSale)) {
            Map<Long, List<MarketItemOnSaleSimple4StoreResp>> skuMap = marketItemBusinessService.selectSaleMapBySkuIds(tenantId, skuIds);

            if (OnSaleTypeEnum.ON_SALE.getCode().equals(onSale)) {
                List<Long> onSaleSkuIds = skuMap.entrySet().stream().filter(entry ->
                        entry.getValue().stream().anyMatch(resp -> OnSaleTypeEnum.ON_SALE.getCode().equals(resp.getOnSale()))
                ).map(Map.Entry::getKey).collect(Collectors.toList());
                return onSaleSkuIds;
            }
            if (OnSaleTypeEnum.SOLD_OUT.getCode().equals(onSale)) {
                List<Long> soldOutSkuIds = skuMap.entrySet().stream().filter(entry ->
                        entry.getValue().stream().allMatch(resp -> OnSaleTypeEnum.SOLD_OUT.getCode().equals(resp.getOnSale()))
                ).map(Map.Entry::getKey).collect(Collectors.toList());
                return soldOutSkuIds;
            }
        }
        return skuIds;
    }

    /**
     * 查询应该先根据market_item找到符合上下架状态的skuId，再到 product_sku=id关联去找货品信息
     *
     * @param productAgentStockQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    @Override
    public PageInfo<ProductStockWarnVO> queryProductWarnList(ProductAgentStockQueryDTO productAgentStockQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        List<Long> skuIds = productSkuMapper.selectAgentTenantSkuId(loginContextInfoDTO.getTenantId(), XianmuSupplyTenant.TENANT_ID);
        if (CollectionUtils.isEmpty(skuIds)) {
            return PageInfoHelper.createPageInfo(Lists.newArrayList(), productAgentStockQueryDTO.getPageSize());
        }

        skuIds = filterOnSaleSku(skuIds, productAgentStockQueryDTO.getOnSale(), loginContextInfoDTO.getTenantId());

        productAgentStockQueryDTO.setSkuIds(skuIds);
        Integer pageNum = productAgentStockQueryDTO.getPageNum();
        Integer pageSize = productAgentStockQueryDTO.getPageSize();
        productAgentStockQueryDTO.setEffectTime(LocalDateTime.now());
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        // 查询鲜沐报价货品信息
        List<ProductSkuDTO> productSkuDtos = productSkuMapper.selectAgentTenantSkuByTenantId(productAgentStockQueryDTO, loginContextInfoDTO.getTenantId(), XianmuSupplyTenant.TENANT_ID);
        if (CollectionUtils.isEmpty(productSkuDtos)) {
            return PageInfoHelper.createPageInfo(Lists.newArrayList(), productAgentStockQueryDTO.getPageSize());
        }

        // 查询货品近七天的销量情况
        skuIds = productSkuDtos.stream().map(ProductSkuDTO::getId).collect(Collectors.toList());
        List<OrderSkuSaleDTO> sevenOrderSkuSaleDtos = orderBusinessService.querySkuSaleAmountByCity(skuIds, loginContextInfoDTO.getTenantId(), NumberConstant.SEVEN);
        Table<Long, String, Integer> sevenOrderSkuSaleTable = HashBasedTable.create();
        sevenOrderSkuSaleDtos.stream().forEach(orderSkuSaleDTO ->
                sevenOrderSkuSaleTable.put(orderSkuSaleDTO.getSkuId(), orderSkuSaleDTO.getCity(), orderSkuSaleDTO.getSaleAmount()));

        // 查询数据的上下架状态,进行拼装
        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> saleSkuIdMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<MarketItemOnSaleSimple4StoreResp> marketItems = marketItemBusinessService.selectSaleStatusBySkuIds(loginContextInfoDTO.getTenantId(), skuIds);
            saleSkuIdMap = marketItems.stream().collect(Collectors.groupingBy(MarketItemOnSaleSimple4StoreResp::getSkuId));
        }
        // 获取报价单中仓库列表 city->sku->list
        Map<String, Map<String, List<WarehouseSkuResp>>> citySkuWarehouseMap = getProductSkuWarehouseInfo(productSkuDtos);

        Map<Long, List<MarketItemOnSaleSimple4StoreResp>> finalSaleSkuIdMap = saleSkuIdMap;
        List<ProductStockWarnVO> voList = productSkuDtos.stream().map(productSkuDto -> {
            ProductStockWarnVO productStockWarnVO = new ProductStockWarnVO();
            productStockWarnVO.setSkuId(productSkuDto.getId());
            productStockWarnVO.setAgentSkuCode(productSkuDto.getAgentSkuCode());
            productStockWarnVO.setTitle(productSkuDto.getTitle());
            productStockWarnVO.setCityName(productSkuDto.getCityName());
            productStockWarnVO.setMainPicture(productSkuDto.getMainPicture());
            productStockWarnVO.setSpecification(productSkuDto.getSpecification());
            productStockWarnVO.setSpecificationUnit(productSkuDto.getSpecificationUnit());
            // 上下架状态组装
            Integer onSale = null;
            List<MarketItemOnSaleSimple4StoreResp> marketItems = finalSaleSkuIdMap.get(productSkuDto.getId());
            if (!CollectionUtils.isEmpty(marketItems)) {
                onSale = marketItems.stream().anyMatch(resp -> OnSaleTypeEnum.ON_SALE.getCode().equals(resp.getOnSale())) ? OnSaleTypeEnum.ON_SALE.getCode() : OnSaleTypeEnum.SOLD_OUT.getCode();
            }
            productStockWarnVO.setOnSale(onSale);

            // 组装供应城市列表
            List<Integer> warehouseIdList = Optional.ofNullable(citySkuWarehouseMap.get(productStockWarnVO.getCityName())).map(map -> map.get(productStockWarnVO.getAgentSkuCode()))
                    .map(list -> list.stream().map(WarehouseSkuResp::getWarehouseNos).flatMap(Collection::stream).filter(warehouseNo -> Objects.nonNull(warehouseNo))
                            .distinct().collect(Collectors.toList())).orElse(null);
            productStockWarnVO.setWarehouseIdList(warehouseIdList);

            Boolean belowAverage = null;
            Integer sevenSales = Optional.ofNullable(sevenOrderSkuSaleTable.get(productStockWarnVO.getSkuId(), productStockWarnVO.getCityName())).orElse(NumberConstant.ZERO);
            // 平均销量进一法
            Integer sevenAverageSales = (int) Math.ceil(sevenSales / NumberConstant.SEVEN_DOUBLE);
            if (Objects.nonNull(productSkuDto.getQuantity())) {
                belowAverage = productSkuDto.getQuantity() < sevenAverageSales;
            }
            productStockWarnVO.setBelowAverage(belowAverage);
            productStockWarnVO.setSevenAverageSales(sevenAverageSales);
            Integer saleOut = null;
            if (Objects.nonNull(productSkuDto.getQuantity())) {
                saleOut = productSkuDto.getQuantity() > NumberConstant.ZERO ? CommonCodeEnum.NO.getCode() : CommonCodeEnum.YES.getCode();
            }
            productStockWarnVO.setSaleOut(saleOut);
            return productStockWarnVO;
        }).collect(Collectors.toList());

        // 构建预计补货时间信息
        builderProductSkuWarehouseInfo(voList);

        return PageInfoConverter.toPageInfo(page, voList);
    }

    @Override
    public CommonResult exportProductWarnList(ProductAgentStockQueryDTO productAgentStockQueryDTO) {
        //存储文件下载记录
        Map<String, Object> paramsMap = Maps.newHashMap();

        if (StringUtils.isNotEmpty(productAgentStockQueryDTO.getTitle())) {
            paramsMap.put("货品名称", productAgentStockQueryDTO.getTitle());
        }
        if (!CollectionUtils.isEmpty(productAgentStockQueryDTO.getCityIds())) {
            List<CommonLocationCityDTO> commonLocationCityDTOS = locationCityService.queryByCityIds(productAgentStockQueryDTO.getCityIds());
            List<String> cityNameList = commonLocationCityDTOS.stream().map(CommonLocationCityDTO::getName).collect(Collectors.toList());
            String cityNames = StringUtils.join(cityNameList, BalanceRecordConstants.PROOF_SPLIT_CHAR);
            paramsMap.put("供应城市", cityNames);
        }
        if (!Objects.isNull(productAgentStockQueryDTO.getSaleOut())) {
            paramsMap.put("是否售罄", CommonCodeEnum.getDesc(productAgentStockQueryDTO.getSaleOut()));
        }
        if (!Objects.isNull(productAgentStockQueryDTO.getOnSale())) {
            paramsMap.put("是否上架", OnSaleTypeEnum.getDesc(productAgentStockQueryDTO.getOnSale()));
        }

        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtils.getMerchantInfoDTO();


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.PRODUCT_STOCK_WARN.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.PRODUCT_STOCK_WARN_RECORD_EXPORT.getFileName());
        recordDTO.setParams(CollectionUtils.isEmpty(paramsMap) ? Constants.TOTAL : JSON.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(productAgentStockQueryDTO, ee -> {
            // 1、表格处理
            String filePath = exportProductWarnData(productAgentStockQueryDTO, loginContextInfoDTO);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(loginContextInfoDTO.getTenantId());
//        record.setParams(CollectionUtils.isEmpty(paramsMap) ? Constants.TOTAL : JSON.toJSONString(paramsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        record.setType(FileDownloadTypeEnum.PRODUCT_STOCK_WARN.getType());
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                exportProductWarnData(productAgentStockQueryDTO, loginContextInfoDTO, record.getId());
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(record.getId());
//                log.error("库存预警数据导出失败", e);
//            }
//        });
        return CommonResult.ok();
    }

    private String exportProductWarnData(ProductAgentStockQueryDTO productAgentStockQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 获取总页数
        productAgentStockQueryDTO.setPageNum(NumberConstants.ONE);
        productAgentStockQueryDTO.setPageSize(NumberConstants.FIFTY);
        ExcelTypeEnum excelTypeEnum = ExcelTypeEnum.PRODUCT_STOCK_WARN_RECORD_EXPORT;
        PageInfo<ProductStockWarnVO> pageInfo = queryProductWarnList(productAgentStockQueryDTO, loginContextInfoDTO);
        int pages = pageInfo.getPages();

        // 创建excelWriter
        String filePath = ExcelUtils.tempExcelFilePath();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        ExcelWriter excelWriter = EasyExcel.write(filePath, ProductStockWarnExportVO.class).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter())
                .withTemplate(ExcelUtils.getExcelFileInputStream(this.getClass(), excelTypeEnum.getName())).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.fill(Collections.EMPTY_LIST, fillConfig, writeSheet);

        // 分页查询
        for (int pageNum = NumberConstants.ONE; pageNum <= pages; pageNum++) {
            productAgentStockQueryDTO.setPageNum(pageNum);
            List<ProductStockWarnVO> productStockWarnVOList = queryProductWarnList(productAgentStockQueryDTO, loginContextInfoDTO).getList();
            if (CollectionUtil.isEmpty(productStockWarnVOList)) {
                break;
            }

            List<ProductStockWarnExportVO> tempList = productStockWarnVOList.stream().map(productStockWarnVO -> {
                ProductStockWarnExportVO productStockWarnExportVO = ProductStockWarnMapper.INSTANCE.productStockWarnToExportVo(productStockWarnVO);
                if (Objects.nonNull(productStockWarnExportVO.getBelowAverage()) && productStockWarnExportVO.getBelowAverage()) {
                    productStockWarnExportVO.setBelowAverageDesc(Constants.LOW_BELOW_AVERAGE_DESC);
                }
                productStockWarnExportVO.setOnSaleDesc(CommonCodeEnum.getDesc(productStockWarnExportVO.getOnSale()));
                productStockWarnExportVO.setSaleOutDesc(CommonCodeEnum.getDesc(productStockWarnExportVO.getSaleOut()));
                return productStockWarnExportVO;
            }).collect(Collectors.toList());

            // 分批写
            excelWriter.fill(tempList, fillConfig, writeSheet);
        }
        excelWriter.finish();

        return filePath;
        // 上传到七牛云
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, excelTypeEnum, fileDownloadRecordId);
    }
}

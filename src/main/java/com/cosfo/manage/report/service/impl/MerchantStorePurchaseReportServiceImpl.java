package com.cosfo.manage.report.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.MerchantStorePurchaseReportConstants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.GoodsTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.common.context.SortTypeEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.LocalDateTimeUtil;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.market.service.MarketService;
import com.cosfo.manage.report.converter.ReportConverter;
import com.cosfo.manage.report.mapper.MerchantStorePurchaseReportMapper;
import com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportExportDTO;
import com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportQueryDTO;
import com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportResultDTO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportResultVO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportVO;
import com.cosfo.manage.report.service.MerchantStorePurchaseReportService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 门店采购明细报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Slf4j
@Service
public class MerchantStorePurchaseReportServiceImpl implements MerchantStorePurchaseReportService {
    @Resource
    private MerchantStorePurchaseReportMapper merchantStorePurchaseReportMapper;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private CommonService commonService;
    @Resource
    private MarketService marketService;

    @Override
    public PageInfo<MerchantStorePurchaseReportResultVO> listAll(MerchantStorePurchaseReportVO merchantStorePurchaseReportVO) {
        List<MerchantStorePurchaseReportResultDTO> resultList = getResultVOList(merchantStorePurchaseReportVO);
        PageInfo<MerchantStorePurchaseReportResultDTO> pageDTOInfo = PageInfoHelper.createPageInfo(resultList, merchantStorePurchaseReportVO.getPageSize());
        List<MerchantStorePurchaseReportResultVO> resultVOList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(resultList)){
            for (MerchantStorePurchaseReportResultDTO resultDTO : resultList) {
                resultVOList.add(ReportConverter.toMerchantStorePurchaseReportResultVO(resultDTO, marketService));
            }
        }
        PageInfo<MerchantStorePurchaseReportResultVO> pageInfo = new PageInfo<>();
        pageInfo.setList(resultVOList);
        pageInfo.setTotal(pageDTOInfo.getTotal());
        pageInfo.setPageSize(pageDTOInfo.getPageSize());
        pageInfo.setPageNum(pageDTOInfo.getPageNum());
        pageInfo.setPages(pageDTOInfo.getPages());
        return pageInfo;
    }

    List<MerchantStorePurchaseReportResultDTO> getResultVOList(MerchantStorePurchaseReportVO merchantStorePurchaseReportVO){
        MerchantStorePurchaseReportQueryDTO reportQueryDTO = ReportConverter.merchantStorePurchaseReportVO2DTO(merchantStorePurchaseReportVO);
        if (StringUtils.isEmpty(reportQueryDTO.getSortWord())) {
            reportQueryDTO.setSortWord(MerchantStorePurchaseReportConstants.DEFAULT_SORT);
        }

        if (StringUtils.isEmpty(reportQueryDTO.getSortType())) {
            reportQueryDTO.setSortType(SortTypeEnum.DESCENDING.getKeyWord());
        } else {
            reportQueryDTO.setSortType(SortTypeEnum.getByType(reportQueryDTO.getSortType()).getKeyWord());
        }

        if (ObjectUtils.isEmpty(reportQueryDTO.getStartTime()) || ObjectUtils.isEmpty(reportQueryDTO.getEndTime())){
            AssertCheckParams.notNull(reportQueryDTO.getStartTime(), ResultDTOEnum.DELIVERY_TIME_START.getCode(), ResultDTOEnum.DELIVERY_TIME_START.getMessage());
            AssertCheckParams.notNull(reportQueryDTO.getEndTime(), ResultDTOEnum.DELIVERY_TIME_END.getCode(), ResultDTOEnum.DELIVERY_TIME_END.getMessage());
        }
        if (ObjectUtils.isNotEmpty(merchantStorePurchaseReportVO.getPageIndex()) && ObjectUtils.isNotEmpty(merchantStorePurchaseReportVO.getPageSize())){
            PageHelper.startPage(merchantStorePurchaseReportVO.getPageIndex(), merchantStorePurchaseReportVO.getPageSize());
        }
        List<MerchantStorePurchaseReportResultDTO> dtoList = merchantStorePurchaseReportMapper.listAll(reportQueryDTO);
        return dtoList;
    }

    @Override
    public void export(LoginContextInfoDTO requestContextInfoDTO, MerchantStorePurchaseReportVO merchantStorePurchaseReportVO) {
        Long tenantId = requestContextInfoDTO.getTenantId();
        merchantStorePurchaseReportVO.setTenantId(tenantId);
        Map<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.TEN);
        // 配送日期
        if (Objects.nonNull(merchantStorePurchaseReportVO.getStartTime()) && Objects.nonNull(merchantStorePurchaseReportVO.getEndTime())) {
            queryParamsMap.put(MerchantStorePurchaseReportConstants.DELIVERY_DATE, LocalDateTimeUtil.localTimeFormat(merchantStorePurchaseReportVO.getStartTime())
                    + StringConstants.SEPARATING_IN_LINE
                    + LocalDateTimeUtil.localTimeFormat(TimeUtils.getLocalDateTimeByTimestamp(merchantStorePurchaseReportVO.getEndTime())));
        }
        // 商品编码
        if (Objects.nonNull(merchantStorePurchaseReportVO.getProductNo())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.PRODUCT_NO, String.valueOf(merchantStorePurchaseReportVO.getProductNo()));
        }
        // 商品名称
        if (Objects.nonNull(merchantStorePurchaseReportVO.getProductName())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.PRODUCT_NAME, merchantStorePurchaseReportVO.getProductName());
        }
        // 货源
        if (Objects.nonNull(merchantStorePurchaseReportVO.getGoodsType())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.WAREHOUSE_TYPE,
                    GoodsTypeEnum.getTypeByCode(merchantStorePurchaseReportVO.getGoodsType()).getDesc());
        }
        // 前台分组
        if (Objects.nonNull(merchantStorePurchaseReportVO.getMarketClassification())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.MARKET_CLASSIFICATION,merchantStorePurchaseReportVO.getMarketClassification());
        }
        // 后台类目
        if (Objects.nonNull(merchantStorePurchaseReportVO.getCategory())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.CATEGORY, merchantStorePurchaseReportVO.getCategory());
        }
        // 品牌
        if (Objects.nonNull(merchantStorePurchaseReportVO.getBrand())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.BRAND, merchantStorePurchaseReportVO.getBrand());
        }
        // 门店名称
        if (Objects.nonNull(merchantStorePurchaseReportVO.getStoreName())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.STORE_NAME, merchantStorePurchaseReportVO.getStoreName());
        }
        // 门店类型
        if (Objects.nonNull(merchantStorePurchaseReportVO.getType())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.TYPE, MerchantStoreEnum.Type.getDesc(merchantStorePurchaseReportVO.getType()));
        }
        // 门店分组
        if (Objects.nonNull(merchantStorePurchaseReportVO.getStoreGroup())){
            queryParamsMap.put(MerchantStorePurchaseReportConstants.STORE_GROUP, merchantStorePurchaseReportVO.getStoreGroup());
        }

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.MERCHANT_STORE_PURCHASE.getType());
        recordDTO.setTenantId(tenantId);
        recordDTO.setFileName(ExcelTypeEnum.MERCHANT_STORE_PURCHASE.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(merchantStorePurchaseReportVO, ee -> {
            // 1、表格处理
            String filePath = generateMerchantStoreExportFileStream(merchantStorePurchaseReportVO);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });

//        //存储文件下载记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(tenantId);
//        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        // 设置导出类型为门店采购明细
//        record.setType(FileDownloadTypeEnum.MERCHANT_STORE_PURCHASE.getType());
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
////            merchantStorePurchaseReportVO.setOffset(0L);
//            generateMerchantStoreExportFileStream(merchantStorePurchaseReportVO, record.getId());
//        });
    }

    private String generateMerchantStoreExportFileStream(MerchantStorePurchaseReportVO merchantStorePurchaseReportVO){
        try {
            MerchantStorePurchaseReportQueryDTO reportQueryDTO = ReportConverter.merchantStorePurchaseReportVO2DTO(merchantStorePurchaseReportVO);
            if (StringUtils.isEmpty(reportQueryDTO.getSortWord())) {
                reportQueryDTO.setSortWord(MerchantStorePurchaseReportConstants.DEFAULT_SORT);
            }

            if (StringUtils.isEmpty(reportQueryDTO.getSortType())) {
                reportQueryDTO.setSortType(SortTypeEnum.DESCENDING.getKeyWord());
            } else {
                reportQueryDTO.setSortType(SortTypeEnum.getByType(reportQueryDTO.getSortType()).getKeyWord());
            }

            if (ObjectUtils.isEmpty(reportQueryDTO.getStartTime()) || ObjectUtils.isEmpty(reportQueryDTO.getEndTime())){
                AssertCheckParams.notNull(reportQueryDTO.getStartTime(), ResultDTOEnum.DELIVERY_TIME_START.getCode(), ResultDTOEnum.DELIVERY_TIME_START.getMessage());
                AssertCheckParams.notNull(reportQueryDTO.getEndTime(), ResultDTOEnum.DELIVERY_TIME_END.getCode(), ResultDTOEnum.DELIVERY_TIME_END.getMessage());
            }

            ExcelLargeDataSetExporter<MerchantStorePurchaseReportResultDTO, MerchantStorePurchaseReportExportDTO> exporterHandler = new ExcelLargeDataSetExporter<MerchantStorePurchaseReportResultDTO, MerchantStorePurchaseReportExportDTO>(ExcelTypeEnum.MERCHANT_STORE_PURCHASE.getName()){
                @Override
                protected List<MerchantStorePurchaseReportExportDTO> convert(MerchantStorePurchaseReportResultDTO resultDTO) {
                    MerchantStorePurchaseReportResultVO merchantStorePurchaseReportResultVO = ReportConverter.toMerchantStorePurchaseReportResultVO(resultDTO, marketService);
                    MerchantStorePurchaseReportExportDTO merchantStorePurchaseReportExportDTO = new MerchantStorePurchaseReportExportDTO();
                    merchantStorePurchaseReportExportDTO.setStoreNo(merchantStorePurchaseReportResultVO.getStoreNo());
                    merchantStorePurchaseReportExportDTO.setStoreCode(merchantStorePurchaseReportResultVO.getStoreCode());
                    merchantStorePurchaseReportExportDTO.setStoreName(merchantStorePurchaseReportResultVO.getStoreName());
                    merchantStorePurchaseReportExportDTO.setStoreGroup(merchantStorePurchaseReportResultVO.getStoreGroup());
                    merchantStorePurchaseReportExportDTO.setType(merchantStorePurchaseReportResultVO.getType());
                    merchantStorePurchaseReportExportDTO.setProductNo(merchantStorePurchaseReportResultVO.getProductNo());
                    merchantStorePurchaseReportExportDTO.setProductName(merchantStorePurchaseReportResultVO.getProductName());
                    merchantStorePurchaseReportExportDTO.setSpecification(merchantStorePurchaseReportResultVO.getSpecification());
                    merchantStorePurchaseReportExportDTO.setSpecificationUnit(merchantStorePurchaseReportResultVO.getSpecificationUnit());
                    merchantStorePurchaseReportExportDTO.setWarehouseType(merchantStorePurchaseReportResultVO.getWarehouseType());
                    merchantStorePurchaseReportExportDTO.setMarketClassification(merchantStorePurchaseReportResultVO.getMarketClassification());
                    merchantStorePurchaseReportExportDTO.setCategory(merchantStorePurchaseReportResultVO.getCategory());
                    merchantStorePurchaseReportExportDTO.setBrand(merchantStorePurchaseReportResultVO.getBrand());
                    merchantStorePurchaseReportExportDTO.setQuantity(merchantStorePurchaseReportResultVO.getQuantity());
                    merchantStorePurchaseReportExportDTO.setDeliveryTime(merchantStorePurchaseReportResultVO.getDeliveryTime());
                    merchantStorePurchaseReportExportDTO.setReceivedQuantity(merchantStorePurchaseReportResultVO.getReceivedQuantity());
                    merchantStorePurchaseReportExportDTO.setReissueQuantity(merchantStorePurchaseReportResultVO.getReissueQuantity());
                    merchantStorePurchaseReportExportDTO.setAmount(merchantStorePurchaseReportResultVO.getAmount());
                    merchantStorePurchaseReportExportDTO.setAfterSaleAmount(merchantStorePurchaseReportResultVO.getAfterSaleAmount());
                    merchantStorePurchaseReportExportDTO.setGoodsType(merchantStorePurchaseReportResultVO.getGoodsType());
                    return Lists.newArrayList(merchantStorePurchaseReportExportDTO);
                }
            };

            merchantStorePurchaseReportMapper.exportAll(reportQueryDTO, exporterHandler);
            String filePath = exporterHandler.finish(true);

            return filePath;
//            commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, ExcelTypeEnum.MERCHANT_STORE_PURCHASE, recordId);
        } catch (Exception e) {
//            fileDownloadRecordService.updateFailStatus(recordId);
            log.error("门店订货明细导出失败", e);
        }
        return null;
    }

}

package com.cosfo.manage.report.service.impl;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/4/26 9:34
 */
//@Deprecated
//@Service
//public class OrderSoldBelowSupplySummaryServiceImpl implements OrderSoldBelowSupplySummaryService {
//
//    @Resource
//    private OrderSoldBelowSupplySummaryRepository orderSoldBelowSupplySummaryRepository;
//
//    @Override
//    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ResultHandler<?> resultHandler) {
//        orderSoldBelowSupplySummaryRepository.queryByConditionWithHandler(tenantId, supplierId, startTime, endTime, resultHandler);
//    }
//}

package com.cosfo.manage.report.mapper;

/**
 * <p>
 * 售价低于成本价订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
//@Mapper
//public interface OrderSoldBelowSupplySummaryMapper extends BaseMapper<OrderSoldBelowSupplySummary> {
//
//    @Select("select * from order_sold_below_supply_summary ${ew.customSqlSegment}")
//    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
//    @ResultType(OrderSoldBelowSupplySummary.class)
//    void queryByConditionWithHandler(@Param(Constants.WRAPPER) Wrapper wrapper, ResultHandler<?> resultHandler);
//}

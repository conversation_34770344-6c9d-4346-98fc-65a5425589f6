package com.cosfo.manage.report.mapper;

import com.cosfo.manage.market.model.po.MarketItemOnSaleSoldOutDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 商品上架售罄汇总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Mapper
public interface MarketItemOnSaleSoldOutDetailMapper extends BaseMapper<MarketItemOnSaleSoldOutDetail> {

    /**
     * 根据租户id和时间标签查询
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    MarketItemOnSaleSoldOutDetail querySummary(@Param("tenantId") Long tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);
}

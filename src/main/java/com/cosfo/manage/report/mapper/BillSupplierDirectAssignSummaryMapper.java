package com.cosfo.manage.report.mapper;

import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.bill.model.po.BillSupplierDirectAssignSummary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 供应商直配账单概要 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Mapper
public interface BillSupplierDirectAssignSummaryMapper extends BaseMapper<BillSupplierDirectAssignSummary> {

    /**
     * 查询汇总
     *
     * @param tenantBillQueryDTO 租户账单查询dto
     * @return {@link BillSupplierDirectAssignSummary}
     */
    BillSupplierDirectAssignSummary querySummary(TenantBillQueryDTO tenantBillQueryDTO);

}

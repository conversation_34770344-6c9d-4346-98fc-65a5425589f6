package com.cosfo.manage.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderOverviewDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 门店订货分析 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Mapper
public interface MerchantStoreOrderAnalysisMapper extends BaseMapper<MerchantStoreOrderAnalysis> {

    /**
     * 查询门店订货数据
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return 门店订货汇总
     */
    MerchantStoreOrderOverviewDTO queryStoreOrderSum(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO);

    /**
     * 根据条件查询
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return 门店订货列表
     */
    List<MerchantStoreOrderAnalysis> listByCondition(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO);

    /**
     * 导出
     * @param merchantStoreOrderAnalysisQueryDTO
     * @param resultHandler
     */
    void exportByCondition(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO, ResultHandler<?> resultHandler);
}

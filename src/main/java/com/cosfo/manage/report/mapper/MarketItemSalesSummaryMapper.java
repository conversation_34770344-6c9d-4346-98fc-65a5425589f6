package com.cosfo.manage.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.manage.report.model.dto.MarketItemSummaryQueryDTO;
import com.cosfo.manage.report.model.po.MarketItemSalesSummary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.report.model.po.OrderSoldBelowSupplySummary;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

import java.time.LocalDateTime;

/**
 * <p>
 * 商品销售汇总 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Mapper
public interface MarketItemSalesSummaryMapper extends BaseMapper<MarketItemSalesSummary> {

    //@Select("select ${ew.sqlSelect} from market_item_sales_summary ${ew.customSqlSegment}")
    //@Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    //@ResultType(MarketItemSalesSummary.class)
    void queryByConditionWithHandler(MarketItemSummaryQueryDTO marketItemSummaryQueryDTO, ResultHandler<?> resultHandler);
}

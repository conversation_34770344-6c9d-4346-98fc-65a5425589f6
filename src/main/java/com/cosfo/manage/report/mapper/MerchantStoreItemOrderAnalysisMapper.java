package com.cosfo.manage.report.mapper;

import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderOverviewDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreItemOrderAnalysis;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 门店商品订货分析 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Mapper
public interface MerchantStoreItemOrderAnalysisMapper extends BaseMapper<MerchantStoreItemOrderAnalysis> {

    /**
     * sum订货数据
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 返回数据
     */
    MerchantStoreItemOrderOverviewDTO queryStoreItemOrderSum(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO);

    /**
     * 根据条件查询
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 查询解雇o
     */
    List<MerchantStoreItemOrderAnalysis> listByCondition(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO);

    /**
     * 导出
     * @param merchantStoreItemOrderAnalysisQueryDTO
     * @param resultHandler
     * @return
     */
    void exportByCondition(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO, ResultHandler<?> resultHandler);
}

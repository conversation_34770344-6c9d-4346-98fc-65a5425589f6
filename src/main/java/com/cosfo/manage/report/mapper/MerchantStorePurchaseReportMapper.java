package com.cosfo.manage.report.mapper;

import com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportDetailDTO;
import com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportQueryDTO;
import com.cosfo.manage.report.model.dto.MerchantStorePurchaseReportResultDTO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportResultVO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportVO;
import java.util.List;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 门店采购明细报表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Repository
public interface MerchantStorePurchaseReportMapper {

    /**
     * 统计所有门店
     * @param merchantStorePurchaseReportQueryDTO
     * @return
     */
    List<MerchantStorePurchaseReportResultDTO> listAll(MerchantStorePurchaseReportQueryDTO merchantStorePurchaseReportQueryDTO);

    void exportAll(MerchantStorePurchaseReportQueryDTO merchantStorePurchaseReportQueryDTO, ResultHandler<?> resultHandler);

}

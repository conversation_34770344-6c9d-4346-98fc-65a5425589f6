package com.cosfo.manage.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cosfo.manage.bill.model.po.OrderProfitSharingDifference;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 订单分账差额 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Mapper
public interface OrderProfitSharingDifferenceMapper extends BaseMapper<OrderProfitSharingDifference> {


    @Select("select * from order_profit_sharing_difference ${ew.customSqlSegment}")
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    @ResultType(OrderProfitSharingDifference.class)
    void queryByConditionWithHandler(@Param(Constants.WRAPPER) Wrapper wrapper, ResultHandler<?> resultHandler);
}

package com.cosfo.manage.report.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.file.model.po.CommonResource;
import com.cosfo.manage.report.model.dto.PosOrderAuditQueryDTO;
import com.cosfo.manage.report.model.po.PosOrderAudit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.report.model.vo.PosOrderAuditVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 门店进销稽核表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Mapper
public interface PosOrderAuditMapper extends BaseMapper<PosOrderAudit> {
    Page<PosOrderAuditVO> listPage(Page<PosOrderAuditVO> page, @Param("query") PosOrderAuditQueryDTO dto);

}

package com.cosfo.manage.report.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.product.model.vo.StoreOrderDelayAnalysisVO;
import com.cosfo.manage.report.model.po.MerchantStoreOrderHysteresisAnalysis;
import com.github.pagehelper.PageInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantStoreOrderHysteresisAnalysisConverter {

    MerchantStoreOrderHysteresisAnalysisConverter INSTANCE = Mappers.getMapper(MerchantStoreOrderHysteresisAnalysisConverter.class);

    @Mapping(target = "isLastPage", expression = "java(!page.hasNext())")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "hasNextPage", expression = "java(page.hasNext())")
    PageInfo<StoreOrderDelayAnalysisVO> fromPage(Page<MerchantStoreOrderHysteresisAnalysis> page);

    List<StoreOrderDelayAnalysisVO> fromList(List<MerchantStoreOrderHysteresisAnalysis> list);
}

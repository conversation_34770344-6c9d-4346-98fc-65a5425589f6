package com.cosfo.manage.report.converter;

import com.cosfo.manage.report.model.vo.ProductAgentWarehouseAggregationVO;
import net.summerfarm.wms.saleinventory.dto.dto.SkuAggregateInventoryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: xiaowk
 * @date: 2025/4/10 下午2:01
 */
@Mapper
public interface SaleInventoryConverter {

    SaleInventoryConverter INSTANCE = Mappers.getMapper(SaleInventoryConverter.class);


    ProductAgentWarehouseAggregationVO dto2vo(SkuAggregateInventoryDTO dto);

    @Mapping(target = "sync", expression = "java((dto.getSync() != null) ? (dto.getSync() ? 1 : 0) : null)")
    ProductAgentWarehouseAggregationVO.WarehouseCityMappingVO dto2vo(SkuAggregateInventoryDTO.WarehouseCityMappingDTO dto);
}

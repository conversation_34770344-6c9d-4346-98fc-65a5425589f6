package com.cosfo.manage.report.converter;

import com.cosfo.manage.report.model.dto.PosOrderAuditExportDTO;
import com.cosfo.manage.report.model.vo.PosOrderAuditVO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PosOrderAuditConverter {

    PosOrderAuditConverter INSTANCE = Mappers.getMapper(PosOrderAuditConverter.class);

    List<PosOrderAuditExportDTO> vo2exportDtoList(List<PosOrderAuditVO> list);

    @Mapping(target = "status", expression = "java(com.cosfo.manage.common.context.PrivateProcurementStatusEnum.getDescByCode(vo.getStatus()))")
    @Mapping(target = "privateProcurement", expression = "java(vo.getPrivateProcurement().toString().concat( \"%\"))")
    PosOrderAuditExportDTO vo2exportDto(PosOrderAuditVO vo);



}

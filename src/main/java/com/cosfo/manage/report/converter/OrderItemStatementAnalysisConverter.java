package com.cosfo.manage.report.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.product.model.vo.OrderItemStatementAnalysisVO;
import com.cosfo.manage.report.model.po.OrderItemStatementAnalysis;
import com.github.pagehelper.PageInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2024/9/26 下午3:02
 */
@Mapper
public interface OrderItemStatementAnalysisConverter {

    OrderItemStatementAnalysisConverter INSTANCE = Mappers.getMapper(OrderItemStatementAnalysisConverter.class);

    @Mapping(target = "isLastPage", expression = "java(!page.hasNext())")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "hasNextPage", expression = "java(page.hasNext())")
    PageInfo<OrderItemStatementAnalysisVO> fromPage(Page<OrderItemStatementAnalysis> page);

    List<OrderItemStatementAnalysisVO> fromList(List<OrderItemStatementAnalysis> list);
}

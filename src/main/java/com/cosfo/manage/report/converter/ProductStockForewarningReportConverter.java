package com.cosfo.manage.report.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.product.model.po.ProductStockForewarningReport;
import com.cosfo.manage.report.model.dto.ProductStockForewarningReportDTO;
import com.github.pagehelper.PageInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ProductStockForewarningReportConverter {

    ProductStockForewarningReportConverter INSTANCE = Mappers.getMapper(ProductStockForewarningReportConverter.class);

    @Mapping(target = "isLastPage", expression = "java(!page.hasNext())")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "hasNextPage", expression = "java(page.hasNext())")
    PageInfo<ProductStockForewarningReportDTO> fromPage(Page<ProductStockForewarningReport> page);

    List<ProductStockForewarningReportDTO> fromList(List<ProductStockForewarningReport> list);
}

package com.cosfo.manage.product.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.product.dao.ProductAgentSkuMappingDao;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/16
 */
@Service
public class ProductAgentSkuMappingDaoImpl extends ServiceImpl<ProductAgentSkuMappingMapper, ProductAgentSkuMapping> implements ProductAgentSkuMappingDao {

    @Override
    public List<ProductAgentSkuMapping> selectBySkuIds(Collection<Long> skuIds, Long tenantId) {
        if(CollectionUtils.isEmpty(skuIds) || tenantId == null){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ProductAgentSkuMapping> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(ProductAgentSkuMapping::getSkuId, skuIds);
        queryWrapper.eq(ProductAgentSkuMapping::getTenantId, tenantId);
        return list(queryWrapper);
    }

    @Override
    public List<ProductAgentSkuMapping> selectByAgentSkuId(Long supplierSkuId) {
        LambdaQueryWrapper<ProductAgentSkuMapping> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductAgentSkuMapping::getAgentSkuId, supplierSkuId);
        // TODO 临时解决 tenantId != 1, 不为鲜沐租户，代仓申请记录查询
        queryWrapper.ne(ProductAgentSkuMapping::getTenantId, 1);
        return list(queryWrapper);
    }

    @Override
    public List<ProductAgentSkuMapping> selectByAgentSkuIds(List<Long> supplierSkuIds, Long tenantId) {
        LambdaQueryWrapper<ProductAgentSkuMapping> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(ProductAgentSkuMapping::getAgentSkuId, supplierSkuIds);
        queryWrapper.eq(ProductAgentSkuMapping::getTenantId, tenantId);
        return list(queryWrapper);
    }

//    @Override
//    public void deleteBySkuId(Long skuId) {
//        LambdaQueryWrapper<ProductAgentSkuMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.in(ProductAgentSkuMapping::getSkuId, skuId);
//        this.remove(queryWrapper);
//    }

    @Override
    public void deleteById(Long id) {
        LambdaQueryWrapper<ProductAgentSkuMapping> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(ProductAgentSkuMapping::getId, id);
        this.remove(queryWrapper);
    }

    @Override
    public List<ProductAgentSkuMapping> selectByTenantId(Long tenantId) {
        LambdaQueryWrapper<ProductAgentSkuMapping> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(ProductAgentSkuMapping::getTenantId, tenantId);
        return list(queryWrapper);
    }
}

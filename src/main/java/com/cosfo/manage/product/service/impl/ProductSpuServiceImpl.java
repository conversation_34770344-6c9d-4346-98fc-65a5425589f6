package com.cosfo.manage.product.service.impl;

import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.product.mapper.*;
import com.cosfo.manage.product.model.dto.ProductSkuExcelDataDTO;
import com.cosfo.manage.product.model.dto.ProductSpuDTO;
import com.cosfo.manage.product.model.po.ProductCategory;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.manage.product.service.ProductSpuService;
import com.cosfo.manage.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.util.stereotypes.Lazy;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 商品SPU Service层
 * @date 2022/5/12 10:42
 */
@Slf4j
@Service
public class ProductSpuServiceImpl implements ProductSpuService {

    @Resource
    private ProductSpuMapper productSpuMapper;
    @Resource
    private ProductSkuMapper productSkuMapper;
    @Resource
    private ProductBrandMapper productBrandMapper;
    @Resource
    private ProductCategoryMapper productCategoryMapper;
    /*@Resource
    private MarketItemService marketItemService;
    @Resource
    private ProductPricingSupplyMapper productPricingSupplyMapper;
    @Resource
    private MarketItemClassificationService marketItemClassificationService;
    @Resource
    private MarketClassificationService marketClassificationService;
    @Resource
    private StockService stockService;*/
    @Resource
    private ProductCategoryService productCategoryService;
    /*@Resource
    private MarketAreaItemService marketAreaItemService;*/
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private TenantService tenantService;
    @Value("${summerfarm.api-host}")
    private String apiHost;
    @Autowired
    @Lazy
    private ProductSkuService productSkuService;
    @Resource
    private ProductPricingSupplyCityMappingMapper productPricingSupplyCityMappingMapper;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private ProductBrandCategoryMappingMapper productBrandCategoryMappingMapper;
    @Resource
    private CommonService commonService;
    /*@Resource
    private MarketService marketService;*/

    /**
     * 第三方仓库
     */
    private static final String WAREHOUSE = "鲜沐";

    @Override
    public ResultDTO<Long> saveSpu(ProductSpuDTO productSpuDTO, LoginContextInfoDTO contextInfoDTO) {
        productSpuDTO.setTenantId(contextInfoDTO.getTenantId());

        //生成SPU
        ProductSpu productSpu = new ProductSpu();
        BeanUtils.copyProperties(productSpuDTO, productSpu);
        productSpuMapper.insert(productSpu);
        return ResultDTO.success(productSpu.getId());
    }

    /*@Transactional(rollbackFor = Exception.class)
    @Override
    public ResultDTO updateSpu(ProductSpuDTO productSpuDTO, LoginContextInfoDTO contextInfoDTO) {
        if (Objects.isNull(contextInfoDTO) || Objects.isNull(contextInfoDTO.getTenantId())) {
            return ResultDTO.fail(ResultDTOEnum.MERCHANT_INFO_NOT_FOUND);
        }
        Long tenantId = contextInfoDTO.getTenantId();
        productSpuDTO.setTenantId(tenantId);

        //更新SPU
        ProductSpu productSpu = new ProductSpu();
        BeanUtils.copyProperties(productSpuDTO, productSpu);
        productSpuMapper.update(productSpu);

        //更新销售商品属性
        ProductSku query = new ProductSku();
        query.setSpuId(productSpuDTO.getId());
        query.setTenantId(contextInfoDTO.getTenantId());
        List<ProductSku> productSkus = productSkuMapper.listAll(query);
        if (CollectionUtils.isEmpty(productSkus)) {
            return ResultDTO.success();
        }
        MarketItem queryItem = new MarketItem();
        MarketItem updateItem = new MarketItem();
        for (ProductSku sku : productSkus) {
            queryItem.setSkuId(sku.getId());
            queryItem.setTenantId(contextInfoDTO.getTenantId());
            MarketItem item = marketItemService.selectBySkuId(tenantId, sku.getId());
            BeanUtils.copyProperties(productSpuDTO, updateItem);
            updateItem.setId(item.getId());
            marketItemService.updateByPrimaryKeySelective(updateItem);
        }
        return ResultDTO.success();
    }*/

    /*@Override
    public ResultDTO listAll(Integer pageIndex, Integer pageSize, ProductQueryDTO productQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        productQueryDTO.setTenantId(contextInfoDTO.getTenantId());
        // 查询类目下所有子类目
        if (!Objects.isNull(productQueryDTO.getCategoryId())) {
            List<CategoryVO> categoryVOS = productCategoryService.queryChildCategoryList(productQueryDTO.getCategoryId());
            List<Long> categoryIds = categoryVOS.stream().map(CategoryVO::getId).collect(Collectors.toList());
            productQueryDTO.setCategoryIds(categoryIds);
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<ProductSpuDTO> productSpuDTOList = productSpuMapper.listAll(productQueryDTO);
        ProductSku query = new ProductSku();
        for (ProductSpuDTO spuDTO : productSpuDTOList) {
            String mainPicture = StringUtils.splitFirstOne(spuDTO.getMainPicture());
            spuDTO.setMainPicture(mainPicture);
            ProductBrand brand = productBrandMapper.selectByPrimaryKey(spuDTO.getBrandId());
            spuDTO.setBrandName(Objects.isNull(brand) ? StringConstants.EMPTY : brand.getName());
            ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(spuDTO.getCategoryId());
            spuDTO.setCategory(categoryDTO.getFirstCategoryName());
            spuDTO.setSecondaryCategory(categoryDTO.getSecondCategoryName());
            spuDTO.setThirdCategory(categoryDTO.getThirdCategoryName());

            Long id = spuDTO.getId();
            query.setSpuId(id);
            query.setTenantId(contextInfoDTO.getTenantId());
            List<ProductSku> skuList = productSkuMapper.listAll(query);
            if (CollectionUtils.isEmpty(skuList)) {
                spuDTO.setSkuAmount(NumberConstants.ZERO);
                spuDTO.setHighestPrice(BigDecimal.ZERO);
                spuDTO.setLowestPrice(BigDecimal.ZERO);
                spuDTO.setOnSaleSkuAmount(NumberConstants.ZERO);
                continue;
            }

            List<ProductSkuDTO> productSkuDTOS = productSkuService.querySkuMallPrice(skuList, contextInfoDTO.getTenantId());
            Map<Long, ProductSkuDTO> productSkuDTOMap = productSkuDTOS.stream().collect(Collectors.toMap(ProductSkuDTO::getId, item -> item));

            List<Long> skuIds = skuList.stream().map(ProductSku::getId).collect(Collectors.toList());
            // 查询鲜沐报价信息
            List<MarketAreaItemDTO> marketAreaItemDTOS = marketItemService.queryMarketAreaItemInfo(skuIds, contextInfoDTO.getTenantId());
            Map<Long, MarketAreaItemDTO> marketAreaItemDTOMap = marketAreaItemDTOS.stream().collect(Collectors.toMap(MarketAreaItemDTO::getSkuId, item -> item));

            // 计算金额
            BigDecimal lowestPrice = BigDecimal.ZERO, highestPrice = BigDecimal.ZERO;
            Integer onSaleSkuAmount = 0;
            for (ProductSku productSku : skuList) {
                MarketAreaItemDTO marketAreaItemDTO = marketAreaItemDTOMap.get(productSku.getId());
                // sku是否在售
                if (OnSaleTypeEnum.ON_SALE.getCode().equals(marketAreaItemDTO.getOnSale())) {
                    onSaleSkuAmount += 1;
                }

                // 价格汇总
                ProductSkuDTO productSkuDTO = productSkuDTOMap.get(productSku.getId());
                lowestPrice = Objects.isNull(productSkuDTO.getMinPrice()) ? lowestPrice : lowestPrice.compareTo(BigDecimal.ZERO) == 0 ? productSkuDTO.getMinPrice() : NumberUtil.min(lowestPrice, productSkuDTO.getMinPrice());
                highestPrice = Objects.isNull(productSkuDTO.getMaxPrice()) ? highestPrice : highestPrice.compareTo(BigDecimal.ZERO) == 0 ? productSkuDTO.getMaxPrice() : NumberUtil.max(highestPrice, productSkuDTO.getMaxPrice());
            }

            spuDTO.setSkuAmount(skuList.size());
            spuDTO.setHighestPrice(highestPrice);
            spuDTO.setLowestPrice(lowestPrice);
            spuDTO.setOnSaleSkuAmount(onSaleSkuAmount);
        }
        return PageResultDTO.success(PageInfoHelper.createPageInfo(productSpuDTOList, pageSize));
    }*/

//    @Override
//    public ResultDTO<ProductSpuDTO> selectDetail(Long id) {
//        ProductSpu productSpu = productSpuMapper.selectByPrimaryKey(id);
//        ProductSpuDTO spuDTO = new ProductSpuDTO();
//        BeanUtils.copyProperties(productSpu, spuDTO);
//        ProductSku querySku = new ProductSku();
//        querySku.setSpuId(id);
//        querySku.setTenantId(productSpu.getTenantId());
//        ProductBrand productBrand = productBrandMapper.selectByPrimaryKey(productSpu.getBrandId());
//        spuDTO.setBrandName(Objects.isNull(productBrand) ? StringConstants.EMPTY : productBrand.getName());
//
//        List<ProductSkuDTO> productSkuList = productSkuMapper.selectAll(querySku);
//        if (CollectionUtils.isEmpty(productSkuList)) {
//            return ResultDTO.success(spuDTO);
//        }
//        assemblySkuInfo(productSkuList, productSpu);
//        spuDTO.setSkuList(productSkuList);
//        return ResultDTO.success(spuDTO);
//    }

    /*@Override
    public void assemblySkuInfo(List<ProductSkuDTO> productSkuList, ProductSpu productSpu) {
        Long tenantId = productSpu.getTenantId();
        // 填充sku信息
        List<ProductSku> productSkus = productSkuList.stream().map(ProductSkuDTO -> {
            ProductSku productSku = new ProductSku();
            BeanUtils.copyProperties(ProductSkuDTO, productSku);
            return productSku;
        }).collect(Collectors.toList());
        List<ProductSkuDTO> productSkuDTOS = productSkuService.querySkuMallPrice(productSkus, tenantId);
        Map<Long, ProductSkuDTO> productSkuDTOMap = productSkuDTOS.stream().collect(Collectors.toMap(ProductSkuDTO::getId, item -> item));

        for (ProductSkuDTO skuDTO : productSkuList) {
            // sku价格
            if (productSkuDTOMap.containsKey(skuDTO.getId())) {
                ProductSkuDTO productSkuDto = productSkuDTOMap.get(skuDTO.getId());
                skuDTO.setPriceStr(productSkuDto.getPriceStr());
                skuDTO.setMinPrice(productSkuDto.getMinPrice());
                skuDTO.setMaxPrice(productSkuDto.getMaxPrice());
                // 价格策略
                skuDTO.setMarketAreaItemMappingVos(productSkuDto.getMarketAreaItemMappingVos());
            }

            Long skuId = skuDTO.getId();
            MarketItem item = marketItemService.selectBySkuId(tenantId, skuId);
            MarketItemClassification itemClassification = marketItemClassificationService.selectByItemId(tenantId, item.getId());
            if (Objects.nonNull(itemClassification)) {
                MarketClassification classification = marketClassificationService.selectByPrimaryKey(itemClassification.getClassificationId());
                skuDTO.setClassificationId(classification.getId());
            }
            MarketAreaItem areaItem = marketAreaItemService.selectByTenantAndSkuId(tenantId, skuId);
            // 如果是自营且品牌方配送
            if (GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(item.getGoodsType())) {
                //库存处理
                Stock stock = stockService.selectByItemId(skuDTO.getTenantId(), skuDTO.getId());
                skuDTO.setAmount(stock.getAmount());
            }

            // 代仓
            if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(item.getGoodsType())) {
                ProductAgentSkuVO productAgentSkuVO = productAgentSkuMappingMapper.queryAgentSkuInfo(skuDTO.getTenantId(), skuDTO.getId());
                // 查询代仓商品信息
                skuDTO.setAgentId(productAgentSkuVO.getId());
                ProductPricingSupplyDTO productPricingSupplyDTO = new ProductPricingSupplyDTO();
                BeanUtils.copyProperties(productAgentSkuVO, productPricingSupplyDTO);
                productPricingSupplyDTO.setSkuId(skuDTO.getId());
                productPricingSupplyDTO.setSupplySkuId(productAgentSkuVO.getSupplySkuId());
                productPricingSupplyDTO.setSupplyTenantId(productAgentSkuVO.getSupplyTenantId());
                productPricingSupplyDTO.setId(productAgentSkuVO.getId());
                skuDTO.setSupplyDTO(productPricingSupplyDTO);
            }

            // 三方品
            if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(item.getGoodsType())) {
                // 查询供应商商品信息
                ProductPricingSupplyDTO productPricingSupplyDTO = productPricingSupplyMapper.selectByTenantAndSkuId(skuDTO.getTenantId(), skuDTO.getId());
                // 如果没有关联报价单
                if (Objects.isNull(productPricingSupplyDTO)) {
                    continue;
                }

                ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(productSpu.getCategoryId());
                productPricingSupplyDTO.setCategory(categoryDTO.getFirstCategoryName());
                productPricingSupplyDTO.setSecondaryCategory(categoryDTO.getSecondCategoryName());
                productPricingSupplyDTO.setThirdCategory(categoryDTO.getThirdCategoryName());
                // 查询供应城市数和时间范围
                ProductPricingSupplyCityRangeDTO productPricingSupplyCityRangeDTO = productPricingSupplyCityMappingMapper.queryCitySupplyPriceRange(productPricingSupplyDTO.getId());
                productPricingSupplyDTO.setStartTime(productPricingSupplyCityRangeDTO.getStartTime());
                productPricingSupplyDTO.setEndTime(productPricingSupplyCityRangeDTO.getEndTime());
                ProductBrand brand = productBrandMapper.selectByPrimaryKey(productPricingSupplyDTO.getBrandId());
                productPricingSupplyDTO.setBrandName(Objects.isNull(brand) ? "" : brand.getName());
                productPricingSupplyDTO.setCityNum(productPricingSupplyCityRangeDTO.getCityNum());
                skuDTO.setCityNum(productPricingSupplyCityRangeDTO.getCityNum());
                // 获取鲜沐商城价
                if (productSkuDTOMap.containsKey(skuDTO.getId())) {
                    ProductSkuDTO productSkuDTO = productSkuDTOMap.get(skuDTO.getId());
                    ProductPricingSupplyDTO supplyDTO = productSkuDTO.getSupplyDTO();
                    if (!Objects.isNull(supplyDTO)) {
                        productPricingSupplyDTO.setPriceStr(supplyDTO.getPriceStr());
                        productPricingSupplyDTO.setMinPrice(supplyDTO.getMinSupplyPrice());
                        productPricingSupplyDTO.setMaxPrice(supplyDTO.getMaxSupplyPrice());
                        skuDTO.setSupplyDTO(productPricingSupplyDTO);
                    }
                }
            }
        }
    }*/

    /*@Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult templateCreateProduct(ProductSpuDTO productSpuDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(productSpuDTO.getClassificationId())) {
            throw new BizException("商品分组不能为空");
        }

        Integer productType = productSpuDTO.getProductType();
        Long templateId = productSpuDTO.getTemplateId();
        Long skuId;
        SummerfarmProductInfoDTO summerfarmProductInfoDTO = null;
        if (Objects.equals(productType, ProductTypeEnum.DISTRIBUTION.getType())) {
            ProductPricingSupply supply = productPricingSupplyMapper.selectByPrimaryKey(templateId);
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByTenantIdAndSkuId(supply.getSupplyTenantId(), supply.getSupplySkuId());
            skuId = productAgentSkuMapping.getSkuId();
            Long agentSkuId = productAgentSkuMapping.getAgentSkuId();
            Map<Long, SummerfarmProductInfoDTO> summerfarmProductInfoDTOMap = productSkuService.callSummerfarmQuerySkuInfo(Arrays.asList(agentSkuId));
            summerfarmProductInfoDTO = summerfarmProductInfoDTOMap.get(agentSkuId);
        } else {
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByPrimaryKey(templateId);
            skuId = productAgentSkuMapping.getSkuId();
        }

        // 查询出模板商品的信息
        ProductSkuDTO productSkuDTO = productSkuService.querySkuInfo(skuId);
        MarketSpuInput marketSpuInput = new MarketSpuInput();
        marketSpuInput.setCategoryId(productSkuDTO.getCategoryId());
        marketSpuInput.setTitle(productSkuDTO.getTitle());
        marketSpuInput.setSubTitle(productSkuDTO.getSubTitle());
        marketSpuInput.setMainPicture(productSkuDTO.getMainPicture());
        marketSpuInput.setClassificationId(productSpuDTO.getClassificationId());
        MarketAddResDTO addRes = marketService.add(marketSpuInput, loginContextInfoDTO);
        if (Objects.isNull(addRes)) {
            throw new BizException("商品新增失败！");
        }
        Long marketId = addRes.getMarketId();

        MarketItemInput insertSkuDTO = new MarketItemInput();
        insertSkuDTO.setMarketId(marketId);
        // 此接口的Controller已废弃，如需要使用，增加insertSkuDTO.setGoodsType;
        // insertSkuDTO.setWarehouseType(Objects.equals(productType, ProductTypeEnum.DISTRIBUTION.getType()) ? WarehouseTypeEnum.THREE_PARTIES.getCode() : WarehouseTypeEnum.NO_WAREHOUSE.getCode());
        // insertSkuDTO.setDeliveryType(DeliveryTypeEnum.THIRD_DELIVERY.getCode());
        insertSkuDTO.setPriceType(StorePriceTypeEnum.ALL.getCode());
        insertSkuDTO.setOnSale(OnSaleTypeEnum.SOLD_OUT.getCode());
        insertSkuDTO.setSkuId(skuId);
        insertSkuDTO.setAfterSaleUnit(Objects.isNull(summerfarmProductInfoDTO) ? "件" : summerfarmProductInfoDTO.getAfterSaleUnit());
        insertSkuDTO.setMaxAfterSaleAmount(Objects.isNull(summerfarmProductInfoDTO) ? 1 : summerfarmProductInfoDTO.getMaxAfterSaleAmount());
        insertSkuDTO.setMiniOrderQuantity(NumberConstants.ONE);

        Long itemId = marketItemService.save(insertSkuDTO, loginContextInfoDTO);

        Map<String, Long> skuInfoMap = new HashMap<>(NumberConstants.TWO);
        skuInfoMap.put(Constants.SKU_ID, itemId);
        skuInfoMap.put(Constants.SPU_ID, marketId);

        return CommonResult.ok(skuInfoMap);
    }*/

    /*@Override
    public void export(ProductQueryDTO productQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        //生成对应的查询条件
        Map<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.FIVE);
        if (!StringUtils.isBlank(productQueryDTO.getTitle())) {
            queryParamsMap.put(Constants.TITLE, productQueryDTO.getTitle());
        }
        if (!StringUtils.isBlank(productQueryDTO.getBrandName())) {
            queryParamsMap.put(Constants.BRAND_NAME, productQueryDTO.getBrandName());
        }
        if (Objects.nonNull(productQueryDTO.getCategoryId())) {
            ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(productQueryDTO.getCategoryId());
            queryParamsMap.put(Constants.CATEGORY, categoryDTO.getCategoryStr());
        }
        if (Objects.nonNull(productQueryDTO.getId())) {
            queryParamsMap.put(Constants.SPU, productQueryDTO.getId().toString());
        }
        if (Objects.nonNull(productQueryDTO.getSkuId())) {
            queryParamsMap.put(Constants.SKU, productQueryDTO.getSkuId().toString());
        }

        //存储文件下载记录
        FileDownloadRecord record = new FileDownloadRecord();
        record.setTenantId(loginContextInfoDTO.getTenantId());
        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
        record.setType(FileDownloadTypeEnum.PRODUCT.getType());
        fileDownloadRecordService.generateFileDownloadRecord(record);

        productQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());

        //发送导出异步消息
        ExecutorFactory.generateExcelExecutor.execute(() -> {
            try {
                generateProductExportFile(productQueryDTO, record.getId());
            } catch (Exception e) {
                fileDownloadRecordService.updateFailStatus(record.getId());
                throw new DefaultServiceException(e);
            }
        });
    }*/

    /*@Override
    public void generateProductExportFile(ProductQueryDTO productQueryDTO, Long fileDownloadRecordId) {
        Long tenantId = productQueryDTO.getTenantId();
        // 查询类目下所有子类目
        if (!Objects.isNull(productQueryDTO.getCategoryId())) {
            List<CategoryVO> categoryVOS = productCategoryService.queryChildCategoryList(productQueryDTO.getCategoryId());
            List<Long> categoryIds = categoryVOS.stream().map(CategoryVO::getId).collect(Collectors.toList());
            productQueryDTO.setCategoryIds(categoryIds);
        }
        List<ProductSpuDTO> productSpuDTOList = productSpuMapper.listAll(productQueryDTO);
        List<ProductSkuExcelDataDTO> excelDataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(productSpuDTOList)) {
            Integer serialNumber = 0;
            for (ProductSpuDTO spu : productSpuDTOList) {
                serialNumber++;
                ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(spu.getCategoryId());
                List<ProductSkuDTO> skuList = productSkuMapper.queryBySpuId(spu.getId());
                Boolean hasSkuFlag = CollectionUtils.isEmpty(skuList) ? Boolean.FALSE : Boolean.TRUE;
                if (!hasSkuFlag) {
                    skuList.add(new ProductSkuDTO());
                }

                for (ProductSkuDTO sku : skuList) {
                    ProductSkuExcelDataDTO excelData = new ProductSkuExcelDataDTO();
                    excelData.setSerialNumber(serialNumber.toString());
                    excelData.setTitle(spu.getTitle());
                    excelData.setSubTitle(spu.getSubTitle());
                    excelData.setFirstCategoryName(categoryDTO.getFirstCategoryName());
                    excelData.setSecondCategoryName(categoryDTO.getSecondCategoryName());
                    excelData.setThirdCategoryName(categoryDTO.getThirdCategoryName());
                    excelData.setBrandName(spu.getBrandName());
                    excelData.setStorageLocationDesc(ProductSkuEnum.STORAGE_LOCATION.getDesc(spu.getStorageLocation()));
                    excelData.setStorageTemperature(spu.getStorageTemperature());
                    excelData.setOrigin(spu.getOrigin());
                    excelData.setGuaranteePeriod(Objects.isNull(spu.getGuaranteePeriod()) ? StringConstants.EMPTY : spu.getGuaranteePeriod().toString());
                    excelData.setGuaranteeUnitDesc(ProductSkuEnum.GUARANTEE_UNIT.getDesc(spu.getGuaranteeUnit()));
                    if (hasSkuFlag) {
                        MarketClassificationDTO classificationDTO = productSkuService.queryClassification(tenantId, sku.getId());
                        MarketAreaItem areaItem = marketAreaItemService.selectByTenantAndSkuId(tenantId, sku.getId());
                        Stock stock = stockService.selectByItemId(tenantId, sku.getId());

                        excelData.setSpecification(sku.getSpecification());
                        excelData.setSpecificationUnit(sku.getSpecificationUnit());
                        excelData.setFirstClassificationName(classificationDTO.getFirstClassificationName());
                        excelData.setSecondClassificationName(classificationDTO.getName());
                        excelData.setOnSale(OnSaleTypeEnum.getDesc(areaItem.getOnSale()));
                        excelData.setId(sku.getId());
                        excelData.setStock(Objects.isNull(stock) ? StringConstants.EMPTY : stock.getAmount().toString());
                    }
                    excelDataList.add(excelData);
                }
            }
        }

        // 写入excel
        // 从第几号开始合并
        int mergeRowIndex = 1;
        // 需要合并的列
        int[] mergeColumnIndex = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
        String filePath = commonService.exportExcel(excelDataList, ExcelTypeEnum.PRODUCT.getName(), new ExcelMergeHandler(mergeRowIndex, mergeColumnIndex));

        // 上传七牛云
        String qiNiuPath = null;
        try {
            qiNiuPath = QiNiuUtils.uploadFile(filePath, "商品信息导出信息" + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss") + ".xlsx");
        } catch (Exception e) {
            log.error("上传七牛云失败", e);
        }

        // 删除临时文件
        commonService.deleteFile(filePath);

        // 更新文件下载记录
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setId(fileDownloadRecordId);
        fileDownloadRecord.setUrl(qiNiuPath);
        if (StringUtils.isBlank(qiNiuPath)) {
            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
        } else {
            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FINISHED.getStatus());
        }
        fileDownloadRecordService.updateSelectiveByPrimaryKey(fileDownloadRecord);
    }*/


    /*@Override
    public ExcelImportResDTO importProduct(MultipartFile file, LoginContextInfoDTO loginContextInfoDTO) throws IOException {
        Long tenantId = loginContextInfoDTO.getTenantId();
        // 读excel
        ImportExcelHelper<ProductSkuExcelDataDTO> helper = new ImportExcelHelper<>();
        List<ProductSkuExcelDataDTO> list = helper.getList(file.getInputStream(), ProductSkuExcelDataDTO.class, 0, 2);
        if (CollectionUtils.isEmpty(list)) {
            throw new DefaultServiceException("导入商品数据不能为空");
        }

        list.stream().filter(el -> Objects.isNull(el.getUId())).forEach(el -> el.setUId(UUID.randomUUID().toString()));

        Map<String, List<ProductSkuExcelDataDTO>> spuMap = list.stream().collect(Collectors.groupingBy(ProductSkuExcelDataDTO::getUId));
        List<ProductSkuExcelDataDTO> errorList = new ArrayList<>();
        spuMap.forEach((key, excelDataList) -> {
            Boolean handleSpuFlag = Boolean.TRUE;
            Long spuId = null;
            for (ProductSkuExcelDataDTO excelDataDTO : excelDataList) {
                // 校验excel数据
                if (handleSpuFlag) {
                    checkSpuImportData(excelDataDTO);
                }
                if (!StringUtils.isBlank(excelDataDTO.getErrorMessage())) {
                    errorList.add(excelDataDTO);
                    continue;
                }
                //如果sku属性全为空 则不进行校验
                Boolean noneSku = StringUtils.isBlank(excelDataDTO.getSpecification()) && StringUtils.isBlank(excelDataDTO.getSpecificationUnit()) && StringUtils.isBlank(excelDataDTO.getClassification()) && StringUtils.isBlank(excelDataDTO.getSpecification()) && StringUtils.isBlank(excelDataDTO.getSpecificationUnit()) && StringUtils.isBlank(excelDataDTO.getClassification()) && StringUtils.isBlank(excelDataDTO.getOnSale()) && StringUtils.isBlank(excelDataDTO.getOperationMode()) && StringUtils.isBlank(excelDataDTO.getStock()) && StringUtils.isBlank(excelDataDTO.getPrice());
                if (!noneSku) {
                    // sku属性校验
                    checkSkuImportData(excelDataDTO, tenantId);
                }

                if (Objects.nonNull(excelDataDTO.getErrorMessage())) {
                    errorList.add(excelDataDTO);
                    continue;
                }

                // 入库
                if (handleSpuFlag) {
                    if (StringUtils.isBlank(excelDataDTO.getSpuId())) {
                        ProductSpuDTO productSpuDTO = transferToSpuDTO(excelDataDTO);
                        productSpuDTO.setTenantId(tenantId);
                        ResultDTO<Long> saveSpuRes = saveSpu(productSpuDTO, loginContextInfoDTO);
                        spuId = saveSpuRes.getData();
                    } else {
                        spuId = Long.valueOf(excelDataDTO.getSpuId());
                    }
                }
                if (!noneSku) {
                    ProductSkuDTO productSkuDto = transferToSkuDTO(excelDataDTO);
                    productSkuDto.setSpuId(spuId);
                    productSkuDto.setTenantId(tenantId);
                    productSkuDto.setPriceType(NumberConstants.ZERO);
                    List<MarketAreaItemMappingVO> marketAreaItemMappingVos = new ArrayList<>(NumberConstants.TEN);
                    if (!StringUtils.isBlank(excelDataDTO.getPrice())) {
                        MarketAreaItemMappingVO marketAreaItemMappingVo = new MarketAreaItemMappingVO();
                        marketAreaItemMappingVo.setStorePriceType(StorePriceTypeEnum.ALL.getCode());
                        marketAreaItemMappingVo.setType(AreaItemTypeEnum.FIXED_PRICE.getCode());
                        marketAreaItemMappingVo.setMappingNumber(new BigDecimal(excelDataDTO.getPrice()));
                        marketAreaItemMappingVos.add(marketAreaItemMappingVo);
                        productSkuDto.setMarketAreaItemMappingVos(marketAreaItemMappingVos);
                    }

                    // productSkuService.saveSku(productSkuDto, loginContextInfoDTO, Boolean.FALSE);
                }
                handleSpuFlag = Boolean.FALSE;
            }
        });

        // 异常数据写入excel
        String qiNiuFilePath = null;
        if (!CollectionUtils.isEmpty(errorList)) {
            // 设置商品的序号
            resetSerialNumber(errorList);
            // 从第几号开始合并
            int mergeRowIndex = 1;
            // 需要合并的列
            int[] mergeColumnIndex = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
            String filePath = commonService.exportExcel(errorList, ExcelTypeEnum.ERROR_PRODUCT.getName(), new ExcelMergeHandler(mergeRowIndex, mergeColumnIndex));
            qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "商品错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
            commonService.deleteFile(filePath);
        }

        // 返回导入结果
        ExcelImportResDTO excelImportResDTO = new ExcelImportResDTO();
        excelImportResDTO.setFailRow(CollectionUtils.isEmpty(errorList) ? NumberConstants.ZERO : errorList.size());
        excelImportResDTO.setSuccessRow(list.size() - excelImportResDTO.getFailRow());
        excelImportResDTO.setErrorFilePath(qiNiuFilePath);
        return excelImportResDTO;
    }*/

//    private void resetSerialNumber(List<ProductSkuExcelDataDTO> list) {
//        Integer serialNumber = NumberConstants.ONE;
//        for (int i = 0; i < list.size(); i++) {
//            if (list.size() == 1) {
//                list.get(i).setSerialNumber(serialNumber.toString());
//            }
//            if (i == list.size() - 1) {
//                continue;
//            }
//            ProductSkuExcelDataDTO data = list.get(i);
//            data.setSerialNumber(serialNumber.toString());
//            ProductSkuExcelDataDTO nextData = list.get(i + 1);
//            if (Objects.equals(data.getUId(), nextData.getUId())) {
//                nextData.setSerialNumber(data.getSerialNumber());
//            } else {
//                nextData.setSerialNumber(String.valueOf(++serialNumber));
//            }
//        }
//
//    }


//    private ProductSpuDTO transferToSpuDTO(ProductSkuExcelDataDTO productSkuExcelDataDTO) {
//        ProductSpuDTO productSpuDTO = new ProductSpuDTO();
//        productSpuDTO.setTitle(productSkuExcelDataDTO.getTitle());
//        productSpuDTO.setSubTitle(productSkuExcelDataDTO.getSubTitle());
//        productSpuDTO.setCategoryId(productSkuExcelDataDTO.getCategoryId());
//        productSpuDTO.setBrandId(productSkuExcelDataDTO.getBrandId());
//        productSpuDTO.setStorageLocation(productSkuExcelDataDTO.getStorageLocation());
//        productSpuDTO.setStorageTemperature(productSkuExcelDataDTO.getStorageTemperature());
//        productSpuDTO.setOrigin(productSkuExcelDataDTO.getOrigin());
//        productSpuDTO.setGuaranteePeriod(Integer.valueOf(productSkuExcelDataDTO.getGuaranteePeriod()));
//        productSpuDTO.setGuaranteeUnit(productSkuExcelDataDTO.getGuaranteeUnit());
//        return productSpuDTO;
//    }

//    private ProductSkuDTO transferToSkuDTO(ProductSkuExcelDataDTO excelDataDTO) {
//        ProductSkuDTO productSkuDTO = new ProductSkuDTO();
//        productSkuDTO.setSpecification(excelDataDTO.getSpecification());
//        productSkuDTO.setSpecificationUnit(excelDataDTO.getSpecificationUnit());
//        productSkuDTO.setClassificationId(excelDataDTO.getClassificationId());
//        productSkuDTO.setAmount(Integer.valueOf(excelDataDTO.getStock()));
//        productSkuDTO.setPrice(Objects.isNull(excelDataDTO.getPrice()) ? null : new BigDecimal(excelDataDTO.getPrice()));
//        if (Objects.equals(excelDataDTO.getOnSale(), OnSaleTypeEnum.ON_SALE.getCode()) && Objects.isNull(excelDataDTO.getPrice())) {
//            productSkuDTO.setOnSale(OnSaleTypeEnum.SOLD_OUT.getCode());
//        } else {
//            productSkuDTO.setOnSale(OnSaleTypeEnum.getType(excelDataDTO.getOnSale()));
//        }
//        return productSkuDTO;
//    }

//    private void checkSpuImportData(ProductSkuExcelDataDTO excelDataDTO) {
//        String spuId = excelDataDTO.getSpuId();
//        if (!StringUtils.isBlank(spuId)) {
//            if (!StringUtils.isInteger(spuId)) {
//                excelDataDTO.setErrorMessage("spuId格式错误");
//                return;
//            }
//            ProductSpu productSpu = productSpuMapper.selectByPrimaryKey(Long.valueOf(spuId));
//            if (Objects.isNull(productSpu)) {
//                excelDataDTO.setErrorMessage("未查询到spuId为" + spuId + "的商品信息");
//            }
//            return;
//        }
//        String title = excelDataDTO.getTitle();
//        if (StringUtils.isBlank(title)) {
//            excelDataDTO.setErrorMessage("商品主标题不能为空");
//            return;
//        }
//        if (title.length() > NumberConstants.THIRTY_TWO) {
//            excelDataDTO.setErrorMessage("商品主标题长度不能超过32个字");
//            return;
//        }
//        if (StringUtils.isBlank(excelDataDTO.getFirstCategoryName()) || StringUtils.isBlank(excelDataDTO.getSecondCategoryName()) || StringUtils.isBlank(excelDataDTO.getThirdCategoryName())) {
//            excelDataDTO.setErrorMessage("类目信息不能为空");
//            return;
//        }
//        if (!checkSpuCategory(excelDataDTO)) {
//            return;
//        }
//        if (!StringUtils.isBlank(excelDataDTO.getBrandName())) {
//            ProductBrand brand = productBrandMapper.selectByName(excelDataDTO.getBrandName());
//            if (Objects.isNull(brand)) {
//                excelDataDTO.setErrorMessage("未查询到该品牌");
//                return;
//            }
//            excelDataDTO.setBrandId(brand.getId());
//        }
//        ProductBrandCategoryMapping mapping = productBrandCategoryMappingMapper.queryByBrandAndCategory(excelDataDTO.getBrandId(), excelDataDTO.getCategoryId());
//        if (Objects.isNull(mapping)) {
//            excelDataDTO.setErrorMessage("该品牌和类目之间未进行关联");
//            return;
//        }
//        if (!StringUtils.isBlank(excelDataDTO.getStorageLocationDesc())) {
//            Integer type = ProductSkuEnum.STORAGE_LOCATION.getType(excelDataDTO.getStorageLocationDesc());
//            if (Objects.isNull(type)) {
//                excelDataDTO.setErrorMessage("储存区域格式有误");
//                return;
//            }
//            excelDataDTO.setStorageLocation(type);
//        }
//        if (!StringUtils.isBlank(excelDataDTO.getStorageTemperature()) && excelDataDTO.getStorageTemperature().length() > NumberConstants.THIRTY_TWO) {
//            excelDataDTO.setErrorMessage("存储温度格式错误，不可以超过32个字");
//            return;
//        }
//        if (!StringUtils.isBlank(excelDataDTO.getOrigin()) && excelDataDTO.getOrigin().length() > NumberConstants.THIRTY) {
//            excelDataDTO.setErrorMessage("产地格式错误，不可以超过30个字");
//            return;
//        }
//        if (!StringUtils.isBlank(excelDataDTO.getGuaranteePeriod()) && !StringUtils.isInteger(excelDataDTO.getGuaranteePeriod())) {
//            excelDataDTO.setErrorMessage("保质期格式错误");
//            return;
//        }
//        if (!StringUtils.isBlank(excelDataDTO.getGuaranteePeriod()) && StringUtils.isBlank(excelDataDTO.getGuaranteeUnitDesc())) {
//            excelDataDTO.setErrorMessage("保质期单位不能为空");
//            return;
//        }
//        if (!StringUtils.isBlank(excelDataDTO.getGuaranteeUnitDesc())) {
//            Integer type = ProductSkuEnum.GUARANTEE_UNIT.getType(excelDataDTO.getGuaranteeUnitDesc());
//            if (Objects.isNull(type)) {
//                excelDataDTO.setErrorMessage("保质期单位格式错误");
//                return;
//            }
//            excelDataDTO.setGuaranteeUnit(type);
//        }
//    }

    private Boolean checkSpuCategory(ProductSkuExcelDataDTO excelDataDTO) {
        ProductCategory firstCategory = productCategoryService.selectByParentIdAndName(Constants.DEFAULT_CLASSIFICATION_ID, excelDataDTO.getFirstCategoryName());
        if (Objects.isNull(firstCategory)) {
            excelDataDTO.setErrorMessage("未查询到该一级类目");
            return Boolean.FALSE;
        }
        ProductCategory secondCategory = productCategoryMapper.selectByParentIdAndName(firstCategory.getId(), excelDataDTO.getSecondCategoryName());
        if (Objects.isNull(secondCategory)) {
            excelDataDTO.setErrorMessage("该一级类目下没有此二级类目");
            return Boolean.FALSE;
        }
        ProductCategory thirdCategory = productCategoryMapper.selectByParentIdAndName(secondCategory.getId(), excelDataDTO.getThirdCategoryName());
        if (Objects.isNull(thirdCategory)) {
            excelDataDTO.setErrorMessage("该二级类目下没有此三级类目");
            return Boolean.FALSE;
        }
        excelDataDTO.setCategoryId(thirdCategory.getId());
        return Boolean.TRUE;
    }

    /*private void checkSkuImportData(ProductSkuExcelDataDTO excelDataDTO, Long tenantId) {
        if (StringUtils.isBlank(excelDataDTO.getSpecification())) {
            excelDataDTO.setErrorMessage("商品规格不能为空");
            return;
        }
        if (StringUtils.isBlank(excelDataDTO.getSpecificationUnit())) {
            excelDataDTO.setErrorMessage("规格单位不能为空");
            return;
        }
        if (excelDataDTO.getSpecification().length() > NumberConstants.FIFTY) {
            excelDataDTO.setErrorMessage("商品规格长度不能超过60个字");
            return;
        }
        if (excelDataDTO.getSpecificationUnit().length() > NumberConstants.TEN) {
            excelDataDTO.setErrorMessage("规格单位长度不能超过10个字");
            return;
        }
        if (!checkClassification(excelDataDTO, tenantId)) {
            return;
        }
        //上下架
        if (Objects.isNull(excelDataDTO.getOnSale())) {
            excelDataDTO.setErrorMessage("上下架不能为空");
            return;
        }
        if (Objects.isNull(OnSaleTypeEnum.getType(excelDataDTO.getOnSale()))) {
            excelDataDTO.setErrorMessage("上下架格式错误");
            return;
        }

        //库存
        if (Objects.isNull(excelDataDTO.getStock())) {
            excelDataDTO.setErrorMessage("库存不能为空");
            return;
        }
        if (!StringUtils.isInteger(excelDataDTO.getStock())) {
            excelDataDTO.setErrorMessage("库存格式错误");
            return;
        }

        //经营方式
        if (StringUtils.isBlank(excelDataDTO.getOperationMode())) {
            excelDataDTO.setErrorMessage("经营方式不能为空");
            return;
        }
        if (!Objects.equals(excelDataDTO.getOperationMode(), MarketOperationModeEnum.SELF_RUN_SELF_DELIVERY.getDesc())) {
            excelDataDTO.setErrorMessage("经营方式只能为自营-品牌方配送");
            return;
        }

        // 价格
        if (StringUtils.isBlank(excelDataDTO.getPrice()) || !StringUtils.isNumeric(excelDataDTO.getPrice())) {
            excelDataDTO.setErrorMessage("价格格式错误");
            return;
        }
        BigDecimal price = new BigDecimal(excelDataDTO.getPrice());
        if (price.compareTo(BigDecimal.ZERO) < NumberConstants.ZERO) {
            excelDataDTO.setErrorMessage("价格不能小于0");
            return;
        }
        if (price.scale() > NumberConstants.TWO) {
            excelDataDTO.setErrorMessage("价格小数最多保留两位");
        }
    }*/

    /*private Boolean checkClassification(ProductSkuExcelDataDTO excelDataDTO, Long tenantId) {
        if (StringUtils.isEmpty(excelDataDTO.getClassification())) {
            excelDataDTO.setErrorMessage("分类不能为空");
            return Boolean.FALSE;
        }
        String[] classificationArray = excelDataDTO.getClassification().split(StringConstants.LEFT_SLASH);
        if (classificationArray.length != NumberConstants.TWO) {
            excelDataDTO.setErrorMessage("分类格式错误");
            return Boolean.FALSE;
        }
        String firstClassificationName = classificationArray[0];
        String secondClassificationName = classificationArray[1];
        MarketClassification firstClassification = marketClassificationService.selectByParentIdAndName(tenantId, Constants.DEFAULT_CLASSIFICATION_ID, firstClassificationName);
        if (Objects.isNull(firstClassification)) {
            excelDataDTO.setErrorMessage("未查询到该一级分类");
            return Boolean.FALSE;
        }
        MarketClassification secondClassification = marketClassificationService.selectByParentIdAndName(tenantId, firstClassification.getId(), secondClassificationName);
        if (Objects.isNull(secondClassification)) {
            excelDataDTO.setErrorMessage("未查询到该一级分类下存在此二级分类");
            return Boolean.FALSE;
        }
        excelDataDTO.setClassificationId(secondClassification.getId());
        return Boolean.TRUE;
    }*/

}

package com.cosfo.manage.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.erp.client.category.req.CategoryQueryReq;
import com.cosfo.erp.client.category.resp.CategoryDetailResultResp;
import com.cosfo.erp.client.enums.CategoryEnums;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.XianmuCategoryFacade;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.product.convert.CategoryConverter;
import com.cosfo.manage.product.mapper.ProductCategoryMapper;
import com.cosfo.manage.product.mapper.ProductSpuMapper;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.dto.ProductCategoryTreeDTO;
import com.cosfo.manage.product.model.po.ProductCategory;
import com.cosfo.manage.product.model.vo.CategoryVO;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/12 12:44
 */
@Service
public class ProductCategoryServiceImpl implements ProductCategoryService {

    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    private ProductCategoryMapper productCategoryMapper;
    @Resource
    private XianmuCategoryFacade categoryFacade;
    @Resource
    private ProductSpuMapper productSpuMapper;
    @Resource
    private MarketFacade marketFacade;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    @Override
    public ResultDTO listCategoryTree() {
        List<ProductCategoryTreeDTO> primaryList = categoryServiceFacade.listCategoryTreeNew ();
        return ResultDTO.success(primaryList);
    }

    @Override
    public List<CategoryVO> queryChildCategoryList(Long id) {
        return categoryServiceFacade.queryChildCategoryList(id);
    }

    @Override
    public List<Long> queryChildCategoryIdsOld(Long id) {
        List<CategoryVO> categoryVOS = queryChildCategoryList(id);
        if (CollectionUtils.isEmpty(categoryVOS)) {
            return Collections.emptyList();
        }
        return categoryVOS.stream().map(CategoryVO::getId).collect(Collectors.toList());
    }
    @Override
    public List<Long> queryChildCategoryIds(Long id) {
        List<CategoryVO> categoryVOS = categoryServiceFacade.queryChildCategoryListNew (id);
        if (CollectionUtils.isEmpty(categoryVOS)) {
            return Collections.emptyList();
        }
        return categoryVOS.stream().map(CategoryVO::getId).collect(Collectors.toList());
    }

    @Override
    public CategoryVO selectByCategoryId(Long id) {
        if (grayReleaseConfig.executeGray()) {
            return categoryServiceFacade.selectByCategoryIdNew (id);
        }else {
            return categoryServiceFacade.selectByCategoryId (id);
        }
    }


    @Override
    public ProductCategoryDTO selectWholeCategory(Long id) {
        return categoryServiceFacade.selectWholeCategoryNew (id);
    }

    public List<CategoryVO> selectByParentIdAndNameNew(Long parentId, String name) {
        CategoryQueryReq cateGoryQueryReq = new CategoryQueryReq();
        cateGoryQueryReq.setParentId(parentId);
        cateGoryQueryReq.setName(name);
        return categoryServiceFacade.listByConditionNew (cateGoryQueryReq);
    }
    public ProductCategory selectByParentIdAndName(Long parentId, String name) {
        CategoryQueryReq cateGoryQueryReq = new CategoryQueryReq();
        cateGoryQueryReq.setParentId(parentId);
        cateGoryQueryReq.setName(name);
        List<CategoryDetailResultResp> categoryDetailResultResps = categoryServiceFacade.listByCondition (cateGoryQueryReq);
        if(CollectionUtils.isEmpty(categoryDetailResultResps)){
            return null;
        }

        List<CategoryDetailResultResp> resultResps = categoryDetailResultResps.stream().filter(categoryDetailResultResp ->
                CategoryEnums.OutDated.IN_USE.getState().equals(categoryDetailResultResp.getOutdated())
        ).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(resultResps)){
            return null;
        }else {
            CategoryDetailResultResp categoryDetailResultResp = resultResps.get(NumberConstant.ZERO);
            return CategoryConverter.toProductCategory(categoryDetailResultResp);
        }
    }

    @Override
    public Map<Long, ProductCategoryDTO> batchQueryWholeCategory(Collection<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyMap();
        }
        if (grayReleaseConfig.executeGray()) {
            Map<Long, ProductCategoryDTO> longProductCategoryDTOMap = categoryServiceFacade.batchSelectWholeCategoryNew (Lists.newArrayList (categoryIds));
            return longProductCategoryDTOMap;
        }else {
            Map<Long, ProductCategoryDTO> longProductCategoryDTOMap = categoryServiceFacade.batchSelectWholeCategory (Lists.newArrayList (categoryIds));
            return longProductCategoryDTOMap;
        }
    }

    @Override
    public Long add(Long id, String name, Long parentId, Integer outDated) {
        CategoryQueryReq categoryQueryReq = new CategoryQueryReq();
        categoryQueryReq.setId(id);
        categoryQueryReq.setName(name);
        categoryQueryReq.setParentId(parentId);
        categoryQueryReq.setOutdated(outDated);
        return categoryServiceFacade.add(categoryQueryReq);
    }

    @Override
    public Long edit(Long id, String name, Long parentId, Integer outDated) {
        CategoryQueryReq categoryQueryReq = new CategoryQueryReq();
        categoryQueryReq.setName(name);
        categoryQueryReq.setParentId(parentId);
        categoryQueryReq.setId(id);
        categoryQueryReq.setOutdated(outDated);
        categoryServiceFacade.edit(categoryQueryReq);
        return id;
    }

    @Override
    public void synchronizedXianmuCategory(List<Long> categoryIds) {
        categoryServiceFacade.synchronizedXianmuCategory(categoryIds);
    }
}

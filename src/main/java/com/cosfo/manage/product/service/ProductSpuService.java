package com.cosfo.manage.product.service;

import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSpuDTO;
import com.cosfo.manage.product.model.dto.ProductQueryDTO;
import com.cosfo.manage.product.model.po.ProductSpu;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @description 商品SPU Service层
 * <AUTHOR>
 * @date 2022/5/12 10:42
 */
public interface ProductSpuService {

    /**
     * 创建商品
     * @param productSpuDTO
     * @param contextInfoDTO
     * @return
     */
    ResultDTO<Long> saveSpu(ProductSpuDTO productSpuDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 更新商品
     * @param productSpuDTO
     * @param contextInfoDTO
     * @return
     */
    //ResultDTO updateSpu(ProductSpuDTO productSpuDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询商品
     * @param pageIndex
     * @param pageSize
     * @param productQueryDTO
     * @param contextInfoDTO
     * @return
     */
    //ResultDTO listAll(Integer pageIndex, Integer pageSize, ProductQueryDTO productQueryDTO, LoginContextInfoDTO contextInfoDTO);

//    /**
//     * 商品详情接口
//     * @param id
//     * @return
//     */
//    ResultDTO<ProductSpuDTO> selectDetail(Long id);

    /**
     * 根据模板Id创建商品
     * @param productSpuDTO
     * @param loginContextInfoDTO
     * @return
     */
    //CommonResult templateCreateProduct(ProductSpuDTO productSpuDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 导出商品
     * @param productQueryDTO
     * @param loginContextInfoDTO
     */
    //void export(ProductQueryDTO productQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 生成导出商品的excel文件
     * @param productQueryDTO
     * @param fileDownloadRecordId
     */
   // void generateProductExportFile(ProductQueryDTO productQueryDTO, Long fileDownloadRecordId);

    /**
     * 导入商品
     * @param file
     * @param loginContextInfoDTO
     * @return
     * @throws IOException
     */
   // ExcelImportResDTO importProduct(MultipartFile file, LoginContextInfoDTO loginContextInfoDTO) throws IOException;

    /**
     * 商品详情
     *
     * @param productSkuList
     * @param productSpu
     */
    //void assemblySkuInfo(List<ProductSkuDTO> productSkuList, ProductSpu productSpu);

    /**
     * 处理代仓货品
     */
}

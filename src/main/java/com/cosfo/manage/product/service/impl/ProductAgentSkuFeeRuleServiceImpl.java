package com.cosfo.manage.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.common.context.ProductAutomaticIncreasePriceFlagEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuFeeRuleDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuFeeRuleDetailJsonDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuFeeRule;
import com.cosfo.manage.product.model.vo.ProductAgentSkuFeeRuleDetailVO;
import com.cosfo.manage.product.model.vo.ProductAgentSkuFeeRuleVO;
import com.cosfo.manage.product.repository.ProductAgentSkuFeeRuleRepository;
import com.cosfo.manage.product.service.ProductAgentSkuFeeRuleService;
import net.xianmu.common.result.CommonResult;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ProductAgentSkuFeeRuleServiceImpl implements ProductAgentSkuFeeRuleService {

    @Resource
    private ProductAgentSkuFeeRuleRepository productAgentSkuFeeRuleRepository;
    @Override
    public CommonResult<ProductAgentSkuFeeRuleVO> queryRule(Long tenantId) {
        LambdaQueryWrapper<ProductAgentSkuFeeRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductAgentSkuFeeRule::getTenantId, tenantId);
        ProductAgentSkuFeeRule productAgentSkuFeeRule = productAgentSkuFeeRuleRepository.getOne(queryWrapper);
        ProductAgentSkuFeeRuleVO productAgentSkuFeeRuleVo = new ProductAgentSkuFeeRuleVO();
        if (ObjectUtils.isEmpty(productAgentSkuFeeRule)) {
            productAgentSkuFeeRuleVo.setAutomaticIncreasePriceFlag(ProductAutomaticIncreasePriceFlagEnum.CLOSE.getFlag());
            return CommonResult.ok(productAgentSkuFeeRuleVo);
        }

        String rule = productAgentSkuFeeRule.getRule();
        productAgentSkuFeeRuleVo.setType(productAgentSkuFeeRule.getType());
        productAgentSkuFeeRuleVo.setAutomaticIncreasePriceFlag(productAgentSkuFeeRule.getAutomaticIncreasePriceFlag());

        List<ProductAgentSkuFeeRuleDetailJsonDTO> jsonDto = JSON.parseArray(rule, ProductAgentSkuFeeRuleDetailJsonDTO.class);
        List<ProductAgentSkuFeeRuleDetailVO> detailVos = jsonDto.stream().map(e -> {
            ProductAgentSkuFeeRuleDetailVO detailVo = new ProductAgentSkuFeeRuleDetailVO();
            detailVo.setAmount(e.getAmount());
            detailVo.setCount(e.getCount());
            detailVo.setPercentage(e.getPercentage());
            detailVo.setMemberCode(e.getMemberCode());
            return detailVo;
        }).collect(Collectors.toList());
        productAgentSkuFeeRuleVo.setDetails(detailVos);

        return CommonResult.ok(productAgentSkuFeeRuleVo);
    }

//    @Override
//    public ProductAgentSkuFeeRule queryByTenantId(Long tenantId) {
//        LambdaQueryWrapper<ProductAgentSkuFeeRule> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(ProductAgentSkuFeeRule::getTenantId, tenantId);
//        ProductAgentSkuFeeRule productAgentSkuFeeRule = productAgentSkuFeeRuleRepository.getOne(queryWrapper);
//        return productAgentSkuFeeRule;
//    }

    @Override
    public CommonResult updateAutomaticIncreasePriceFlag(ProductAgentSkuFeeRuleDTO productAgentSkuFeeRuleDto, LoginContextInfoDTO loginContextInfoDTO) {
        Assert.notNull(productAgentSkuFeeRuleDto.getAutomaticIncreasePriceFlag(), "标识不能为空");

        LambdaQueryWrapper<ProductAgentSkuFeeRule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductAgentSkuFeeRule::getTenantId, loginContextInfoDTO.getTenantId());
        ProductAgentSkuFeeRule productAgentSkuFeeRule = productAgentSkuFeeRuleRepository.getOne(lambdaQueryWrapper);
        if (Objects.isNull(productAgentSkuFeeRule)) {
            return CommonResult.ok();
        }

        ProductAgentSkuFeeRule update = new ProductAgentSkuFeeRule();
        update.setId(productAgentSkuFeeRule.getId());
        update.setAutomaticIncreasePriceFlag(productAgentSkuFeeRuleDto.getAutomaticIncreasePriceFlag());
        productAgentSkuFeeRuleRepository.updateById(update);
        return CommonResult.ok();
    }

//    @Override
//    public ProductAgentSkuFeeDTO buildAgentSkuFeeRuleList(Long tenantId, BigDecimal basePrice) {
//        ProductAgentSkuFeeDTO data = new ProductAgentSkuFeeDTO();
//        // 查询当前租户的加价规则
//        ProductAgentSkuFeeRule productAgentSkuFeeRule = queryByTenantId(tenantId);
//        // 如果没有配置 或者 配置关闭 就不处理
//        if (Objects.isNull(productAgentSkuFeeRule)
//                || Objects.equals(productAgentSkuFeeRule.getAutomaticIncreasePriceFlag(), ProductAutomaticIncreasePriceFlagEnum.CLOSE.getFlag())) {
//            return null;
//        }
//
//        List<ProductAgentSkuFeeRuleDetailDTO> productAgentSkuFeeRuleDetailDTOS = JSON.parseArray(productAgentSkuFeeRule.getRule(), ProductAgentSkuFeeRuleDetailDTO.class);
//        if (CollectionUtil.isEmpty(productAgentSkuFeeRuleDetailDTOS)) {
//            return null;
//        }
//        data.setType(productAgentSkuFeeRule.getType());
//
//        // 按照比例
//        if (Objects.equals(productAgentSkuFeeRule.getType(), ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode())) {
//            ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO = productAgentSkuFeeRuleDetailDTOS.stream()
//                .filter(el -> Objects.equals(el.getMemberCode(), ProfitSharingMerberCodeEnum.BRAND.getCode())).findFirst()
//                .orElse(new ProductAgentSkuFeeRuleDetailDTO());
//            BigDecimal percentage = productAgentSkuFeeRuleDetailDTO.getPercentage();
//            if (Objects.isNull(percentage) || percentage.compareTo(BigDecimal.ZERO) == 0) {
//                throw new DefaultServiceException("价格查询有误，请联系管理员");
//            }
//            BigDecimal percentageRate = percentage.divide(NumberConstant.DECIMAL_HUNDRED);
//            BigDecimal price = basePrice.divide(percentageRate, 2, BigDecimal.ROUND_HALF_UP);
//            data.setAfterPercentPrice(price);
//            return data;
//        }
//
//        // 按照加价规则
//        List<ProductAgentSkuFeeCountRuleDTO> ruleList = productAgentSkuFeeRuleDetailDTOS.stream().sorted(Comparator.comparing(ProductAgentSkuFeeRuleDetailDTO::getCount)).map(el -> {
//            ProductAgentSkuFeeCountRuleDTO ruleDTO = new ProductAgentSkuFeeCountRuleDTO();
//            ruleDTO.setAmount(el.getAmount());
//            ruleDTO.setCount(el.getCount());
//            ruleDTO.setPrice(basePrice.add(el.getAmount()));
//            return ruleDTO;
//        }).collect(Collectors.toList());
//        data.setCountRuleDTOList(ruleList);
//        return data;
//    }
}

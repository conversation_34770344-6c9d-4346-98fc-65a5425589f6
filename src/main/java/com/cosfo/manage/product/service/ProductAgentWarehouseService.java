package com.cosfo.manage.product.service;

import com.cosfo.manage.product.model.vo.ProductAgentWarehouseVO;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/4
 */
public interface ProductAgentWarehouseService {

    /**
     * 查询品牌方代仓品使用仓
     *
     * @param tenantId
     * @return
     */
    List<ProductAgentWarehouseVO> queryByTenantId(Long tenantId);

    /**
     * 查询品牌方所有仓库 (自营及代仓)
     *
     * @param tenantId
     * @return
     */
//    List<ProductAgentWarehouseVO> queryAllByTenantId(Long tenantId);

    /**
     * 根据仓库id查询仓库信息
     *
     * @param warehouseNos
     * @return
     */
//    List<ProductAgentWarehouseVO> queryByWarehouseIds(Long tenantId, List<Integer> warehouseNos);
}

package com.cosfo.manage.product.service.impl;

import com.cosfo.manage.facade.WarehouseStorageQueryFacade;
import com.cosfo.manage.product.model.vo.ProductAgentWarehouseVO;
import com.cosfo.manage.product.service.ProductAgentWarehouseService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/4
 */
@Slf4j
@Service
public class ProductAgentWarehouseServiceImpl implements ProductAgentWarehouseService {

    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;

    @Override
    public List<ProductAgentWarehouseVO> queryByTenantId(Long tenantId) {
        //
        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(tenantId, null , null);
        if(CollectionUtils.isEmpty(warehouseStorageResps)){
            return new ArrayList<>();
        }

        List<ProductAgentWarehouseVO> productAgentWarehouseVos = warehouseStorageResps.stream().map(productAgentWarehouse -> {
            ProductAgentWarehouseVO productAgentWarehouseVo = new ProductAgentWarehouseVO();
            productAgentWarehouseVo.setWarehouseId(Long.valueOf(productAgentWarehouse.getWarehouseNo()));
            productAgentWarehouseVo.setWarehouseName(productAgentWarehouse.getWarehouseName());
            return productAgentWarehouseVo;
        }).collect(Collectors.toList());
        return productAgentWarehouseVos;
    }

//    @Override
//    public List<ProductAgentWarehouseVO> queryAllByTenantId(Long tenantId) {
//        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(tenantId, null, null);
//        if(CollectionUtils.isEmpty(warehouseStorageResps)){
//            return new ArrayList<>();
//        }
//
//        List<ProductAgentWarehouseVO> productAgentWarehouseVos = warehouseStorageResps.stream().map(productAgentWarehouse -> {
//            ProductAgentWarehouseVO productAgentWarehouseVo = new ProductAgentWarehouseVO();
//            productAgentWarehouseVo.setWarehouseId(Long.valueOf(productAgentWarehouse.getWarehouseNo()));
//            productAgentWarehouseVo.setWarehouseName(productAgentWarehouse.getWarehouseName());
//            return productAgentWarehouseVo;
//        }).collect(Collectors.toList());
//        return productAgentWarehouseVos;
//    }

//    @Override
//    public List<ProductAgentWarehouseVO> queryByWarehouseIds(Long tenantId, List<Integer> warehouseNos) {
//        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(tenantId, null, warehouseNos);
//        if(CollectionUtils.isEmpty(warehouseStorageResps)){
//            return new ArrayList<>();
//        }
//
//        List<ProductAgentWarehouseVO> productAgentWarehouseVos = warehouseStorageResps.stream().map(productAgentWarehouse -> {
//            ProductAgentWarehouseVO productAgentWarehouseVo = new ProductAgentWarehouseVO();
//            productAgentWarehouseVo.setWarehouseId(Long.valueOf(productAgentWarehouse.getWarehouseNo()));
//            productAgentWarehouseVo.setWarehouseName(productAgentWarehouse.getWarehouseName());
//            return productAgentWarehouseVo;
//        }).collect(Collectors.toList());
//        return productAgentWarehouseVos;
//    }
}

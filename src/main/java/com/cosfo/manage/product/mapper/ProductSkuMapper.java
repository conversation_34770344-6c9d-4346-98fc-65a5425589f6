package com.cosfo.manage.product.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.product.model.dto.AgentSkuDumpDTO;
import com.cosfo.manage.product.model.dto.AgentTenantSkuDTO;
import com.cosfo.manage.product.model.dto.AgentTenantSkuQueryDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.ProductSkuQueryConditionDTO;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.report.model.dto.ProductAgentStockQueryDTO;
import com.cosfo.manage.report.model.dto.ProductAgentWarehouseDateQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import org.apache.ibatis.session.ResultHandler;

/**
 * @Discription 实物商品SKU持久层
 * <AUTHOR>
 * @date 2022/5/9
 */
@Mapper
public interface ProductSkuMapper extends BaseMapper<ProductSku> {

    /**
     * 插入数据
     * @param record
     * @return
     */
    int insert(ProductSku record);

    /**
     * 插入数据
     * @param record
     * @return
     */
    int insertSelective(ProductSku record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductSku selectByPrimaryKey(Long id);

    /**
     * 根据主键批量查询
     * @param ids
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductSku> selectBatchByPrimaryKey(@Param("ids") List<Long> ids);

    /**
     * 根据主键更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductSku record);

    /**
     * 根据主键更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductSku record);


    /**
     * 根据主键id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据实体查询
     * @param query
     * @return
     */
    List<ProductSku> listAll(ProductSku query);

//    /**
//     * 根据实体查询
//     * @param query
//     * @return
//     */
//    List<ProductSkuDTO> selectAll(ProductSku query);

//    /**
//     * 查询供应商sku信息
//     *
//     * @param supplySkuIds
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<ProductSkuDTO> querySupplySkuInfo(@Param("supplySkuIds") List<Long> supplySkuIds);

    /**
     * 查询SKU信息
     * @param skuId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductSkuDTO querySkuInfo(Long skuId);

    /**
     * 查询spu下的sku数量
     * @param spuId
     * @return
     */
    Integer querySkuCount(Long spuId);

    /**
     * 根据spuId查询
     * @param id
     * @return
     */
    List<ProductSkuDTO> queryBySpuId(Long id);

    /**
     * 根据品牌方代仓品商品信息
     *
     * @param productAgentWarehouseDateQueryDTO
     * @param tenantId
     * @return
     */
    List<ProductSkuDTO> selectAgentSkuByTenantId(@Param("productAgentWarehouseDateQueryDTO")ProductAgentWarehouseDateQueryDTO productAgentWarehouseDateQueryDTO,
                                                 @Param("tenantId") Long tenantId);

    void saveBatch(@Param("skuList") List<ProductSku> skuList);

    /**
     * 更新是否关联商品字段
     *
     * @param skuId
     * @param tenantId
     * @param associated
     */
    void updateAssociated(@Param("skuId") Long skuId,
                          @Param("tenantId") Long tenantId,
                          @Param("associated") Integer associated);

    /**
     * 根据条件查询
     *
     * @param productSkuQueryConditionDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductSkuDTO> listByCondition(ProductSkuQueryConditionDTO productSkuQueryConditionDTO);

    /**
     * 获取某个代理商供应的商品信息
     * @param productAgentStockQueryDTO
     * @param tenantId
     * @param agentTenantId
     * @return
     */
    List<ProductSkuDTO> selectAgentTenantSkuByTenantId(@Param("productAgentStockQueryDTO") ProductAgentStockQueryDTO productAgentStockQueryDTO, @Param("tenantId") Long tenantId,@Param("agentTenantId") Long agentTenantId);

    /**
     * 获取某个代理商供应的需要更新城市库存的商品信息
     * @param agentTenantSkuQueryDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<AgentTenantSkuDTO> selectAgentTenantSkuInfoByAgentTenantId(AgentTenantSkuQueryDTO agentTenantSkuQueryDTO);

    /**
     * 获取某个代理商供应的商品ID
     * @param tenantId
     * @param agentTenantId
     * @return
     */
    List<Long> selectAgentTenantSkuId(@Param("tenantId") Long tenantId,@Param("agentTenantId") Long agentTenantId);

    /**
     * Dump某个代理商供应的、需要更新城市库存的SKU信息,通常用来更新鲜沐的SKU库存信息；
     * 此SQL以城市为group By的字段，所以理论上不会有很多数据（全国地级市总共不超过400个）；
     * @param agentTenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<AgentSkuDumpDTO> dumpAgentTenantSkuInfoByAgentTenantId(@Param("agentTenantId") Long agentTenantId);
}

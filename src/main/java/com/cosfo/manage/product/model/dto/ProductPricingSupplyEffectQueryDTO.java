package com.cosfo.manage.product.model.dto;

import lombok.Data;
import org.omg.CORBA.LongHolder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/21 10:52
 */
@Data
public class ProductPricingSupplyEffectQueryDTO {

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 省id
     */
    private Long provinceId;

    /**
     * 0 经销 1 代仓商品
     */
    private Integer productType;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 类目ids
     */
    private List<Long> categoryIds;

    /**
     * 城市ids
     */
    private List<Long> cityIds;

    /**
     * 品牌ids
     */
    private List<Long> brandIds;

    /**
     * 是否关联标识
     */
    private String associated;

    /**
     * 是否过滤生效时间，有值时过滤
     */
    private LocalDateTime effectTime;
    /**
     * sku id
     */
    private Long skuId;
    /**
     * 供应商sku编码
     */
    private List<Long> supplySkuIds;
}

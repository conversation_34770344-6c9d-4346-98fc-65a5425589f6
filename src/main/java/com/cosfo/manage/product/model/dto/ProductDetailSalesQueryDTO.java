package com.cosfo.manage.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * product_detail_sales
 *
 * <AUTHOR>
@Data
public class ProductDetailSalesQueryDTO implements Serializable {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 类目ids
     */
    private List<Long> categoryIds;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private List<Integer> storeTypes;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 城市
     */
    private List<String> addressList;

    /**
     * 商品类型0自营1经销
     */
    @Deprecated
    private Integer warehouseType;

    /**
     * 配送方式0三方配送1品牌方配送
     */
    @Deprecated
    private Integer deliveryType;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品项
     */
    private Long itemId;

    /**
     * 导出参数json
     */
    private String exportParam;

    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     * @see com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;
}

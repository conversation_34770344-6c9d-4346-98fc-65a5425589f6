package com.cosfo.manage.product.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/10 9:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductMovementQueryDTO {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 时间标签
     */
    private String timeTag;

    /**
     * 时间标签s
     */
    private List<String> timeTags;

    /**
     * 1、日 2、周 3、月
     */
    private Integer type;

    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     * @see com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;

}

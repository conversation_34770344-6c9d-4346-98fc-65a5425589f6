package com.cosfo.manage.product.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.common.context.ForewarningStatusEnum;
import com.cosfo.manage.common.context.GoodsTypeEnum;
import com.cosfo.manage.product.mapper.ProductStockForewarningReportMapper;
import com.cosfo.manage.product.model.dto.ProductStockWarningQueryDTO;
import com.cosfo.manage.product.model.po.ProductStockForewarningReport;
import com.cosfo.manage.product.repository.ProductStockForewarningReportRepository;
import com.cosfo.manage.report.converter.ProductStockForewarningReportConverter;
import com.cosfo.manage.report.model.dto.ProductStockForewarningReportDTO;
import com.cosfo.manage.report.model.dto.ProductStockWarnInput;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 库存预警表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Service
public class ProductStockForewarningReportRepositoryImpl extends ServiceImpl<ProductStockForewarningReportMapper, ProductStockForewarningReport> implements ProductStockForewarningReportRepository {


    @Override
    public PageInfo<ProductStockForewarningReportDTO> queryProductWarnList(ProductStockWarnInput input) {
        LambdaQueryWrapper<ProductStockForewarningReport> reportLambdaQueryWrapper = new LambdaQueryWrapper<>();
        reportLambdaQueryWrapper.eq(ProductStockForewarningReport::getTenantId, input.getTenantId());
        reportLambdaQueryWrapper.eq(input.getSkuId() != null, ProductStockForewarningReport::getSkuId, input.getSkuId());
        reportLambdaQueryWrapper.eq(input.getStatus() != null, ProductStockForewarningReport::getForewarningStatus, input.getStatus());
        reportLambdaQueryWrapper.in(!CollectionUtils.isEmpty(input.getSkuIds()), ProductStockForewarningReport::getSkuId, input.getSkuIds());
        if (input.getWarehouseNo() != null) {
            if (input.getWarehouseNo() == -1) {
                reportLambdaQueryWrapper.eq(ProductStockForewarningReport::getGoodsType, GoodsTypeEnum.QUOTATION_TYPE.getCode());
            } else {
                reportLambdaQueryWrapper.eq(ProductStockForewarningReport::getWarehouseNo, input.getWarehouseNo());
                reportLambdaQueryWrapper.eq(ProductStockForewarningReport::getGoodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode());

            }
        }
        if(input.getUseFlag() != null){
            reportLambdaQueryWrapper.eq(ProductStockForewarningReport::getUseFlag, input.getUseFlag());
            reportLambdaQueryWrapper.eq(ProductStockForewarningReport::getGoodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode());
        }
        reportLambdaQueryWrapper.orderByAsc(ProductStockForewarningReport::getQuantity, ProductStockForewarningReport::getId);
        Page<ProductStockForewarningReport> page = page(new Page<>(input.getPageIndex(), input.getPageSize()), reportLambdaQueryWrapper);
        return ProductStockForewarningReportConverter.INSTANCE.fromPage(page);
    }

    @Override
    public PageInfo<ProductStockForewarningReportDTO> queryAllProductWarnPage(Long tenantId, Integer pageSize, Long maxId) {
        LambdaQueryWrapper<ProductStockForewarningReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductStockForewarningReport::getTenantId, tenantId);
        queryWrapper.gt(ProductStockForewarningReport::getId, maxId);
        queryWrapper.orderByAsc(ProductStockForewarningReport::getId);
        Page<ProductStockForewarningReport> page = page(new Page<>(1, pageSize), queryWrapper);
        return ProductStockForewarningReportConverter.INSTANCE.fromPage(page);
    }


    @Override
    public ProductStockForewarningReport selectByTenantAndSkuAndWarehouseNo(Long tenantId, String skucode, Integer warehouseNo) {
        LambdaQueryWrapper<ProductStockForewarningReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductStockForewarningReport::getTenantId, tenantId);
        queryWrapper.eq(ProductStockForewarningReport::getSkuCode, skucode);
        queryWrapper.eq(ProductStockForewarningReport::getWarehouseNo, warehouseNo);
        return getOne(queryWrapper);
    }

    @Override
    public List<ProductStockForewarningReport> selectByTenantAndSkuId(Long tenantId, Long skuId) {
        LambdaQueryWrapper<ProductStockForewarningReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductStockForewarningReport::getTenantId, tenantId);
        queryWrapper.eq(ProductStockForewarningReport::getSkuId, skuId);
        return list(queryWrapper);
    }

    @Override
    public boolean deleteByTenantIdAndSkuId(Long tenantId, Long skuId) {
        List<ProductStockForewarningReport> list = selectByTenantAndSkuId(tenantId, skuId);
        List<Long> idList = list.stream().map(ProductStockForewarningReport::getId).collect(Collectors.toList());
        return removeByIds(idList);
    }

    @Override
    public boolean deleteByTenantIdAndSkuIdAndWarehouseNos(Long tenantId, Long skuId, List<Integer> warehouseNos) {
        LambdaQueryWrapper<ProductStockForewarningReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductStockForewarningReport::getTenantId, tenantId);
        queryWrapper.eq(ProductStockForewarningReport::getSkuId, skuId);
        queryWrapper.in(ProductStockForewarningReport::getWarehouseNo, warehouseNos);
        List<ProductStockForewarningReport> list = list(queryWrapper);
        List<Long> idList = list.stream().map(ProductStockForewarningReport::getId).collect(Collectors.toList());
        return removeByIds(idList);
    }

    @Override
    public boolean insertOrUpdateBatch(List<ProductStockForewarningReport> list) {
        return this.baseMapper.insertOrUpdateBatch(list);
    }

    @Override
    public boolean updateUseFlagBySkuId(Long tenantId, Long skuId, Integer useFlag) {
        List<ProductStockForewarningReport> list = selectByTenantAndSkuId(tenantId, skuId);
        if(CollectionUtils.isEmpty(list)){
            return false;
        }

        List<Long> idList = list.stream().map(ProductStockForewarningReport::getId).collect(Collectors.toList());

        LambdaUpdateWrapper<ProductStockForewarningReport> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductStockForewarningReport::getTenantId, tenantId);
        updateWrapper.in(ProductStockForewarningReport::getId, idList);

        updateWrapper.set(ProductStockForewarningReport::getUseFlag, useFlag);
        return update(updateWrapper);
    }

    @Override
    public List<ProductStockForewarningReport> queryWarningSummary(ProductStockWarningQueryDTO productStockWarningQueryDTO) {
        Page<ProductStockForewarningReport> page = new Page<>(productStockWarningQueryDTO.getPageIndex(), productStockWarningQueryDTO.getPageSize());
        QueryWrapper<ProductStockForewarningReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", productStockWarningQueryDTO.getTenantId());
        queryWrapper.eq("forewarning_status", productStockWarningQueryDTO.getStockStatus());
        queryWrapper.orderByAsc("quantity");

        IPage<ProductStockForewarningReport> result = this.page(page, queryWrapper);
        return result.getRecords();
    }

    @Override
    public long queryWarningSkuCnt(Long tenantId) {
        QueryWrapper<ProductStockForewarningReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct sku_id");
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.eq("forewarning_status", ForewarningStatusEnum.FOREWARNING.getType());
        return count(queryWrapper);
    }
}

package com.cosfo.manage.product.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationItemQueryDTO;
import com.cosfo.manage.product.model.dto.ProductAgentApplicationQueryDTO;
import com.cosfo.manage.product.model.po.ProductAgentApplicationItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 代仓商品申请item表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
public interface ProductAgentApplicationItemRepository extends IService<ProductAgentApplicationItem> {

}

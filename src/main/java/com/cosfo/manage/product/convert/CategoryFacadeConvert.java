package com.cosfo.manage.product.convert;

import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.dto.ProductCategoryTreeDTO;
import com.cosfo.manage.product.model.vo.CategoryVO;
import net.summerfarm.goods.client.resp.CategoryAllPathResp;
import net.summerfarm.goods.client.resp.CategoryDetailResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CategoryFacadeConvert {
    CategoryFacadeConvert INSTANCE = Mappers.getMapper(CategoryFacadeConvert.class);

    @Mapping(source = "childrenList", target = "childList")
    @Mapping(target = "parentId",defaultValue = "0L")
    ProductCategoryTreeDTO convert2ProductCategoryTreeDTO(CategoryDetailResp data);

    @Mapping(source = "childrenList", target = "categoryVOS")
    @Mapping(target = "parentId",defaultValue = "0L")
    CategoryVO convert2CategoryVO(CategoryDetailResp data);

    List<ProductCategoryDTO> convert2ProductCategoryDTOs(List<CategoryAllPathResp> respList);

    @Mapping(source = "firstCategory", target = "firstCategoryName")
    @Mapping(source = "secondCategory", target = "secondCategoryName")
    @Mapping(source = "thirdCategory", target = "thirdCategoryName")
    ProductCategoryDTO convert2ProductCategoryDTO(CategoryAllPathResp data);
}

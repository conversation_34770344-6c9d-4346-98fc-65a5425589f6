package com.cosfo.manage.product.convert;

import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductSku;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: monna.chen
 * @Date: 2023/8/22 17:36
 * @Description:
 */
@Mapper
public interface ProductConvert {
    ProductConvert INSTANCE = Mappers.getMapper(ProductConvert.class);

    ProductSkuDTO convert2Dto(ProductSku sku);
}

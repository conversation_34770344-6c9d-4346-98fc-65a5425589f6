package com.cosfo.manage.product.convert;

import com.cofso.preferential.client.resp.ProductSkuPreferentialBasicResp;
import com.cosfo.manage.product.model.vo.ProductPreferentialCostPriceBasicDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2024-02-22
 * @Description:
 */
@Mapper
public interface ProductSkuPreferentialCostPriceConverter {

    ProductSkuPreferentialCostPriceConverter INSTANCE = Mappers.getMapper(ProductSkuPreferentialCostPriceConverter.class);

    ProductPreferentialCostPriceBasicDataVO basicRespToBasicVO(ProductSkuPreferentialBasicResp basicResp);
}

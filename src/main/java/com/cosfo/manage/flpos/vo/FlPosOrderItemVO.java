package com.cosfo.manage.flpos.vo;

import lombok.Data;

import java.util.List;

@Data
public class FlPosOrderItemVO {
    private Long id;
    private Long orderId;
    private FlPosProduct product;//商品明细信息
//    private String displayCategory;
    private Integer count;
    private Double price;
    private Double originalPrice;
    private Double totalPrice;
    private Double totalOriginalPrice;
    private String serviceType;
    private Double discountAmount;
    private List<FlPosOrderItemOption> options;//规格
//    private String genCoupons;
//    private String vipCoupons;
    private String sku;
    private List<FlPosOrderItemVO> items;//套餐明细
//    private String giftCouponCode;
//    private String giftCouponName;
}

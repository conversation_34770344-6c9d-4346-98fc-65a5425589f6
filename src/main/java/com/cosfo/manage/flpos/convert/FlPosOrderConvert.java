package com.cosfo.manage.flpos.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.flpos.vo.FlPosOrderDetailVO;
import com.cosfo.manage.flpos.vo.FlPosOrderItemOption;
import com.cosfo.manage.flpos.vo.FlPosOrderItemVO;
import com.cosfo.manage.pos.model.po.PosOrder;
import com.cosfo.manage.pos.model.po.PosOrderItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.util.List;
import java.util.stream.Collectors;

@Mapper
public interface FlPosOrderConvert {

    FlPosOrderConvert INSTANCE = Mappers.getMapper(FlPosOrderConvert.class);

    @Mapping(target = "channelType",source = "dataVO.channelType", defaultValue = "4")
    @Mapping(target = "tenantId",source = "tenantId")
    @Mapping(target = "outStoreCode", source = "dataVO.storeId")
    @Mapping(target = "outStoreName", source = "dataVO.storeName")
    @Mapping(target = "merchantStoreCode",source = "ftStoreCode")
    @Mapping(target = "orderNo",source = "dataVO.bill")
    @Mapping(target = "remarks",source = "dataVO.remark")
    @Mapping(target = "createTime",expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "availableDate",expression = "java(com.cosfo.manage.common.util.LocalDateTimeUtil.stringToDateSimple1(dataVO.getPaidAt()))")
    PosOrder flPosOrderDetailVO2PosOrder(FlPosOrderDetailVO dataVO, String ftStoreCode, Long tenantId);


    @Mapping(target = "thirdId", source = "itemVO.id")
    @Mapping(target = "channelType",source = "dataVO.channelType", defaultValue = "4")
    @Mapping(target = "outStoreCode", source = "dataVO.storeId")
    @Mapping(target = "outStoreName", source = "dataVO.storeName")
    @Mapping(target = "merchantStoreCode",source = "ftStoreCode")
    @Mapping(target = "orderNo",source = "dataVO.bill")
    @Mapping(target = "outMenuCode",source = "itemVO.sku")
    @Mapping(target = "outMenuName", source = "itemVO.product.name")
    @Mapping(target = "quantity",source = "itemVO.count")
    @Mapping(target = "remarks",source = "dataVO.remark")
    @Mapping(target = "availableDate",expression = "java(com.cosfo.manage.common.util.LocalDateTimeUtil.stringToDateSimple1(dataVO.getPaidAt()))")
    @Mapping(target = "outMenuSpecification", expression = "java(com.cosfo.manage.flpos.convert.FlPosOrderConvert.buildOutMenuSpecification(itemVO.getOptions()))")
    PosOrderItem flPosOrderItemVOsList2PosOrderItem(FlPosOrderDetailVO dataVO, FlPosOrderItemVO itemVO, String ftStoreCode, Long tenantId);

    static String buildOutMenuSpecification(List<FlPosOrderItemOption> options) {
        if(CollectionUtil.isNotEmpty (options)){
           return options.stream().map (FlPosOrderItemOption::getName).map(String::valueOf).collect(Collectors.joining(","));
        }
        return null;
    }
}

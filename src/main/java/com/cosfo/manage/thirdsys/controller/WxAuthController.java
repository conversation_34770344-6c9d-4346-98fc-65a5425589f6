package com.cosfo.manage.thirdsys.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.cosfo.manage.thirdsys.service.WxService;
import com.cosfo.manage.thirdsys.tool.SignUtil;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 微信 公众号 回调
 */
@RestController
@RequestMapping("/auth/wx/")
@Slf4j
public class WxAuthController {

    @Autowired
    private WxService wxService;

    @GetMapping(path = "/authGet")
    public Long authGet(@RequestParam(name = "signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {

        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }
        if (SignUtil.checkSignature("summerfarm", signature,timestamp, nonce)) {
            return Long.valueOf(echostr);
        }
        return Long.valueOf(timestamp);
    }

    @PostMapping(value = "/authGet")
    public String dealNotify(@RequestParam(value = "signature", required = false) String signature,
                          @RequestParam(value = "timestamp", required = false) String timestamp,
                          @RequestParam(value = "echostr", required = false) String echostr,
                          @RequestParam(value = "openid", required = false) String openid,
                          HttpServletRequest httpServletRequest) {
        String xml;
        try {
            xml = IOUtils.toString (httpServletRequest.getInputStream (), httpServletRequest.getCharacterEncoding ());
            log.info ("接收微信请求：[openid=[{}], 消息请求体:{}", openid, xml);
        } catch (IOException ioe) {
            log.error ("Wechat callback /check occur io error,msg:{}", ioe.getMessage (), ioe);
            throw new RuntimeException (ioe);
        }
        return wxService.dealNotify (xml);
    }
}
package com.cosfo.manage.facade.ordercenter;

import com.cosfo.manage.common.exception.OpenApiProviderException;
import com.cosfo.ordercenter.client.provider.OrderItemExtraQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @Author: fansongsong
 * @Date: 2023-09-26
 * @Description:
 */
@Component
@Slf4j
public class OrderItemExtraQueryFacade {

    @DubboReference
    private OrderItemExtraQueryProvider orderItemExtraQueryProvider;

    /**
     * 根据条件获取订单子项外部扩展数据
     * @param orderItemExtraQueryReq
     * @return
     */
    public List<OrderItemExtraResp> queryOrderItemExtraList(OrderItemExtraQueryReq orderItemExtraQueryReq) {
        DubboResponse<List<OrderItemExtraResp>> response = orderItemExtraQueryProvider.queryOrderItemExtraList(orderItemExtraQueryReq);
        if (!response.isSuccess()) {
            throw new OpenApiProviderException(response.getMsg());
        }
        return response.getData();
    }
}

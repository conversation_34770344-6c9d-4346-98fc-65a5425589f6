package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderNeedDeliveryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderQueryFacade {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;

    /**
     * 根据订单id查询
     *
     * @param orderId 订单id
     * @return 无数据时返回null
     */
    public OrderResp queryById(Long orderId) {
        DubboResponse<OrderResp> resp = orderQueryProvider.queryById(orderId);
        if (!resp.isSuccess()) {
            log.error("根据订单id查询失败, orderId: {}, resp: {}", orderId, resp);
            throw new BizException("查询订单失败");
        }
        return resp.getData();
    }

    /**
     * 根据订单no查询
     *
     * @param orderNo 订单no
     * @return 订单信息
     */
    public OrderResp queryByNo(String orderNo) {
        DubboResponse<OrderResp> resp = orderQueryProvider.queryByNo(orderNo);
        if (!resp.isSuccess()) {
            log.error("根据订单no查询失败, orderNo: {}, resp: {}", orderNo, resp);
            throw new BizException("查询订单失败");
        }
        return resp.getData();
    }

    /**
     * 根据订单id批量查询
     *
     * @param orderIds 订单ids
     * @return 无数据时返回空list
     */
    public List<OrderResp> queryByIds(List<Long> orderIds) {
        DubboResponse<List<OrderResp>> resp = orderQueryProvider.queryByIds(orderIds);
        if (!resp.isSuccess()) {
            log.error("根据订单id批量查询失败, orderIds: {}, resp: {}", orderIds, resp);
            throw new BizException("查询订单失败");
        }
        return resp.getData();
    }

    /**
     * 根据订单nos 查询
     *
     * @param orderNos 订单nos
     * @return
     */
    public List<OrderResp> queryByNos(List<String> orderNos) {
        DubboResponse<List<OrderResp>> resp = orderQueryProvider.queryByNos(orderNos);
        if (!resp.isSuccess()) {
            log.error("根据订单nos查询失败, orderNos: {}, resp: {}", orderNos, resp);
            throw new BizException("查询订单失败");
        }
        return resp.getData();
    }

    /**
     * 根据条件查询订单
     * 每次最多查询500条，通过maxId滚动查询，
     *
     * @param orderQueryReq 查询条件
     * @return 订单列表
     */
    public List<OrderResp> queryOrderList(OrderQueryReq orderQueryReq) {
        DubboResponse<List<OrderResp>> resp = orderQueryProvider.queryOrderList(orderQueryReq);
        if (!resp.isSuccess()) {
            log.error("根据条件查询订单失败, orderQueryReq: {}, resp: {}", orderQueryReq, resp);
            throw new BizException("查询订单失败");
        }
        return resp.getData();
    }

    public List<OrderResp> queryAllOrderListByCondition(OrderQueryReq orderQueryReq) {
        orderQueryReq.setMaxId(0L);
        List<OrderResp> resultList = Lists.newArrayList();
        boolean loopFlag = true;
        while (loopFlag) {
            List<OrderResp> orderResps = queryOrderList(orderQueryReq);
            if (CollectionUtils.isEmpty(orderResps)) {
                loopFlag = false;
                break;
            }
            resultList.addAll(orderResps);
            orderQueryReq.setMaxId(orderResps.get(orderResps.size() - 1).getId());
        }

        return resultList;
    }

    /**
     * 根据条件分页查询订单
     *
     * @param orderQueryReq
     * @return
     */
    public PageInfo<OrderResp> queryOrderPage(OrderQueryReq orderQueryReq) {
        DubboResponse<PageInfo<OrderResp>> resp = orderQueryProvider.queryOrderPage(orderQueryReq);
        if (!resp.isSuccess()) {
            log.error("根据条件分页查询订单失败, orderQueryReq: {}, resp: {}", orderQueryReq, resp);
            throw new BizException("查询订单失败");
        }
        return resp.getData();
    }


    /**
     * 查询待配送订单no
     *
     * @param needDeliveryReq
     * @return
     */
    public List<String> queryNeedDeliveryOrder(OrderNeedDeliveryReq needDeliveryReq) {
        DubboResponse<List<String>> resp = orderQueryProvider.queryNeedDeliveryOrder(needDeliveryReq);
        if (!resp.isSuccess()) {
            log.error("查询待配送订单no失败, needDeliveryReq: {}, resp: {}", needDeliveryReq, resp);
            throw new BizException("查询待配送订单no失败");
        }
        return resp.getData();
    }
}

package com.cosfo.manage.facade.ordercenter;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.order.convert.OrderConvert;
import com.cosfo.manage.order.model.dto.BillOrderDTO;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.provider.OrderStatisticsQueryProvider;
import com.cosfo.ordercenter.client.req.OrderDetailReq;
import com.cosfo.ordercenter.client.req.OrderSkuSaleReq;
import com.cosfo.ordercenter.client.req.OrderSummaryReq;
import com.cosfo.ordercenter.client.req.SupplierOrderTotalReq;
import com.cosfo.ordercenter.client.resp.SupplierOrderTotalResp;
import com.cosfo.ordercenter.client.resp.order.OrderDetailResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderSkuQuantityResp;
import com.cosfo.ordercenter.client.resp.order.OrderSummaryResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderStatisticsQueryFacade {

    @DubboReference
    private OrderStatisticsQueryProvider orderStatisticsQueryProvider;

    /**
     * 查询订单统计数据
     *
     * @param orderSummaryReq
     * @return
     */
    public OrderSummaryResp queryOrderSummary(OrderSummaryReq orderSummaryReq) {
        DubboResponse<OrderSummaryResp> response = orderStatisticsQueryProvider.queryOrderSummary(orderSummaryReq);
        if (!response.isSuccess()) {
            log.error("查询订单统计数据失败, orderSummaryReq:{}, response:{}", orderSummaryReq, response);
            throw new ProviderException("查询订单统计数据失败");
        }
        return response.getData();
    }


    /**
     * 查询待配送数量
     *
     * @param tenantId
     * @return
     */
    public Integer getWaitDeliveryQuantity(Long tenantId) {
        DubboResponse<Integer> response = orderStatisticsQueryProvider.getWaitDeliveryQuantity(tenantId);
        if (!response.isSuccess()) {
            log.error("查询待配送数量失败, tenantId:{}, response:{}", tenantId, response);
            throw new ProviderException("查询待配送数量失败");
        }
        return response.getData();
    }

    /**
     * 查询sku维度销量
     *
     * @param orderSkuSaleReq
     * @return
     */
    public List<OrderSkuQuantityResp> querySkuSaleQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        DubboResponse<List<OrderSkuQuantityResp>> response = orderStatisticsQueryProvider.querySkuSaleQuantity(orderSkuSaleReq);
        if (!response.isSuccess()) {
            log.error("查询sku维度销量失败, orderSkuSaleReq:{}, response:{}", orderSkuSaleReq, response);
            throw new ProviderException("查询sku维度销量失败");
        }
        return response.getData();
    }

    /**
     * 查询sku，仓库维度销量
     *
     * @param orderSkuSaleReq
     * @return
     */
    public List<OrderSkuQuantityResp> querySkuSaleWithStoreNoQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        DubboResponse<List<OrderSkuQuantityResp>> response = orderStatisticsQueryProvider.querySkuSaleWithStoreNoQuantity(orderSkuSaleReq);
        if (!response.isSuccess()) {
            log.error("查询sku，仓库维度销量失败, orderSkuSaleReq:{}, response:{}", orderSkuSaleReq, response);
            throw new ProviderException("查询sku，仓库维度销量失败");
        }
        return response.getData();
    }


    /**
     * 查询sku、城市维度销量
     * @param orderSkuSaleReq
     * @return
     */
    public List<OrderSkuQuantityResp> querySkuSaleWithCityQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        DubboResponse<List<OrderSkuQuantityResp>> response = orderStatisticsQueryProvider.querySkuSaleWithCityQuantity(orderSkuSaleReq);
        if (!response.isSuccess()) {
            log.error("查询sku、城市维度销量失败, orderSkuSaleReq:{}, response:{}", orderSkuSaleReq, response);
            throw new ProviderException("查询sku、城市维度销量失败");
        }
        return response.getData();
    }

    /**
     * 查询订单明细列表
     * 包含orderItem和快照信息
     * @param orderDetailReq
     * @return
     */
    public List<OrderDetailResp> queryOrderDetail(OrderDetailReq orderDetailReq) {
        DubboResponse<List<OrderDetailResp>> response = orderStatisticsQueryProvider.queryOrderDetail(orderDetailReq);
        if (!response.isSuccess()) {
            log.error("查询订单明细列表失败, orderDetailReq:{}, response:{}", orderDetailReq, response);
            throw new ProviderException("查询订单明细列表失败");
        }
        return response.getData();
    }

    public List<BillOrderDTO> queryOrderDetailForBill(Long tenantId, List<Long> orderIds) {
        OrderDetailReq req = new OrderDetailReq();
        req.setOrderIds(orderIds);
        req.setTenantId(tenantId);
        List<OrderDetailResp> orderDetailResps = queryOrderDetail(req);
        return OrderConvert.INSTANCE.convertResp2BillDTOs(orderDetailResps);
    }

    /**
     * 获取sku销售信息
     *
     * @param orderIds
     * @param tenantId
     * @return
     */
    public OrderItemSaleResp querySkuSaleQuantity(List<Long> orderIds, Long tenantId) {
        DubboResponse<OrderItemSaleResp> response = orderStatisticsQueryProvider.querySkuSaleQuantity(orderIds, tenantId);
        if (!response.isSuccess()) {
            log.error("获取sku销售信息失败, orderIds:{}, tenantId:{}, response:{}", orderIds, tenantId, response);
            throw new ProviderException("获取sku销售信息失败");
        }
        return response.getData();
    }

    /**
     * 查询供应商订单统计信息
     * @param supplierOrderTotalReq
     * @return
     */
    public List<SupplierOrderTotalResp> querySupplierOrderSummary(SupplierOrderTotalReq supplierOrderTotalReq) {
        DubboResponse<List<SupplierOrderTotalResp>> response = orderStatisticsQueryProvider.querySupplierOrderSummary(supplierOrderTotalReq);
        if (!response.isSuccess()) {
            log.error("查询供应商订单统计信息失败, supplierOrderTotalReq:{}, response:{}", supplierOrderTotalReq, response);
            throw new ProviderException("查询供应商订单统计信息失败");
        }
        return response.getData();
    }

    public List<SupplierOrderTotalResp> querySupplierOrderNeedNotifyByIds(Long tenantId, List<Long> supplierIds) {
        SupplierOrderTotalReq supplierOrderTotalReq = new SupplierOrderTotalReq();
        supplierOrderTotalReq.setSupplierIds(supplierIds);
        supplierOrderTotalReq.setTenantId(tenantId);
        supplierOrderTotalReq.setWarehouseType(WarehouseTypeEnum.PROPRIETARY.getCode());
        supplierOrderTotalReq.setStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode()));
        List<SupplierOrderTotalResp> supplierOrderTotalResps = querySupplierOrderSummary(supplierOrderTotalReq);
        if(CollectionUtil.isEmpty(supplierOrderTotalResps)){
            return Collections.emptyList();
        }
        return supplierOrderTotalResps;
    }


    public Integer getWaitAuditQuantity(Long tenantId) {
        DubboResponse<Integer> response = orderStatisticsQueryProvider.getWaitAuditQuantity(tenantId);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }
}

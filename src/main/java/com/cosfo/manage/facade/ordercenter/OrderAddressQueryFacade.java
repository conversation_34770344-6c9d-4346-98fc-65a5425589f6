package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.provider.OrderAddressQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAddressQueryFacade {

    @DubboReference
    private OrderAddressQueryProvider orderAddressQueryProvider;

    /**
     * 查询订单地址
     *
     * @param orderId
     * @param tenantId
     * @return
     */
    public OrderAddressResp queryByOrderId(Long tenantId, Long orderId) {
        DubboResponse<OrderAddressResp> response = orderAddressQueryProvider.queryByOrderId(tenantId, orderId);
        if (!response.isSuccess()) {
            log.error("查询订单地址失败, tenantId:{}, orderId:{}", tenantId, orderId);
            throw new ProviderException("查询订单地址失败");
        }
        return response.getData();
    }

    /**
     * 批量查询订单地址
     * @param tenantId
     * @param orderIds
     * @return
     */
    public List<OrderAddressResp> queryByOrderIds(Long tenantId, List<Long> orderIds) {
        DubboResponse<List<OrderAddressResp>> response = orderAddressQueryProvider.queryByOrderIds(tenantId, orderIds);
        if (!response.isSuccess()) {
            log.error("批量查询订单地址失败, tenantId:{}, orderIds:{}", tenantId, orderIds);
            throw new ProviderException("批量查询订单地址失败");
        }
        return response.getData();
    }

}

package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAfterSaleQueryFacade {

    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    /**
     * 查询当前订单所有售后单信息
     *
     * @param orderId
     * @param tenantId
     * @return
     */
    public List<OrderAfterSaleResp> queryByOrderId(Long orderId, Long tenantId) {
        DubboResponse<List<OrderAfterSaleResp>> response = orderAfterSaleQueryProvider.queryByOrderId(orderId, tenantId);
        if (!response.isSuccess()) {
            log.error("查询订单售后单信息失败, orderId: {}, tenantId: {}, response: {}", orderId, tenantId, response);
            throw new ProviderException("查询订单售后单信息失败");
        }
        return response.getData();
    }

    /**
     * 查询订单可售后数量
     *
     * @param req
     * @return
     */
    public Map<Long, OrderAfterSaleEnableResp> queryEnableApply(OrderAfterSaleEnableApplyReq req) {
        DubboResponse<Map<Long, OrderAfterSaleEnableResp>> response = orderAfterSaleQueryProvider.queryEnableApply(req);
        if (!response.isSuccess()) {
            log.error("查询订单可售后数量失败, req: {}, response: {}", req, response);
            throw new ProviderException("查询订单可售后数量失败");
        }
        return response.getData();
    }

    /**
     * 查询订单可申请售后信息
     *
     * @param tenantId
     * @param orderId
     * @param orderItemId 可为空，为空查所有订单项
     * @return
     */
    public Map<Long, OrderAfterSaleEnableResp> queryEnableApply(Long tenantId, Long orderId, Long orderItemId) {
        OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setTenantId(tenantId);
        req.setOrderId(orderId);
        req.setOrderItemId(orderItemId);
        Map<Long, OrderAfterSaleEnableResp> orderAfterSaleEnableDTOMap = queryEnableApply(req);

        if (orderAfterSaleEnableDTOMap == null) {
            throw new BizException("查询可售后信息不存在");
        }

        if (orderItemId != null) {
            OrderAfterSaleEnableResp orderAfterSaleEnableApplyDTO = Optional.of(orderAfterSaleEnableDTOMap).map(e -> e.get(orderItemId)).orElse(null);
            if (orderAfterSaleEnableApplyDTO == null) {
                throw new BizException("查询可售后信息不存在");
            }
        }

        return orderAfterSaleEnableDTOMap;
    }

    /**
     * 统计售后单数量
     *
     * @param req
     * @return
     */
    public Integer countOrderAfterSale(OrderAfterSaleCountReq req) {
        DubboResponse<Integer> response = orderAfterSaleQueryProvider.countOrderAfterSale(req);
        if (!response.isSuccess()) {
            log.error("统计售后单数量失败, req: {}, response: {}", req, response);
            throw new ProviderException("统计售后单数量失败");
        }
        return response.getData();
    }


    /**
     * 根据条件查询售后单
     *
     * @param req
     * @return
     */
    public List<OrderAfterSaleResp> queryList(OrderAfterSaleQueryReq req) {
        DubboResponse<List<OrderAfterSaleResp>> response = orderAfterSaleQueryProvider.queryList(req);
        if (!response.isSuccess()) {
            log.error("根据条件查询售后单失败, req: {}, response: {}", req, response);
            throw new ProviderException("售后单失败");
        }
        return response.getData();
    }

    /**
     * 分页查询售后单
     *
     * @param req
     * @return
     */
    public PageInfo<OrderAfterSaleWithOrderResp> queryPage(OrderAfterSalePageQueryReq req) {
        DubboResponse<PageInfo<OrderAfterSaleWithOrderResp>> response = orderAfterSaleQueryProvider.queryPage(req);
        if (!response.isSuccess()) {
            log.error("分页查询售后单失败, req: {}, response: {}", req, response);
            throw new ProviderException("查询售后单失败");
        }
        return response.getData();
    }

    /**
     * 根据售后单no查询
     *
     * @param orderAfterSaleNos
     * @return
     */
    public List<OrderAfterSaleResp> queryByNos(List<String> orderAfterSaleNos) {
        DubboResponse<List<OrderAfterSaleResp>> response = orderAfterSaleQueryProvider.queryByNos(orderAfterSaleNos);
        if (!response.isSuccess()) {
            log.error("根据售后单no查询失败, orderAfterSaleNos: {}, response: {}", orderAfterSaleNos, response);
            throw new ProviderException("售后单查询失败");
        }
        return response.getData();
    }

    public OrderAfterSaleResp getOrderAfterSaleByNo(String orderAfterSaleNo) {
        if (StringUtils.isEmpty(orderAfterSaleNo)) {
            return null;
        }
        List<OrderAfterSaleResp> orderAfterSaleResps = queryByNos(Collections.singletonList(orderAfterSaleNo));
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            return null;
        }
        return orderAfterSaleResps.get(0);
    }


    /**
     * 根据售后单id查询
     *
     * @param orderAfterSaleIds
     * @return
     */
    public List<OrderAfterSaleResp> queryByIds(List<Long> orderAfterSaleIds) {
        if (CollectionUtils.isEmpty(orderAfterSaleIds)) {
            return Collections.emptyList();
        }
        DubboResponse<List<OrderAfterSaleResp>> response = orderAfterSaleQueryProvider.queryByIds(orderAfterSaleIds);
        if (!response.isSuccess()) {
            log.error("根据售后单id查询失败, orderAfterSaleIds: {}, response: {}", orderAfterSaleIds, response);
            throw new ProviderException("售后单查询失败");
        }
        return response.getData();
    }

    public OrderAfterSaleResp getOrderAfterSaleById(Long orderAfterSaleId) {
        if (orderAfterSaleId == null) {
            return null;
        }
        List<OrderAfterSaleResp> orderAfterSaleResps = queryByIds(Collections.singletonList(orderAfterSaleId));
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            return null;
        }
        return orderAfterSaleResps.get(0);
    }

    /**
     * 计算退款金额
     *
     * @param req
     * @return
     */
    public BigDecimal calculateRefundPrice(OrderAfterSaleCalRefundPriceReq req) {
        DubboResponse<BigDecimal> response = orderAfterSaleQueryProvider.calculateRefundPrice(req);
        if (!response.isSuccess()) {
            log.error("计算退款金额失败, req: {}, response: {}", req, response);
            throw new ProviderException("计算退款金额失败");
        }
        return response.getData();
    }

    /**
     * 查询售后记录最近使用的退回地址id
     * @param tenantId
     * @return
     */
    public Long getRecentlyUsedReturnAddressId(Long tenantId) {
        DubboResponse<Long> response = orderAfterSaleQueryProvider.getRecentlyUsedReturnAddressId(tenantId);
        if (!response.isSuccess()) {
            log.error("查询售后记录最近使用的退回地址id失败, tenantId: {}, response: {}", tenantId, response);
            throw new ProviderException("查最近使用的退回地址失败");
        }
        return response.getData();
    }

    /**
     * 查询售后单信息，账单使用
     * @param req
     * @return
     */
    public List<OrderAfterSaleResp> queryOrderAfterSaleForBill(QueryBillOrderAfterSaleReq req) {
        DubboResponse<List<OrderAfterSaleResp>> response = orderAfterSaleQueryProvider.queryOrderAfterSaleForBill(req);
        if (!response.isSuccess()) {
            log.error("查询售后单信息失败, req: {}, response: {}", req, response);
            throw new ProviderException("查询售后单信息失败");
        }
        return response.getData();
    }
}

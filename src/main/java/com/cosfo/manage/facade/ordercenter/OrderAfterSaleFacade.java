package com.cosfo.manage.facade.ordercenter;

import com.cosfo.manage.common.exception.OpenApiProviderException;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleBatchReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 * @author: xiaowk
 * @date: 2023/8/11 下午4:27
 */
@Component
@Slf4j
public class OrderAfterSaleFacade {

    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;

    /**
     * 开放平台批量新增配送后售后单
     * @param orderAfterSaleDTOList
     * @return
     */
    public List<Long> batchCreateAfterSaleForOpen(List<OrderAfterSaleDTO> orderAfterSaleDTOList) {
        OrderAfterSaleBatchReq orderAfterSaleBatchReq = new OrderAfterSaleBatchReq();
        orderAfterSaleBatchReq.setApplyAfterSaleList(orderAfterSaleDTOList);
        DubboResponse<List<Long>> response = orderAfterSaleCommandProvider.batchCreateAfterSaleForOpen(orderAfterSaleBatchReq);
        if (!response.isSuccess()) {
            throw new OpenApiProviderException(response.getMsg(), response.getStatus(), response.getCode());
        }
        return response.getData();
    }
}

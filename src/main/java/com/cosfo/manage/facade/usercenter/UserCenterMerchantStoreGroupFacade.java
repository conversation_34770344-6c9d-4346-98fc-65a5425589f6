package com.cosfo.manage.facade.usercenter;

import com.cosfo.manage.common.util.StringUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreGroupCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreGroupQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupBatchImportReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupBatchImportResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterMerchantStoreGroupFacade {

    @DubboReference
    private MerchantStoreGroupQueryProvider merchantStoreGroupQueryProvider;
    @DubboReference
    private MerchantStoreGroupCommandProvider merchantStoreGroupCommandProvider;

    /**
     * 获取门店分组信息
     *
     * @param tenantId
     * @param storeIds
     * @return
     */
    public List<MerchantStoreGroupResultResp> getGroupByStoreIds(Long tenantId, List<Long> storeIds) {
        DubboResponse<List<MerchantStoreGroupResultResp>> response = merchantStoreGroupQueryProvider.getGroupByStoreIds(tenantId, storeIds);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取门店分组失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 根据分组id列表查询门店分组信息
     *
     * @param tenantId
     * @param groupIdList
     * @return
     */
    public List<MerchantStoreGroupResultResp> getGroupByStoreGroupIds(Long tenantId, List<Long> groupIdList) {
        DubboResponse<List<MerchantStoreGroupResultResp>> response = merchantStoreGroupQueryProvider.getGroupByStoreGroupIds(tenantId, groupIdList);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取门店分组失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 根据指定参数查询分组列表
     *
     * @param merchantStoreGroupQueryReq
     * @return
     */
    public List<MerchantStoreGroupPageResultResp> getMerchantStoreGroups(MerchantStoreGroupQueryReq merchantStoreGroupQueryReq) {
        DubboResponse<List<MerchantStoreGroupPageResultResp>> response = merchantStoreGroupQueryProvider.getMerchantStoreGroups(merchantStoreGroupQueryReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"根据指定参数查询分组列表失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 分页获取门店分组信息
     *
     * @param merchantStoreGroupQueryReq
     * @param pageQueryReq
     * @return
     */
    public PageInfo<MerchantStoreGroupPageResultResp> getMerchantStoreGroupPage(MerchantStoreGroupQueryReq merchantStoreGroupQueryReq, PageQueryReq pageQueryReq) {
        DubboResponse<PageInfo<MerchantStoreGroupPageResultResp>> response = merchantStoreGroupQueryProvider.getMerchantStoreGroupPage(merchantStoreGroupQueryReq, pageQueryReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"分页获取门店分组信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 新增
     * @param merchantStoreGroupCommandReq
     */
    public Long create(MerchantStoreGroupCommandReq merchantStoreGroupCommandReq){
        DubboResponse<Long> response = merchantStoreGroupCommandProvider.create(SystemOriginEnum.COSFO_MANAGE, merchantStoreGroupCommandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"新增门店分组失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 批量导入
     * @param reqList
     * @param tenantId
     */
    public List<MerchantStoreGroupBatchImportResp> batchCreate(List<MerchantStoreGroupBatchImportReq> reqList, Long tenantId) {
        DubboResponse<List<MerchantStoreGroupBatchImportResp>> response = merchantStoreGroupCommandProvider.batchCreate(SystemOriginEnum.COSFO_MANAGE, reqList, tenantId);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"批量导入失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 更新门店分组
     * 说明：以参数中的storeIdList为基准，在列表中的会被插入，不在列表中的门店会被踢出分组
     * @param merchantStoreGroupCommandReq
     */
    public Boolean updateMerchantGroupWithRemove(MerchantStoreGroupCommandReq merchantStoreGroupCommandReq){
        DubboResponse<Boolean> response = merchantStoreGroupCommandProvider.updateMerchantGroupWithRemove(SystemOriginEnum.COSFO_MANAGE, merchantStoreGroupCommandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"更新门店分组失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 删除门店分组
     * @param merchantStoreGroupCommandReq
     */
    public Boolean remove(MerchantStoreGroupCommandReq merchantStoreGroupCommandReq){
        DubboResponse<Boolean> response = merchantStoreGroupCommandProvider.remove(SystemOriginEnum.COSFO_MANAGE, merchantStoreGroupCommandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"删除门店分组失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 分页获取门店分组信息
     *
     * @param queryReq
     * @return
     */
    public List<MerchantStoreGroupPageResultResp> listGroupByParam(MerchantStoreGroupQueryReq queryReq) {
        DubboResponse<List<MerchantStoreGroupPageResultResp>> response = merchantStoreGroupQueryProvider.getGroupsWithStoreCount(queryReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response, "分页获取门店分组信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }
}

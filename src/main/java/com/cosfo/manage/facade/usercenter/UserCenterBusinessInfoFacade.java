package com.cosfo.manage.facade.usercenter;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.context.usercenter.BusinessInformationTypeEnum;
import com.cosfo.manage.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.businessInfo.provider.BusinessInformationCommandProvider;
import net.xianmu.usercenter.client.businessInfo.provider.BusinessInformationQueryProvider;
import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationQueryReq;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterBusinessInfoFacade {

    @DubboReference
    private BusinessInformationQueryProvider queryProvider;
    @DubboReference
    private BusinessInformationCommandProvider commandProvider;

    /**
     * 获取租户工商信息
     *
     * @param tenantId
     * @return
     */
    public BusinessInformationResultResp getBusinessInfo(Long tenantId) {
        BusinessInformationQueryReq queryReq = new BusinessInformationQueryReq();
        queryReq.setBizId(tenantId);
        queryReq.setType(BusinessInformationTypeEnum.BRAND_USER_TYPE.getCode());
        DubboResponse<BusinessInformationResultResp> response = queryProvider.getBusinessInfoByBizIdAndType(queryReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取租户工商信息失败");
            log.warn("获取租户信息失败, msg = {},req={}", JSON.toJSONString(response), JSON.toJSONString(response));
            throw new BizException(errorMsg);
        }

        return response.getData();
    }

    /**
     * 根据指定参数查询工商信息列表
     *
     * @param queryReq
     * @return
     */
    public List<BusinessInformationResultResp> getBusinessInfos(BusinessInformationQueryReq queryReq) {
        DubboResponse<List<BusinessInformationResultResp>> response = queryProvider.getBusinessInfos(queryReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response, "根据指定参数查询工商信息列表失败");
            log.warn("根据指定参数查询工商信息列表, msg = {},req={}", JSON.toJSONString(response), JSON.toJSONString(queryReq));
            throw new BizException(errorMsg);
        }

        return response.getData();
    }

}

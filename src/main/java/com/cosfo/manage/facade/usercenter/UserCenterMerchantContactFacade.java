package com.cosfo.manage.facade.usercenter;

import com.cosfo.manage.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantContactCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantContactQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-31
 * @Description:
 */
@Service
@Slf4j
public class UserCenterMerchantContactFacade {

    @DubboReference
    private MerchantContactCommandProvider merchantContactCommandProvider;

    @DubboReference
    private MerchantContactQueryProvider merchantContactQueryProvider;

    /**
     * 查询门店下所有的联系人
     *
     * @param
     * @return
     */
    public List<MerchantContactResultResp> getMerchantContactsByStoreId(Long tenantId, Long storeId) {
        DubboResponse<List<MerchantContactResultResp>> response = merchantContactQueryProvider.getMerchantContactsByStoreId(tenantId, storeId);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"查询门店下所有的联系人列表失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 根据指定参数查询联系人列表
     *
     * @param
     * @return
     */
    public List<MerchantContactResultResp> getMerchantContacts(MerchantContactQueryReq req) {
        DubboResponse<List<MerchantContactResultResp>> response = merchantContactQueryProvider.getMerchantContacts(req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"查询门店下所有的联系人列表失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 移除
     */
    public Boolean remove(MerchantContactCommandReq req) {
        DubboResponse<Boolean> response = merchantContactCommandProvider.remove(SystemOriginEnum.COSFO_MANAGE, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"移除联系人信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 根据id批量移除
     */
    public Boolean removeBatch(MerchantContactCommandReq req) {
        DubboResponse<Boolean> response = merchantContactCommandProvider.removeBatch(SystemOriginEnum.COSFO_MANAGE, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response, "移除联系人信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

}

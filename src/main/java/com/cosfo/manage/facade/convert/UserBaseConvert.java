package com.cosfo.manage.facade.convert;

import com.cosfo.manage.tenant.model.dto.TenantAccountDTO;
import net.xianmu.common.user.UserBase;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
public class UserBaseConvert {

    /**
     * 转化为UserBase
     *
     * @param tenantAccountDTO
     * @return
     */
    public static UserBase convertToUserBase(TenantAccountDTO tenantAccountDTO){
        if (tenantAccountDTO == null) {
            return null;
        }

        UserBase userBase = new UserBase();
        userBase.setUsername(tenantAccountDTO.getPhone());
        userBase.setId(tenantAccountDTO.getAuthBaseId());
        userBase.setLoginPassword(tenantAccountDTO.getLoginPassword());
        userBase.setNickname(tenantAccountDTO.getNickname());
        userBase.setPhone(tenantAccountDTO.getPhone());
        userBase.setStatus(tenantAccountDTO.getStatus());
        userBase.setTenantId(tenantAccountDTO.getTenantId());
        userBase.setPassword(tenantAccountDTO.getLoginPassword());
        userBase.setRoleIds(tenantAccountDTO.getRoleIds());
        return userBase;
    }
}

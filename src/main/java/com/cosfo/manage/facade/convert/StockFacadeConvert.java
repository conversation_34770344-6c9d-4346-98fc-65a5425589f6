package com.cosfo.manage.facade.convert;

import com.cosfo.mall.client.stock.resp.StockInfoResp;
import com.cosfo.manage.facade.dto.StockInfoDTO;

/**
 * @author: monna.chen
 * @Date: 2024/2/29 13:59
 * @Description:
 */
public class StockFacadeConvert {

    public static StockInfoDTO convert2Dto(StockInfoResp stockInfoResp) {
        if (stockInfoResp == null) {
            return null;
        }
        StockInfoDTO stockInfoDTO = new StockInfoDTO();
        stockInfoDTO.setItemId(stockInfoResp.getItemId());
        stockInfoDTO.setAmount(stockInfoResp.getAmount());
        stockInfoDTO.setQuantityDate(stockInfoResp.getQuantityDate());
        stockInfoDTO.setSupplySkuId(stockInfoResp.getSupplySkuId());
        stockInfoDTO.setSupplyTenantId(stockInfoResp.getSupplyTenantId());
        return stockInfoDTO;
    }
}

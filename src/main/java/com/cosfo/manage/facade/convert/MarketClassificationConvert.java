package com.cosfo.manage.facade.convert;

import com.cofso.item.client.req.MarketClassificationAddReq;
import com.cofso.item.client.req.MarketClassificationUpdateReq;
import com.cofso.item.client.resp.MarketClassificationTreeResp;
import com.cosfo.manage.market.model.dto.MarketClassificationDTO;
import com.cosfo.manage.market.model.po.MarketClassification;
import com.cosfo.manage.market.model.vo.MarketClassificationTreeVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/9
 */
public class MarketClassificationConvert {

    /**
     * 转化为MarketClassificationAddReq
     *
     * @return
     */
    public static MarketClassificationAddReq convertToMarketClassificationAddReq(MarketClassificationDTO classificationDTO) {
        if (classificationDTO == null) {
            return null;
        }

        MarketClassificationAddReq marketClassificationAddReq = new MarketClassificationAddReq();
        marketClassificationAddReq.setTenantId(classificationDTO.getTenantId());
        marketClassificationAddReq.setName(classificationDTO.getName());
        marketClassificationAddReq.setParentId(classificationDTO.getParentId());
        marketClassificationAddReq.setIcon(classificationDTO.getIcon());
        marketClassificationAddReq.setSort(classificationDTO.getSort());
        marketClassificationAddReq.setFirstClassificationName(classificationDTO.getFirstClassificationName());
        marketClassificationAddReq.setSecondClassificationName(classificationDTO.getSecondClassificationName());
        marketClassificationAddReq.setClassificationStr(classificationDTO.getClassificationStr());
        return marketClassificationAddReq;
    }

    /**
     * 转为MarketClassificationUpdateReq
     *
     * @param classificationDTO
     * @return
     */
    public static MarketClassificationUpdateReq convertToMarketClassificationUpdateReq(MarketClassificationDTO classificationDTO) {
        if (classificationDTO == null) {
            return null;
        }

        MarketClassificationUpdateReq marketClassificationUpdateReq = new MarketClassificationUpdateReq();
        marketClassificationUpdateReq.setId(classificationDTO.getId());
        marketClassificationUpdateReq.setTenantId(classificationDTO.getTenantId());
        marketClassificationUpdateReq.setName(classificationDTO.getName());
        marketClassificationUpdateReq.setParentId(classificationDTO.getParentId());
        marketClassificationUpdateReq.setIcon(classificationDTO.getIcon());
        marketClassificationUpdateReq.setSort(classificationDTO.getSort());
        marketClassificationUpdateReq.setFirstClassificationName(classificationDTO.getFirstClassificationName());
        marketClassificationUpdateReq.setSecondClassificationName(classificationDTO.getSecondClassificationName());
        marketClassificationUpdateReq.setClassificationStr(classificationDTO.getClassificationStr());
        return marketClassificationUpdateReq;
    }

    /**
     * 转化成MarketClassificationTreeVO
     *
     * @param marketClassificationTreeResps
     * @return
     */
    public static List<MarketClassificationTreeVO> convertToMarketClassificationTreeVOList(List<MarketClassificationTreeResp> marketClassificationTreeResps) {
        if (marketClassificationTreeResps == null) {
            return Collections.emptyList();
        }
        List<MarketClassificationTreeVO> marketClassificationTreeVOList = new ArrayList<>();
        for (MarketClassificationTreeResp marketClassificationTreeResp : marketClassificationTreeResps) {
            marketClassificationTreeVOList.add(toMarketClassificationTreeVO(marketClassificationTreeResp));
        }
        return marketClassificationTreeVOList;
    }

    public static MarketClassificationTreeVO toMarketClassificationTreeVO(MarketClassificationTreeResp marketClassificationTreeResp) {
        if (marketClassificationTreeResp == null) {
            return null;
        }
        MarketClassificationTreeVO marketClassificationTreeVO = new MarketClassificationTreeVO();
        marketClassificationTreeVO.setId(marketClassificationTreeResp.getId());
        marketClassificationTreeVO.setTenantId(marketClassificationTreeResp.getTenantId());
        marketClassificationTreeVO.setName(marketClassificationTreeResp.getName());
        marketClassificationTreeVO.setParentId(marketClassificationTreeResp.getParentId());
        marketClassificationTreeVO.setIcon(marketClassificationTreeResp.getIcon());
        marketClassificationTreeVO.setSort(marketClassificationTreeResp.getSort());
        List<MarketClassificationTreeResp> childList = marketClassificationTreeResp.getChildList();
        if (!CollectionUtils.isEmpty(childList)) {
            List<MarketClassificationTreeVO> marketClassificationTreeVOS = convertToMarketClassificationTreeVOList(childList);
            marketClassificationTreeVO.setChildList(marketClassificationTreeVOS);
        }

        return marketClassificationTreeVO;
    }
}

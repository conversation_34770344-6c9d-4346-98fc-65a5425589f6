package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.req.QueryFulfillmentDeliveryReq;
import net.summerfarm.ofc.client.req.QueryFulfillmentWaitDeliveryReq;
import net.summerfarm.ofc.client.resp.FulfillmentDeliveryResp;
import net.summerfarm.ofc.client.resp.FulfillmentWaitDeliveryResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-11
 * @Description:
 */
@Slf4j
@Component
public class FulfillmentOrderQueryFacade {

    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;

    /**
     * 查询订单配送信息
     * @param queryFulfillmentDeliveryReq
     * @return
     */
    public List<FulfillmentDeliveryResp> queryOrderDelivery(QueryFulfillmentDeliveryReq queryFulfillmentDeliveryReq) {
        log.info("fulfillmentOrderQueryProvider.queryOrderDelivery queryFulfillmentDeliveryReq={}", JSON.toJSONString(queryFulfillmentDeliveryReq));
        DubboResponse<List<FulfillmentDeliveryResp>> response = fulfillmentOrderQueryProvider.queryOrderDelivery(queryFulfillmentDeliveryReq);
        log.info("fulfillmentOrderQueryProvider.queryOrderDelivery response={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 查询等待配送清单
     * @param queryFulfillmentWaitDeliveryReq
     * @return
     */
    public List<FulfillmentWaitDeliveryResp> queryWaitDelivery(QueryFulfillmentWaitDeliveryReq queryFulfillmentWaitDeliveryReq) {
        log.info("fulfillmentOrderQueryProvider.queryWaitDelivery queryFulfillmentDeliveryReq={}", JSON.toJSONString(queryFulfillmentWaitDeliveryReq));
        DubboResponse<List<FulfillmentWaitDeliveryResp>> response = fulfillmentOrderQueryProvider.queryWaitDelivery(queryFulfillmentWaitDeliveryReq);
        log.info("fulfillmentOrderQueryProvider.queryWaitDelivery response={}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }
}

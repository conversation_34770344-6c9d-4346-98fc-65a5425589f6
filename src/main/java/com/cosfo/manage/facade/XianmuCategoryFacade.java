package com.cosfo.manage.facade;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/5
 */
@Slf4j
@Component
public class XianmuCategoryFacade {

//    @DubboReference
//    private CategoryProvider categoryProvider;
//
//    public PageInfo<CategoryResDTO> pageQueryCategory(CategoryReqDTO categoryReqDTO){
//        DubboResponse<PageInfo<CategoryResDTO>> response = categoryProvider.pageQueryCategory(categoryReqDTO);
//        if (!response.isSuccess()) {
//            throw new ProviderException(response.getMsg());
//        }
//
//        return response.getData();
//    }
}

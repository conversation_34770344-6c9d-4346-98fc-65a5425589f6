package com.cosfo.manage.facade;

import com.cofso.page.PageResp;
import com.cofso.preferential.client.enums.ProductSkuPreferentialStatusEnum;
import com.cofso.preferential.client.provider.ProductSkuPreferentialCostPriceProvider;
import com.cofso.preferential.client.req.ProductSkuPreferentialQueryReq;
import com.cofso.preferential.client.resp.ProductSkuCityPreferentialCostPriceResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialBasicResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialPageResp;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import com.cosfo.manage.product.model.dto.PreferentialCostPriceQueryDTO;
import com.cosfo.manage.tenant.model.vo.TenantNumberCommonConfigVO;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductSkuPreferentialCostPriceFacade {

    @DubboReference
    private ProductSkuPreferentialCostPriceProvider priceProvider;

    @Resource
    private TenantCommonConfigService tenantCommonConfigService;

    /**
     * 查询sku+城市 生效中的省心定
     *
     * @returnF
     */
    public List<ProductSkuCityPreferentialCostPriceResp> queryCityPreferentialCostPrice(Long tenantId, Long skuId, List<Long> cityIds) {
        TenantNumberCommonConfigVO configVO = tenantCommonConfigService.queryTenantConfig (tenantId, TenantConfigEnum.TenantConfig.SAVE_WORRY.getConfigKey ());
        Integer configValue = configVO.getConfigValue ();
        if(0!=configValue) {
            DubboResponse<List<ProductSkuCityPreferentialCostPriceResp>> dubboResponse = priceProvider.queryCityPreferentialCostPrice (tenantId, skuId, cityIds);
            FacadeExceptionUtil.executeFaceException (dubboResponse, null);
            return dubboResponse.getData ();
        }else{
            return Collections.emptyList ();
        }
    }

    /**
     * 查询sku 生效中的价格区间
     *
     * @returnF
     */
    public Map<Long,ProductSkuPreferentialCostPriceRangeResp> queryPreferentialCostPriceRange(Long tenantId, Map<Long,List<Long>> skuIdCityMap) {
        TenantNumberCommonConfigVO configVO = tenantCommonConfigService.queryTenantConfig (tenantId, TenantConfigEnum.TenantConfig.SAVE_WORRY.getConfigKey ());
        Integer configValue = configVO.getConfigValue ();
        if(0!=configValue) {
            DubboResponse<List<ProductSkuPreferentialCostPriceRangeResp>> dubboResponse = priceProvider.queryPreferentialCostPriceRange (tenantId, skuIdCityMap);
            FacadeExceptionUtil.executeFaceException (dubboResponse, null);
            return dubboResponse.getData ().stream().collect(Collectors.toMap(ProductSkuPreferentialCostPriceRangeResp::getSkuId, Function.identity()));
        }else{
            return Collections.emptyMap ();
        }
    }

    /**
     * 分页查询省心订商品列表
     *
     * @param productSkuPreferentialQueryReq
     * @return
     */
    public PageResp<ProductSkuPreferentialPageResp> pagePreferentialCostPrice(ProductSkuPreferentialQueryReq productSkuPreferentialQueryReq) {
        DubboResponse<PageResp<ProductSkuPreferentialPageResp>> response = priceProvider.pagePreferentialCostPrice(productSkuPreferentialQueryReq);
        if (!response.isSuccess()) {
            throw new ProviderException("分页查询省心订商品列表失败，失败原因: " + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 查询省心订汇总基础数据信息
     * @param tenantId
     * @param skuIds
     * @return
     */
    public ProductSkuPreferentialBasicResp queryBasicData(Long tenantId, List<Long> skuIds,Integer status) {
        ProductSkuPreferentialQueryReq queryReq = new ProductSkuPreferentialQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setSkuIds(skuIds);
        queryReq.setStatusEnum(ProductSkuPreferentialStatusEnum.getByStatus(status));
        DubboResponse<ProductSkuPreferentialBasicResp> response = priceProvider.queryBasicData(queryReq);
        if (!response.isSuccess()) {
            throw new ProviderException("查询省心订汇总基础数据信息失败，失败原因: " + response.getMsg());
        }
        return response.getData();
    }

    public PageResp<ProductSkuPreferentialPageResp> pagePreferentialCostPriceByParam(PreferentialCostPriceQueryDTO queryDTO, List<Long> skuIds) {
        ProductSkuPreferentialQueryReq queryReq = new ProductSkuPreferentialQueryReq();
        queryReq.setTenantId(queryDTO.getTenantId());
        queryReq.setPageIndex(queryDTO.getPageIndex());
        queryReq.setPageSize(queryDTO.getPageSize());
        queryReq.setSkuIds(skuIds);
        queryReq.setStatusEnum(ProductSkuPreferentialStatusEnum.getByStatus(queryDTO.getStatus()));
        return pagePreferentialCostPrice(queryReq);
    }
}

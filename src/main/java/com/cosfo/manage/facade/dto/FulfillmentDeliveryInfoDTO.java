package com.cosfo.manage.facade.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 履约单物流明细返回对象信息
 * @author: xiaowk
 * @date: 2023/4/13 下午10:48
 */
@Data
public class FulfillmentDeliveryInfoDTO implements Serializable {
  private static final long serialVersionUID = -6363000995245247682L;

  /**
   * 订单号
   */
  private String orderNo;

  /**
   * 物流单号
   */
  private String logisticsNo;

  /**
   * 物流公司
   */
  private String logisticsCompany;

  /**
   * 商品id
   */
  private String itemId;

  /**
   * 鲜沐sku编码
   */
  private String skuCode;


  /**
   * 配送数量
   */
  private Integer quantity;

  /**
   * 配送方式，0-无需物流，1-商家物流配送，2-仓库物流配送
   */
  private Integer deliveryType;


  /**
   * 备注
   */
  private String remark;


  /**
   * 图片
   */
  private String pics;

  /**
   * 录入时间
   */
  private Date createTime;

  /**
   * 批次号
   */
  private String batchNo;

  /**
   * 出库单号
   */
  private String outBoundNo;

  /**
   * 库存号
   */
  private Integer warehouseNo;

  /**
   * 库存仓名称
   */
  private String warehouseName;

  /**
   * 库存仓服务商名称
   */
  private String warehouseServiceName;
}

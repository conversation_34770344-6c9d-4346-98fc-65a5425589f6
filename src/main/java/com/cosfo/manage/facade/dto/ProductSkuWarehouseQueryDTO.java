package com.cosfo.manage.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fansongsong
 * @Date: 2023-08-09
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ProductSkuWarehouseQueryDTO {

    /**
     * 库存同步状态，true：同步，false：不同步，null：查询全部
     */
    private Boolean sync;
    /**
     * 是否有出入库流水，true：有，false：没有，null：查询全部
     */
    private Boolean inOutRecord;
}

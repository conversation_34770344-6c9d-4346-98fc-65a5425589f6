package com.cosfo.manage.facade;

import com.cosfo.common.util.RpcResponseUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import net.xianmu.scp.client.provider.replenishment.ReplenishmentStockConfigQueryProvider;
import net.xianmu.scp.client.req.replenishment.ReplenishmentStockConfigBaseQueryInput;
import net.xianmu.scp.client.req.replenishment.ReplenishmentStockConfigQueryInput;
import net.xianmu.scp.client.resp.replenishment.ReplenishmentStockConfigDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * scp接口
 *
 * @author: xiaowk
 * @date: 2023/11/23 下午5:21
 */
@Component
public class ScpFacade {

    @DubboReference
    private ReplenishmentStockConfigQueryProvider replenishmentStockConfigQueryProvider;

    /**
     * 批量查询安全库存
     * @param tenantId
     * @param replenishmentStockConfigBaseQueryInputs
     * @return
     */
    public Table<String, Integer, ReplenishmentStockConfigDTO> queryReplenishmentStockConfig(Long tenantId, List<ReplenishmentStockConfigBaseQueryInput> replenishmentStockConfigBaseQueryInputs) {
        Table<String, Integer, ReplenishmentStockConfigDTO> table = HashBasedTable.create();

        if(tenantId == null || CollectionUtils.isEmpty(replenishmentStockConfigBaseQueryInputs)){
            return table;
        }

        ReplenishmentStockConfigQueryInput queryInput = new ReplenishmentStockConfigQueryInput();
        queryInput.setReplenishmentStockConfigBaseQueryInputs(replenishmentStockConfigBaseQueryInputs);
        queryInput.setTenantId(tenantId);
        queryInput.setPageIndex(1);
        queryInput.setPageSize(replenishmentStockConfigBaseQueryInputs.size());
        PageInfo<ReplenishmentStockConfigDTO> respPageInfo = RpcResponseUtil.handler(replenishmentStockConfigQueryProvider.queryReplenishmentStockConfig(queryInput));
        if (respPageInfo == null || CollectionUtils.isEmpty(respPageInfo.getList())) {
            return table;
        }

        respPageInfo.getList().stream().forEach(e ->
                table.put(e.getSku(), e.getWarehouseNo(), e));

        return table;
    }
}

package com.cosfo.manage.facade.planorder;

import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.client.planorder.provider.PlanOrderCreateOrderProvider;
import com.cosfo.mall.client.planorder.req.PlanOrderCreateOrderReq;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @author: monna.chen
 * @Date: 2024/2/27 10:18
 * @Description:
 */
@Service
@Slf4j
public class PlanOrderFacade {
    @DubboReference
    private PlanOrderCreateOrderProvider planOrderCreateOrderProvider;

    /**
     * 计划单创建订单
     *
     * @param req
     */
    public void createOrder(PlanOrderCreateOrderReq req) {
        DubboResponse<Boolean> response = planOrderCreateOrderProvider.createOrder(req);
        RpcResponseUtil.handler(response);
        if (Boolean.FALSE.equals(response.getData())) {
            log.error("计划单创建订单失败！计划单号：{}", req.getPlanOrderNo());
        }
    }


}

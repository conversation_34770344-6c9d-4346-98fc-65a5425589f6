package com.cosfo.manage.facade.ofc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.provider.DeliveryInfoQueryProvider;
import net.summerfarm.ofc.client.req.DeliveryDateQueryReq;
import net.summerfarm.ofc.client.req.fulfillment.OrderInfoQueryReq;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.summerfarm.ofc.client.resp.fulfillment.OrderDeliveryInfoStandardResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @author: xiaowk
 * @date: 2024/7/22 下午2:57
 */
@Component
@Slf4j
public class OfcDeliveryQueryFacade {

    @DubboReference
    private DeliveryInfoQueryProvider deliveryInfoQueryProvider;

    /**
     * 查询标准化的配送信息
     *
     * @param orderNo
     * @return
     */
    public OrderDeliveryInfoStandardResp queryOrderDeliveryInfo(String orderNo) {
        // 查询订单配送相关信息
        OrderInfoQueryReq req = new OrderInfoQueryReq();
        req.setOrderNo(orderNo);
        DubboResponse<OrderDeliveryInfoStandardResp> dubboResponse = deliveryInfoQueryProvider.queryOrderDeliveryInfo(req);

        if (dubboResponse == null || !dubboResponse.isSuccess()) {
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 查询门店的配送周期
     *
     * @param merchantAddress 门店地址信息
     * @param payTime 支付时间
     * @return 门店的配送周期
     */
    public DeliveryDateQueryResp queryMerchantDeliveryDate(MerchantAddressResultResp merchantAddress, LocalDateTime payTime) {
        DeliveryDateQueryReq req = new DeliveryDateQueryReq();
        try {
            req.setCity(merchantAddress.getCity());
            req.setArea(merchantAddress.getArea());
            req.setPayTime(payTime);
            req.setSource(OfcOrderSourceEnum.SAAS_MALL);

            req.setTenantId(merchantAddress.getTenantId());
            req.setStoreId(merchantAddress.getStoreId());

            DubboResponse<DeliveryDateQueryResp> dubboResponse = deliveryInfoQueryProvider.queryDeliveryDate(req);
            if (!dubboResponse.isSuccess()) {
                throw new ProviderException(dubboResponse.getMsg());
            }

            if (dubboResponse.getData() == null) {
                throw new ProviderException("配送时间为空异常");
            }

            return dubboResponse.getData();
        } catch (ProviderException e) {
            log.error("三方仓订单配送时间查询业务异常，参数：{}，异常信息：", req, e);
            throw new BizException("三方配送时间查询异常");
        } catch (Throwable e) {
            log.error("三方仓订单配送时间查询异常，参数：{}，异常信息：", req, e);
            throw e;
        }
    }

}

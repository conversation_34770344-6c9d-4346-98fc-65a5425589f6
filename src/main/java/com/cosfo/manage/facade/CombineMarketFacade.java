package com.cosfo.manage.facade;

import com.cofso.item.client.provider.CombineMarketProvider;
import com.cofso.item.client.resp.CombineMarketDetailResp;
import com.cofso.item.client.resp.CombineMarketListResp;
import com.cofso.page.PageResp;
import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import com.cosfo.manage.market.converter.MarketFacadeConvert;
import com.cosfo.manage.market.model.dto.CombineInputDTO;
import com.cosfo.manage.market.model.dto.CombineMarketDetailDTO;
import com.cosfo.manage.market.model.dto.CombineMarketListDTO;
import com.cosfo.manage.market.model.dto.CombineMarketQueryDTO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @author: monna.chen
 * @Date: 2023/5/12 15:38
 * @Description:
 */
@Service
@Slf4j
public class CombineMarketFacade {
    @DubboReference
    private CombineMarketProvider combineMarketProvider;

    public PageInfo<CombineMarketListDTO> combineList(CombineMarketQueryDTO queryDTO) {
        DubboResponse<PageResp<CombineMarketListResp>> dubboResponse = combineMarketProvider.combineList(MarketFacadeConvert.INSTANCE.convert2CombineQueryReq(queryDTO));
        if (!dubboResponse.isSuccess()) {
            throw new ProviderException(dubboResponse.getMsg());
        }
        return MarketFacadeConvert.INSTANCE.convert2CombinePage(dubboResponse.getData());
    }

    public CombineMarketDetailDTO combineDetail(CombineMarketQueryDTO queryDTO) {
        DubboResponse<CombineMarketDetailResp> dubboResponse = combineMarketProvider.combineDetail(MarketFacadeConvert.INSTANCE.convert2CombineQueryReq(queryDTO));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2CombineDetail(dubboResponse.getData());
    }

    public Long combineAdd(CombineInputDTO inputDTO) {
        DubboResponse<Long> dubboResponse = combineMarketProvider.combineAdd(MarketFacadeConvert.INSTANCE.convert2CombineReq(inputDTO));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return dubboResponse.getData();
    }

    public Boolean combineUpdate(CombineInputDTO inputDTO) {
        DubboResponse<Boolean> dubboResponse = combineMarketProvider.combineUpdate(MarketFacadeConvert.INSTANCE.convert2CombineReq(inputDTO));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return dubboResponse.getData();
    }
}

package com.cosfo.manage.facade;

import cn.hutool.core.lang.Pair;
import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import net.summerfarm.wms.storealert.StoreAlertQueryProvider;
import net.summerfarm.wms.storealert.dto.req.StoreAlertCountByStatusReq;
import net.summerfarm.wms.storealert.dto.res.StoreAlertCountByStatusResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: George
 * @date: 2024-04-01
 **/
@Component
public class WmsStoreAlertQueryFacade {

    @DubboReference
    private StoreAlertQueryProvider storeAlertQueryProvider;

    public List<StoreAlertCountByStatusResp> countStoreAlertByStatus(StoreAlertCountByStatusReq req) {
        DubboResponse<List<StoreAlertCountByStatusResp>> dubboResponse = storeAlertQueryProvider.countStoreAlertByStatus(req);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        List<StoreAlertCountByStatusResp> data = dubboResponse.getData();

        // 构建哈希表存储已有的类型和状态
        Set<Pair<Integer, Integer>> existingTypesAndStatuses = new HashSet<>();
        for (StoreAlertCountByStatusResp resp : data) {
            existingTypesAndStatuses.add(new Pair<>(resp.getType(), resp.getStatus()));
        }

        // 添加缺失的类型和状态的响应对象，如果已存在则不添加
        List<StoreAlertCountByStatusResp> result = new ArrayList<>(data);
        for (StoreAlertCountByStatusReq.StoreAlertCountByStatus param : req.getCountByStatusList()) {
            Integer type = param.getType();
            for (Integer status : param.getStatusList()) {
                Pair<Integer, Integer> pair = new Pair<>(type, status);
                if (!existingTypesAndStatuses.contains(pair)) {
                    StoreAlertCountByStatusResp newResp = new StoreAlertCountByStatusResp();
                    newResp.setType(type);
                    newResp.setStatus(status);
                    newResp.setCount(0);
                    result.add(newResp);
                    existingTypesAndStatuses.add(pair);
                }
            }
        }

        return result;
    }

}

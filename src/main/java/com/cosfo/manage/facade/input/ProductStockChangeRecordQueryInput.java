package com.cosfo.manage.facade.input;

import lombok.Data;
import net.summerfarm.manage.client.saas.enums.QuantityChangeRecordEnum;
import net.summerfarm.manage.client.saas.enums.StockTypeEnum;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/27 11:13
 */
@Data
public class ProductStockChangeRecordQueryInput {

    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 鲜沐skuId
     */
    private Long skuId;
    /**
     * 库存仓
     */
    private Integer warehouseNo;
    /**
     * 商品名称
     */
    private String pdName;
    /**
     * 库存类型 {@link StockTypeEnum}
     */
    private List<Integer> stockTypeList;
    /**
     * 变动类型 {@link QuantityChangeRecordEnum}
     */
    private List<String> changeTypeNameList;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 有权限的skuId列表
     */
    private List<Long> permissionSkuIdList;
    /**
     * 租户id
     */
    private Long tenantId;
}

package com.cosfo.manage.facade.input;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class OrderAfterSaleQueryInput implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单no
     */
    private List<String> orderNos;
    /**
     * 订单id
     */
    private List<Long> orderIds;
    /**
     * 订单id
     */
    /**
     * 退款单id
     */
    private List<Long> ids;
    /**
     * 订单项id
     */
    private List<Long> orderItemIds;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 门店Id
     */
    private Long storeId;
    /**
     * 售后订单编号
     */
    private List<String> afterSaleOrderNos;
    /**
     * 售后类型 0 已到货 1 未到货
     */
    private Integer afterSaleType;
    /**
     * 售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发
     */
    private List<Integer> serviceType;
    /**
     * 状态 1,待审核 2，已成功 3，已失败 4 ，已取消
     系统内部状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款9 三方处理中
     */
    private List<Integer> status;
    /**
     * 责任类型0供应商1品牌方2门店
     */
    private Integer responsibilityType;

    /**
     * 配送仓类型 0,自营仓1优选仓
     */
    private Integer warehouseType;
    /**
     * 审核时间 - 开始（包含当前时间）
     */
    private LocalDateTime handleTimeBegin;
    /**
     *  审核时间 - 结束（包含当前时间）
     */
    private LocalDateTime handleTimeEnd;
    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 订单明细id
     */
    private Long orderItemId;

    /**
     * 数量
     */
    private Integer amount;
}

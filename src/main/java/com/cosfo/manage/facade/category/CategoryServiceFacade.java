package com.cosfo.manage.facade.category;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.erp.client.category.provider.CategoryCommandProvider;
import com.cosfo.erp.client.category.provider.CategoryQueryProvider;
import com.cosfo.erp.client.category.req.CategoryQueryReq;
import com.cosfo.erp.client.category.resp.CategoryDetailResultResp;
import com.cosfo.erp.client.category.resp.CategoryLevelResultResp;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.product.convert.CategoryConverter;
import com.cosfo.manage.product.convert.CategoryFacadeConvert;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.dto.ProductCategoryTreeDTO;
import com.cosfo.manage.product.model.vo.CategoryVO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.CategoryProvider;
import net.summerfarm.goods.client.provider.TaxRateProvider;
import net.summerfarm.goods.client.req.TaxRateQueryReq;
import net.summerfarm.goods.client.resp.CategoryAllPathResp;
import net.summerfarm.goods.client.resp.CategoryDetailResp;
import net.summerfarm.goods.client.resp.CategoryResp;
import net.summerfarm.goods.client.resp.TaxRateResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/1/31 11:05
 */
@Slf4j
@Component
public class CategoryServiceFacade {
    @DubboReference
    private CategoryProvider categoryProvider;
    @DubboReference
    private TaxRateProvider taxRateProvider;
    @DubboReference
    private CategoryQueryProvider cateGoryQueryProvider;

    @DubboReference
    private CategoryCommandProvider cateGoryCommandProvider;

    /**cateGoryCommandProvider;

    /**
     * 类目树
     * @return
     */
    public List<ProductCategoryTreeDTO> listCategoryTree() {
        CategoryQueryReq categoryQueryReq = new CategoryQueryReq();
        categoryQueryReq.setType(NumberConstant.TWO);
        DubboResponse<List<CategoryDetailResultResp>> response = cateGoryQueryProvider.list(categoryQueryReq);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        List<CategoryDetailResultResp> resultRespList = response.getData();
        if (CollectionUtils.isEmpty(resultRespList)){
            return new ArrayList<>();
        }
        List<ProductCategoryTreeDTO> categoryVOList = resultRespList.stream().map(CategoryConverter::toCategoryTreeDTO).collect(Collectors.toList());;
        return categoryVOList;
    }

    /**
     * 类目树
     * @return
     */
    public List<ProductCategoryTreeDTO> listCategoryTreeNew() {
        net.summerfarm.goods.client.req.CategoryQueryReq categoryQueryReq = new net.summerfarm.goods.client.req.CategoryQueryReq();
        DubboResponse<List<CategoryDetailResp>> response = categoryProvider.treeList(categoryQueryReq);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        List<CategoryDetailResp> resultRespList = response.getData();
        if (CollectionUtils.isEmpty(resultRespList)){
            return new ArrayList<>();
        }
        List<ProductCategoryTreeDTO> categoryVOList = resultRespList.stream().map(CategoryFacadeConvert.INSTANCE::convert2ProductCategoryTreeDTO).collect(Collectors.toList());
        Iterator<ProductCategoryTreeDTO> iterator = categoryVOList.iterator();
        while (iterator.hasNext()) {
            ProductCategoryTreeDTO next = iterator.next();
            List<ProductCategoryTreeDTO> categoryVos = next.getChildList ();
            if (CollectionUtils.isEmpty(categoryVos)) {
                iterator.remove();
            } else {
                Iterator<ProductCategoryTreeDTO> child = categoryVos.iterator();
                while (child.hasNext()) {
                    ProductCategoryTreeDTO categoryDTO = child.next();
                    if (CollectionUtils.isEmpty(categoryDTO.getChildList ())) {
                        child.remove();
                    }
                }

                if (CollectionUtils.isEmpty(categoryVos)) {
                    iterator.remove();
                }
            }
        }
        return categoryVOList;
    }

    /**
     * 批量获取类目详情
     *
     * @return
     */
    public List<CategoryResp> selectCategoryDetail(List<Long> categoryIds) {
        DubboResponse<List<CategoryResp>> response = categoryProvider.selectCategoryDetail(categoryIds);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return Optional.ofNullable(response.getData()).orElse(Collections.EMPTY_LIST);
    }

    /**
     * 获取类目对应的税率
     *
     * @return
     */
    public BigDecimal getTaxRate(Long categoryId) {
        TaxRateQueryReq categoryQueryReq = new TaxRateQueryReq ();
        categoryQueryReq.setCategoryId (categoryId);
        DubboResponse<TaxRateResp> response = taxRateProvider.getTaxRate (categoryQueryReq);
        if (!response.isSuccess ()) {
            throw new ProviderException (response.getMsg ());
        }
        TaxRateResp resp = response.getData ();
        if(Objects.isNull (resp)){
            return BigDecimal.ZERO;
        }
        return Objects.isNull (resp.getTaxRateValue ()) ? BigDecimal.ZERO : resp.getTaxRateValue ();
    }

    /**
     * 根据id查询子类目集合
     * @param id
     * @return
     */
    public List<CategoryVO> queryChildCategoryList(Long id) {
        CategoryQueryReq req = new CategoryQueryReq();
        req.setId(id);
        DubboResponse<List<CategoryDetailResultResp>> response = cateGoryQueryProvider.list(req);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        List<CategoryDetailResultResp> resultRespList = response.getData();
        if (CollectionUtils.isEmpty(resultRespList)){
            return new ArrayList<>();
        }
        List<CategoryVO> categoryVOList = CategoryConverter.cateResultRespList2CateGoryVO(resultRespList);
        log.error("子类目集合:{}", JSONObject.toJSONString(categoryVOList));
        List<CategoryVO> newCategoryList = new ArrayList<>();
        getAllList(categoryVOList, newCategoryList);
        Set<CategoryVO> newCategorySet = new HashSet<>();
        Map<Long, Set<CategoryVO>> categoryVOMap = new HashMap<>();
        // 保存当前id对应的CategoryVO子集合
        CategoryVO parentCategoryVO = new CategoryVO();
        for (CategoryVO categoryVO : newCategoryList) {
            if (id!=null && id.equals(categoryVO.getId())){
                parentCategoryVO = categoryVO;
                newCategorySet.add(parentCategoryVO);
            }
            if (ObjectUtils.isEmpty(categoryVOMap.get(categoryVO.getParentId()))){
                Set<CategoryVO> categoryVOS = new HashSet<>();
                categoryVOS.add(categoryVO);
                categoryVOMap.put(categoryVO.getParentId(),categoryVOS);
            }else {
                categoryVOMap.get(categoryVO.getParentId()).add(categoryVO);
            }
        }
        getChildList(categoryVOMap, newCategorySet, id);
        List<CategoryVO> childrenList = Arrays.asList(newCategorySet.toArray(new CategoryVO[0]));
        return childrenList;
    }

    /**
     * 根据id查询子类目集合
     * @param id
     * @return
     */
    public List<CategoryVO> queryChildCategoryListNew(Long id) {
        net.summerfarm.goods.client.req.CategoryQueryReq req = new net.summerfarm.goods.client.req.CategoryQueryReq();
        req.setIds(Collections.singletonList(id));
        DubboResponse<List<CategoryDetailResp>> response = categoryProvider.treeList(req);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        List<CategoryDetailResp> resultRespList = response.getData();
        if (CollectionUtils.isEmpty(resultRespList)){
            return new ArrayList<>();
        }
        List<CategoryVO> categoryVOList = resultRespList.stream ().map (CategoryFacadeConvert.INSTANCE::convert2CategoryVO).collect(Collectors.toList());
        log.error("子类目集合:{}", JSONObject.toJSONString(categoryVOList));
        List<CategoryVO> newCategoryList = new ArrayList<>();
        getAllList(categoryVOList, newCategoryList);
        Set<CategoryVO> newCategorySet = new HashSet<>();
        Map<Long, Set<CategoryVO>> categoryVOMap = new HashMap<>();
        // 保存当前id对应的CategoryVO子集合
        CategoryVO parentCategoryVO = new CategoryVO();
        for (CategoryVO categoryVO : newCategoryList) {
            if (id!=null && id.equals(categoryVO.getId())){
                parentCategoryVO = categoryVO;
                newCategorySet.add(parentCategoryVO);
            }
            if (ObjectUtils.isEmpty(categoryVOMap.get(categoryVO.getParentId()))){
                Set<CategoryVO> categoryVOS = new HashSet<>();
                categoryVOS.add(categoryVO);
                categoryVOMap.put(categoryVO.getParentId(),categoryVOS);
            }else {
                categoryVOMap.get(categoryVO.getParentId()).add(categoryVO);
            }
        }
        getChildList(categoryVOMap, newCategorySet, id);
        List<CategoryVO> childrenList = Arrays.asList(newCategorySet.toArray(new CategoryVO[0]));
        return childrenList;
    }

    /**
     * 根据类目Id查询类目
     *
     * @param id
     * @return
     */
    public CategoryVO selectByCategoryId(Long id) {
        DubboResponse<CategoryDetailResultResp> response = cateGoryQueryProvider.selectByPrimaryKey(id);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        CategoryDetailResultResp resultResp = response.getData();
        CategoryVO categoryVO = CategoryConverter.toCategoryVO(resultResp);
        return categoryVO;
    }

    /**
     * 根据类目Id查询类目
     *
     * @param id
     * @return
     */
    public CategoryVO selectByCategoryIdNew(Long id) {
        net.summerfarm.goods.client.req.CategoryQueryReq req = new net.summerfarm.goods.client.req.CategoryQueryReq();
        req.setIds(Collections.singletonList(id));
        DubboResponse<List<CategoryDetailResp>> response = categoryProvider.treeList(req);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        List<CategoryDetailResp> categoryVOList = response.getData();
        if (CollectionUtils.isEmpty(categoryVOList) || categoryVOList.size() != 1){
            return null;
        }
        CategoryVO categoryVO = CategoryFacadeConvert.INSTANCE.convert2CategoryVO(categoryVOList.get(0));
        return categoryVO;
    }

    /**
     * 根据三级类目id查询整个类目级别
     * @param id
     * @return
     */
    public ProductCategoryDTO selectWholeCategoryNew(Long id){
        return getWholeCategoryAndSeparator(id, StringConstants.LEFT_SLASH);
    }

    /**
     * 根据三级类目id批量查询整个类目级别
     * @param categoryIds
     * @return
     */
    public Map<Long, ProductCategoryDTO> selectWholeCategoryNewBatch(Collection<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)){
            return Collections.emptyMap();
        }
        List<ProductCategoryDTO> categoryDTOS = getWholeCategoryAndSeparatorBatch(categoryIds, StringConstants.LEFT_SLASH);
        return categoryDTOS.stream().collect(Collectors.toMap(ProductCategoryDTO::getThirdCategoryId, Function.identity(), (k1, k2) -> k1));
    }

    private void getChildList(Map<Long,Set<CategoryVO>> categoryVOMap, Set<CategoryVO> categoryVOSet, Long id){
        if (ObjectUtils.isNotEmpty(categoryVOMap.get(id))){
            categoryVOSet.addAll(categoryVOMap.get(id));
            for (CategoryVO categoryVO : categoryVOMap.get(id)) {
                if (ObjectUtils.isNotEmpty(categoryVOMap.get(categoryVO.getId()))){
                    getChildList(categoryVOMap, categoryVOSet, categoryVO.getId());
                }
            }
        }

    }

    private List<CategoryVO> getAllList(List<CategoryVO> categoryVOList,List<CategoryVO> newCategoryList){
        newCategoryList.addAll(categoryVOList);
        for (CategoryVO categoryVO : categoryVOList) {
            if (ObjectUtils.isNotEmpty(categoryVO.getCategoryVOS())){
                getAllList(categoryVO.getCategoryVOS(),newCategoryList);
            }
        }
        return newCategoryList;
    }

    /**
     * 新增
     *
     * @param categoryQueryReq
     * @return
     */
    public Long add(CategoryQueryReq categoryQueryReq){
        DubboResponse<Long> response = cateGoryCommandProvider.add(categoryQueryReq);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }

        return response.getData();
    }

    /**
     * 编辑
     *
     * @param categoryQueryReq
     * @return
     */
    public Boolean edit(CategoryQueryReq categoryQueryReq){
        DubboResponse<Long> response = cateGoryCommandProvider.edit(categoryQueryReq);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }

        return Boolean.TRUE;
    }

    /**
     * 批量根据三级类目id查询整个类目级别
     * @param ids
     * @return
     */
    public Map<Long,ProductCategoryDTO> batchSelectWholeCategory(List<Long> ids){
        DubboResponse<Map<Long, CategoryLevelResultResp>> response = cateGoryQueryProvider.batchSelectWholeCategory(ids);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }


        Map<Long, CategoryLevelResultResp> resultResp = response.getData();
        if(CollectionUtils.isEmpty(resultResp)){
            return new HashMap<>();
        }

        Set<Long> categoryIds = resultResp.keySet();
        return categoryIds.stream().collect(Collectors.toMap(item -> item, item -> {
            CategoryLevelResultResp categoryLevelResultResp = resultResp.get(item);
            return CategoryConverter.toProductCategoryDTO(categoryLevelResultResp);
        }));
    }

    /**
     * 批量根据三级类目id查询整个类目级别
     * @param ids
     * @return
     */
    public Map<Long,ProductCategoryDTO> batchSelectWholeCategoryNew(List<Long> ids){
        return ids.stream().collect(Collectors.toMap(item -> item, this::selectWholeCategoryNew));
    }

    public Boolean synchronizedXianmuCategory(List<Long> categoryIds){
        DubboResponse<Boolean> dubboResponse = cateGoryCommandProvider.synchronizedXianmuCategory(categoryIds);
        if (!dubboResponse.isSuccess()){
            throw new BizException(dubboResponse.getMsg());
        }

        return dubboResponse.getData();
    }

    public List<CategoryDetailResultResp> listByCondition(CategoryQueryReq cateGoryQueryReq){
        DubboResponse<List<CategoryDetailResultResp>> dubboResponse = cateGoryQueryProvider.listByCondition(cateGoryQueryReq);
        if (!dubboResponse.isSuccess()){
            throw new BizException(dubboResponse.getMsg());
        }

        return dubboResponse.getData();
    }

    public List<CategoryVO> listByConditionNew(CategoryQueryReq cateGoryQueryReq){
        net.summerfarm.goods.client.req.CategoryQueryReq categoryQueryReq = new net.summerfarm.goods.client.req.CategoryQueryReq();
        categoryQueryReq.setParentIds(Collections.singletonList(cateGoryQueryReq.getParentId()));
        categoryQueryReq.setName(cateGoryQueryReq.getName());
        DubboResponse<List<CategoryDetailResp>> response = categoryProvider.treeList(categoryQueryReq);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        List<CategoryDetailResp> resultRespList = response.getData();
        if (CollectionUtils.isEmpty(resultRespList)){
            return new ArrayList<>();
        }
        return resultRespList.stream ().map (CategoryFacadeConvert.INSTANCE::convert2CategoryVO).collect(Collectors.toList());
    }

    public String queryCategoryTreeByThirdCategoryId(Long categoryId) {
        DubboResponse<String> response = cateGoryQueryProvider.queryCategoryTreeByThirdCategoryId(categoryId);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        return response.getData();
    }

    public String queryCategoryTreeByThirdCategoryIdNew(Long categoryId) {
        return getWholeCategoryAndSeparator(categoryId, ">").getCategoryStr();
    }

    private ProductCategoryDTO getWholeCategoryAndSeparator(Long id, String separator) {
        DubboResponse<CategoryAllPathResp> response = categoryProvider.selectAllPath(id);
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        CategoryAllPathResp resultResp = response.getData();
        ProductCategoryDTO categoryVO = CategoryFacadeConvert.INSTANCE.convert2ProductCategoryDTO(resultResp);
        buildCategoryFullName(separator, categoryVO);
        return categoryVO;
    }

    private List<ProductCategoryDTO> getWholeCategoryAndSeparatorBatch(Collection<Long> categoryIds, String separator) {
        DubboResponse<List<CategoryAllPathResp>> response = categoryProvider.selectAllPathList(new ArrayList<>(categoryIds));
        if (!response.isSuccess()){
            throw new DefaultServiceException(response.getMsg());
        }
        List<CategoryAllPathResp> resultRespList = response.getData();
        List<ProductCategoryDTO> categoryVOList = CategoryFacadeConvert.INSTANCE.convert2ProductCategoryDTOs(resultRespList);
        for (ProductCategoryDTO categoryVO : categoryVOList) {
            buildCategoryFullName(separator, categoryVO);
        }
        return categoryVOList;
    }

    private void buildCategoryFullName(String separator, ProductCategoryDTO categoryVO) {
        StringBuffer categoryStr = new StringBuffer();
        if (StringUtils.isNotBlank(categoryVO.getFirstCategoryName())) {
            categoryStr.append(categoryVO.getFirstCategoryName());
        }
        if (StringUtils.isNotBlank(categoryVO.getSecondCategoryName())) {
            categoryStr.append(separator).append(categoryVO.getSecondCategoryName());
        }
        if (StringUtils.isNotBlank(categoryVO.getThirdCategoryName())) {
            categoryStr.append(separator).append(categoryVO.getThirdCategoryName());
        }
        categoryVO.setCategoryStr(categoryStr.toString());
    }
}

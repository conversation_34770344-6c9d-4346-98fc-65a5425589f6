package com.cosfo.manage.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.config.ContactConfigCommandProvider;
import net.summerfarm.wnc.client.provider.config.ContactConfigQueryProvider;
import net.summerfarm.wnc.client.req.ContactConfigQueryReq;
import net.summerfarm.wnc.client.req.ContactConfigRemoveCommandReq;
import net.summerfarm.wnc.client.resp.ContactConfigResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author: fansongsong
 * @Date: 2023-09-22
 * @Description:
 */
@Component
@Slf4j
public class ContactConfigQueryFacade {

    @DubboReference
    private ContactConfigQueryProvider contactConfigQueryProvider;

    @DubboReference
    private ContactConfigCommandProvider contactConfigCommandProvider;

    /**
     * 查询联系人绑定关系
     *
     * @param queryReq 查询请求
     * @return 是否成功
     */
    public ContactConfigResp queryContactConfig(ContactConfigQueryReq queryReq) {
        DubboResponse<ContactConfigResp> response = contactConfigQueryProvider.queryContactConfig(queryReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }


    /**
     * 删除联系人配置
     *
     * @param commandReq 删除操作请求
     * @return 是否成功
     */
    public void removeContactConfig(ContactConfigRemoveCommandReq commandReq) {
        DubboResponse<Void> response = contactConfigCommandProvider.removeContactConfig(commandReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
    }
}

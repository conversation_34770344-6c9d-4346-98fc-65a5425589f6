package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.converter.SaleInventoryReqMapper;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.facade.dto.ProductSkuWarehouseQueryDTO;
import com.cosfo.summerfarm.model.input.SummerfarmAgentSkuWarehouseDataInput;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.saleinventory.SaleInventoryProvider;
import net.summerfarm.wms.saleinventory.dto.dto.SkuAggregateInventoryDTO;
import net.summerfarm.wms.saleinventory.dto.req.QueryAggregateInventoryRequest;
import net.summerfarm.wms.saleinventory.dto.req.QueryInventoryPageRequest;
import net.summerfarm.wms.saleinventory.dto.req.QuerySkuAggregateInventoryRequest;
import net.summerfarm.wms.saleinventory.dto.res.QueryAggregateInventoryResp;
import net.summerfarm.wms.saleinventory.dto.res.QueryInventoryResp;
import net.summerfarm.wms.saleinventory.dto.res.QuerySkuAggregateInventoryResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SaleInventoryServiceFacade {

    @DubboReference
    private SaleInventoryProvider saleInventoryProvider;

    public QueryAggregateInventoryResp.AggregateInventoryDTO queryAggregateInventory(QueryAggregateInventoryRequest queryAggregateInventoryRequest) {
        log.info("saleInventoryProvider.queryAggregateInventory req = {}", JSON.toJSONString(queryAggregateInventoryRequest));
        DubboResponse<QueryAggregateInventoryResp> response = saleInventoryProvider.queryAggregateInventory(queryAggregateInventoryRequest);
        log.info("saleInventoryProvider.queryAggregateInventory res = {}", JSON.toJSONString(response));

        if (!response.isSuccess()) {
            throw new DefaultServiceException(response.getMsg());
        }
        QueryAggregateInventoryResp.AggregateInventoryDTO aggregateInventoryDTO = response.getData().getAggregateInventoryDTO();
        return aggregateInventoryDTO;
    }

    public PageInfo<SkuAggregateInventoryDTO> querySkuAggregateInventory(QuerySkuAggregateInventoryRequest querySkuAggregateInventoryRequest) {
        log.info("saleInventoryProvider.querySkuAggregateInventory req = {}", JSON.toJSONString(querySkuAggregateInventoryRequest));
        DubboResponse<QuerySkuAggregateInventoryResp> response = saleInventoryProvider.querySkuAggregateInventory(querySkuAggregateInventoryRequest);
        log.info("saleInventoryProvider.querySkuAggregateInventory res = {}", JSON.toJSONString(response));

        if (!response.isSuccess()) {
            throw new DefaultServiceException(response.getMsg());
        }
        PageInfo<SkuAggregateInventoryDTO> skuInventoryPageResult = response.getData().getSkuInventoryPageResult();
        return skuInventoryPageResult;

    }

    /**
     * 实时获取代仓库存
     *
     * @param dataInput
     * @return
     */
    public PageInfo<QueryInventoryResp> pageQueryAgentSkuWarehouseData(SummerfarmAgentSkuWarehouseDataInput dataInput, ProductSkuWarehouseQueryDTO productSkuWarehouseQueryDTO) {
        QueryInventoryPageRequest dataReq = SaleInventoryReqMapper.INSTANCE.inputToReq(dataInput);
        dataReq.setSkuIds(dataInput.getSkuId());
        dataReq.setWarehouseNos(dataInput.getWarehouseIds().stream().map(Long::intValue).collect(Collectors.toList()));
        dataReq.setSync(productSkuWarehouseQueryDTO.getSync());
        dataReq.setInOutRecord(productSkuWarehouseQueryDTO.getInOutRecord());
        DubboResponse<PageInfo<QueryInventoryResp>> response = saleInventoryProvider.pageQueryInventory(dataReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        PageInfo<QueryInventoryResp> data = response.getData();
        return data;
    }
}

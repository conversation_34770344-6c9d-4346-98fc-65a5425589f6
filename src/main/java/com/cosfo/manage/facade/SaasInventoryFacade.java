package com.cosfo.manage.facade;

import com.cosfo.manage.common.converter.PurchaseBatchInventoryReqMapper;
import com.cosfo.manage.common.converter.QueryAreaStoreQuantityReqMapper;
import com.cosfo.manage.facade.convert.InventoryConvert;
import com.cosfo.manage.facade.input.ProductStockChangeRecordQueryInput;
import com.cosfo.manage.report.model.dto.ProductAgentShelfLifeQueryDTO;
import com.cosfo.summerfarm.model.input.SummerfarmAgentSkuWarehouseDataInput;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.saleinventory.SaasInventoryProvider;
import net.summerfarm.wms.saleinventory.dto.dto.MinShelfLifePurchaseBatchDTO;
import net.summerfarm.wms.saleinventory.dto.dto.PurchaseBatchInventoryDTO;
import net.summerfarm.wms.saleinventory.dto.req.BatchQueryMinShelfLifePurchaseBatchReq;
import net.summerfarm.wms.saleinventory.dto.req.PageQueryPurchaseBatchInventoryReq;
import net.summerfarm.wms.saleinventory.dto.req.PageQuerySkuQuantityChangeRecordReq;
import net.summerfarm.wms.saleinventory.dto.req.QueryAreaStoreQuantityReq;
import net.summerfarm.wms.saleinventory.dto.req.StockChangeRecordQueryReq;
import net.summerfarm.wms.saleinventory.dto.res.BatchQueryMinShelfLifePurchaseBatchResp;
import net.summerfarm.wms.saleinventory.dto.res.PageQueryPurchaseBatchInventoryResp;
import net.summerfarm.wms.saleinventory.dto.res.PageQuerySkuQuantityChangeRecordResp;
import net.summerfarm.wms.saleinventory.dto.res.QueryAreaStoreQuantityResp;
import net.summerfarm.wms.saleinventory.dto.res.StockChangeRecordResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.input.PageSortInput;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-09-12
 * @Description:
 */
@Slf4j
@Component
public class SaasInventoryFacade {

    @DubboReference
    private SaasInventoryProvider saasInventoryProvider;

    /**
     * 库存变更记录
     *
     */
    public PageQuerySkuQuantityChangeRecordResp pageQuerySkuQuantityChangeRecord(ProductStockChangeRecordQueryInput input) {
        PageQuerySkuQuantityChangeRecordReq request = InventoryConvert.productStockChangeRecord2ChangeRecordReq(input);
        DubboResponse<PageQuerySkuQuantityChangeRecordResp> response = saasInventoryProvider.pageQuerySkuQuantityChangeRecord(request);
        if (!response.isSuccess()) {
            throw new ProviderException("查询库存变更记录失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 出入库流水
     *
     */
    public PageInfo<StockChangeRecordResp> pageQueryStockChangeRecord(StockChangeRecordQueryReq request){
        DubboResponse<PageInfo<StockChangeRecordResp>> response = saasInventoryProvider.pageQueryStockChangeRecord(request);
        if (!response.isSuccess()) {
            throw new ProviderException("查询库存变更记录失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 采购批次库存
     *
     */
    public PageInfo<PurchaseBatchInventoryDTO> pageQueryPurchaseBatchInventory(ProductAgentShelfLifeQueryDTO queryDTO){
        PageQueryPurchaseBatchInventoryReq request = PurchaseBatchInventoryReqMapper.INSTANCE.queryToReq(queryDTO, CollectionUtils.isEmpty(queryDTO.getSortList()) ? new PageSortInput() : queryDTO.getSortList().get(0));
        DubboResponse<PageQueryPurchaseBatchInventoryResp> response = saasInventoryProvider.pageQueryPurchaseBatchInventory(request);
        if (!response.isSuccess()) {
            throw new ProviderException("查询采购批次库存失败：" + response.getMsg());
        }
        return response.getData().getPageResult();
    }

    /**
     * 批量查找最早保质期和批次
     *
     * @return sku->warehouse->shelf life
     */
    public Map<Long, Map<Integer, MinShelfLifePurchaseBatchDTO>> batchQueryLatestShelfLife(BatchQueryMinShelfLifePurchaseBatchReq request) {
        DubboResponse<BatchQueryMinShelfLifePurchaseBatchResp> response = saasInventoryProvider.batchQueryMinShelfLifePurchaseBatch(request);
        if (!response.isSuccess()) {
            log.warn("SaasInventoryProvider.batchQueryMinShelfLifePurchaseBatch error");
            //异常降级不展示
            return Collections.EMPTY_MAP;
        }

        BatchQueryMinShelfLifePurchaseBatchResp data = response.getData();
        if (data == null || CollectionUtils.isEmpty(data.getBatchDTOList())) {
            return new HashMap<>(0);
        }
        List<net.summerfarm.wms.saleinventory.dto.dto.MinShelfLifePurchaseBatchDTO> batchDTOList = data.getBatchDTOList();
        Map<Long, Map<Integer, MinShelfLifePurchaseBatchDTO>> map = batchDTOList.stream()
                .collect(Collectors.groupingBy(MinShelfLifePurchaseBatchDTO::getSkuId,
                        Collectors.groupingBy(MinShelfLifePurchaseBatchDTO::getWarehouseNo,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        values -> values.get(0))
                        )
                ));
        return map;
    }


    /**
     * 实时获取代仓库存
     *
     * @param dataInput
     * @return
     */
    public PageInfo<QueryAreaStoreQuantityResp> pageQueryAgentSkuWarehouseData(SummerfarmAgentSkuWarehouseDataInput dataInput) {
        QueryAreaStoreQuantityReq dataReq = QueryAreaStoreQuantityReqMapper.INSTANCE.inputToReq(dataInput);
        DubboResponse<PageInfo<QueryAreaStoreQuantityResp>> response = saasInventoryProvider.queryAreaStoreQuantity(dataReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        PageInfo<QueryAreaStoreQuantityResp> data = response.getData();
        return data;
    }
}

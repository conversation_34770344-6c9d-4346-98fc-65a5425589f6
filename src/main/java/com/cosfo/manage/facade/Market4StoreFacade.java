package com.cosfo.manage.facade;

import com.cofso.item.client.provider.Market4StoreProvider;
import com.cofso.item.client.req.MarketItemOnSaleReq;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@Slf4j
@Component
public class Market4StoreFacade {

    @DubboReference
    private Market4StoreProvider market4StoreProvider;

    public List<MarketItemOnSaleSimple4StoreResp> queryMarketItemOnSaleInfo(MarketItemOnSaleReq req) {
        DubboResponse<List<MarketItemOnSaleSimple4StoreResp>> response = market4StoreProvider.queryMarketItemOnSaleInfo(req);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    public List<MarketItemOnSaleSimple4StoreResp> queryOnSaleMarketItems(MarketItemOnSaleReq req) {
        DubboResponse<List<MarketItemOnSaleSimple4StoreResp>> response = market4StoreProvider.queryOnSaleMarketItems(req);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }
}

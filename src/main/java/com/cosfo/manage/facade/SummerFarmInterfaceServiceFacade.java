package com.cosfo.manage.facade;

import com.cosfo.manage.facade.convert.Convert;
import com.cosfo.manage.facade.dto.ApplyAgentSkuDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.saas.SaasInterfaceServiceProvider;
import net.summerfarm.manage.client.saas.req.ApplyAgentSkuReq;
import net.summerfarm.manage.client.saas.req.SummerFarmSynchronizedSkuReq;
import net.summerfarm.manage.client.saas.resp.ApplyAgentSkuResp;
import net.summerfarm.manage.client.saas.resp.SummerFarmSynchronizedSkuResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/22
 */
@Slf4j
@Component
public class SummerFarmInterfaceServiceFacade {
    @DubboReference
    private SaasInterfaceServiceProvider saasInterfaceServiceProvider;

//    /**
//     * 查询报价单区间
//     *
//     * @param summerFarmSkuPriceInfoReqs 报价单查询
//     * @return
//     */
//    @Deprecated
//    public List<SummerFarmSkuPriceInfoResp> queryAdminSkuPricingInfo(List<SummerFarmSkuPriceInfoReq> summerFarmSkuPriceInfoReqs) {
//        DubboResponse<List<SummerFarmSkuPriceInfoResp>> response = saasInterfaceServiceProvider.queryAdminSkuPricingInfo(summerFarmSkuPriceInfoReqs);
//        if (!response.isSuccess()) {
//            throw new ProviderException(response.getMsg());
//        }
//
//        return response.getData();
//    }

//    /**
//     * 查询城市供应状态
//     *
//     * @param summerFarmSkuSupplyStatusInputs
//     * @return
//     */
//    @Deprecated
//    public List<SummerFarmSkuSupplyStatusResp> queryCitySupplyStatus(List<SummerFarmSkuSupplyStatusReq> summerFarmSkuSupplyStatusInputs) {
//        DubboResponse<List<SummerFarmSkuSupplyStatusResp>> response = saasInterfaceServiceProvider.queryCitySupplyStatus(summerFarmSkuSupplyStatusInputs);
//        if (!response.isSuccess()) {
//            throw new ProviderException(response.getMsg());
//        }
//
//        return response.getData();
//    }

//    /**
//     * 查询仓库信息
//     *
//     * @return
//     */
//    public List<SummerFarmWarehouseInfoResp> queryWarehouseList() {
//        DubboResponse<List<SummerFarmWarehouseInfoResp>> response = saasInterfaceServiceProvider.queryWarehouseInfo();
//        if (!response.isSuccess()) {
//            throw new ProviderException(response.getMsg());
//        }
//        return response.getData();
//    }
//
//    public List<SummerfarmProductInfoDTO> batchQuerySkuInfo(List<Long> agentSkuIds) {
//        if(CollectionUtil.isEmpty (agentSkuIds)){
//            return Collections.emptyList ();
//        }
//        SummerFarmSkuReq req = new SummerFarmSkuReq();
//        req.setSkuIds(agentSkuIds);
//        log.info ("调用鲜沐rpc查询商品接口,req={}", JSON.toJSONString (req));
//        DubboResponse<List<SummerfarmProductInfoResp>> resp = saasInterfaceServiceProvider.batchQuerySkuInfo(req);
//        log.info ("调用鲜沐rpc查询商品接口,resp={}", JSON.toJSONString (resp));
//        if (!resp.isSuccess()) {
//            throw new ProviderException(resp.getMsg());
//        }
//        List<SummerfarmProductInfoResp> data = resp.getData();
//        if (CollectionUtils.isEmpty(data)) {
//            return Lists.newArrayList();
//        }
//        return data.stream().map(Convert::summerfarmProductInfoResp2Dto).collect(Collectors.toList());
//    }
//
//    /**
//     * 实时获取代仓库存
//     *
//     * @param dataInput
//     * @return
//     */
//    public PageInfo<SummerFarmAgentSkuWarehouseDataResp> pageQueryAgentSkuWarehouseData(SummerfarmAgentSkuWarehouseDataInput dataInput) {
//        SummerFarmAgentSkuWarehouseDataReq dataReq = SummerFarmAgentSkuWarehouseDataReqMapper.INSTANCE.inputToReq(dataInput);
//        DubboResponse<PageInfo<SummerFarmAgentSkuWarehouseDataResp>> response = saasInterfaceServiceProvider.queryAreaStoreQuantity(dataReq);
//        if (!response.isSuccess()) {
//            throw new ProviderException(response.getMsg());
//        }
//        PageInfo<SummerFarmAgentSkuWarehouseDataResp> data = response.getData();
//        return data;
//    }
//
//    /**
//     * 实时查询库存批次有效期+
//     *
//     * @param queryDTO
//     * @return
//     */
//    public PageInfo<PurchaseBatchInventoryDTO> pageQueryBatchInventory(ProductAgentShelfLifeQueryDTO queryDTO) {
//        PageQueryPurchaseBatchInventoryReq req = PageQueryPurchaseBatchInventoryReqMapper.INSTANCE.queryToReq(queryDTO, CollectionUtils.isEmpty(queryDTO.getSortList()) ? new PageSortInput() : queryDTO.getSortList().get(0));
//        log.info("saasInterfaceServiceProvider.pageQueryPurchaseBatchInventory req = {}", JSON.toJSONString(req));
//        DubboResponse<PageQueryPurchaseBatchInventoryResp> response = saasInterfaceServiceProvider.pageQueryPurchaseBatchInventory(req);
//        log.info("saasInterfaceServiceProvider.pageQueryPurchaseBatchInventory res = {}", JSON.toJSONString(response));
//        if (!response.isSuccess()) {
//            throw new ProviderException(response.getMsg());
//        }
//        PageQueryPurchaseBatchInventoryResp data = response.getData();
//        return data.getPageResult();
//    }
//
//    /**
//     * 批量查找最早保质期和批次
//     *
//     * @return sku->warehouse->shelf life
//     */
//    public Map<Long, Map<Integer, MinShelfLifePurchaseBatchDTO>> batchQueryLatestShelfLife(BatchQueryMinShelfLifePurchaseBatchReq batchReq) {
//        log.info("saasInterfaceServiceProvider.batchQueryMinShelfLifePurchaseBatch req = {}", JSON.toJSONString(batchReq));
//        DubboResponse<BatchQueryMinShelfLifePurchaseBatchResp> response = saasInterfaceServiceProvider.batchQueryMinShelfLifePurchaseBatch(batchReq);
//        log.info("saasInterfaceServiceProvider.batchQueryMinShelfLifePurchaseBatch res = {}", JSON.toJSONString(response));
//        if (!response.isSuccess()) {
//            log.warn("saasInterfaceServiceProvider.batchQueryMinShelfLifePurchaseBatch error");
//            //异常降级不展示
//            return new HashMap<>(0);
//        }
//        BatchQueryMinShelfLifePurchaseBatchResp data = response.getData();
//        if (data == null || CollectionUtils.isEmpty(data.getBatchDTOList())) {
//            return new HashMap<>(0);
//        }
//        List<MinShelfLifePurchaseBatchDTO> batchDTOList = data.getBatchDTOList();
//        Map<Long, Map<Integer, MinShelfLifePurchaseBatchDTO>> map = batchDTOList.stream()
//                .collect(Collectors.groupingBy(MinShelfLifePurchaseBatchDTO::getSkuId,
//                        Collectors.groupingBy(MinShelfLifePurchaseBatchDTO::getWarehouseNo,
//                                Collectors.collectingAndThen(
//                                        Collectors.toList(),
//                                        values -> values.get(0))
//                        )
//                ));
//        return map;
//    }

    /**
     * sku代仓申请
     *
     * @param agentSkuDTO 请求
     * @return net.xianmu.common.result.DubboResponse<ApplyAgentSkuResp>
     * <AUTHOR>
     * @date 2023/4/7 12:35
     */
    public Boolean applyAgentSku(ApplyAgentSkuDTO agentSkuDTO) {
        ApplyAgentSkuReq applyAgentSkuReq = Convert.convertToApplyAgentSkuReq(agentSkuDTO);
        DubboResponse<ApplyAgentSkuResp> response = saasInterfaceServiceProvider.applyAgentSku(applyAgentSkuReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }

        ApplyAgentSkuResp data = response.getData();
        return data.getApplyResult();
    }

    /**
     * 同步代仓申请取消状态
     *
     * @param agentSkuDTO 请求
     * @return net.xianmu.common.result.DubboResponse<ApplyAgentSkuResp>
     */
    public Boolean cancelAgentSku(ApplyAgentSkuDTO agentSkuDTO) {
        ApplyAgentSkuReq applyAgentSkuReq = Convert.convertToApplyAgentSkuReq(agentSkuDTO);
        DubboResponse<ApplyAgentSkuResp> response = saasInterfaceServiceProvider.cancelAgentSku(applyAgentSkuReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }

        ApplyAgentSkuResp data = response.getData();
        return data.getApplyResult();
    }

    /**
     * 查询需要同步的sku信息
     *
     * @param input
     * @return
     */
    public SummerFarmSynchronizedSkuResp queryNeedSynchronizedSku(SummerFarmSynchronizedSkuReq input) {
        DubboResponse<SummerFarmSynchronizedSkuResp> response = saasInterfaceServiceProvider.queryNeedSynchronizedSku(input);
        if (!response.isSuccess()) {
            throw new ProviderException("查询鲜沐sku信息失败" + response.getMsg());
        }

        return response.getData();
    }

    public List<Long> querySkuIdsByAdminId(Long adminId) {
        DubboResponse<List<Long>> response = saasInterfaceServiceProvider.querySkuIdsByAdminId(adminId.intValue());
        if (!response.isSuccess()) {
            throw new ProviderException("查询获取大客户所属skuId失败 adminId=" + adminId + response.getMsg());
        }

        return response.getData();
    }
}

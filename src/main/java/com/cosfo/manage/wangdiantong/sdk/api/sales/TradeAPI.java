package com.cosfo.manage.wangdiantong.sdk.api.sales;


import com.cosfo.manage.wangdiantong.sdk.Pager;
import com.cosfo.manage.wangdiantong.sdk.WdtErpException;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.TradeQueryHistoryRequest;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.TradeQueryHistoryResponse;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.TradeQueryRequest;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.TradeQueryResponse;
import com.cosfo.manage.wangdiantong.sdk.impl.Api;

public interface TradeAPI {
    @Api(value = "sales.TradeQuery.queryWithDetail", paged = true)
    TradeQueryResponse query(TradeQueryRequest request, Pager pager);

    @Api(value = "sales.TradeQuery.queryHistoryWithDetail", paged = true)
    TradeQueryHistoryResponse queryHistory(TradeQueryHistoryRequest request, Pager pager) throws WdtErpException;
}
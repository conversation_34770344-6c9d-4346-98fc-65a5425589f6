package com.cosfo.manage.wangdiantong.sdk.api.sales;


import com.cosfo.manage.wangdiantong.sdk.Pager;
import com.cosfo.manage.wangdiantong.sdk.WdtErpException;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.StockOutQueryRequest;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.StockOutQueryResponse;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.StockoutQueryHistoryRequest;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.StockoutQueryHistoryResponse;
import com.cosfo.manage.wangdiantong.sdk.impl.Api;

public interface StockOutAPI {
    @Api(value = "wms.stockout.Sales.queryWithDetail", paged = true)
    StockOutQueryResponse query(StockOutQueryRequest request, Pager pager);

    @Api(value = "wms.stockout.Sales.queryHistoryWithDetail", paged = true)
    StockoutQueryHistoryResponse queryHistory(StockoutQueryHistoryRequest request, Pager pager) throws WdtErpException;
}

package com.cosfo.manage.wangdiantong.sdk.api.sales;


import com.cosfo.manage.wangdiantong.sdk.Pager;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.PushSelfRequest;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.PushSelfResponse;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.RawTradeSearchHistoryRequest;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.RawTradeSearchHistoryResponse;
import com.cosfo.manage.wangdiantong.sdk.impl.Api;

import java.util.List;

public interface RawTradeAPI {
    @Api(value = "sales.RawTrade.pushSelf", paged = false)
    PushSelfResponse pushSelf(String shopNo, List<PushSelfRequest.RawTrade> rawTradeList,
                              List<PushSelfRequest.RawTradeOrder> rawTradeOrderList, List<PushSelfRequest.RawTradeDiscount> rawTradeDiscountList);


    @Api(value = "sales.RawTrade.searchHistory", paged = true)
    RawTradeSearchHistoryResponse query(RawTradeSearchHistoryRequest request, Pager pager);
}
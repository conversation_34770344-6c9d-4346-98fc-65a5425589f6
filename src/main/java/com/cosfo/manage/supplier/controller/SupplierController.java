package com.cosfo.manage.supplier.controller;

import com.cosfo.manage.common.context.AccountTypeEnum;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.huifu.service.HuiFuAccountService;
import com.cosfo.manage.huifu.dto.HuiFuSupplierAccountDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:供应商管理
 * @author: George
 * @date: 2024-01-04
 **/
@RestController
@RequestMapping("/supplier")
public class SupplierController {

    @Resource
    private HuiFuAccountService huiFuAccountService;

    /**
     * 根据供应商ids查询分账信息
     * @param supplierIds
     * @return
     */
    @PostMapping("/query/sharing-info")
    public CommonResult<List<HuiFuSupplierAccountDTO>> queryBySupplierIds(@RequestBody List<Long> supplierIds) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        List<HuiFuSupplierAccountDTO> supplierProfitSharingList = huiFuAccountService.queryByAccountIds(tenantId, supplierIds, AccountTypeEnum.SUPPLIER.getType());
        return CommonResult.ok(supplierProfitSharingList);
    }

    /**
     * 新建供应商分账信息
     * @param huiFuSupplierAccountDTO
     * @return
     */
    @PostMapping("/save-or-update/sharing-info")
    public CommonResult saveOrUpdateSharingInfo(@RequestBody HuiFuSupplierAccountDTO huiFuSupplierAccountDTO) {
        huiFuAccountService.saveOrUpdateSharingInfo(huiFuSupplierAccountDTO);
        return CommonResult.ok();
    }
}

package com.cosfo.manage.supplier.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * supplier_delivery_info
 * <AUTHOR>
@Data
public class SupplierDeliveryInfo implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 供应商收取运费
     */
    private BigDecimal supplierDeliveryFee;

    /**
     * 供应商收取运费规则
     */
    private String supplierDeliveryFeeRule;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 0、跟随供应商 1、免运费
     */
    private Integer type;

    private static final long serialVersionUID = 1L;
}
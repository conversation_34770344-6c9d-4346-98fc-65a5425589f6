package com.cosfo.manage.supplier.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/9
 */
@Data
public class SupplierDeliveryInfoDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 供应商收取运费
     */
    private BigDecimal supplierDeliveryFee;

    /**
     * 供应商收取运费规则
     */
    private String supplierDeliveryFeeRule;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 0、跟随供应商 1、免运费
     */
    private Byte type;
}

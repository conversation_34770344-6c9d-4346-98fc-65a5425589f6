package com.cosfo.manage.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.bill.model.dto.FinancialBillRuleDTO;
import com.cosfo.manage.bill.service.FinancialBillRuleService;
import com.cosfo.manage.client.merchant.openapi.MerchantStoreOpenProvider;
import com.cosfo.manage.client.merchant.req.MerchantStoreBatchCommandReq;
import com.cosfo.manage.client.merchant.req.MerchantStoreCloseReq;
import com.cosfo.manage.client.merchant.req.MerchantStoreOpenReq;
import com.cosfo.manage.client.merchant.resp.MerchantStoreBatchOpenResp;
import com.cosfo.manage.client.merchant.resp.MerchantStoreOpenResp;
import com.cosfo.manage.common.config.OpenApiConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.exception.OpenApiProviderException;
import com.cosfo.manage.common.exception.code.OpenApiErrorCode;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.util.FormatUtils;
import com.cosfo.manage.facade.ContactConfigQueryFacade;
import com.cosfo.manage.facade.DeliveryFenceQueryFacade;
import com.cosfo.manage.facade.LbsQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantContactFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.merchant.convert.MerchantConvertUtil;
import com.cosfo.manage.merchant.mapper.MerchantStoreBalanceMapper;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOpenDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalance;
import com.cosfo.manage.merchant.service.MerchantAddressHandler;
import com.cosfo.manage.merchant.service.MerchantAddressService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.provider.handler.status.StoreStatusHandler;
import com.cosfo.manage.provider.handler.status.StoreStatusRegistry;
import com.cosfo.manage.provider.model.StoreParam;
import com.cosfo.manage.tenant.service.impl.SpecialTenantService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.req.AddressQueryReq;
import net.summerfarm.wnc.client.req.ContactConfigQueryReq;
import net.summerfarm.wnc.client.req.ContactConfigRemoveCommandReq;
import net.summerfarm.wnc.client.req.DeliveryFenceQueryReq;
import net.summerfarm.wnc.client.resp.ContactConfigResp;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.wnc.client.resp.GeoResp;
import net.summerfarm.wnc.client.resp.LocationResp;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.robot.feishu.SignedFeishuBotUtil;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.regional.resp.RegionalOrganizationResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-09-18
 * @Description: openapi接口
 */
@DubboService
@Slf4j
public class MerchantStoreOpenProviderImpl implements MerchantStoreOpenProvider {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private FinancialBillRuleService financialBillRuleService;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private UserCenterMerchantContactFacade userCenterMerchantContactFacade;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private DeliveryFenceQueryFacade deliveryFenceQueryFacade;
    @Resource
    private LbsQueryFacade lbsQueryFacade;
    @Resource
    private ContactConfigQueryFacade contactConfigQueryFacade;
    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private MerchantStoreBalanceMapper merchantStoreBalanceMapper;
    @Resource
    private StoreStatusRegistry storeStatusRegistry;
    @Resource
    private SpecialTenantService specialTenantService;
    @Resource
    private MerchantAddressHandler merchantAddressHandler;

    /**
     * 门店下发
     *
     * @param merchantStoreOpenReq
     * @return
     */
    @Override
    public DubboResponse<MerchantStoreOpenResp> upsertStoreInfo(@Valid MerchantStoreOpenReq merchantStoreOpenReq) {
        List<MerchantStoreOpenDTO> merchantStoreOpenDTOList = batchUpsertStoreList(Collections.singletonList(merchantStoreOpenReq), false);
        if (CollectionUtils.isNotEmpty(merchantStoreOpenDTOList) && merchantStoreOpenDTOList.size() > NumberConstant.ONE) {
            throw new OpenApiProviderException("当前门店编号数据异常，请稍后再试");
        }
        MerchantStoreOpenDTO merchantStoreOpenDTO = merchantStoreOpenDTOList.get(NumberConstant.ZERO);
        MerchantStoreOpenResp merchantStoreOpenResp = MerchantStoreOpenResp.builder().storeId(merchantStoreOpenDTO.getStoreId()).build();
        return DubboResponse.getOK(merchantStoreOpenResp);
    }

    @Override
    public DubboResponse<MerchantStoreBatchOpenResp> upsertStoreBatch(@Valid MerchantStoreBatchCommandReq merchantStoreBatchCommandReq) {
        List<MerchantStoreOpenDTO> merchantStoreOpenDTOList = batchUpsertStoreList(merchantStoreBatchCommandReq.getStoreList(), true);

        List<String> successStoreNoList = merchantStoreOpenDTOList.stream().filter(merchantStoreOpenDTO -> merchantStoreOpenDTO.getSuccess())
                .map(MerchantStoreOpenDTO::getStoreNo).collect(Collectors.toList());

        List<MerchantStoreBatchOpenResp.StoreInfoResp> failStoreNoList = merchantStoreOpenDTOList.stream().filter(merchantStoreOpenDTO -> !merchantStoreOpenDTO.getSuccess())
                .map(dto -> MerchantStoreBatchOpenResp.StoreInfoResp.builder().storeNo(dto.getStoreNo()).failMessage(dto.getFailMessage()).build())
                .collect(Collectors.toList());

        MerchantStoreBatchOpenResp merchantStoreBatchOpenResp = MerchantStoreBatchOpenResp.builder()
                .successStoreNoList(successStoreNoList)
                .failStoreNoList(failStoreNoList).build();
        return DubboResponse.getOK(merchantStoreBatchOpenResp);
    }

    @Override
    public DubboResponse<Void> closeStore(@Valid MerchantStoreCloseReq merchantStoreCloseReq) {
        Long tenantId = Optional.ofNullable(IsvInfoHolder.getAccount()).map(IsvInfo::getAccountId).orElse(null);
        if (Objects.isNull(tenantId)) {
            log.info("关闭门店,租戶信息不存在 IsvInfo:{},merchantStoreOpenReq:{}", JSON.toJSONString(IsvInfoHolder.getAccount()), JSON.toJSONString(merchantStoreCloseReq));
            throw new BizException("租戶信息不存在");
        }

        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setTenantId(tenantId);
        merchantStoreQueryReq.setStoreNo(merchantStoreCloseReq.getStoreNo());
        List<MerchantStoreResultResp> MerchantStoreResultRespList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
        if (CollectionUtil.isEmpty(MerchantStoreResultRespList)) {
            throw new OpenApiProviderException(OpenApiErrorCode.NOT_EXIST_STORE);
        }

        MerchantStoreResultResp merchantStoreResultResp = MerchantStoreResultRespList.get(NumberConstant.ZERO);

        // 如果门店已关店,拦截
        if (Objects.equals(merchantStoreResultResp.getStatus(), MerchantStoreEnum.Status.CLOSE.getCode())) {
            throw new OpenApiProviderException(OpenApiErrorCode.STORE_CLOSE_ALREADY);
        }

        MerchantStoreCommandReq merchantStoreCommandReq = new MerchantStoreCommandReq();
        merchantStoreCommandReq.setId(merchantStoreResultResp.getId());
        merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.CLOSE.getCode());
        Boolean update = userCenterMerchantStoreFacade.updateMerchantStore(merchantStoreCommandReq);
        if (Boolean.TRUE.equals(update)) {
            return DubboResponse.getOK();
        }

        return DubboResponse.getDefaultError("业务处理失败");
    }

    /**
     * 批量同步门店信息
     * @param merchantStoreOpenReqList
     * @return
     */
    public List<MerchantStoreOpenDTO> batchUpsertStoreList(List<MerchantStoreOpenReq> merchantStoreOpenReqList, Boolean isBatchReq) {
        Long tenantId = Optional.ofNullable(IsvInfoHolder.getAccount()).map(IsvInfo::getAccountId).orElse(null);
        if (Objects.isNull(tenantId)) {
            log.info("门店下发,租戶信息不存在 IsvInfo:{},merchantStoreOpenReq:{}", JSON.toJSONString(IsvInfoHolder.getAccount()), JSON.toJSONString(merchantStoreOpenReqList));
            throw new BizException("租戶信息不存在");
        }

        List<String> keys = Lists.newArrayList();
        try {
            // 门店锁
            lockStoreList(keys, merchantStoreOpenReqList, tenantId);
            List<MerchantStoreOpenDTO> merchantStoreOpenDTOList = UPSERT_STORE_BATCH_FUNCTION.apply(merchantStoreOpenReqList, isBatchReq);
            // 批量初始化门店余额信息
            batchInitMerchantStoreBalanceIfNeed(tenantId, merchantStoreOpenDTOList);
            return merchantStoreOpenDTOList;
        } finally {
            unlockStoreList(keys);
        }
    }

    /**
     * 外部门店编号加锁
     *
     * @param keys
     * @param merchantStoreOpenReqList
     * @param tenantId
     */
    private void lockStoreList(List<String> keys, List<MerchantStoreOpenReq> merchantStoreOpenReqList, Long tenantId) {
        for (MerchantStoreOpenReq merchantStoreOpenReq : merchantStoreOpenReqList) {
            String redisKey = RedisKeyEnum.CM00003.join(tenantId, merchantStoreOpenReq.getStoreNo());
            RLock lock = redissonClient.getLock(redisKey);
            // 未获取到锁，退出
            if (!lock.tryLock()) {
                throw new BizException(merchantStoreOpenReq.getStoreNo() + "正在操作中，请稍后重试");
            }
            keys.add(redisKey);
        }
    }

    /**
     * 外部门店编号列表解锁
     *
     * @param keys
     */
    private void unlockStoreList(List<String> keys) {
        for (String key : keys) {
            RLock lock = redissonClient.getLock(key);
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 批量执行门店变更操作
     */
    public BiFunction<List<MerchantStoreOpenReq>, Boolean, List<MerchantStoreOpenDTO>> UPSERT_STORE_BATCH_FUNCTION = new BiFunction<List<MerchantStoreOpenReq>, Boolean, List<MerchantStoreOpenDTO>>() {

        @Override
        public List<MerchantStoreOpenDTO> apply(List<MerchantStoreOpenReq> merchantStoreOpenReqList, Boolean isBatchReq) {
            Long tenantId = IsvInfoHolder.getAccount().getAccountId();
            paramCheck(merchantStoreOpenReqList, tenantId);
            // 查询已存在该storeNo的门店信息
            List<String> storeNoList = merchantStoreOpenReqList.stream().map(MerchantStoreOpenReq::getStoreNo).collect(Collectors.toList());
            MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
            merchantStoreQueryReq.setTenantId(tenantId);
            merchantStoreQueryReq.setStoreNoList(storeNoList);
            List<MerchantStoreResultResp> MerchantStoreResultRespList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
            Map<String, List<MerchantStoreResultResp>> MerchantStoreResultRespMap = MerchantStoreResultRespList.stream()
                    .collect(Collectors.groupingBy(MerchantStoreResultResp::getStoreNo));

            // 批量查询门店地址、联系人信息
            Map<Long, MerchantAddressResultResp> merchantAddressResultRespMap = Collections.EMPTY_MAP;
            Map<Long, List<MerchantContactResultResp>> addressMerchantContactsMap = Collections.EMPTY_MAP;
            if (!CollectionUtil.isEmpty(MerchantStoreResultRespList)) {
                List<Long> storeIds = MerchantStoreResultRespList.stream().map(MerchantStoreResultResp::getId).collect(Collectors.toList());
                List<MerchantAddressResultResp> merchantAddressResultResps = merchantAddressService.selectAddressListByStoreIds(tenantId, storeIds);
                merchantAddressResultRespMap = merchantAddressResultResps.stream().collect(Collectors.toMap(MerchantAddressResultResp::getStoreId, Function.identity(), (v1, v2) -> v1));

                // 门店联系人查询
                List<Long> addressIdList = merchantAddressResultResps.stream().map(MerchantAddressResultResp::getId).collect(Collectors.toList());
                if (!CollectionUtil.isEmpty(addressIdList)) {
                    MerchantContactQueryReq merchantContactQueryReq = new MerchantContactQueryReq();
                    merchantContactQueryReq.setAddressIdList(addressIdList);
                    merchantContactQueryReq.setTenantId(tenantId);
                    List<MerchantContactResultResp> merchantContacts = userCenterMerchantContactFacade.getMerchantContacts(merchantContactQueryReq);
                    addressMerchantContactsMap = merchantContacts.stream().collect(Collectors.groupingBy(MerchantContactResultResp::getAddressId));
                }
            }


            // 租户默认区域组织
            RegionalOrganizationResultResp regionalOrganization = userCenterMerchantStoreFacade.saasGetRegionalByTenantId(tenantId);

            // 调用接口批量查询poi信息
            builderMerchantStoreAddressPoi(merchantStoreOpenReqList);

            // 非批量接口,单条执行
            if (!isBatchReq) {
                MerchantStoreOpenDTO merchantStoreOpenDTO = executeSingleStore(tenantId, MerchantStoreResultRespMap, merchantStoreOpenReqList.get(NumberConstant.ZERO),
                        regionalOrganization, merchantAddressResultRespMap, addressMerchantContactsMap);
                return Collections.singletonList(merchantStoreOpenDTO);
            }

            List<MerchantStoreOpenDTO> merchantStoreOpenDTOList = Lists.newArrayList();
            for (MerchantStoreOpenReq merchantStoreOpenReq : merchantStoreOpenReqList) {
                MerchantStoreOpenDTO merchantStoreOpenDTO;
                try {
                    // 依次执行变更操作
                    merchantStoreOpenDTO = executeSingleStore(tenantId, MerchantStoreResultRespMap, merchantStoreOpenReq,
                            regionalOrganization, merchantAddressResultRespMap, addressMerchantContactsMap);
                } catch (BizException e) {
                    merchantStoreOpenDTO = MerchantStoreOpenDTO.builder().storeNo(merchantStoreOpenReq.getStoreNo())
                            .success(false).failMessage(e.getMessage()).build();
                } catch (OpenApiProviderException e) {
                    merchantStoreOpenDTO = MerchantStoreOpenDTO.builder().storeNo(merchantStoreOpenReq.getStoreNo())
                            .success(false).failMessage(e.getMessage()).build();
                    log.error("门店下发异常 OpenApiProviderException", e);
                } catch (Exception e) {
                    merchantStoreOpenDTO = MerchantStoreOpenDTO.builder().storeNo(merchantStoreOpenReq.getStoreNo())
                            .success(false).failMessage(ResultStatusEnum.SERVER_ERROR.getMsg()).build();
                    log.error("门店下发异常", e);
                }
                merchantStoreOpenDTOList.add(merchantStoreOpenDTO);
            }
            return merchantStoreOpenDTOList;
        }

        /**
         * 优化后的批量查询
         * @param merchantStoreOpenReqList
         */
        private void builderMerchantStoreAddressPoi(List<MerchantStoreOpenReq> merchantStoreOpenReqList) {
            Map<String, String> storeAddressMap = new HashMap<>(merchantStoreOpenReqList.size());
            List<AddressQueryReq> addressQueryReqList = new ArrayList<>(merchantStoreOpenReqList.size());
            for (MerchantStoreOpenReq merchantStoreOpenReq : merchantStoreOpenReqList) {
                // 获取门店默认联系人、地址信息
                MerchantStoreOpenReq.ContactReq defaultContactReq = getReqDefaultContact(merchantStoreOpenReq);
                MerchantStoreOpenReq.AddressReq addressReq = defaultContactReq.getAddressInfo();
                // 获取门店poi信息
                AddressQueryReq queryReq = new AddressQueryReq();
                queryReq.setProvince(addressReq.getProvince());
                queryReq.setCity(addressReq.getCity());
                queryReq.setArea(addressReq.getArea());
                queryReq.setAddress(addressReq.getAddress());
                addressQueryReqList.add(queryReq);
                String completeAddress = queryReq.getProvince() + queryReq.getCity() + (net.summerfarm.common.util.StringUtils.isNotBlank(queryReq.getArea()) ? queryReq.getArea() : "") + queryReq.getAddress();
                storeAddressMap.put(merchantStoreOpenReq.getStoreNo(), completeAddress);
            }
            List<LocationResp> contactQueryRespList = lbsQueryFacade.batchQueryPoiByAddress(addressQueryReqList);
            Map<String, LocationResp> poiMap = contactQueryRespList.stream().collect(Collectors.toMap(LocationResp::getCompleteAddress, Function.identity(), (v1, v2) -> v1));

            for (MerchantStoreOpenReq merchantStoreOpenReq : merchantStoreOpenReqList) {
                String addressInfo = storeAddressMap.get(merchantStoreOpenReq.getStoreNo());
                LocationResp locationQueryResp = poiMap.get(addressInfo);
                if (Objects.isNull(locationQueryResp)) {
                    log.info("地址获取poi信息，数据响应异常:addressInfo:{},poiMap:{},merchantStoreOpenReq:{}", addressInfo, JSON.toJSONString(poiMap), JSON.toJSONString(merchantStoreOpenReq));
                    continue;
                }
                MerchantStoreOpenReq.ContactReq defaultContactReq = getReqDefaultContact(merchantStoreOpenReq);
                MerchantStoreOpenReq.AddressReq addressReq = defaultContactReq.getAddressInfo();
                addressReq.setPoiNote(locationQueryResp.getPoi());
                if (StringUtils.isBlank(addressReq.getArea())) {
                    // 若入参area为空，填充优先级 area > township > street > city
                    String poiArea = Optional.ofNullable(locationQueryResp.getGeoResp()).map(GeoResp::getArea).orElse(
                            Optional.ofNullable(locationQueryResp.getGeoResp()).map(GeoResp::getTownship).orElse(
                                    Optional.ofNullable(locationQueryResp.getGeoResp()).map(GeoResp::getStreet).orElse(
                                            Optional.ofNullable(locationQueryResp.getGeoResp()).map(GeoResp::getCity).orElse(null)
                                    )));
                    log.info("填充area结果，门店编号：{}，area为空，填充area入参为:{},locationQueryResp:{}", merchantStoreOpenReq.getStoreNo(), poiArea, JSON.toJSONString(locationQueryResp));
                    addressReq.setArea(poiArea);
                }
                merchantStoreOpenReq.setContactList(Collections.singletonList(defaultContactReq));
            }
        }

        /**
         * 单门店执行变更操作
         * @param tenantId
         * @param merchantStoreResultRespMap
         * @param merchantStoreOpenReq
         * @param regionalOrganization
         * @param addressMap
         * @param addressMerchantContactsMap
         * @return
         */
        private MerchantStoreOpenDTO executeSingleStore(Long tenantId, Map<String, List<MerchantStoreResultResp>> merchantStoreResultRespMap,
                                                        MerchantStoreOpenReq merchantStoreOpenReq, RegionalOrganizationResultResp regionalOrganization,
                                                        Map<Long, MerchantAddressResultResp> addressMap,
                                                        Map<Long, List<MerchantContactResultResp>> addressMerchantContactsMap) {
            List<MerchantStoreResultResp> merchantStoreList = merchantStoreResultRespMap.get(merchantStoreOpenReq.getStoreNo());
            if (CollectionUtils.isNotEmpty(merchantStoreList) && merchantStoreList.size() > NumberConstant.ONE) {
                throw new OpenApiProviderException("当前门店编号数据异常，请稍后再试");
            }

            // 前置校验门店参数信息
            boolean update = CollectionUtils.isNotEmpty(merchantStoreList);
            // 部分租户不传账号信息，需要根据默认联系人创建账号信息
            if (!update) {
                createNewAccountInfoByContactInfo(merchantStoreOpenReq, tenantId);
            }
            checkStoreInfo(merchantStoreOpenReq, update, tenantId);

            // 处理门店地址里 非标准的省市区名称
            merchantAddressHandler.handleNonstandardAddressName(merchantStoreOpenReq);

            if (update) {
                MerchantStoreResultResp storeResp = merchantStoreList.get(NumberConstant.ZERO);
                MerchantAddressResultResp merchantAddressResultResp = addressMap.get(storeResp.getId());
                List<MerchantContactResultResp> merchantContact = null;
                if (Objects.nonNull(merchantAddressResultResp)) {
                    merchantContact = addressMerchantContactsMap.get(merchantAddressResultResp.getId());
                }
                return executeUpdateMerchantStore(merchantStoreOpenReq, tenantId, storeResp, merchantAddressResultResp, merchantContact);
            }
            return executeCreateMerchantStore(merchantStoreOpenReq, tenantId, regionalOrganization);
        }
    };

    /**
     * 创建新账号信息 by 联系人信息
     * @param storeDTO 门店请求参数
     * @param tenantId 租户id
     */
    private void createNewAccountInfoByContactInfo(MerchantStoreOpenReq storeDTO, Long tenantId) {
        if (!specialTenantService.createMerchantAllowEmptyAccount(tenantId)) {
            return;
        }
        if (!CollectionUtils.isEmpty(storeDTO.getAccountList())){
            return;
        }
        MerchantStoreOpenReq.ContactReq contactInfo = storeDTO.getContactList().stream()
                .filter(contactReq -> DefaultFlagEnum.TURE.getFlag().equals(contactReq.getDefaultFlag()))
                .findFirst()
                .orElse(storeDTO.getContactList().get(0));
        if (null == contactInfo){
            return;
        }
        MerchantStoreOpenReq.AccountReq accountReq = new MerchantStoreOpenReq.AccountReq();
        accountReq.setAccountName(contactInfo.getContactName());
        accountReq.setPhone(contactInfo.getPhone());
        accountReq.setType(MerchantAccountTypeEnum.MANAGER.getType());
        storeDTO.setAccountList(Collections.singletonList(accountReq));
    }


    /**
     * 校验租户是否设置账期规则
     * @param merchantStoreOpenReqList
     * @param tenantId
     */
    private void paramCheck(List<MerchantStoreOpenReq> merchantStoreOpenReqList, Long tenantId) {
        int size = merchantStoreOpenReqList.stream().map(MerchantStoreOpenReq::getStoreNo).collect(Collectors.toSet()).size();
        if (merchantStoreOpenReqList.size() > size) {
            throw new BizException("存在重复门店编号");
        }

        // openApi非关闭账期开关,则去校验账期规则
        boolean openBill = merchantStoreOpenReqList.stream().anyMatch(req -> !BillSwitchEnum.SHUTDOWN.getCode().equals(req.getBillSwitch()));
        if (openBill) {
            // 判断品牌方是否已设置账期规则，未设置需要设置
            FinancialBillRuleDTO financialBillRuleDTO = financialBillRuleService.queryTenantFinancialRule(tenantId);
            if (financialBillRuleDTO == null) {
                throw new OpenApiProviderException(OpenApiErrorCode.NOT_SET_BILL_RULE);
            }
        }
    }

    private MerchantStoreOpenDTO executeCreateMerchantStore(MerchantStoreOpenReq merchantStoreOpenReq, Long tenantId, RegionalOrganizationResultResp regionalOrganizationResultResp) {
        MerchantStoreDomainCommandReq merchantStoreDomainCommandReq = new MerchantStoreDomainCommandReq();
        // 门店、地址处理
        MerchantStoreCommandReq merchantStoreCommandReq = MerchantConvertUtil.merchantStoreOpenReqToCreateStoreCommandReq(merchantStoreOpenReq, tenantId);
        MerchantAddressCommandReq merchantAddress = builderMerchantStoreAddress(merchantStoreOpenReq, null, tenantId, null);
        // 调用接口判断门店状态、审核时间
        builderMerchantStoreStatus(merchantStoreCommandReq, null, merchantAddress, tenantId);
        builderAuditTimeIfNeed(merchantStoreCommandReq, null);
        merchantStoreDomainCommandReq.setMerchantStore(merchantStoreCommandReq);
        if (Objects.isNull(regionalOrganizationResultResp)) {
            throw new OpenApiProviderException("租户默认区域组织不存在");
        }
        merchantStoreCommandReq.setRegionalId(regionalOrganizationResultResp.getId());

        // 注册联系人信息
        List<MerchantContactCommandReq> merchantContactCommandReqList = Lists.newArrayList();
        MerchantStoreOpenReq.ContactReq defaultContact = getReqDefaultContact(merchantStoreOpenReq);
        MerchantContactCommandReq merchantContact = new MerchantContactCommandReq();
        merchantContact.setTenantId(tenantId);
        merchantContact.setAddressId(merchantAddress.getId());
        merchantContact.setName(defaultContact.getContactName());
        merchantContact.setPhone(defaultContact.getPhone());
        merchantContact.setDefaultFlag(defaultContact.getDefaultFlag());
        merchantContactCommandReqList.add(merchantContact);

        merchantAddress.setMerchantContactList(merchantContactCommandReqList);
        merchantStoreDomainCommandReq.setMerchantAddressList(Collections.singletonList(merchantAddress));
        // 注册门店账户信息
        List<MerchantStoreAccountCommandReq> merchantStoreAccountList = builderMerchantStoreAccountList(merchantStoreOpenReq, tenantId);

        merchantStoreDomainCommandReq.setMerchantStoreAccountList(merchantStoreAccountList);
        // 若配置进行门店分组的填充
        openApiInitStoreGroupIfNeed(merchantStoreDomainCommandReq, tenantId);
        Long merchantStoreId = userCenterMerchantStoreFacade.createMerchantStoreInfo(merchantStoreDomainCommandReq);
        merchantStoreCommandReq.setId(merchantStoreId);
        // 异步处理通知
        asyncExecuteNotify(merchantStoreCommandReq, Collections.EMPTY_LIST, merchantAddress, null, null);
        return MerchantStoreOpenDTO.builder().storeId(merchantStoreId).storeNo(merchantStoreOpenReq.getStoreNo()).success(true).build();
    }

    /**
     * 组装创建门店账号信息参数
     * @param merchantStoreOpenReq
     * @param tenantId
     * @return
     */
    private List<MerchantStoreAccountCommandReq> builderMerchantStoreAccountList(MerchantStoreOpenReq merchantStoreOpenReq, Long tenantId) {
        // 注册门店账户信息
        List<MerchantStoreAccountCommandReq> merchantStoreAccountList = Lists.newArrayList();
        List<MerchantStoreOpenReq.AccountReq> accountList = merchantStoreOpenReq.getAccountList();
        if (CollectionUtils.isEmpty(accountList)){
            return merchantStoreAccountList;
        }
        for (MerchantStoreOpenReq.AccountReq merchantStoreAccountDTO : accountList) {
            MerchantStoreAccountCommandReq merchantStoreAccount = new MerchantStoreAccountCommandReq();
            merchantStoreAccount.setTenantId(tenantId);
            merchantStoreAccount.setAccountName(merchantStoreAccountDTO.getAccountName());
            merchantStoreAccount.setPhone(merchantStoreAccountDTO.getPhone());
            merchantStoreAccount.setType(merchantStoreAccountDTO.getType());
            merchantStoreAccount.setRegisterTime(LocalDateTime.now());
            merchantStoreAccount.setAuditTime(LocalDateTime.now());
            merchantStoreAccount.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
            merchantStoreAccountList.add(merchantStoreAccount);
        }
        // 账号未设置店长，默认第一个为店长
        boolean hasManage = merchantStoreAccountList.stream().anyMatch(merchantStoreAccountCommandReq -> MerchantAccountTypeEnum.MANAGER.getType().equals(merchantStoreAccountCommandReq.getType()));
        if (!hasManage) {
            merchantStoreAccountList.get(NumberConstant.ZERO).setType(MerchantAccountTypeEnum.MANAGER.getType());
        }
        return merchantStoreAccountList;
    }

    private void builderAuditTimeIfNeed(MerchantStoreCommandReq merchantStoreCommandReq, MerchantStoreResultResp merchantStoreResultResp) {
        // 新增场景、审核成功设置审核时间
        if (Objects.isNull(merchantStoreResultResp)) {
            if (MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode().equals(merchantStoreCommandReq.getStatus())) {
                merchantStoreCommandReq.setAuditTime(LocalDateTime.now());
            }
            return;
        }

        // 更新场景，之前不是审核成功状态、改为审核成功状态，设置审核时间
        if (!MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode().equals(merchantStoreResultResp.getStatus()) && MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode().equals(merchantStoreCommandReq.getStatus())) {
            merchantStoreCommandReq.setAuditTime(LocalDateTime.now());
            return;
        }
    }

    /**
     * 更新门店操作
     *
     * @param merchantStoreOpenReq
     * @param tenantId
     * @param merchantStoreResultResp
     * @param address
     * @param merchantContacts
     * @return
     */
    private MerchantStoreOpenDTO executeUpdateMerchantStore(MerchantStoreOpenReq merchantStoreOpenReq,
                                                            Long tenantId,
                                                            MerchantStoreResultResp merchantStoreResultResp,
                                                            MerchantAddressResultResp address,
                                                            List<MerchantContactResultResp> merchantContacts) {
        // 校验门店信息
        Long storeId = merchantStoreResultResp.getId();
        if (MerchantStoreEnum.Status.CLOSE.getCode().equals(merchantStoreResultResp.getStatus())) {
            throw new BizException("该门店已关闭", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
        }

        MerchantStoreDomainCommandReq merchantStoreDomainCommandReq = new MerchantStoreDomainCommandReq();
        // 门店、地址处理
        MerchantStoreCommandReq merchantStoreCommandReq = MerchantConvertUtil.merchantStoreOpenReqToStoreCommandReq(merchantStoreOpenReq, merchantStoreResultResp);
        if (Objects.isNull(address)) {
            throw new OpenApiProviderException("门店地址信息异常");
        }
        MerchantAddressCommandReq updateAddress = builderMerchantStoreAddress(merchantStoreOpenReq, address, tenantId, storeId);
        // 调用接口判断门店状态、审核时间
        builderMerchantStoreStatus(merchantStoreCommandReq, address, updateAddress, tenantId);
        builderAuditTimeIfNeed(merchantStoreCommandReq, merchantStoreResultResp);
        merchantStoreDomainCommandReq.setMerchantStore(merchantStoreCommandReq);

        // 门店联系人处理
        Pair<List<MerchantContactCommandReq>, List<MerchantContactResultResp>> contactParamPair = builderContactParamPair(merchantContacts, merchantStoreOpenReq, address.getId(), tenantId);
        List<MerchantContactCommandReq> merchantContactList = contactParamPair.getLeft();
        updateAddress.setMerchantContactList(merchantContactList);
        merchantStoreDomainCommandReq.setMerchantAddressList(Collections.singletonList(updateAddress));
        // 需要删除的门店联系人信息
        List<MerchantContactResultResp> needDeleteMerchantContactResultRespList = contactParamPair.getRight();
        // 更新门店不设置账号信息


        merchantStoreDomainCommandReq.setTenantId(tenantId);
        // 若配置进行门店分组的填充
        openApiInitStoreGroupIfNeed(merchantStoreDomainCommandReq, tenantId);
        Boolean result = userCenterMerchantStoreFacade.updateMerchantStoreInfo(merchantStoreDomainCommandReq);
        if (!Boolean.TRUE.equals(result)) {
            throw new OpenApiProviderException("门店下发操作失败");
        }
        asyncExecuteNotify(merchantStoreCommandReq, needDeleteMerchantContactResultRespList, updateAddress, address, merchantStoreResultResp);
        return MerchantStoreOpenDTO.builder().storeId(storeId).storeNo(merchantStoreOpenReq.getStoreNo()).success(true).build();
    }

    /**
     * 若配置进行门店分组的填充
     * @param merchantStoreDomainCommandReq
     * @param tenantId
     */
    private void openApiInitStoreGroupIfNeed(MerchantStoreDomainCommandReq merchantStoreDomainCommandReq, Long tenantId) {
        Map<Long, Long> storeGroupMap = openApiConfig.getOpenApiDefaultStoreGroupMap();
        Long storeGroupId = storeGroupMap.get(tenantId);
        if (Objects.nonNull(storeGroupId)) {
            merchantStoreDomainCommandReq.setGroupId(storeGroupId);
        }
    }

    /**
     * 移除需删除人员信息、飞书群告警
     * @param merchantStoreCommandReq
     * @param needDeleteMerchantContactResultRespList
     */
    private void asyncExecuteNotify(MerchantStoreCommandReq merchantStoreCommandReq,
                                    List<MerchantContactResultResp> needDeleteMerchantContactResultRespList,
                                    MerchantAddressCommandReq address,
                                    MerchantAddressResultResp oldAddress,
                                    MerchantStoreResultResp merchantStoreResultResp) {
        ExecutorFactory.ASYNC_EXECUTOR.execute(() -> {
            if (CollectionUtils.isNotEmpty(needDeleteMerchantContactResultRespList)) {
                List<Long> idList = needDeleteMerchantContactResultRespList.stream().map(MerchantContactResultResp::getId).collect(Collectors.toList());
                MerchantContactCommandReq merchantContactCommandReq = new MerchantContactCommandReq();
                merchantContactCommandReq.setIdList(idList);
                merchantContactCommandReq.setTenantId(merchantStoreCommandReq.getTenantId());
                userCenterMerchantContactFacade.removeBatch(merchantContactCommandReq);
            }

            StoreStatusHandler storeStatusHandler = storeStatusRegistry.getHandlerByTenantId(merchantStoreCommandReq.getTenantId());
            if (Objects.nonNull(storeStatusHandler)) {
                StoreParam storeParam = StoreParam.builder()
                        .merchantStoreCommandReq(merchantStoreCommandReq)
                        .newAddress(address)
                        .oldAddress(oldAddress)
                        .merchantStoreResultResp(merchantStoreResultResp)
                        .tenantId(merchantStoreCommandReq.getTenantId())
                        .build();
                storeStatusHandler.asyncExecuteNotify(storeParam);
                return;
            }

            // 待审核发送飞书群告警消息
            if (MerchantStoreEnum.Status.IN_AUDIT.getCode().equals(merchantStoreCommandReq.getStatus())) {
                StringBuilder text = new StringBuilder();
                text.append("门店待审核提醒").append(Constants.LINE).append("租户ID：").append(merchantStoreCommandReq.getTenantId()).append(Constants.LINE).append("门店ID：").append(merchantStoreCommandReq.getId())
                        .append(Constants.COMMA).append("门店编号：").append(merchantStoreCommandReq.getStoreNo()).append(Constants.COMMA).append("门店名称：").append(merchantStoreCommandReq.getStoreName())
                        .append(Constants.LINE).append("门店地址：").append(address.getProvince()).append(address.getCity()).append(address.getArea()).append(address.getAddress())
                        .append(Constants.LINE).append("门店处于待审核状态，请更新该门店与城配仓绑定关系");

                log.info("发送飞书群告警消息,text:{}", text.toString());
                CommonResult<Boolean> result = SignedFeishuBotUtil.sendTextMsgAndAtAll(openApiConfig.getOpenApiWarnUrl(), text.toString(), openApiConfig.getOpenApiWarnUrlSign());
                log.info("发送飞书群告警消息结果,text:{}", JSON.toJSONString(result));

            }
        });
    }

    /**
     * 构建填充门店状态
     * @param merchantStoreCommandReq
     * @param oldAddress
     * @param newAddress
     * @param tenantId
     */
    private void builderMerchantStoreStatus(MerchantStoreCommandReq merchantStoreCommandReq, MerchantAddressResultResp oldAddress, MerchantAddressCommandReq newAddress, Long tenantId) {
        // 定制租户填充状态逻辑
        StoreStatusHandler storeStatusHandler = storeStatusRegistry.getHandlerByTenantId(tenantId);
        if (Objects.nonNull(storeStatusHandler)) {
            StoreParam storeParam = StoreParam.builder()
                    .merchantStoreCommandReq(merchantStoreCommandReq)
                    .oldAddress(oldAddress)
                    .newAddress(newAddress)
                    .tenantId(tenantId)
                    .build();
            storeStatusHandler.builderOrderDeliveringDTO(storeParam);
            return;
        }

        // 是否在鲜沐配送仓、绑定城配仓
        Pair<Boolean, Boolean> StoreDeliveryPair = queryStoreDeliveryInfo(merchantStoreCommandReq, newAddress, tenantId);
        Boolean bindCityWarehouse = StoreDeliveryPair.getLeft();
        Boolean inXianmuWarehouse = StoreDeliveryPair.getRight();
        // 是否修改门店省市区
        boolean change = changeStoreArea(oldAddress, newAddress);

        // action 1、未绑定城配仓，在鲜沐配送范围
        if (!bindCityWarehouse && inXianmuWarehouse) {
            merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
            return;
        }

        // action 2、未绑定城配仓，不在鲜沐配送范围
        if (!bindCityWarehouse && !inXianmuWarehouse) {
            merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.IN_AUDIT.getCode());
            return;
        }

        // action 3、绑定城配仓,在鲜沐配送范围
        if (bindCityWarehouse && inXianmuWarehouse) {
            // 修改了省市区，删除门店绑定的城配仓关系
            if (change) {
                removeContactConfig(tenantId, merchantStoreCommandReq.getId());
            }
            merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
            return;
        }

        // action 4、已绑定城配仓,新地址不在鲜沐配送范围
        if (bindCityWarehouse && !inXianmuWarehouse) {
            // 修改了省市区，人工确认绑定城配仓关系
            if (change) {
                merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.IN_AUDIT.getCode());
                return;
            }
            merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
        }
    }

    /**
     * 判断是否修改门店省市区信息
     * @param oldAddress
     * @param newAddress
     * @return
     */
    private boolean changeStoreArea(MerchantAddressResultResp oldAddress, MerchantAddressCommandReq newAddress) {
        if (Objects.isNull(oldAddress)) {
            return false;
        }
        return !(StringUtils.equals(oldAddress.getProvince(), newAddress.getProvince()) &&
                StringUtils.equals(oldAddress.getCity(), newAddress.getCity())
                && StringUtils.equals(oldAddress.getArea(), newAddress.getArea()));
    }

    /**
     * 删除城配仓绑定关系
     */
    public void removeContactConfig(Long tenantId, Long storeId) {
        ContactConfigRemoveCommandReq contactConfigQueryReq = new ContactConfigRemoveCommandReq();
        contactConfigQueryReq.setTenantId(tenantId);
        contactConfigQueryReq.setContactId(storeId);
        contactConfigQueryFacade.removeContactConfig(contactConfigQueryReq);
    }

    /**
     * 查询门店是否绑定关系、新地址是否在鲜沐围栏范围内
     * @param merchantStoreCommandReq
     * @param newAddress
     * @param tenantId
     * @return
     */
    private Pair<Boolean, Boolean> queryStoreDeliveryInfo(MerchantStoreCommandReq merchantStoreCommandReq, MerchantAddressCommandReq newAddress, Long tenantId) {
        Boolean bindCityWarehouse = false;
        if (Objects.nonNull(merchantStoreCommandReq.getId())) {
            ContactConfigQueryReq contactConfigQueryReq = new ContactConfigQueryReq();
            contactConfigQueryReq.setTenantId(tenantId);
            contactConfigQueryReq.setContactId(merchantStoreCommandReq.getId());
            ContactConfigResp contactConfigResp = contactConfigQueryFacade.queryContactConfig(contactConfigQueryReq);
            bindCityWarehouse = Objects.nonNull(contactConfigResp) && Objects.nonNull(contactConfigResp.getStoreNo());
        }

        DeliveryFenceQueryReq deliveryFenceQueryReq = new DeliveryFenceQueryReq();
        deliveryFenceQueryReq.setCity(newAddress.getCity());
        deliveryFenceQueryReq.setArea(newAddress.getArea());
        deliveryFenceQueryReq.setPoi(newAddress.getPoiNote());
        DeliveryFenceResp deliveryFenceResp = deliveryFenceQueryFacade.queryDeliveryFence(deliveryFenceQueryReq);
        Boolean inXianmuWarehouse = Objects.nonNull(deliveryFenceResp) && Objects.nonNull(deliveryFenceResp.getFenceId());
        log.info("查询门店地址配送关系结果 storeNo:{},storeName:{},bindCityWarehouse:{},inXianmuWarehouse:{}", merchantStoreCommandReq.getStoreNo(), merchantStoreCommandReq.getStoreName(), bindCityWarehouse, inXianmuWarehouse);
        return Pair.of(bindCityWarehouse, inXianmuWarehouse);
    }

    /**
     * 获取需要更新、删除的联系人列表
     *
     * @param merchantContacts
     * @param merchantStoreOpenReq
     */
    private Pair<List<MerchantContactCommandReq>, List<MerchantContactResultResp>> builderContactParamPair(List<MerchantContactResultResp> merchantContacts, MerchantStoreOpenReq merchantStoreOpenReq, Long addressId, Long tenantId) {
        List<MerchantContactCommandReq> commandReqList = Lists.newArrayList();
        Map<String, MerchantContactResultResp> respMap = merchantContacts.stream().collect(Collectors.toMap(MerchantContactResultResp::getPhone, Function.identity(), (v1, v2) -> v1));
        MerchantStoreOpenReq.ContactReq defaultContact = getReqDefaultContact(merchantStoreOpenReq);

        // 新增/更新的联系人
        MerchantContactCommandReq mc = new MerchantContactCommandReq();
        Optional.ofNullable(respMap.get(defaultContact.getPhone())).ifPresent(resp -> mc.setId(resp.getId()));
        mc.setTenantId(tenantId);
        mc.setAddressId(addressId);
        mc.setName(defaultContact.getContactName());
        mc.setPhone(defaultContact.getPhone());
        mc.setDefaultFlag(defaultContact.getDefaultFlag());
        commandReqList.add(mc);

        // 需删除的联系人
        Map<Long, MerchantContactCommandReq> commandReqMap = commandReqList.stream().filter(merchantContactCommandReq -> Objects.nonNull(merchantContactCommandReq.getId()))
                .collect(Collectors.toMap(MerchantContactCommandReq::getId, Function.identity(), (v1, v2) -> v1));
        List<MerchantContactResultResp> deleteReqList = merchantContacts.stream().filter(merchantContactResultResp -> Objects.isNull(commandReqMap.get(merchantContactResultResp.getId()))).collect(Collectors.toList());
        return Pair.of(commandReqList, deleteReqList);
    }

    /**
     * 组装门店地址信息
     *
     * @param merchantStoreOpenReq
     * @param merchantAddress
     * @param tenantId
     * @param storeId
     * @return
     */
    private MerchantAddressCommandReq builderMerchantStoreAddress(MerchantStoreOpenReq merchantStoreOpenReq, MerchantAddressResultResp merchantAddress, Long tenantId, Long storeId) {
        MerchantStoreOpenReq.AddressReq addressReq = getReqDefaultAddress(merchantStoreOpenReq);
        MerchantAddressCommandReq updateAddress = new MerchantAddressCommandReq();
        updateAddress.setTenantId(tenantId);
        updateAddress.setStoreId(storeId);
        updateAddress.setProvince(addressReq.getProvince());
        updateAddress.setCity(addressReq.getCity());
        updateAddress.setArea(addressReq.getArea());
        updateAddress.setAddress(addressReq.getAddress());
        updateAddress.setPoiNote(addressReq.getPoiNote());

        String newDetailAddress = StringUtils.join(addressReq.getProvince(), addressReq.getCity(), addressReq.getArea(), addressReq.getAddress());
        String existDetailAddress = null;
        if (Objects.nonNull(merchantAddress)) {
            updateAddress.setId(merchantAddress.getId());
            existDetailAddress = StringUtils.join(merchantAddress.getProvince(), merchantAddress.getCity(), merchantAddress.getArea(), merchantAddress.getAddress());
        }
        boolean equals = StringUtils.equals(newDetailAddress, existDetailAddress);
        if (equals && StringUtils.isNotEmpty(merchantAddress.getPoiNote())) {
            updateAddress.setPoiNote(merchantAddress.getPoiNote());
        }
        if (StringUtils.isEmpty(updateAddress.getPoiNote())) {
            throw new ProviderException("根据地址获取门店poi信息异常，请稍后再试");
        }

        updateAddress.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        return updateAddress;
    }

    /**
     * 校验门店信息
     *
     * @param storeDTO
     * @param update true:更新；false：创建
     * @param tenantId
     * @return
     */
    private void checkStoreInfo(MerchantStoreOpenReq storeDTO, boolean update, Long tenantId) {
        if (Objects.isNull(StoreTypeEnum.getByCode(storeDTO.getType()))) {
            throw new BizException("门店类型参数异常", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
        }
        if (StringUtils.isBlank(storeDTO.getStoreName()) || !com.cosfo.manage.common.util.StringUtils.isStoreName(storeDTO.getStoreName())) {
            throw new BizException("门店名称不符合条件，请检查是否包含特殊符号以及门店名称长度", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
        }

        long defaultNum = storeDTO.getContactList().stream().filter(contactReq -> DefaultFlagEnum.TURE.getFlag().equals(contactReq.getDefaultFlag())).count();
        if (defaultNum > NumberConstant.ONE) {
            throw new BizException("默认联系人只能有一个", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
        }
        boolean validateFail = storeDTO.getContactList().stream().anyMatch(el -> !FormatUtils.validatePhone(el.getPhone()));
        if (validateFail) {
            throw new BizException("门店联系人手机号参数格式校验异常", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
        }
        validateFail = storeDTO.getContactList().stream().anyMatch(el -> StringUtils.isNotEmpty(el.getContactName()) && el.getContactName().length() > 150);
        if (validateFail) {
            throw new BizException("门店联系人名称过长", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
        }
        MerchantStoreOpenReq.AddressReq addressInfo = getReqDefaultAddress(storeDTO);
        if (StringUtils.isBlank(addressInfo.getProvince()) || StringUtils.isBlank(addressInfo.getCity())) {
            throw new BizException("门店地址省、市信息不符合条件，请检查是否包含特殊符号以及地址长度", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
        }
        if (StringUtils.isBlank(addressInfo.getArea())) {
            throw new BizException("门店地址区信息不符合条件，无法获取到有效区信息，请检查地址数据是否正常", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
        }

        if (!update) {
            // 账号信息校验
            List<MerchantStoreOpenReq.AccountReq> accountList = storeDTO.getAccountList();
            if (org.springframework.util.CollectionUtils.isEmpty(accountList)) {
                throw new BizException("账号信息必填", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
            }
            validateFail = accountList.stream().anyMatch(el -> Objects.isNull(el.getType()));
            if (validateFail) {
                throw new BizException("门店账号列表账号类型参数不能为空", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
            }
            validateFail = accountList.stream().anyMatch(el -> StringUtils.isEmpty(el.getAccountName()));
            if (validateFail) {
                throw new BizException("门店账号列表账号名称参数不能为空", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
            }
            validateFail = accountList.stream().anyMatch(el -> StringUtils.isNotEmpty(el.getAccountName()) && el.getAccountName().length() > 50);
            if (validateFail) {
                throw new BizException("门店账号列表账号名称过长", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
            }
            long managerNum = accountList.stream().filter(el -> java.util.Objects.equals(el.getType(), MerchantAccountTypeEnum.MANAGER.getType())).count();
            if (managerNum > NumberConstant.ONE) {
                throw new BizException("店长只能有一个", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
            }
            if (accountList.size() > NumberConstants.ELEVEN) {
                throw new BizException("最多有一个店长和十个店员", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
            }
            validateFail = accountList.stream().anyMatch(el -> !FormatUtils.validatePhone(el.getPhone()));
            if (validateFail) {
                throw new BizException("门店账号列表手机号参数格式校验异常", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
            }
            long count = accountList.stream().map(MerchantStoreOpenReq.AccountReq::getPhone).distinct().count();
            if (count < accountList.size()) {
                throw new BizException("同一个门店账号信息下不能有相同的手机号码", OpenApiErrorCode.STORE_BIZ_VALID_ERROR);
            }
        }
    }

    private MerchantStoreOpenReq.AddressReq getReqDefaultAddress(MerchantStoreOpenReq storeDTO) {
        // 获取默认地址
        MerchantStoreOpenReq.ContactReq defaultContactReq = getReqDefaultContact(storeDTO);
        return defaultContactReq.getAddressInfo();
    }

    /**
     * 获取需要保存的联系人信息
     *
     * @param storeDTO
     * @return
     */
    private MerchantStoreOpenReq.ContactReq getReqDefaultContact(MerchantStoreOpenReq storeDTO) {
        // 获取联系人信息
        MerchantStoreOpenReq.ContactReq defaultContactReq = storeDTO.getContactList().stream()
                .filter(contactReq -> DefaultFlagEnum.TURE.getFlag().equals(contactReq.getDefaultFlag())).findFirst()
                .orElse(storeDTO.getContactList().get(NumberConstant.ZERO));
        return defaultContactReq;
    }

    public void batchInitMerchantStoreBalanceIfNeed(Long tenantId, List<MerchantStoreOpenDTO> merchantStoreOpenDTOList) {
        List<MerchantStoreOpenDTO> merchantStoreOpenDTOS = merchantStoreOpenDTOList.stream().filter(dto -> dto.getSuccess()).collect(Collectors.toList());
        List<Long> storeIds = merchantStoreOpenDTOS.stream().map(MerchantStoreOpenDTO::getStoreId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(storeIds)) {
            return;
        }

        List<MerchantStoreBalance> merchantStoreBalances = merchantStoreBalanceMapper.selectByStoreIds(tenantId, storeIds, MerchantStoreBalanceEnums.AccountTypeEnum.CASH.getType());
        Map<Long, MerchantStoreBalance> merchantStoreBalanceMap = merchantStoreBalances.stream()
                .collect(Collectors.toMap(MerchantStoreBalance::getStoreId, Function.identity(), (v1, v2) -> v1));

        List<MerchantStoreBalance> newMerchantStoreBalanceList = Lists.newArrayList();
        for (MerchantStoreOpenDTO merchantStoreOpenDTO : merchantStoreOpenDTOS) {
            if (Objects.nonNull(merchantStoreBalanceMap.get(merchantStoreOpenDTO.getStoreId()))) {
                continue;
            }
            MerchantStoreBalance merchantStoreBalance = new MerchantStoreBalance();
            merchantStoreBalance.setStoreId(merchantStoreOpenDTO.getStoreId());
            merchantStoreBalance.setTenantId(tenantId);
            merchantStoreBalance.setStoreNo(merchantStoreOpenDTO.getStoreNo());
            merchantStoreBalance.setBalance(BigDecimal.ZERO);
            merchantStoreBalance.setAccountType(MerchantStoreBalanceEnums.AccountTypeEnum.CASH.getType());
            newMerchantStoreBalanceList.add(merchantStoreBalance);
        }

        if (!CollectionUtil.isEmpty(newMerchantStoreBalanceList)) {
            merchantStoreBalanceMapper.batchInsert(newMerchantStoreBalanceList);
        }

    }
}

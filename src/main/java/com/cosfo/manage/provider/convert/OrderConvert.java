package com.cosfo.manage.provider.convert;

import com.cosfo.manage.client.order.req.OrderReq;
import com.cosfo.manage.client.order.resp.OrderAddressResp;
import com.cosfo.manage.client.order.resp.OrderItemResp;
import com.cosfo.manage.client.order.resp.OrderResp;
import com.cosfo.manage.order.model.dto.OrderQueryDTO;
import com.cosfo.manage.order.model.vo.OrderAddressVO;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.order.model.vo.OrderVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/21
 */
public class OrderConvert {

    /**
     * 转化为OrderQueryDTO
     *
     * @param orderReq
     * @return
     */
    public static OrderQueryDTO convertToOrderQueryDTO(OrderReq orderReq){

        if (orderReq == null) {
            return null;
        }
        OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
        orderQueryDTO.setOrderId(orderReq.getOrderId());
        orderQueryDTO.setOrderNo(orderReq.getOrderNo());
        return orderQueryDTO;
    }

    public static OrderResp convertToOrderResp(OrderVO orderVO){
        if (orderVO == null) {
            return null;
        }

        OrderResp orderResp = new OrderResp();
        orderResp.setTenantId(orderVO.getTenantId());
        orderResp.setStoreId(orderVO.getStoreId());
        orderResp.setOrderId(orderVO.getOrderId());
        orderResp.setSupplierTenantId(orderVO.getSupplierTenantId());
        orderResp.setSupplierName(orderVO.getSupplierName());
        orderResp.setWarehouseType(orderVO.getWarehouseType());
        orderResp.setStatus(orderVO.getStatus());
        orderResp.setPayablePrice(orderVO.getPayablePrice());
        orderResp.setDeliveryFee(orderVO.getDeliveryFee());
        orderResp.setTotalPrice(orderVO.getTotalPrice());
        orderResp.setOrderTime(orderVO.getOrderTime());
        orderResp.setOrderNo(orderVO.getOrderNo());
        orderResp.setAccountId(orderVO.getAccountId());
        orderResp.setAccountName(orderVO.getAccountName());
        orderResp.setDeliveryTime(orderVO.getDeliveryTime());
        orderResp.setPayType(orderVO.getPayType());
        orderResp.setOnlinePayChannel(orderVO.getOnlinePayChannel());
        orderResp.setPayTime(orderVO.getPayTime());
        orderResp.setTotalAmount(orderVO.getTotalAmount());
        orderResp.setFinishedTime(orderVO.getFinishedTime());
        orderResp.setStoreName(orderVO.getStoreName());
        orderResp.setRemark(orderVO.getRemark());
        orderResp.setApplyEndTime(orderVO.getApplyEndTime());
        orderResp.setAutoFinishedTime(orderVO.getAutoFinishedTime());
        // 地址
        OrderAddressResp orderAddressResp = convertToOrderAddressResp(orderVO.getOrderAddressVO());
        orderResp.setOrderAddressResp(orderAddressResp);
        // 订单项
        List<OrderItemResp> orderItemResps = convertToOrderItemRespList(orderVO.getOrderItemVOS());
        orderResp.setOrderItemResps(orderItemResps);
        return orderResp;
    }

    public static List<OrderItemResp> convertToOrderItemRespList(List<OrderItemVO> orderItemVOS){
        if (orderItemVOS == null) {
            return Collections.emptyList();
        }

        List<OrderItemResp> orderItemRespList = new ArrayList<>();
        for (OrderItemVO orderItemVO : orderItemVOS) {
            orderItemRespList.add(toOrderItemResp(orderItemVO));
        }
        return orderItemRespList;
    }

    public static OrderAddressResp convertToOrderAddressResp(OrderAddressVO orderAddressVO){
        if (orderAddressVO == null) {
            return null;
        }

        OrderAddressResp orderAddressResp = new OrderAddressResp();
        orderAddressResp.setContactName(orderAddressVO.getContactName());
        orderAddressResp.setContactPhone(orderAddressVO.getContactPhone());
        orderAddressResp.setProvince(orderAddressVO.getProvince());
        orderAddressResp.setCity(orderAddressVO.getCity());
        orderAddressResp.setArea(orderAddressVO.getArea());
        orderAddressResp.setAddress(orderAddressVO.getAddress());
        orderAddressResp.setPoiNote(orderAddressVO.getPoiNote());
        return orderAddressResp;
    }

    public static OrderItemResp toOrderItemResp(OrderItemVO orderItemVO) {
        if (orderItemVO == null) {
            return null;
        }
        OrderItemResp orderItemResp = new OrderItemResp();
        orderItemResp.setId(orderItemVO.getId());
        orderItemResp.setOrderId(orderItemVO.getOrderId());
        orderItemResp.setItemId(orderItemVO.getItemId());
        orderItemResp.setSkuId(orderItemVO.getSkuId());
        orderItemResp.setSupplierSkuId(orderItemVO.getSupplierSkuId());
        orderItemResp.setAreaItemId(orderItemVO.getAreaItemId());
        orderItemResp.setAmount(orderItemVO.getAmount());
        orderItemResp.setPrice(orderItemVO.getPrice());
        orderItemResp.setTotalPrice(orderItemVO.getTotalPrice());
        orderItemResp.setSupplierTenantId(orderItemVO.getSupplierTenantId());
        orderItemResp.setSupplierName(orderItemVO.getSupplierName());
        orderItemResp.setTitle(orderItemVO.getTitle());
        orderItemResp.setMainPicture(orderItemVO.getMainPicture());
        orderItemResp.setSpecification(orderItemVO.getSpecification());
        orderItemResp.setSpecificationUnit(orderItemVO.getSpecificationUnit());
        orderItemResp.setWarehouseType(orderItemVO.getWarehouseType());
        orderItemResp.setDeliveryType(orderItemVO.getDeliveryType());
        orderItemResp.setAfterSaleUnit(orderItemVO.getAfterSaleUnit());
        return orderItemResp;
    }
}

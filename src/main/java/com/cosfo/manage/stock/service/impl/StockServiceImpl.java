package com.cosfo.manage.stock.service.impl;

import com.cosfo.manage.stock.mapper.StockMapper;
import com.cosfo.manage.stock.model.po.Stock;
import com.cosfo.manage.stock.service.StockService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/20 13:48
 */
@Service
public class StockServiceImpl implements StockService {

    @Resource
    private StockMapper stockMapper;

    @Override
    public void insert(Stock stock) {
        stockMapper.insertSelective(stock);
    }

    @Override
    public Stock selectByItemId(Long tenantId, Long itemId) {
        return stockMapper.selectByItemId(tenantId, itemId);
    }

    @Override
    public void delete(Long id) {
        stockMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void increaseStock(Long id, Integer addAmount) {
        stockMapper.increaseStock(id, addAmount);
    }

    @Override
    public void update(Stock updateStock) {
        stockMapper.increaseStock(updateStock.getId(), updateStock.getAmount());
    }

    @Override
    public List<Stock> selectByItemIds(Long tenantId, List<Long> ItemIds) {
        return stockMapper.selectByItemIds(tenantId, ItemIds);
    }
}

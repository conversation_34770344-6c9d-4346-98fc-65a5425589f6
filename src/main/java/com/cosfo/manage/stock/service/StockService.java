package com.cosfo.manage.stock.service;

import com.cosfo.manage.stock.model.po.Stock;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/20 13:47
 */
public interface StockService {

    /**
     * 插入
     * @param stock
     */
    void insert(Stock stock);

    /**
     * 根据tenantId和id查询
     * @param tenantId 租户id
     * @param itemId itemId
     * @return
     */
    Stock selectByItemId(Long tenantId, Long itemId);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 更新库存数量
     * @param id 主键
     * @param addAmount 增加数量
     */
    void increaseStock(@Param("id") Long id, @Param("addAmount") Integer addAmount);

    /**
     * 更新库存
     * @param updateStock
     */
    void update(Stock updateStock);

    /**
     * 批量查询库存信息
     *
     * @param tenantId
     * @param ItemIds
     * @return
     */
    List<Stock> selectByItemIds(Long tenantId, List<Long> ItemIds);
}

package com.cosfo.manage.msg.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.sms.model.Sms;
import com.cosfo.manage.msg.model.req.MsgNoticeEditDTO;
import com.cosfo.manage.msg.model.req.MsgNoticeQueryDTO;
import com.cosfo.manage.msg.model.req.MsgNoticeStoreQueryDTO;
import com.cosfo.manage.msg.model.req.ReadOrSupportLogQeryDTO;
import com.cosfo.manage.msg.model.resp.MsgNoticeDetailVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeListVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeMerchantStoreVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeStoreAccountVO;
import com.github.pagehelper.PageInfo;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface NoticeService {

    PageInfo<MsgNoticeListVO> pageNotic(MsgNoticeQueryDTO req, LoginContextInfoDTO contextInfoDTO);

    MsgNoticeDetailVO detail(MsgNoticeQueryDTO req, LoginContextInfoDTO contextInfoDTO);

    Long edit(MsgNoticeEditDTO req, LoginContextInfoDTO contextInfoDTO);

    void delete(Long id, LoginContextInfoDTO contextInfoDTO);

    PageInfo<MsgNoticeMerchantStoreVO> pageStore(MsgNoticeStoreQueryDTO req, LoginContextInfoDTO contextInfoDTO);

    PageInfo<MsgNoticeStoreAccountVO> pageReadOrSupportLog(ReadOrSupportLogQeryDTO dto, LoginContextInfoDTO contextInfoDTO);

    /**
     * 发送sms通知短信
     * @param sms
     */
    @Async
    void sendSmsCode(Sms sms);
}
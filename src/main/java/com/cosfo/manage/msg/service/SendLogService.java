package com.cosfo.manage.msg.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.msg.model.req.MessageUnReadDTO;
import com.cosfo.manage.msg.model.req.MsgTipMarkDTO;
import com.cosfo.manage.msg.model.req.MessageTipPageDTO;
import com.cosfo.manage.msg.model.resp.MsgTipVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-18
 * @Description:
 */
public interface SendLogService {

    /**
     * 分页查询消息信息
     *
     * @param messageTipPageDTO
     * @return
     */
    PageInfo<MsgTipVO> page(MessageTipPageDTO messageTipPageDTO, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 查询共计未读消息
     *
     * @param messageUnReadDTO
     * @return
     */
    Integer countUnRead(MessageUnReadDTO messageUnReadDTO, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 批量标记消息
     *
     * @param msgTipMarkDTO
     */
    void markBatch(MsgTipMarkDTO msgTipMarkDTO, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 获取消息强提醒信息
     * @param
     * @param merchantInfoDTO
     * @return
     */
    List<MsgTipVO> warnList(LoginContextInfoDTO merchantInfoDTO);
}

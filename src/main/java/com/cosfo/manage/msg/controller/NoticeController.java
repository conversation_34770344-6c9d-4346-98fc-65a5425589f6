package com.cosfo.manage.msg.controller;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.msg.model.req.MsgNoticeStoreQueryDTO;
import com.cosfo.manage.msg.model.req.ReadOrSupportLogQeryDTO;
import com.cosfo.manage.msg.model.resp.MsgNoticeDetailVO;
import com.cosfo.manage.msg.model.req.MsgNoticeEditDTO;
import com.cosfo.manage.msg.model.resp.MsgNoticeListVO;
import com.cosfo.manage.msg.model.req.MsgNoticeQueryDTO;
import com.cosfo.manage.msg.model.resp.MsgNoticeMerchantStoreVO;
import com.cosfo.manage.msg.model.resp.MsgNoticeStoreAccountVO;
import com.cosfo.manage.msg.service.NoticeService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 公告
 */
@RestController
@RequestMapping("/msg/notic")
public class NoticeController extends BaseController {
    @Resource
    private NoticeService noticeService;
    /**
     * 公告列表分页查询
     */
    @PostMapping("page")
    public CommonResult<PageInfo<MsgNoticeListVO>> pageNotic(@RequestBody MsgNoticeQueryDTO req) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(noticeService.pageNotic(req,contextInfoDTO));
    }
    /**
     * 公告详情
     */
    @PostMapping("detail")
    public CommonResult<MsgNoticeDetailVO> detail(@RequestBody MsgNoticeQueryDTO req) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(noticeService.detail(req,contextInfoDTO));
    }
    /**
     * 创建公告
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:mall-notice:add", expireError = true)
    @PostMapping("edit")
    public CommonResult<Long> edit(@RequestBody @Valid MsgNoticeEditDTO req) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(noticeService.edit(req,contextInfoDTO));
    }

    /**
     * 删除公告
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:mall-notice:delete", expireError = true)
    @PostMapping("delete")
    public CommonResult<Void> delete(@RequestBody MsgNoticeEditDTO req) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        noticeService.delete(req.getId(),contextInfoDTO);
        return CommonResult.ok();
    }
    /**
     * 门店列表
     */
    @PostMapping("store/page")
    public CommonResult<PageInfo<MsgNoticeMerchantStoreVO>> pageStore(@RequestBody MsgNoticeStoreQueryDTO req) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(noticeService.pageStore(req,contextInfoDTO));
    }
    /**
     * 点赞/阅读 账号记录列表
     */
    @PostMapping("readOrSupportLog/page")
    public CommonResult<PageInfo<MsgNoticeStoreAccountVO>> pageReadOrSupportLog(@RequestBody ReadOrSupportLogQeryDTO req) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(noticeService.pageReadOrSupportLog(req,contextInfoDTO));
    }
}

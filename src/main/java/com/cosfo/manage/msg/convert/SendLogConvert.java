package com.cosfo.manage.msg.convert;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.msg.model.req.MessageTipPageDTO;
import com.cosfo.manage.msg.model.resp.MsgTipBodyVO;
import com.cosfo.manage.msg.model.resp.MsgTipDataVO;
import com.cosfo.manage.msg.model.resp.MsgTipVO;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.ReadStatusEnum;
import com.cosfo.message.client.req.MsgSendLogQueryReq;
import com.cosfo.message.client.resp.MsgNotifySendLogResp;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:
 */
@Slf4j
public class SendLogConvert {

    public static MsgSendLogQueryReq messageTipPageDTO2Req(MessageTipPageDTO messageTipPageDTO, LoginContextInfoDTO merchantInfoDTO) {
        ReadStatusEnum readStatusEnum = ReadStatusEnum.getByStatus(messageTipPageDTO.getReadStatus());
        MsgSendLogQueryReq msgSendLogQueryReq = MsgSendLogQueryReq.builder()
                .tenantId(merchantInfoDTO.getTenantId())
                .readStatusEnum(readStatusEnum).build();
        return msgSendLogQueryReq;
    }

    public static MsgTipVO sendLogRespToVO(MsgNotifySendLogResp msgSendLogResp) {
        MsgTipVO msgTipVO = new MsgTipVO();
        msgTipVO.setId(msgSendLogResp.getId());
        try {
            MsgTipDataVO msgTipDataVO = JSON.parseObject(msgSendLogResp.getData(), MsgTipDataVO.class);
            msgTipVO.setSubTitle(Optional.ofNullable(msgTipDataVO).map(MsgTipDataVO::getSubTitle).orElse(StringUtils.EMPTY));
            List<MsgTipBodyVO> list = Optional.ofNullable(msgTipDataVO).map(MsgTipDataVO::getInputData).orElse(Collections.EMPTY_LIST);
            msgTipVO.setMsgBody(list);
        } catch (Exception e) {
            log.info("数据转换异常，无法正常转换 msgSendLogResp:{} ,e", JSON.toJSONString(msgSendLogResp));
        }
        msgTipVO.setPageId(msgSendLogResp.getPageId());
        msgTipVO.setContentType(msgSendLogResp.getContentType());
        msgTipVO.setReadStatus(msgSendLogResp.getReadStatus());
        msgTipVO.setSendTime(msgSendLogResp.getCreateTime());
        msgTipVO.setTitle(msgSendLogResp.getTitle());
        return msgTipVO;
    }
}

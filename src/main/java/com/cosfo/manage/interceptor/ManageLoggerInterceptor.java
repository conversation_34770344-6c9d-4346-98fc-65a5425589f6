package com.cosfo.manage.interceptor;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-05-11
 * @Description:
 */
@Slf4j
public class ManageLoggerInterceptor implements HandlerInterceptor {

    private static final String PHONE_FORMAT_REGEX = "(\\d{3})\\d{4}(\\d{4})";

    private static final String PHONE_FORMAT_REPLACEMENT = "$1****$2";

    private static final String OK = "/ok";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtils.getMerchantInfoDTO();
        String requestURI = request.getRequestURI();
        if (!requestURI.equals(OK)) {
            log.info("店铺：{}请求了接口：{}", printLoginContext(loginContextInfoDTO), request.getRequestURI());
        }
        return true;
    }

    private static String printLoginContext(LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(loginContextInfoDTO)) {
            return "<empty-login-context>";
        }
        return String.format("(tenantId:%d, phone:%s, authUserId:%d)", loginContextInfoDTO.getTenantId(), formatPhone(loginContextInfoDTO.getPhone()),
                loginContextInfoDTO.getAuthUserId());
    }

    private static String formatPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return phone;
        }
        return phone.replaceAll(PHONE_FORMAT_REGEX, PHONE_FORMAT_REPLACEMENT);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        PageHelper.clearPage();
    }
}

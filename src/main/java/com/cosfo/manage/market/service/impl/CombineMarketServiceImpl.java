package com.cosfo.manage.market.service.impl;

import com.cosfo.manage.facade.CombineMarketFacade;
import com.cosfo.manage.market.model.dto.CombineInputDTO;
import com.cosfo.manage.market.model.dto.CombineMarketDetailDTO;
import com.cosfo.manage.market.model.dto.CombineMarketListDTO;
import com.cosfo.manage.market.model.dto.CombineMarketQueryDTO;
import com.cosfo.manage.market.service.CombineMarketSerivce;
import com.cosfo.manage.tenant.model.vo.TenantAccountVO;
import com.cosfo.manage.tenant.service.TenantAccountService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/5/12 15:53
 * @Description:
 */
@Service
public class CombineMarketServiceImpl implements CombineMarketSerivce {
    @Resource
    private CombineMarketFacade combineMarketFacade;
    @Resource
    private TenantAccountService tenantAccountService;

    @Override
    public PageInfo<CombineMarketListDTO> combineList(CombineMarketQueryDTO queryDTO) {
        PageInfo<CombineMarketListDTO> pageInfo = combineMarketFacade.combineList(queryDTO);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }
        Set<Long> authUserIds = new HashSet<>();
        for (CombineMarketListDTO market : pageInfo.getList()) {
            authUserIds.add(market.getCreateUserId());
            authUserIds.add(market.getEditUserId());
        }
        List<TenantAccountVO> tenantAccountVOS = tenantAccountService.selectByAuthUserIds(new ArrayList<>(authUserIds));
        if (CollectionUtils.isEmpty(tenantAccountVOS)) {
            return pageInfo;
        }
        Map<Long, List<TenantAccountVO>> accountMap = tenantAccountVOS.stream().collect(Collectors.groupingBy(TenantAccountVO::getAuthUserId));
        for (CombineMarketListDTO market : pageInfo.getList()) {
            market.setCreateUserName(getAuthName(accountMap.get(market.getCreateUserId()), queryDTO.getTenantId()));
            market.setEditUserName(getAuthName(accountMap.get(market.getEditUserId()), queryDTO.getTenantId()));
        }

        return pageInfo;
    }

    private String getAuthName(List<TenantAccountVO> accountVOS, Long tenantId) {
        if (Objects.isNull(accountVOS) || Objects.isNull(tenantId)) {
            return Strings.EMPTY;
        }
        return accountVOS.stream()
            .filter(i -> tenantId.equals(i.getTenantId()))
            .findFirst().orElse(new TenantAccountVO())
            .getNickname();
    }

    @Override
    public CombineMarketDetailDTO combineDetail(CombineMarketQueryDTO queryDTO) {
        return combineMarketFacade.combineDetail(queryDTO);
    }

    @Override
    public Long combineAdd(CombineInputDTO inputDTO) {
        return combineMarketFacade.combineAdd(inputDTO);
    }

    @Override
    public Boolean combineUpdate(CombineInputDTO inputDTO) {
        return combineMarketFacade.combineUpdate(inputDTO);
    }
}

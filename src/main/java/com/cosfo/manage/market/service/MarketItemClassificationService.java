package com.cosfo.manage.market.service;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 11:25
 */
public interface MarketItemClassificationService {

//    /**
//     * 更新
//     * @param updateClassification
//     */
//    void updateByPrimaryKey(MarketItemClassification updateClassification);
//
//    /**
//     * 根据tenant和itemId查询
//     * @param tenantId
//     * @param itemId
//     * @return
//     */
//    MarketItemClassification selectByItemId(Long tenantId, Long itemId);
//
//    /**
//     * 插入
//     * @param marketItemClassification
//     */
//    void insert(MarketItemClassification marketItemClassification);
}

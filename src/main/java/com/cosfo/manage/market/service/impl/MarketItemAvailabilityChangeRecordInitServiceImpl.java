package com.cosfo.manage.market.service.impl;

import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cosfo.manage.common.config.MarketItemAvailabilityConfig;
import com.cosfo.manage.common.context.DeleteFlagEnum;
import com.cosfo.manage.common.context.GoodsTypeEnum;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.market.model.dto.MarketItemInfoDTO;
import com.cosfo.manage.market.model.dto.MarketItemQueryDTO;
import com.cosfo.manage.market.model.po.MarketItemAvailabilityChangeRecord;
import com.cosfo.manage.market.service.MarketItemAvailabilityChangeRecordInitService;
import com.cosfo.manage.market.service.MarketItemAvailabilityChangeRecordService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class MarketItemAvailabilityChangeRecordInitServiceImpl implements MarketItemAvailabilityChangeRecordInitService {

    private static final int PAGE_SIZE = 300;

    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private MarketItemAvailabilityChangeRecordService marketItemAvailabilityChangeRecordService;
    @Resource
    private MarketItemAvailabilityConfig marketItemAvailabilityConfig;


    @Override
    public void initOnSaleStatus(Set<Long> tenantIds) {
        tenantIds.forEach(tenantId -> processTenantItems(tenantId, pageInfo -> processOnSaleStatus(tenantId, pageInfo)));
    }

    @Override
    public void initStockStatus(Set<Long> tenantIds) {
        tenantIds.forEach(tenantId -> processTenantItems(tenantId, pageInfo -> processStockStatus(tenantId, pageInfo)));
    }


    private void processTenantItems(Long tenantId, Consumer<PageInfo<MarketItemInfoDTO>> processor) {
        // 批量操作, 每次处理500条
        int pageNum = 1;
        PageInfo<MarketItemInfoDTO> marketItems;
        do {
            marketItems = getMarketItems(tenantId, pageNum);
            processor.accept(marketItems);
            pageNum++;
        } while (pageNum <= marketItemAvailabilityConfig.getMaxLoopCount() // 防止死循环
                && !marketItems.isIsLastPage() && pageNum <= marketItems.getPages());
    }


    private void processOnSaleStatus(Long tenantId, PageInfo<MarketItemInfoDTO> pageInfo) {
        List<MarketItemAvailabilityChangeRecord> records = pageInfo.getList().stream()
                .filter(this::isNotDeleted)
                .map(item -> {
                    OnSaleTypeEnum onSaleType = OnSaleTypeEnum.ON_SALE.getCode().equals(item.getOnSale()) ?
                            OnSaleTypeEnum.ON_SALE : OnSaleTypeEnum.SOLD_OUT;
                    return marketItemAvailabilityChangeRecordService
                            .createItemOnSaleChange(item.getTenantId(), item.getItemId(), onSaleType, LocalDateTime.now());
                }).filter(Objects::nonNull).collect(Collectors.toList());

        marketItemAvailabilityChangeRecordService.batchSaveRecords(records);
        log.info("初始化上下架状态完成，tenantId:{}, size:{}, pageNum: {}, 共{}页", tenantId, records.size(), pageInfo.getPageNum(), pageInfo.getPages());
    }


    private void processStockStatus(Long tenantId, PageInfo<MarketItemInfoDTO> pageInfo) {
        List<MarketItemInfoDTO> notDeletedItems = pageInfo.getList().stream()
                .filter(this::isNotDeleted)
                .collect(Collectors.toList());

        // 区分有仓和无仓商品
        Map<Boolean, List<MarketItemInfoDTO>> partitionedItems = notDeletedItems.stream()
                .collect(Collectors.partitioningBy(this::isNoGoodItem));
        List<MarketItemInfoDTO> hasGoodItems = partitionedItems.get(false);
        List<MarketItemInfoDTO> noGoodItems = partitionedItems.get(true);

        LocalDateTime now = LocalDateTime.now();

        // 无仓商品
        List<MarketItemAvailabilityChangeRecord> noGoodItemRecords = noGoodItems.stream()
                .map(item -> marketItemAvailabilityChangeRecordService.createNoGoodsItemStockChange(tenantId,
                        item.getItemId(), Optional.ofNullable(item.getStockAmount()).orElse(0), now))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 有仓商品
        Map<Long, Long> itemIdSkuIdMap = hasGoodItems.stream()
                .collect(Collectors.toMap(MarketItemInfoDTO::getItemId, MarketItemInfoDTO::getSkuId));
        List<MarketItemAvailabilityChangeRecord> hasGoodItemRecords = marketItemAvailabilityChangeRecordService
                .createHasGoodItemStockChanges(tenantId, itemIdSkuIdMap, now);

        List<MarketItemAvailabilityChangeRecord> records = Stream.concat(noGoodItemRecords.stream(), hasGoodItemRecords.stream())
                .collect(Collectors.toList());
        marketItemAvailabilityChangeRecordService.batchSaveRecords(records);
        log.info("初始化库存状态完成，tenantId:{}, size:{}, pageNum: {}, 共{}页", tenantId, records.size(), pageInfo.getPageNum(), pageInfo.getPages());
    }


    private PageInfo<MarketItemInfoDTO> getMarketItems(Long tenantId, int pageNum) {
        MarketItemQueryDTO queryDTO = new MarketItemQueryDTO();
        queryDTO.setTenantId(tenantId);
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(PAGE_SIZE);
        return marketItemFacade.pageMarketItemAllInfo(queryDTO);
    }

    private boolean isNotDeleted(MarketItemInfoDTO item) {
        return !DeleteFlagEnum.DELETED.getFlag().equals(item.getDeleteFlag());
    }

    private boolean isNoGoodItem(MarketItemInfoDTO item) {
        return item.getSkuId() == null
                || item.getSkuId() == -1
                || GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(item.getGoodsType());
    }

}

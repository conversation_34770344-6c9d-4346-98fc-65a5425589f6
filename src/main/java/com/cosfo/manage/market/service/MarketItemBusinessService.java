package com.cosfo.manage.market.service;

import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;

import java.util.List;
import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
public interface MarketItemBusinessService {


    /**
     * 根据货品Ids批量查询
     *
     * @param tenantId
     * @param skuIds
     * @return
     */
    Map<Long, List<MarketItemOnSaleSimple4StoreResp>> selectSaleMapBySkuIds(Long tenantId, List<Long> skuIds);

    /**
     * 根据货品Ids批量查询
     *
     * @param tenantId
     * @param skuIds
     * @return MarketItemOnSaleSimple4StoreResp
     */
    List<MarketItemOnSaleSimple4StoreResp> selectSaleStatusBySkuIds(Long tenantId, List<Long> skuIds);


    /**
     * 查询租户所有可用的商品id
     * @param tenantId
     * @return
     */
    List<Long> queryAllItemIds(Long tenantId);

    /**
     * 查询上架的商品数
     * @param tenantId
     * @return
     */
    long selectOnSaleMarketItemCounts(Long tenantId, String dateTime);
}

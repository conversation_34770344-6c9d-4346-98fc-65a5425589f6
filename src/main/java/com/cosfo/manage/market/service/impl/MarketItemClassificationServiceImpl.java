package com.cosfo.manage.market.service.impl;

import com.cosfo.manage.market.service.MarketItemClassificationService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 11:25
 */
@Service
public class MarketItemClassificationServiceImpl implements MarketItemClassificationService {

//    @Resource
//    private MarketItemClassificationMapper marketItemClassificationMapper;
//
//    @Override
//    public void updateByPrimaryKey(MarketItemClassification updateClassification) {
//        marketItemClassificationMapper.updateByPrimaryKey(updateClassification);
//    }
//
//    @Override
//    public MarketItemClassification selectByItemId(Long tenantId, Long itemId) {
//        return marketItemClassificationMapper.selectByItemId(tenantId, itemId);
//    }
//
//    @Override
//    public void insert(MarketItemClassification marketItemClassification) {
//        marketItemClassificationMapper.insert(marketItemClassification);
//    }
}

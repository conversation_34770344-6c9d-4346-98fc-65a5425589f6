package com.cosfo.manage.market.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.context.MarketDeleteFlagEnum;
import com.cosfo.manage.common.context.MarketItemPresaleSwitchEnum;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.market.converter.MarketConvert;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.vo.*;
import com.cosfo.manage.market.service.CombineMarketSerivce;
import com.cosfo.manage.market.service.MarketItemService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 17:48
 * @Description: 组合包管理
 */
@RestController
@RequestMapping("/market/combine")
public class MarketCombineController extends BaseController {
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private CombineMarketSerivce combineMarketSerivce;


    /**
     * 组合商品 列表
     *
     * @return
     */
    @RequiresPermissions(value = {"cosfo_manage:combine-market:query"}, logical = Logical.OR)
    @PostMapping(value = "/query/list")
    public CommonResult<PageInfo<CombineMarketListResVO>> combineList(@RequestBody CombineMarketQueryReqVO reqVO) {
        CombineMarketQueryDTO combineMarketQueryDTO = MarketConvert.INSTANCE.convert2queryDto(reqVO);
        combineMarketQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        PageInfo<CombineMarketListDTO> pageInfo = combineMarketSerivce.combineList(combineMarketQueryDTO);
        return CommonResult.ok(MarketConvert.INSTANCE.convert2listResVo(pageInfo));
    }

    /**
     * 组合商品 详情
     *
     * @return
     */
    @PostMapping(value = "/query/detail")
    public CommonResult<CombineMarketDetailVO> combineDetail(Long combineMarketId) {
        CombineMarketDetailDTO detailDTO = combineMarketSerivce.combineDetail(CombineMarketQueryDTO.builder()
            .combineMarketId(combineMarketId)
            .tenantId(getMerchantInfoDTO().getTenantId())
            .build());
        return CommonResult.ok(MarketConvert.INSTANCE.convert2CombineDetailVo(detailDTO));
    }

    /**
     * 组合商品 新增
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/add")
    public CommonResult<Long> combineAdd(@RequestBody MarketCombineItemInputVO input) {
        CombineInputDTO inputDTO = MarketConvert.INSTANCE.convert2InputDto(input);
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        inputDTO.setTenantId(merchantInfoDTO.getTenantId());
        inputDTO.setCreateUserId(merchantInfoDTO.getAuthUserId());
        inputDTO.setEditUserId(merchantInfoDTO.getAuthUserId());

        return CommonResult.ok(combineMarketSerivce.combineAdd(inputDTO));
    }

    /**
     * 组合商品 修改
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/update")
    public CommonResult<Boolean> combineUpdate(@RequestBody MarketCombineItemInputVO input) {
        CombineInputDTO inputDTO = MarketConvert.INSTANCE.convert2InputDto(input);
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        inputDTO.setTenantId(merchantInfoDTO.getTenantId());
        inputDTO.setEditUserId(merchantInfoDTO.getAuthUserId());
        return CommonResult.ok(combineMarketSerivce.combineUpdate(inputDTO));
    }
    /**
     * 组合商品 选择商品
     *
     * ！！如果涉及到相关功能修改请前端配合移除此接口替换为/choose/page接口，并配合修改相应入参 2024。4。25
     * @return
     */
    @PostMapping(value = "/item/list")
    public CommonResult<PageInfo<MarketItemInfoPageVO>> combineItemList(@RequestBody CombineItemReqVO reqVO) {
        MarketItemQueryDTO marketItemQueryDTO = MarketConvert.INSTANCE.convert2QueryDto(reqVO);
        marketItemQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        marketItemQueryDTO.setCombineFlag(Boolean.FALSE);
        marketItemQueryDTO.setDeleteFlag (MarketDeleteFlagEnum.NORMAL.getFlag());
        // 组合商品不能选预售商品
        marketItemQueryDTO.setPresaleSwitch(MarketItemPresaleSwitchEnum.NOT_PRE_SALE.getType());

        MarketItemInfoQueryFlagDTO flagDTO = new MarketItemInfoQueryFlagDTO();
        flagDTO.setPriceRangeFlag (true);

        return CommonResult.ok(marketItemService.queryMarketItemList(marketItemQueryDTO,flagDTO));
    }
}

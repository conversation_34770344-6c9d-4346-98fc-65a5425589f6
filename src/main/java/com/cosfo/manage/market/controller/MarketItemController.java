package com.cosfo.manage.market.controller;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.context.DeleteFlagEnum;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.market.converter.MarketItemConverter;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.input.PageMarketItemInput;
import com.cosfo.manage.market.model.vo.*;
import com.cosfo.manage.market.service.MarketItemService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售商品项管理
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/11
 */
@RestController
@RequestMapping("/market/item")
public class MarketItemController extends BaseController {
    @Resource
    private MarketItemService marketItemService;

    /**
     * 详情
     *
     * @param itemId
     * @return
     */
    @PostMapping("query/detail")
    public CommonResult<MarketItemVO> detail(Long itemId) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        MarketItemDTO marketItemDto = marketItemService.detail(contextInfoDTO.getTenantId(), itemId);
        MarketItemVO marketItemVO = MarketItemConverter.convertToMarketItemVO(marketItemDto);
        return CommonResult.ok(marketItemVO);
    }
    /**
     * 新增
     *
     * @param marketItemInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/add")
    public CommonResult<Long> add(@RequestBody @Valid MarketItemInput marketItemInput) {
        return CommonResult.ok(marketItemService.save(marketItemInput, getMerchantInfoDTO().getTenantId()));
    }
    /**
     * 修改
     *
     * @param marketItemInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/upsert")
    public CommonResult upsert(@RequestBody @Valid MarketItemInput marketItemInput) {
        marketItemService.update(marketItemInput, getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 列表
     *
     * @param marketItemQueryInput
     * @return
     */
    @PostMapping("/query/list")
    public CommonResult<List<MarketItemVO>> list(@RequestBody MarketItemQueryInput marketItemQueryInput) {
        List<MarketItemDTO> marketItemDTOS = marketItemService.list(marketItemQueryInput, getMerchantInfoDTO());
        List<MarketItemVO> marketItemVOS = marketItemDTOS.stream().map(MarketItemConverter::convertToMarketItemVO).collect(Collectors.toList());
        return CommonResult.ok(marketItemVOS);
    }

    /**
     * 上下架
     *
     * @param marketItemInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/changOnSale", method = RequestMethod.POST)
    public CommonResult changOnSale(@RequestBody MarketItemOnSaleInput marketItemInput) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return marketItemService.changOnSale(marketItemInput, contextInfoDTO);
    }

    /**
     * 商品删除
     *
     * @param marketDeleteDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/delete")
    public CommonResult delete(@RequestBody MarketDeleteDTO marketDeleteDTO) {
        marketDeleteDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        marketItemService.delete(marketDeleteDTO);
        return CommonResult.ok();
    }

    /**
     * 批量上下架
     *
     * @param marketItemInput
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/query/batch-change-on-sale")
    public CommonResult<BatchOnSaleResultVO> batchChangOnSale(@RequestBody MarketItemOnSaleInput marketItemInput) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(marketItemService.batchChangOnSale(marketItemInput, contextInfoDTO));
    }

    /**
     * 查询单个item的价格策略
     *
     * @param itemId
     * @return
     */
    @PostMapping(value = "/query/item-price-strategy")
    public CommonResult<MarketItemPriceStrategyVO> queryItemPriceStrategy(Long itemId) {
        MarketItemDTO marketItemDTO = marketItemService.queryItemPriceStrategy(getMerchantInfoDTO().getTenantId(), itemId);
        return CommonResult.ok(MarketItemConverter.convert2MarketItemPriceVO(marketItemDTO));
    }

    /**
     * 修改单个item的价格策略
     *
     * @param input
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/item-price-strategy")
    public CommonResult<Boolean> updatePriceStrategy(@RequestBody MarketItemPriceStrategyInput input) {
        input.setTenantId(getMerchantInfoDTO().getTenantId());
        marketItemService.updatePriceStrategy(input);
        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 批量修改价格策略
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "batch/update/priceStrategy")
    public CommonResult batchUpdatePriceStrategy(@RequestBody @Valid PriceStrategyFloatingRangeDTO dto) {
        marketItemService.batchUpdatePriceStrategy(getMerchantInfoDTO().getTenantId(), dto);
        return CommonResult.ok();
    }

    /**
     * excel批量修改价格策略
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "batch/import/priceStrategy")
    public CommonResult<ExcelImportResDTO> importPriceStrategy(@RequestBody MultipartFile file) throws IOException {
        ExcelImportResDTO excelImportResDTO = marketItemService.importPriceStrategy(getMerchantInfoDTO().getTenantId(), file);
        return CommonResult.ok(excelImportResDTO);
    }

    /**
     * excel批量修改上下架
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:goods-market-putaway:importIn", expireError = true)
    @PostMapping(value = "batch/import/onSaleStrategy")
    public CommonResult<ExcelImportResDTO> importOnSaleStrategy(@RequestBody MultipartFile file) throws IOException {
        ExcelImportResDTO excelImportResDTO = marketItemService.importOnSaleStrategy(getMerchantInfoDTO().getTenantId(), file);
        return CommonResult.ok(excelImportResDTO);
    }

    /**
     * 批量查询 原价格策略区间
     */
    @PostMapping(value = "batch/query/priceStrategy")
    public CommonResult<PriceStrategyRangeVO> batchQueryPriceStrategy(@RequestBody @Valid PriceStrategyBatchQueryDTO dto) {
        return CommonResult.ok(marketItemService.batchQueryPriceStrategy(getMerchantInfoDTO().getTenantId(), dto.getMarketItemIds()));
    }

    /**
     * 查询最高成本价
     */
    @PostMapping(value = "query/maxCostPrice")
    public CommonResult<BigDecimal> queryMaxCostPrice(@Valid @RequestBody MaxCostPriceQueryDTO dto) {
        return CommonResult.ok(marketItemService.queryMaxCostPrice(getMerchantInfoDTO().getTenantId(), dto));
    }

    /**
     * 商品分页查询
     * 运费模版处使用
     *
     * ！！如果涉及到相关功能修改请前端配合移除此接口替换为/choose/page接口 并配合修改相应入参 2024。4。25
     * @param
     * @return
     */
    @PostMapping("/query/page")
    @Deprecated
    public CommonResult<PageInfo<MarketItemInfoPageVO>> queryMarketItemPage(@RequestBody MarketItemPageQueryInput input) {
//        queryInput.setTenantId(getMerchantInfoDTO().getTenantId());
//        return CommonResult.ok(marketItemService.queryMarketItemPage(queryInput));
        MarketItemQueryDTO queryDTO = new MarketItemQueryDTO ();
        queryDTO.setTenantId (getMerchantInfoDTO().getTenantId());
        queryDTO.setTitle(input.getTitle());
        queryDTO.setId (input.getItemId ());
        queryDTO.setClassificationId(input.getClassificationId());
        queryDTO.setItemIds (input.getItemIds ());
        queryDTO.setPageNum (input.getPageIndex());
        queryDTO.setPageSize(input.getPageSize());
        queryDTO.setCombineFlag(Boolean.FALSE);
        queryDTO.setDeleteFlag (DeleteFlagEnum.NORMAL.getFlag ());
        MarketItemInfoQueryFlagDTO flagDTO = new MarketItemInfoQueryFlagDTO();
        flagDTO.setClassificationIdFlag(true);
        return CommonResult.ok(marketItemService.queryMarketItemList(queryDTO,flagDTO));
    }

    /**
     * 选择商品通用组件的 分页查询
     * 代下单使用
     * 如果涉及到相关功能修改请前端配合替换此接口入参， 统一使用MarketItemQueryDTO 2024。4。25
     * @return
     */
    @PostMapping("/choose/page")
    public CommonResult<PageInfo<MarketItemInfoPageVO>> queryMarketItemPage4common(@RequestBody PageMarketItemInput input) {
//        return CommonResult.ok(marketItemService.queryMarketItemPage(pageInput));
        MarketItemQueryDTO queryDTO = new MarketItemQueryDTO ();
        queryDTO.setTenantId (getMerchantInfoDTO().getTenantId());
        queryDTO.setCombineFlag(Boolean.FALSE);
        queryDTO.setGoodsTypes (input.getGoodsType ());
        queryDTO.setPresaleSwitch(input.getPresaleSwitch());

        queryDTO.setTitle(input.getTitle());
        queryDTO.setId (input.getItemId ());
        queryDTO.setClassificationId(input.getClassificationId());
        if(ObjectUtil.isNotNull (input.getCategoryId())) {
            queryDTO.setCategoryIds (Collections.singletonList (input.getCategoryId()));
        }
        queryDTO.setItemCode(input.getItemCode());
        queryDTO.setBrandName(input.getBrandName());
        queryDTO.setGoodsTypes(input.getGoodsType());
        queryDTO.setOnSale(input.getOnSale());
        queryDTO.setDeleteFlag (DeleteFlagEnum.NORMAL.getFlag ());
        queryDTO.setPageNum (input.getPageIndex());
        queryDTO.setPageSize(input.getPageSize());
        MarketItemInfoQueryFlagDTO flagDTO = new MarketItemInfoQueryFlagDTO();
        flagDTO.setClassificationIdFlag(true);
        flagDTO.setPriceRangeFlag (true);
        flagDTO.setCategoryIdFlag (true);
        flagDTO.setUnitFlag (true);
        return CommonResult.ok(marketItemService.queryMarketItemList(queryDTO,flagDTO));
    }


    /**
     * excel批量修改无货商品成本价
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/batch/import/noGoodsSupplyPrice")
    public CommonResult<ExcelImportResDTO> importNoGoodsSupplyPrice(@RequestBody MultipartFile file) throws IOException {
        ExcelImportResDTO excelImportResDTO = marketItemService.importNoGoodsSupplyPrice(getMerchantInfoDTO().getTenantId(), file);
        return CommonResult.ok(excelImportResDTO);
    }

    /**
     * 修改商品自有编码
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/item-code")
    public CommonResult updateItemCode(@RequestBody @Valid ItemCodeUpdateDTO dto) throws IOException {
        marketItemService.updateItemCode(getMerchantInfoDTO().getTenantId(), dto);
        return CommonResult.ok();
    }
}

package com.cosfo.manage.market.model.vo;

import com.cosfo.manage.market.model.dto.LadderPriceDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketAreaItemMappingVO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 倒挂值：固定价 - 最高成本价
     */
    private BigDecimal differenceValue;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 映射区域item id
     */
    private Long areaItemId;

    /**
     * 定价方式0统一价1其他价
     */
    private Integer storePriceType;

    /**
     * 价格类型：1-默认价格 2-指定分组 3-指定门店
     */
    private Integer priceType;

    /**
     * 0、百分比上浮 1、定额上浮 2、固定价 3、组合百分比下调4、组合定额下调
     */
    private Integer type;

    /**
     * 价格配置数额
     */
    private BigDecimal mappingNumber;

    /**
     * 最小价格
     */
    private BigDecimal minPrice;

    /**
     * 最大价格
     */
    private BigDecimal maxPrice;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 门店列表
     */
    private List<MerchantStoreDTO> merchantStoreDtoList;

    /**
     * 门店Ids
     */
    private List<Long> storeIds;

    /**
     * 分组列表
     */
    private List<MerchantStoreGroupVO> storeGroupList;

    /**
     * 门店分组Ids
     */
    private List<Long> storeGroupIds;

    /**
     * 备注
     */
    private String remark;

    /**
     * 总门店数
     */
    private Integer totalStoreCount;
    /**
     * 阶梯价
     * eg：[{"price":197.14,"unit":1}]
     */
    private List<LadderPriceDTO> ladderPrices;
}

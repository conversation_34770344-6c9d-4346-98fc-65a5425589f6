package com.cosfo.manage.market.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 门店商品定价策略映射
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Getter
@Setter
@TableName("market_area_item_store_price_mapping")
public class MarketAreaItemStorePriceMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 门店Id
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 价格定价方式Id
     */
    @TableField("area_item_mapping_id")
    private Long areaItemMappingId;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}

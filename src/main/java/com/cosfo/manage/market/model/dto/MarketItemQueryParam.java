package com.cosfo.manage.market.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/11 17:56
 * @Description: market_item 查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketItemQueryParam {


    /**
     * market_id
     */
    private List<Long> marketIds;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 货源类型
     * @see com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;
}

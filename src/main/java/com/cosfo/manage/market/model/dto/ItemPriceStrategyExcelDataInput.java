package com.cosfo.manage.market.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cosfo.manage.common.easy.excel.model.RowIndex;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/26 23:01
 */
@Data
public class ItemPriceStrategyExcelDataInput {
    @ExcelProperty("商品编码")
    private Long id;

    @ExcelProperty("定价方式")
    private String strategyType;

    @ExcelProperty("定价值")
    private BigDecimal strategyValue;
    /**
     * 错误信息
     */
    @ExcelIgnore
    private String errorMessage;
}

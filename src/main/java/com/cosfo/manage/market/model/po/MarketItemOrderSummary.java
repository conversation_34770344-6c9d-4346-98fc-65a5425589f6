package com.cosfo.manage.market.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * item下单汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Getter
@Setter
@TableName("market_item_order_summary")
public class MarketItemOrderSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 货品id
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 交易金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 交易件数
     */
    @TableField("order_quantity")
    private Integer orderQuantity;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 交易时间
     */
    @TableField("order_time")
    private LocalDate orderTime;

}

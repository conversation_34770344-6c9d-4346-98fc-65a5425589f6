package com.cosfo.manage.market.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cosfo.manage.common.constant.RegularExpressionConstant;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Data
public class MarketItemCodeMappingDTO {

    /**
     * 租户id
     */
    @ExcelIgnore
    private Long tenantId;

    /**
     * spu
     */
    @ExcelProperty("SPU")
    private String spu;

    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    private String marketItemName;

    /**
     * 规格名称
     */
    @ExcelProperty("规格名称")
    private String marketItemSpecName;

    @ExcelProperty("商品编码\n" +
            "(必填)")
    private String marketItemCode;


    @ExcelProperty("三方物品编码\n" +
            "(必填)")
    private String outItemCode;

    /**
     * 内部系统code
     */
    @ExcelProperty("三方物品名称")
    private String outItemName;

    @ExcelProperty("规格型号")
    private String outItemSpecName;
    @ExcelProperty("POS渠道")
    private String posChannel;

    @ExcelIgnore
    private String errorMsg;

    public boolean validateFields() {
        if (this.marketItemCode == null || this.marketItemCode.isEmpty()) {
            this.errorMsg = "商品编码不能为空.";
            return false;
        } else if (RegularExpressionConstant.SPECIAL_CHAR_PATTERN.matcher(marketItemCode).find()) {
            this.errorMsg = "商品编码不能包含特殊字符.";
            return false;
        } else if (this.marketItemCode.length() > 60) {
            this.errorMsg = "商品编码长度不能超过60.";
            return false;

        }
        if (this.outItemCode == null || this.outItemCode.isEmpty()) {
            this.errorMsg = "三方物品编码不能为空.";
            return false;
        } else if (RegularExpressionConstant.SPECIAL_CHAR_PATTERN.matcher(outItemCode).find()) {
            this.errorMsg = "三方物品编码不能包含特殊字符.";
            return false;
        } else if (this.outItemCode.length() > 60) {
            this.errorMsg = "三方物品编码长度不能超过60.";
            return false;
        }

        if (!StringUtils.isEmpty(marketItemSpecName) && RegularExpressionConstant.SPECIAL_CHAR_PATTERN.matcher(marketItemSpecName).find()) {
            this.errorMsg = "规格名称不能包含特殊字符.";
            return false;
        } else if (!StringUtils.isEmpty(marketItemSpecName) && marketItemSpecName.length() > 60) {
            this.errorMsg = "规格名称长度不能超过60.";
            return false;
        }

        this.errorMsg = null;
        return true;
    }
}

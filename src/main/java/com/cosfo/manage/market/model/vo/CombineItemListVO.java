package com.cosfo.manage.market.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/11
 */
@Data
public class CombineItemListVO {
    /**
     * 组合包编码
     */
    private Long itemId;
    /**
     * 组合包标题
     */
    private String title;
    /**
     * 二级分类Id
     */
    private Long secondClassificationId;
    /**
     * 二级分类
     */
    private String secondClassificationName;
    /**
     * 一级分类Id
     */
    private Long firstClassificationId;
    /**
     * 一级分类名称
     */
    private String firstClassificationName;
    /**
     * 类目Id
     */
    private Long firstCategoryId;
    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目Id
     */
    private Long secondCategoryId;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目Id
     */
    private Long thirdCategoryId;
    /**
     * 三级类目
     */
    private String thirdCategory;
    /**
     * 0 下架 1 上架
     */
    private Integer onSale;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 最后编辑人
     */
    private String editUserName;
    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}

package com.cosfo.manage.market.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/17
 */
@Data
public class MarketAreaItemMappingDTO {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 门店价格类型 0,统一价1其他价
     */
    private Integer storePriceType;

    /**
     * 0、百分比上浮 1、定额上浮 2、固定价
     */
    private Integer type;

    /**
     * 配置数额
     */
    private BigDecimal mappingNumber;

    /**
     * 门店Ids
     */
    private List<Long> storeIds;
}

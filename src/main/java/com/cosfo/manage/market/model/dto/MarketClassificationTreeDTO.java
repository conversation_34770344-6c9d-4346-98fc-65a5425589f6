package com.cosfo.manage.market.model.dto;

import com.cosfo.manage.market.model.po.MarketClassification;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description 分类树 DTO
 * <AUTHOR>
 * @date 2022/5/10 15:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketClassificationTreeDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 父类id
     */
    private Long parentId;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 子级分类
     */
    private List<MarketClassification> childList;
}

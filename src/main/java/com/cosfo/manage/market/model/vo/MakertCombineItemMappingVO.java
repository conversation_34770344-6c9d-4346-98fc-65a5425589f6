package com.cosfo.manage.market.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/4/26 14:41
 * @Description:
 */
@Data
public class MakertCombineItemMappingVO implements Serializable {
    private static final long serialVersionUID = -2593170674136143886L;

    /**
     * 组合品映射表的主键
     */
    private Long mappingId;

    /**
     * market_item 商品编码
     */
    private Long itemId;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 商城售卖价
     */
    private String priceStr;
    /**
     * 最大售价
     */
    private BigDecimal maxPrice;
    /**
     * 最小售价
     */
    private BigDecimal minPrice;
    /**
     * 数量
     */
    private Integer quantity;
}

package com.cosfo.manage.market.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cosfo.manage.common.easy.excel.model.RowIndex;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ItemOnSaleStrategyExcelDataInput {
    @ExcelProperty("商品编码")
    private Long id;

    @ExcelProperty("选择上下架")
    private String onsaleType;
    /**
     * 错误信息
     */
    @ExcelIgnore
    private String errorMessage;
}

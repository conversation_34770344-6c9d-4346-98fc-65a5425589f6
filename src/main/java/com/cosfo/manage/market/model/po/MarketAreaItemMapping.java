package com.cosfo.manage.market.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 城市销售商品配置实体类
 * <AUTHOR>
 * @date 2022/5/12 17:57:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketAreaItemMapping implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 映射区域item id
     */
    private Long areaItemId;

    /**
     * 定价方式0统一价1其他价
     */
    private Integer storePriceType;

    /**
     * 0、百分比上浮 1、定额上浮 2、固定价
     */
    private Integer type;

    /**
     * 价格配置数额
     */
    private BigDecimal mappingNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}

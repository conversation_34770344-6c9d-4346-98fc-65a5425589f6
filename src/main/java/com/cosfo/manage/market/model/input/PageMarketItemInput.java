package com.cosfo.manage.market.model.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/27 14:10
 * @Description:
 */
@Data
public class PageMarketItemInput extends BasePageInput implements Serializable {
    private static final long serialVersionUID = 4661635979210289028L;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 前台分类
     */
    private Long classificationId;

    /**
     * 后台类目
     */
    private Long categoryId;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 货品类型
     */
    private List<Integer> goodsType;

    /**
     * 上下架状态 0-下架 1-上架
     *
     * @see com.cofso.item.client.enums.OnSaleTypeEnum
     */
    private Integer onSale;

    /**
     * 商品预售开关 0-不可预售 1-可预售
     */
    private Integer presaleSwitch;
}

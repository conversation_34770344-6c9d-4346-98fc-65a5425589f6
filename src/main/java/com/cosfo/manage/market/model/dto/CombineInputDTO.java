package com.cosfo.manage.market.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/12 16:27
 * @Description:
 */
@Data
public class CombineInputDTO {

    /**
     * tenantId
     */
    private Long tenantId;

    /**
     * 组合包ID
     */
    private Long combineMarketId;
    /**
     * 组合包标题
     */
    private String combineMarketTitle;
    /**
     * 副标题
     */
    private String combineMarketSubTitle;
    /**
     * 分类Id
     */
    private Long classificationId;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 组合包itemId
     */
    private Long combineItemId;
    /**
     * 0 下架 1 上架
     */
    private Integer onSale;
    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价 3组合品按总价下调固定额度,4组合品按总价下调百分比 5组合品总价
     */
    private Integer priceType;
    /**
     * 价格调整金额/比例
     */
    private BigDecimal strategyValue;
    /**
     * 组合商品
     */
    private List<CombineItemDTO> combineItemList;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 最后编辑人
     */
    private Long editUserId;
}

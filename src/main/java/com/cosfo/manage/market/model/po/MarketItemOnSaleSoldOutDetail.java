package com.cosfo.manage.market.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商品上架售罄汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Getter
@Setter
@TableName("market_item_on_sale_sold_out_detail")
public class MarketItemOnSaleSoldOutDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 时间标签（yyyyMMdd）
     */
    @TableField("time_tag")
    private String timeTag;

    /**
     * 商品id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 售价
     */
    @TableField("sales_price")
    private BigDecimal salesPrice;

    /**
     * 售罄时长（秒级）
     */
    @TableField("sold_out_time")
    private Integer soldOutTime;

    /**
     * 上架时长（秒级）
     */
    @TableField("on_sale_time")
    private Integer onSaleTime;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}

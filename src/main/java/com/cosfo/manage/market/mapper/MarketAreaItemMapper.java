package com.cosfo.manage.market.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.market.model.dto.MarketAreaItemDTO;
import com.cosfo.manage.market.model.po.MarketAreaItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @discrition 城市销售商品持久层
 * <AUTHOR>
 * @date 2022/5/12 18:07
 */
@Repository
@Mapper
public interface MarketAreaItemMapper extends BaseMapper<MarketAreaItem> {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(MarketAreaItem record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(MarketAreaItem record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    MarketAreaItem selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(MarketAreaItem record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(MarketAreaItem record);

    /**
     * 根据skuId查询
     * @param tenantId
     * @param skuId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    MarketAreaItem selectByTenantAndSkuId(@Param("tenantId") Long tenantId, @Param("skuId") Long skuId);

    /**
     * 批量查询
     *
     * @param tenantId
     * @param skuIds
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<MarketAreaItemDTO> queryMarketAreaItemInfo(@Param("tenantId") Long tenantId, @Param("skuIds") List<Long> skuIds);

    /**
     * 根据租户和skuId查询
     *
     * @param tenantId
     * @param skuId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    MarketAreaItemDTO queryByTenantAndSkuId(@Param("tenantId") Long tenantId, @Param("skuId") Long skuId);
}

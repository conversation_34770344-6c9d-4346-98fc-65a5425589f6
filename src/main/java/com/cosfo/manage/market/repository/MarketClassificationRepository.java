package com.cosfo.manage.market.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.market.model.po.MarketClassification;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/20
 */
public interface MarketClassificationRepository extends IService<MarketClassification> {

    /**
     * 根据marketId查询分类
     *
     * @param marketId
     * @param tenantId
     * @return
     */
//    MarketClassification queryByMarketId(Long marketId, Long tenantId);


}

package com.cosfo.manage.market.repository.impl;

/**
 * <p>
 * 门店商品定价策略映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
//@Service
//public class MarketAreaItemStorePriceMappingRepositoryImpl extends ServiceImpl<MarketAreaItemStorePriceMappingMapper, MarketAreaItemStorePriceMapping> implements MarketAreaItemStorePriceMappingRepository {
//
//    @Override
//    public void deleteByAreaItemMappingId(Long areaItemMappingId, Long tenantId) {
//        LambdaQueryWrapper<MarketAreaItemStorePriceMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MarketAreaItemStorePriceMapping::getAreaItemMappingId, areaItemMappingId);
//        queryWrapper.eq(MarketAreaItemStorePriceMapping::getTenantId, tenantId);
//        remove(queryWrapper);
//    }
//
//    @Override
//    public List<MarketAreaItemStorePriceMapping> batchQuery(List<Long> areaItemMappingIds, Long tenantId) {
//        LambdaQueryWrapper<MarketAreaItemStorePriceMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.in(MarketAreaItemStorePriceMapping::getAreaItemMappingId, areaItemMappingIds);
//        queryWrapper.eq(MarketAreaItemStorePriceMapping::getTenantId, tenantId);
//        return list(queryWrapper);
//    }
//
//    @Override
//    public void batchDelete(List<Long> storeIds, Long tenantId) {
//        LambdaQueryWrapper<MarketAreaItemStorePriceMapping> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.in(MarketAreaItemStorePriceMapping::getStoreId, storeIds);
//        queryWrapper.eq(MarketAreaItemStorePriceMapping::getTenantId, tenantId);
//        remove(queryWrapper);
//    }
//}

package com.cosfo.manage.market.repository.impl;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/20
 */
//@Service
//public class MarketItemClassificationRepositoryImpl extends ServiceImpl<MarketItemClassificationMapper, MarketItemClassification> implements MarketItemClassificationRepository {
//
//    @Override
//    public void updateByMarketId(Long tenantId, Long marketId, Long classificationId) {
//        LambdaQueryWrapper<MarketItemClassification> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MarketItemClassification::getMarketId, marketId);
//        queryWrapper.eq(MarketItemClassification::getTenantId, tenantId);
//        MarketItemClassification marketItemClassification = new MarketItemClassification();
//        marketItemClassification.setTenantId(tenantId);
//        marketItemClassification.setClassificationId(classificationId);
//        marketItemClassification.setMarketId(marketId);
//        update(marketItemClassification, queryWrapper);
//    }
//
//    @Override
//    public MarketItemClassification selectByMarketId(Long tenantId, Long marketId) {
//        LambdaQueryWrapper<MarketItemClassification> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MarketItemClassification::getMarketId, marketId);
//        queryWrapper.eq(MarketItemClassification::getTenantId, tenantId);
//        return getOne(queryWrapper);
//    }
//}

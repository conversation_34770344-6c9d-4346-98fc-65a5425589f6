package com.cosfo.manage.market.repository.impl;

import cn.hutool.core.lang.Pair;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import com.cosfo.manage.market.repository.MarketItemCompositeGoodsRepository;
import com.cosfo.manage.market.service.MarketItemCompositeMarketService;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2023-11-28
 **/
@Service
public class MarketItemCompositeGoodsRepositoryImpl implements MarketItemCompositeGoodsRepository {

    //TODO：George 2023/11/28 商品货品聚合

    @Resource
    private MarketItemCompositeMarketService marketItemCompositeMarketService;
    @Resource
    private ProductFacade productFacade;

    @Override
    public Pair<Map<Long, MarketItemCompositeMarket>, Map<Long, ProductSkuDTO>> queryByIds(Long tenantId, List<Long> itemIds, List<Long> skuIds) {
        // 商品信息
        List<MarketItemCompositeMarket> marketItemCompositeMarkets = marketItemCompositeMarketService
                .queryByMarketItemIds(tenantId, itemIds);
        Map<Long, MarketItemCompositeMarket> marketInfoMap = marketItemCompositeMarkets.stream()
                .collect(Collectors.toMap(MarketItemCompositeMarket::getItemId, Function.identity()));
        // 货品信息
        Map<Long, ProductSkuDTO> skuInfoMap = productFacade.listSkuByIds(skuIds, tenantId).stream()
                .collect(Collectors.toMap(ProductSkuDTO::getId, Function.identity()));
        return Pair.of(marketInfoMap, skuInfoMap);
    }
}

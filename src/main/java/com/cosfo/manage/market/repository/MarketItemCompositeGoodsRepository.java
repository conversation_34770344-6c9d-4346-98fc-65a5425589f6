package com.cosfo.manage.market.repository;

import cn.hutool.core.lang.Pair;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-11-28
 **/
public interface MarketItemCompositeGoodsRepository {

    /**
     * 根据ids查询商品货品信息
     * @param tenantId
     * @param itemIds
     * @param skuIds
     * @return
     */
    Pair<Map<Long, MarketItemCompositeMarket>, Map<Long, ProductSkuDTO>> queryByIds(Long tenantId, List<Long> itemIds, List<Long> skuIds);
}

package com.cosfo.manage.market.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.market.model.dto.MarketItemQueryParam;
import com.cosfo.manage.market.model.dto.MarketQueryInput;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;

import java.util.Collection;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/15
 */
public interface MarketItemRepository extends IService<MarketItem> {

    /**
     * 批量查询
     *
     * @param tenantId
     * @param marketIds
     * @return
     */
    List<MarketItem> selectByMarketIds(Long tenantId, List<Long> marketIds);

    /**
     * 根据货品Id或销售商品Id查询
     *
     * @param marketQueryInput
     * @return
     */
    List<MarketItem> selectByMarketQueryInput(MarketQueryInput marketQueryInput);

    /**
     * 根据货品Id查询
     *
     * @param tenantId
     * @param skuId
     * @return
     */
    List<MarketItem> selectBySkuId(Long tenantId, Long skuId);
    /**
     * 根据货品Id 和 itemcode查询
     *
     * @param tenantId
     * @param skuId
     * @return
     */
    List<MarketItem> selectBySkuIdAndItemCode(Long tenantId, Long skuId, String itemCode);

    /**
     * 批量查询
     *
     * @param tenantId
     * @param skuIds
     * @return
     */
    List<MarketItem> batchQueryBySkuIds(Long tenantId, Collection<Long> skuIds);

    @InterceptorIgnore(tenantLine = "on")
    List<MarketItem> batchQueryByItemIds(List<Long> itemIds);

    /**
     * 判断是否已经存在此编码， false=不存在；true=存在
     * @param itemCode
     * @param tenantId
     * @return
     */
    boolean chargeItemCodeRepeat(Long id,String itemCode, Long tenantId);

    /**
     * 根据查询条件查询
     * 条件可增加
     * @param param
     * @return
     */
    List<MarketItem> listByParam(MarketItemQueryParam param);

//    /**
//     * 根据货品Ids批量查询
//     *
//     * @param tenantId
//     * @param skuIds
//     * @param onSale 上下架状态 1上架 0下架
//     * @return
//     */
//    List<Long> selectSaleBySkuIds(Long tenantId, List<Long> skuIds, Integer onSale);
//
//    /**
//     * 根据货品Ids批量查询
//     *
//     * @param tenantId
//     * @param skuIds
//     * @return skuId、OnSale
//     */
//    List<MarketItem> selectSaleStatusBySkuIds(Long tenantId, List<Long> skuIds);
}

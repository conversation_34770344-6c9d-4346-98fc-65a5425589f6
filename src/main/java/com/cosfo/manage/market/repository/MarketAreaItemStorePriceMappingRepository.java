package com.cosfo.manage.market.repository;

/**
 * <p>
 * 门店商品定价策略映射 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
//public interface MarketAreaItemStorePriceMappingRepository extends IService<MarketAreaItemStorePriceMapping> {
//
//    /**
//     * 删除价格策略关联门店
//     *
//     * @param areaItemMappingId
//     */
//    void deleteByAreaItemMappingId(Long areaItemMappingId, Long tenantId);
//
//    /**
//     * 批量查询
//     *
//     * @param areaItemMappingIds
//     * @param tenantId
//     * @return
//     */
//    List<MarketAreaItemStorePriceMapping> batchQuery(List<Long> areaItemMappingIds, Long tenantId);
//
//    /**
//     * 批量删除
//     *
//     * @param storeIds
//     * @param tenantId
//     */
//    void batchDelete(List<Long> storeIds, Long tenantId);
//}

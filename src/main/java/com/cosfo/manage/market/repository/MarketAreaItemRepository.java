package com.cosfo.manage.market.repository;


/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/15
 */
//public interface MarketAreaItemRepository extends IService<MarketAreaItem> {
//
//    /**
//     * 根据租户Id和销售商品Id查询
//     *
//     * @param tenantId
//     * @param itemId
//     * @return
//     */
//     MarketAreaItem selectByTenantIdAndItemId(Long tenantId, Long itemId);
//
//    /**
//     * 根据租户Id和销售商品Id查询
//     *
//     * @param tenantId
//     * @param itemIds
//     * @return
//     */
//    List<MarketAreaItem> selectByTenantIdAndItemIds(Long tenantId, List<Long> itemIds);
//
//    /**
//     * 批量更新上下架状态
//     * @param itemIds
//     * @param onSale
//     */
//    void batchUpdateOnSale(List<Long> itemIds,Integer onSale,Long tenantId);
//}

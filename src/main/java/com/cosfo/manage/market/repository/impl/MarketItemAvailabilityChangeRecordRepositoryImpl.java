package com.cosfo.manage.market.repository.impl;

import com.cosfo.manage.common.context.MarketItemAvailabilityChangeTypeEnum;
import com.cosfo.manage.market.model.po.MarketItemAvailabilityChangeRecord;
import com.cosfo.manage.market.mapper.MarketItemAvailabilityChangeRecordMapper;
import com.cosfo.manage.market.repository.MarketItemAvailabilityChangeRecordRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品可用性记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Service
public class MarketItemAvailabilityChangeRecordRepositoryImpl extends ServiceImpl<MarketItemAvailabilityChangeRecordMapper, MarketItemAvailabilityChangeRecord> implements MarketItemAvailabilityChangeRecordRepository {

    @Resource
    private MarketItemAvailabilityChangeRecordMapper marketItemAvailabilityChangeRecordMapper;

    @Override
    public MarketItemAvailabilityChangeRecord selectLastStockChangeRecord(Long tenantId, Long itemId) {
        int normal = MarketItemAvailabilityChangeTypeEnum.NORMAL.getCode();
        int soldOut = MarketItemAvailabilityChangeTypeEnum.SOLD_OUT.getCode();

        return this.lambdaQuery()
                .eq(MarketItemAvailabilityChangeRecord::getTenantId, tenantId)
                .eq(MarketItemAvailabilityChangeRecord::getItemId, itemId)
                .in(MarketItemAvailabilityChangeRecord::getChangeType, normal, soldOut)
                .orderByDesc(MarketItemAvailabilityChangeRecord::getChangeTime)
                .last("limit 1")
                .one();
    }


    public void batchInsert(List<MarketItemAvailabilityChangeRecord> records) {
        marketItemAvailabilityChangeRecordMapper.saveBatch(records);
    }
}

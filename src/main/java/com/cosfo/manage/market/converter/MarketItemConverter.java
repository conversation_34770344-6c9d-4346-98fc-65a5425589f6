package com.cosfo.manage.market.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.cofso.item.client.req.MarketItemInfoQueryFlagReq;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.downloadcenter.item.MarketExcelDataDTO;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.po.MarketAreaItem;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.model.vo.*;
import com.cosfo.manage.product.convert.ProductConverter;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.vo.ProductSkuDetailVO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.cosfo.manage.common.constant.PriceStrategyConstants.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/16
 */
public class MarketItemConverter {

    /**
     * 转化为MarketItemDTO
     *
     * @param marketItem
     * @param marketAreaItem
     * @return
     */
    public static MarketItemDTO convertToMarketItemDTO(MarketItem marketItem, MarketAreaItem marketAreaItem) {
        if (marketItem == null) {
            return null;
        }

        MarketItemDTO marketItemDTO = new MarketItemDTO();
        marketItemDTO.setId(marketItem.getId());
        marketItemDTO.setMarketId(marketItem.getMarketId());
        marketItemDTO.setTenantId(marketItem.getTenantId());
        marketItemDTO.setSkuId(marketItem.getSkuId());
        marketItemDTO.setSpecification(marketItem.getSpecification());
        marketItemDTO.setWeightNotes(marketItem.getWeightNotes());
        marketItemDTO.setSpecificationUnit(marketItem.getSpecificationUnit());
        marketItemDTO.setBrandName(marketItem.getBrandName());
        marketItemDTO.setSupplierId(marketItem.getSupplierId());
        marketItemDTO.setSupplierName(marketItem.getSupplierName());
        marketItemDTO.setItemCode(marketItem.getItemCode());
        marketItemDTO.setMaxAfterSaleAmount(marketItem.getMaxAfterSaleAmount());
        marketItemDTO.setAfterSaleUnit(marketItem.getAfterSaleUnit());
        marketItemDTO.setGoodsType(marketItem.getGoodsType());
        if (marketAreaItem != null) {
            marketItemDTO.setOnSale(marketAreaItem.getOnSale());
            marketItemDTO.setPriceType(marketAreaItem.getPriceType());
            marketItemDTO.setMiniOrderQuantity(marketAreaItem.getMiniOrderQuantity());
        }
        return marketItemDTO;
    }

    /**
     * 传唤为convertToMarketItemVO
     *
     * @param marketItemDto
     * @return
     */
    public static MarketItemVO convertToMarketItemVO(MarketItemDTO marketItemDto) {
        MarketItemVO marketItemVo = new MarketItemVO();
        marketItemVo.setId(marketItemDto.getId());
        marketItemVo.setTenantId(marketItemDto.getTenantId());
        marketItemVo.setSkuId(Objects.equals(marketItemDto.getSkuId(), -1L) ? null : marketItemDto.getSkuId());
        marketItemVo.setSpecification(marketItemDto.getSpecification());
        marketItemVo.setWeightNotes(marketItemDto.getWeightNotes());
        marketItemVo.setSpecificationUnit(marketItemDto.getSpecificationUnit());
        marketItemVo.setOnSale(marketItemDto.getOnSale());
        marketItemVo.setPriceType(marketItemDto.getPriceType());
        marketItemVo.setMiniOrderQuantity(marketItemDto.getMiniOrderQuantity());
        ProductSkuDetailVO ProductSkuDetailVo = ProductConverter.convertToProductSkuVO(marketItemDto.getProductSkuDTO());
        marketItemVo.setProductSkuVo(ProductSkuDetailVo);
        marketItemVo.setPriceStr(marketItemDto.getPriceStr());
        marketItemVo.setMinPrice(marketItemDto.getMinPrice());
        marketItemVo.setMaxPrice(marketItemDto.getMaxPrice());
        marketItemVo.setBrandName(marketItemDto.getBrandName());
        marketItemVo.setAmount(marketItemDto.getAmount());
        marketItemVo.setSupplierName(marketItemDto.getSupplierName());
        marketItemVo.setItemCode(marketItemDto.getItemCode());
        marketItemVo.setSupplierId(marketItemDto.getSupplierId());
        marketItemVo.setAfterSaleUnit(marketItemDto.getAfterSaleUnit());
        marketItemVo.setMaxAfterSaleAmount(marketItemDto.getMaxAfterSaleAmount());
        marketItemVo.setGoodsType(marketItemDto.getGoodsType());
        marketItemVo.setNoGoodsSupplyPrice(marketItemDto.getNoGoodsSupplyPrice());
        marketItemVo.setItemSaleMode(marketItemDto.getItemSaleMode());
        marketItemVo.setMarketItemUnfairPriceStrategyVO(convertToMarketItemUnfairPriceStrategyVO(marketItemDto.getMarketItemUnfairPriceStrategyDTO()));
        marketItemVo.setSaleLimitQuantity(marketItemDto.getSaleLimitQuantity());
        marketItemVo.setSaleLimitRule(marketItemDto.getSaleLimitRule());
        marketItemVo.setBuyMultiple(marketItemDto.getBuyMultiple());
        marketItemVo.setBuyMultipleSwitch(marketItemDto.getBuyMultipleSwitch());
        marketItemVo.setPresaleSwitch(marketItemDto.getPresaleSwitch());
        marketItemVo.setStandardUnitPrice(marketItemDto.getStandardUnitPrice ());
        marketItemVo.setStoreInventoryControlFlag(marketItemDto.getStoreInventoryControlFlag ());
        marketItemVo.setStoreOrderingUnit(marketItemDto.getStoreOrderingUnit ());
        marketItemVo.setStoreInventoryCostUnitMultiple(marketItemDto.getStoreInventoryCostUnitMultiple ());
        marketItemVo.setStoreInventoryUnit(marketItemDto.getStoreInventoryUnit ());
        marketItemVo.setStoreOrderingInventoryUnitMultiple(marketItemDto.getStoreOrderingInventoryUnitMultiple ());
        marketItemVo.setStoreCostUnit(marketItemDto.getStoreCostUnit ());
        marketItemVo.setWeight(marketItemDto.getWeight());
        // 价格策略列表
        convert2MarketItemPrice(marketItemDto, marketItemVo);
        return marketItemVo;
    }

    public static MarketItemUnfairPriceStrategyVO convertToMarketItemUnfairPriceStrategyVO(MarketItemUnfairPriceStrategyDTO marketItemUnfairPriceStrategyDTO) {
        if (marketItemUnfairPriceStrategyDTO == null) {
            return null;
        }

        MarketItemUnfairPriceStrategyVO marketItemUnfairPriceStrategyVO = new MarketItemUnfairPriceStrategyVO();
        marketItemUnfairPriceStrategyVO.setDefaultFlag(marketItemUnfairPriceStrategyDTO.getDefaultFlag());
        marketItemUnfairPriceStrategyVO.setStrategyType(marketItemUnfairPriceStrategyDTO.getStrategyType());
        return marketItemUnfairPriceStrategyVO;
    }

    private static void convert2MarketItemPrice(MarketItemDTO marketItemDTO, MarketItemVO marketItemVO) {
        List<MarketAreaItemMappingVO> marketAreaItemMappingVos = marketItemDTO.getMarketAreaItemMappingVos();
        if (CollectionUtil.isEmpty(marketAreaItemMappingVos)) {
            return;
        }
        Map<Integer, List<MarketAreaItemMappingVO>> priceTypeMap = marketAreaItemMappingVos.stream().collect(Collectors.groupingBy(MarketAreaItemMappingVO::getPriceType));
        for (Integer priceType : priceTypeMap.keySet()) {
            if (PRICE_TYPE_DEFAULT.equals(priceType)) {
                marketItemVO.setDefaultPrice(Optional.ofNullable(priceTypeMap.get(priceType)).orElse(Collections.emptyList()).get(0));
            } else if (PRICE_TYPE_STORE_GROUP.equals(priceType)) {
                marketItemVO.setStoreGroupPrice(priceTypeMap.get(priceType));
            } else if (PRICE_TYPE_STORE.equals(priceType)) {
                marketItemVO.setStorePrice(priceTypeMap.get(priceType));
            }
        }
    }


    public static MarketItemPriceStrategyVO convert2MarketItemPriceVO(MarketItemDTO marketItemDto) {
        MarketItemPriceStrategyVO marketItemPriceStrategyVO = MarketConvert.INSTANCE.convert2VO(marketItemDto);
        List<MarketAreaItemMappingVO> areaItemMappingVOS = marketItemDto.getMarketAreaItemMappingVos();
        if (CollectionUtil.isEmpty(areaItemMappingVOS)) {
            return marketItemPriceStrategyVO;
        }
        Map<Integer, List<MarketAreaItemMappingVO>> priceTypeMap = areaItemMappingVOS.stream().collect(Collectors.groupingBy(MarketAreaItemMappingVO::getPriceType));
        for (Integer priceType : priceTypeMap.keySet()) {
            if (PRICE_TYPE_DEFAULT.equals(priceType)) {
                marketItemPriceStrategyVO.setDefaultPrice(Optional.ofNullable(priceTypeMap.get(priceType)).orElse(Collections.emptyList()).get(0));
            } else if (PRICE_TYPE_STORE_GROUP.equals(priceType)) {
                marketItemPriceStrategyVO.setStoreGroupPrice(priceTypeMap.get(priceType));
            } else if (PRICE_TYPE_STORE.equals(priceType)) {
                marketItemPriceStrategyVO.setStorePrice(priceTypeMap.get(priceType));
            }
        }

        return marketItemPriceStrategyVO;
    }


    /**
     * 转换为MarketItemInput
     *
     * @param excelDataDTO
     * @return
     */
//    public static MarketItemInput convertToMarketItemInput(MarketExcelImportDataDTO excelDataDTO) {
//        if (Objects.isNull(excelDataDTO)) {
//            return null;
//        }
//
//        MarketItemInput marketItemInput = new MarketItemInput();
//        marketItemInput.setSpecification(excelDataDTO.getSpecification());
//        marketItemInput.setSpecificationUnit(excelDataDTO.getSpecificationUnit());
//        marketItemInput.setGoodsType(GoodsTypeEnum.NO_GOOD_TYPE.getCode());
//        marketItemInput.setOnSale(OnSaleTypeEnum.getType(excelDataDTO.getOnSale()));
//        marketItemInput.setAmount(Integer.valueOf(excelDataDTO.getStock()));
//        marketItemInput.setPriceType(MarketAreaItemPriceTypeEnum.ALL_STORE_UNIFIED_PRICE.getCode());
//        marketItemInput.setItemCode(excelDataDTO.getItemCode());
//        if (!StringUtils.isEmpty(excelDataDTO.getPrice())) {
//            MarketAreaItemMappingInput marketAreaItemMappingInput = new MarketAreaItemMappingInput();
//            marketAreaItemMappingInput.setStorePriceType(StorePriceTypeEnum.ALL.getCode());
//            marketAreaItemMappingInput.setType(AreaItemTypeEnum.FIXED_PRICE.getCode());
//            marketAreaItemMappingInput.setMappingNumber(new BigDecimal(excelDataDTO.getPrice()));
//            marketAreaItemMappingInput.setPriceType(PRICE_TYPE_DEFAULT);
//            marketItemInput.setDefaultPrice(marketAreaItemMappingInput);
//        }
//        return marketItemInput;
//    }

    public static MarketItemExportVO convertToMarketItemExportVo(MarketSpuVO marketSpuVO, MarketItemDTO marketItemDTO) {
        if (Objects.isNull(marketItemDTO)) {
            return null;
        }
        MarketItemExportVO marketItemExportVO = new MarketItemExportVO();
        marketItemExportVO.setMarketId(marketSpuVO.getId());
        marketItemExportVO.setTitle(marketSpuVO.getTitle());
        marketItemExportVO.setSubTitle(marketSpuVO.getSubTitle());
        marketItemExportVO.setFirstCategoryName(marketSpuVO.getFirstCategory());
        marketItemExportVO.setSecondCategoryName(marketSpuVO.getSecondCategory());
        marketItemExportVO.setThirdCategoryName(marketSpuVO.getThirdCategory());
        marketItemExportVO.setFirstClassificationName(marketSpuVO.getFirstClassificationName());
        marketItemExportVO.setSecondClassificationName(marketSpuVO.getSecondClassificationName());
        marketItemExportVO.setMarketItemId(marketItemDTO.getId());
        marketItemExportVO.setBrandName(marketItemDTO.getBrandName());
        ProductSkuDTO productSkuDTO = marketItemDTO.getProductSkuDTO();
        if (!Objects.isNull(productSkuDTO)) {
            marketItemExportVO.setStorageLocationDesc(ProductSkuEnum.STORAGE_LOCATION.getDesc(productSkuDTO.getStorageLocation()));
            marketItemExportVO.setStorageTemperature(Objects.isNull(productSkuDTO.getStorageTemperature()) ? "" : productSkuDTO.getStorageTemperature() + Constants.CENTIGRADE);
            marketItemExportVO.setOrigin(productSkuDTO.getOrigin());
            marketItemExportVO.setGuaranteePeriod(productSkuDTO.getGuaranteePeriod());
            marketItemExportVO.setGuaranteeUnitDesc(ProductSkuEnum.GUARANTEE_UNIT.getDesc(productSkuDTO.getGuaranteeUnit()));
        }

        marketItemExportVO.setSpecification(marketItemDTO.getSpecification());
        marketItemExportVO.setSpecificationUnit(marketItemDTO.getSpecificationUnit());
        marketItemExportVO.setOnSale(OnSaleTypeEnum.getDesc(marketItemDTO.getOnSale()));
        marketItemExportVO.setStock(marketItemDTO.getAmount());
        marketItemExportVO.setMiniOrderQuantity(marketItemDTO.getMiniOrderQuantity());
        marketItemExportVO.setItemCode(marketItemDTO.getItemCode());
        marketItemExportVO.setSkuId(Objects.equals(marketItemDTO.getSkuId(), -1L) ? null : marketItemDTO.getSkuId());
        if(GoodsTypeEnum.NO_GOOD_TYPE.getCode ().equals (marketItemDTO.getGoodsType ())) {
            marketItemExportVO.setNoGoodsSupplyPrice (marketItemDTO.getNoGoodsSupplyPrice ());
        }
        marketItemExportVO.setSupplierName(marketItemDTO.getSupplierName());
        marketItemExportVO.setSupplierId(Objects.equals(marketItemDTO.getSupplierId(), "-1") ? null : marketItemDTO.getSupplierId());

        marketItemExportVO.setOperationMode(Optional.ofNullable(GoodsTypeEnum.getShowDescByCodeV2(marketItemDTO.getGoodsType()))
            .orElse(""));
        marketItemExportVO.setSkuId(Objects.equals(-1L, marketItemDTO.getSkuId()) ? null : marketItemDTO.getSkuId());
        marketItemExportVO.setMaxPrice(marketItemDTO.getMaxPrice());
        marketItemExportVO.setMinPrice(marketItemDTO.getMinPrice());
        marketItemExportVO.setMarketId(marketItemDTO.getMarketId());
        return marketItemExportVO;
    }

//    public static PageMarketItemQueryReq convert2Req(PageMarketItemInput input) {
//        if (input == null) {
//            return null;
//        }
//        PageMarketItemQueryReq pageMarketItemQueryReq = new PageMarketItemQueryReq();
//        pageMarketItemQueryReq.setTitle(input.getTitle());
//        pageMarketItemQueryReq.setItemId(input.getItemId());
//        pageMarketItemQueryReq.setClassificationId(input.getClassificationId());
//        pageMarketItemQueryReq.setItemCode(input.getItemCode());
//        pageMarketItemQueryReq.setBrandName(input.getBrandName());
//        pageMarketItemQueryReq.setGoodsType(input.getGoodsType());
//        pageMarketItemQueryReq.setOnSale(input.getOnSale());
//        pageMarketItemQueryReq.setPageIndex(input.getPageIndex());
//        pageMarketItemQueryReq.setPageSize(input.getPageSize());
//        pageMarketItemQueryReq.setSortList(input.getSortList());
//        return pageMarketItemQueryReq;
//    }
    public static MarketItemInfoQueryFlagReq flagDTO2Req(MarketItemInfoQueryFlagDTO dto){
        MarketItemInfoQueryFlagReq req = new MarketItemInfoQueryFlagReq ();
        req.setClassificationIdFlag(dto.getClassificationIdFlag ());
        req.setPriceRangeFlag(dto.getPriceRangeFlag ());
        req.setStockFlag(dto.getStockFlag ());
        req.setCategoryIdFlag(dto.getCategoryIdFlag ());
        req.setUnitFlag (dto.getUnitFlag ());
        return req;
    }

    public static MarketItemInput convertToMarketItemInput4ExcelDataDTO(MarketExcelDataDTO excelDataDTO, Long tenantId) {
        MarketItemInput marketItemInput = new MarketItemInput ();
        marketItemInput.setTenantId(tenantId);
        marketItemInput.setSkuId(excelDataDTO.getSkuId ());
        marketItemInput.setSpecification(excelDataDTO.getSpecification ());
        marketItemInput.setSpecificationUnit(excelDataDTO.getSpecificationUnit ());
        marketItemInput.setSupplierId(excelDataDTO.getSupplierId ()!=null?excelDataDTO.getSupplierId ().toString():null);
        marketItemInput.setSupplierName(excelDataDTO.getSupplierName ());
        marketItemInput.setNoGoodsSupplyPrice(excelDataDTO.getNoGoodsSupplyPrice ());
        marketItemInput.setAmount(excelDataDTO.getStock ());
        marketItemInput.setMiniOrderQuantity(excelDataDTO.getMiniOrderQuantity ());
        marketItemInput.setItemCode(excelDataDTO.getItemCode ());
        marketItemInput.setAfterSaleUnit(excelDataDTO.getAfterSaleUnit ());
        marketItemInput.setMaxAfterSaleAmount(excelDataDTO.getMaxAfterSaleAmount ());
        marketItemInput.setSaleLimitQuantity(excelDataDTO.getSaleLimitQuantity ());
        marketItemInput.setStandardUnitPrice(excelDataDTO.getStandardUnitPrice ());
        marketItemInput.setStoreOrderingUnit(excelDataDTO.getStoreOrderingUnit ());
        marketItemInput.setStoreInventoryCostUnitMultiple(excelDataDTO.getStoreInventoryCostUnitMultiple ());
        marketItemInput.setStoreInventoryUnit(excelDataDTO.getStoreInventoryUnit ());
        marketItemInput.setStoreOrderingInventoryUnitMultiple(excelDataDTO.getStoreOrderingInventoryUnitMultiple ());
        marketItemInput.setStoreCostUnit(excelDataDTO.getStoreCostUnit ());
        return marketItemInput;
    }
}

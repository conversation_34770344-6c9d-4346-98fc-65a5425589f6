package com.cosfo.manage.market.converter;

import com.cosfo.manage.market.model.dto.MarketSpuInput;
import com.cosfo.manage.market.model.dto.MarketExcelImportDataDTO;

import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/23
 */
public class MarketConverter {

//    public static MarketSpuInput convertToMarketSpuInput(MarketExcelImportDataDTO excelImportDataDTO){
//        if(Objects.isNull(excelImportDataDTO)){
//            return null;
//        }
//
//        MarketSpuInput marketSpuInput = new MarketSpuInput();
//        marketSpuInput.setTitle(excelImportDataDTO.getTitle());
//        marketSpuInput.setSubTitle(excelImportDataDTO.getSubTitle());
//        marketSpuInput.setCategoryId(excelImportDataDTO.getCategoryId());
//        marketSpuInput.setClassificationId(excelImportDataDTO.getClassificationId());
//        return marketSpuInput;
//    }
}

package com.cosfo.manage.downloadcenter.presaleorder;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.order.model.dto.OrderPresaleDeliveryTimeDTO;
import com.cosfo.manage.order.service.OrderPresaleService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2024/7/9 下午6:37
 */
@Component
@Slf4j
public class PresaleOrderSetDeliveryDateImportHandler extends DownloadCenterImportDefaultHandler<PresaleOrderSetDeliveryDateImportDTO> {

    @Resource
    private OrderPresaleService orderPresaleService;


    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.SAAS;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.PRESALE_ORDER_SET_DELIVERY_DATE_IMPORT.getType();
    }

    @Override
    protected void dealExcelData(List<PresaleOrderSetDeliveryDateImportDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {

        // 为每个订单设置配送时间
        list.forEach(it -> {
            OrderPresaleDeliveryTimeDTO orderPresaleDeliveryTimeDTO = new OrderPresaleDeliveryTimeDTO();
            orderPresaleDeliveryTimeDTO.setOrderId(it.getOrderId());
            orderPresaleDeliveryTimeDTO.setDeliveryTime(it.getDeliveryDate());

            try {
                orderPresaleService.setDeliveryTime(orderPresaleDeliveryTimeDTO);
            } catch (Exception e) {
                it.setErrorMsg(e.getMessage());
            }
        });
    }

    @Override
    protected File generateErrDataExcelFile(List<PresaleOrderSetDeliveryDateImportDTO> errorDataList) {
        log.info("生成错误数据文件, {}", errorDataList);
        File file = new File(UUID.randomUUID().toString() + ".xlsx");
        List<PresaleOrderSetDeliveryDateExportDTO> exportData = errorDataList.stream().map(it -> {
            PresaleOrderSetDeliveryDateExportDTO exportDTO = new PresaleOrderSetDeliveryDateExportDTO();
            BeanUtils.copyProperties(it, exportDTO);
            return exportDTO;
        }).collect(Collectors.toList());
        EasyExcel.write(file, PresaleOrderSetDeliveryDateExportDTO.class).sheet().registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(exportData);
        return file;
    }
}

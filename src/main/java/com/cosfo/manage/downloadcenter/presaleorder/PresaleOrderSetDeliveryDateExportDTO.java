package com.cosfo.manage.downloadcenter.presaleorder;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class PresaleOrderSetDeliveryDateExportDTO extends ImportExcelBaseDTO {

    @ExcelProperty("订单编号")
    private String orderNo;

    @ExcelProperty("订单创建时间")
    private String orderCreateTime;

    @ExcelProperty("支付方式")
    private String payType;

    @ExcelProperty("门店编号")
    private Long storeId;

    @ExcelProperty("门店名称")
    private String storeName;

    @ExcelProperty("商品编码")
    private Long itemId;

    @ExcelProperty("商品名称")
    private String itemName;

    @ExcelProperty("单价")
    private BigDecimal price;

    @ExcelProperty("下单数量")
    private Integer amount;

    @ExcelProperty("商品总价")
    private BigDecimal totalPrice;

    @ExcelProperty("售后数量")
    private Integer afterSaleCount;

    @ExcelProperty("售后金额")
    private BigDecimal afterSalePrice;

    @ExcelProperty("配送费")
    private BigDecimal deliveryFee;
}

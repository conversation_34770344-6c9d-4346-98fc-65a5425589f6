package com.cosfo.manage.wangdiantong;

import com.cosfo.manage.wangdiantong.sdk.Client;
import com.cosfo.manage.wangdiantong.sdk.WdtErpException;
import com.cosfo.manage.wangdiantong.sdk.api.sales.RawTradeAPI;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.PushSelfRequest;
import com.cosfo.manage.wangdiantong.sdk.api.sales.dto.PushSelfResponse;
import com.cosfo.manage.wangdiantong.sdk.impl.ApiFactory;
import com.cosfo.manage.wangdiantong.sdk.impl.DefaultClient;
import com.google.gson.GsonBuilder;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.ConnectException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/6/25 下午2:01
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RawTradeAPITest {


    public void buildRequest(List<PushSelfRequest.RawTrade> rawTradeList, List<PushSelfRequest.RawTradeOrder> tradeOderList, List<PushSelfRequest.RawTradeDiscount> tradeDiscountList)
    {

        PushSelfRequest.RawTrade rawTrade = new PushSelfRequest.RawTrade();
        rawTrade.setAutoWms(false);
        // rawTrade.setWarehouseNo("pos_inner"); // 非自流转订单不需要设置此字段.

        rawTrade.setPostAmount(BigDecimal.valueOf(2.0000D));

        rawTrade.setOtherAmount(BigDecimal.valueOf(5.2000D));

        rawTrade.setDiscount(BigDecimal.valueOf(1.0000D));
        // rawTrade.receivable='';
        rawTrade.setPlatformCost(BigDecimal.valueOf(0.0000D));
        rawTrade.setCodAmount(BigDecimal.valueOf(0.0000D));
        rawTrade.setReceived(BigDecimal.valueOf(0.0000D));

        rawTrade.setTid("OR171620056650542");
        rawTrade.setOrderCount(1);

        rawTrade.setProcessStatus(PushSelfRequest.RawTrade.PROCESS_STATUS_WAIT_DELIVERY);
        rawTrade.setTradeStatus(PushSelfRequest.RawTrade.TRADE_STATUS_WAIT_CONSIGN);
        rawTrade.setRefundStatus(PushSelfRequest.RawTrade.REFUND_NONE);
        rawTrade.setPayStatus(PushSelfRequest.RawTrade.PAY_STATUS_FULL_PAID);

        rawTrade.setPayMethod(PushSelfRequest.RawTrade.PAY_METHOD_ONLINE); // 1在线转帐
        // 2现金，3银行转账，4邮局汇款
        // 5预付款
        // 6刷卡
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateTimeStr = simpleDateFormat.format(new Date());
        // System.out.println("\n\ndateTimeStr"+dateTimeStr);
        rawTrade.setTradeTime("2020-11-20 10:00:00");
        rawTrade.setPayTime(dateTimeStr);
        rawTrade.setEndTime(dateTimeStr);

        rawTrade.setBuyerNick("test_openapi");
        rawTrade.setBuyerMessage("test_openapi");
        rawTrade.setBuyerEmail("<EMAIL>");
        rawTrade.setBuyerArea("test_area");

        rawTrade.setReceiverName("api_receiver_name");
        rawTrade.setReceiverArea("api_receiver_area");
        rawTrade.setReceiverAddress("A街道B小区C栋");
        rawTrade.setReceiverZip("000001");
        rawTrade.setReceiverMobile("*********");
        rawTrade.setReceiverTelno("");

        rawTrade.setInvoiceType(PushSelfRequest.RawTrade.INVOICE_TYPE_NONE);
        rawTrade.setInvoiceTitle("");
        rawTrade.setInvoiceContent("");

        // rawTrade.logistics_type=-1;
        rawTrade.setConsignInterval(0);
        rawTrade.setDeliveryTerm(PushSelfRequest.RawTrade.DELIVERY_TERM_DAP);
        rawTrade.setToDeliverTime("");
        rawTrade.setPayId("");
        rawTrade.setPayAccount("");
        rawTrade.setRemark("");
        rawTrade.setRemarkFlag(0);

        for (int j = 0; j < rawTrade.getOrderCount(); j++)
        {
            PushSelfRequest.RawTradeOrder tradeOrder = new PushSelfRequest.RawTradeOrder();

            tradeOrder.setTid(rawTrade.getTid());
            tradeOrder.setOid(tradeOrder.getTid() + "_" + j);
            tradeOrder.setOrderType(PushSelfRequest.RawTradeOrder.ORDER_TYPE_NORMAL);

            tradeOrder.setStatus(PushSelfRequest.RawTradeOrder.PLATFORM_STATUS);
            tradeOrder.setRefundStatus(PushSelfRequest.RawTradeOrder.REFUND_STATUS_NONE);

            tradeOrder.setSpecId("tb-p1" );
            tradeOrder.setGoodsId("tb-l1" );
            tradeOrder.setGoodsNo("bm2" );
            tradeOrder.setSpecNo("p1" );
            tradeOrder.setGoodsName("大件");

            tradeOrder.setNum(BigDecimal.valueOf(3.0000D));
            tradeOrder.setPrice(BigDecimal.valueOf(2.5000D));
            tradeOrder.setAdjustAmount(BigDecimal.valueOf(0.0000D));
            tradeOrder.setRefundAmount(BigDecimal.valueOf(0.0000D));
            tradeOrder.setDiscount(BigDecimal.valueOf(0.5000D)); // 优惠金额
            tradeOrder.setShareDiscount(BigDecimal.valueOf(0.20000D));// 分摊优惠金额
            tradeOrder.setTotalAmount(BigDecimal.valueOf(0.00000D));
            // $tradeOrder->total_amount - $tradeOrder->share_discount ;

            tradeOrder.setCid("");
            tradeOrder.setRemark("");
            tradeOrder.setJson("");

            // 处理主单部分数据
            rawTrade.setGoodsCount(rawTrade.getGoodsCount().add(tradeOrder.getNum()));
            // rawTrade.receivable += tradeOrder.getShareAmount();
            tradeOderList.add(tradeOrder);
        }

        for (int k = 0; k < tradeOderList.size(); k++)
        {
            PushSelfRequest.RawTradeDiscount tradeDiscount = new PushSelfRequest.RawTradeDiscount();

            tradeDiscount.setTid(rawTrade.getTid());
            tradeDiscount.setOid(tradeOderList.get(k).getOid());
            tradeDiscount.setType("api_type_"+k);
            tradeDiscount.setSn("api_sn_20201120"+k);
            tradeDiscount.setName("api_discount_"+k);
            tradeDiscount.setIsBonus( Byte.valueOf((byte) 0));
            tradeDiscount.setDetail("raw_discount");
            tradeDiscount.setAmount(BigDecimal.valueOf(0.20000D));

            tradeDiscountList.add(tradeDiscount);
        }

        rawTradeList.add(rawTrade);
    }


    @Test
    public void pushSelf() throws WdtErpException, ConnectException
    {
        List<PushSelfRequest.RawTrade> rawTradeList = new ArrayList<>();
        List<PushSelfRequest.RawTradeOrder> tradeOrderList = new ArrayList<>();
        List<PushSelfRequest.RawTradeDiscount> tradeDiscountList = new ArrayList<>();
        buildRequest(rawTradeList, tradeOrderList, tradeDiscountList);
//		String shopNo = "LSf";
        String shopNo = "depiao3-test";
//		Client client = DefaultClient.get("wdterp30", "http://192.168.26.1:30000/", "spw001",
//				"b54e8fa409635d6c9789e2345c426337:703ae3e06cf0281d3b92744fd33b31f7");
        Client client = DefaultClient.get("wdtapi3", "http://47.92.239.46/", "depiao3-test",
                "e741d52ed:357572692283515062637953e4f490de");
//		 Client client = DefaultClient.get("wdtapi3", "test", "test");
        RawTradeAPI rawTradeAPI = ApiFactory.get(client, RawTradeAPI.class);
        PushSelfResponse response = rawTradeAPI.pushSelf(shopNo, rawTradeList, tradeOrderList, tradeDiscountList);
        System.out.println("push rawTrade response: " + new GsonBuilder().create().toJson(response));
    }

    public static void main(String[] args) throws IOException, WdtErpException {
        new RawTradeAPITest().pushSelf();
    }

}

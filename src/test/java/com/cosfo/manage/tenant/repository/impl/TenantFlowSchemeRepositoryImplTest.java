package com.cosfo.manage.tenant.repository.impl;

import com.cosfo.manage.tenant.repository.TenantFlowSchemeRepository;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(SpringRunner.class)
class TenantFlowSchemeRepositoryImplTest {

    @Resource
    TenantFlowSchemeRepository tenantFlowSchemeRepository;

    @Test
    void updateFlowSchemeExcludeStoreIds() {
        tenantFlowSchemeRepository.updateFlowSchemeExcludeStoreIds(2L, 19L, Lists.newArrayList(151793L, 151791L, 2224L));
    }
}
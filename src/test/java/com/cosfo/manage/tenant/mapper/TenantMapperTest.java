package com.cosfo.manage.tenant.mapper;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.tenant.model.vo.SupplierTenantVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantMapperTest {

//    @Resource
//    private TenantMapper tenantMapper;
//
//    @Test
//    void listSupplier() {
//        List<SupplierTenantVO> supplierTenantVOS = tenantMapper.listSupplier();
//        System.out.println(JSON.toJSONString(supplierTenantVOS));
//        assertNotNull(supplierTenantVOS);
//    }
//
//    @Test
//    void listSupplierMap() {
//        Set<Long> ids = new HashSet<>();
//        ids.add(1L);
//        List<SupplierTenantVO> supplierTenantVOS = tenantMapper.listSupplierByIds(ids);
//        System.out.println(supplierTenantVOS);
//        assertNotNull(supplierTenantVOS);
//    }
}
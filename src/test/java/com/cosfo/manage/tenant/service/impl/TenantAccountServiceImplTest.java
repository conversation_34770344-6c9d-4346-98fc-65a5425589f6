package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.tenant.service.TenantAccountService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/2
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class TenantAccountServiceImplTest {
    @Resource
    private TenantAccountService tenantAccountService;

    @Test
    void sychronizedUser() {
//        tenantAccountService.synchronizedUser();
    }
}
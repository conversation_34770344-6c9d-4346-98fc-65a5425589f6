package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.ThreadTokenHolder;
import com.cosfo.manage.tenant.model.dto.RoleDTO;
import com.cosfo.manage.tenant.model.dto.RoleQueryDTO;
import com.cosfo.manage.tenant.model.vo.RoleVO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class AuthRoleServiceTest {

    @Resource
    private AuthRoleService authRoleService;


    @BeforeEach
    void init() {
        LoginContextInfoDTO dto = new LoginContextInfoDTO();
        dto.setAuthUserId(6L);
        dto.setTenantId(2L);
        ThreadTokenHolder.setToken(dto);
    }

    @Test
    void queryRolePage() {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();
//        roleQueryDTO.setRoleName();
        roleQueryDTO.setPageIndex(1);
        roleQueryDTO.setPageSize(10);
        PageInfo<RoleVO> roleVOPageInfo = authRoleService.queryRolePage(roleQueryDTO, new LoginContextInfoDTO());
    }

    @Test
    void addAuthRole() {
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setRoleName("试试看啊");
        roleDTO.setRemarks("试试看");
        roleDTO.setMenuPurviewIds(Lists.newArrayList(615L));
        List<Long> longs = authRoleService.addAuthRole(roleDTO);
        assertEquals(longs, Lists.newArrayList(615L));
    }

    @Test
    void updateAuthRole() {
    }

    @Test
    void delAuthRole() {
    }

    @Test
    void getAuthRole() {
    }
}
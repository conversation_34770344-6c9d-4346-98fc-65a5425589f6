package com.cosfo.manage.pos.mapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.cosfo.manage.pos.model.po.PosBomItem;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PosBomItemMapperTest {

    @Resource
    private PosBomItemMapper posBomItemMapper;

    @Test
    void batchInsert() {

        PosBomItem posBomItem = new PosBomItem();
        posBomItem.setChannelType(0);
        posBomItem.setTenantId(0L);
//        posBomItem.setOutStoreCode("1");
        posBomItem.setMerchantStoreId(0L);
        posBomItem.setOutItemCode("123");
        posBomItem.setMarketItemId(0L);
        posBomItem.setAvailableDate(LocalDate.now());
        posBomItem.setUnitCostPrice(new BigDecimal("1.0"));
        posBomItem.setNetQuantity(new BigDecimal("1"));
        posBomItem.setRemarks("213");


        posBomItemMapper.batchInsert(Lists.newArrayList(posBomItem));
    }
}
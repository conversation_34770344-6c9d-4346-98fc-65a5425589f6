package com.cosfo.manage.pos.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.jindie.facade.JindieFacade;
import com.cosfo.manage.keruyun.service.KryServiceOnPos;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.Map;

@SpringBootTest
@ActiveProfiles("dev")
public class KryApiTest {

    @Autowired
    private KryServiceOnPos kryServiceOnPos;
    @Autowired
    private JindieFacade jindieFacade;

    @Test
    public void orderDetail() {
        System.out.println ( JSON.toJSONString (kryServiceOnPos.orderDetail(870242171L, Arrays.asList (828217753063203840L))));
    }
    @Test
    public void JindieFacade() {
        String token = jindieFacade.getToken ();
        System.out.println (token);
    }
}

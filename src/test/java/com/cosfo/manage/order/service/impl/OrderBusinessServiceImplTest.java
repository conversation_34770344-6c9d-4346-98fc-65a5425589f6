package com.cosfo.manage.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.order.model.dto.BatchDeliveryNotifyDTO;
import com.cosfo.manage.order.model.dto.OrderQueryDTO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * @Author: fansongsong
 * @Date: 2023-04-14
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderBusinessServiceImplTest {

    @Resource
    private OrderBusinessServiceImpl orderService;

    @Test
    public void needDeliveryData() {
        BatchDeliveryNotifyDTO batchDeliveryNotifyDTO = new BatchDeliveryNotifyDTO();
        batchDeliveryNotifyDTO.setType(1);
        batchDeliveryNotifyDTO.setCutOffTime(LocalDateTime.now());
        batchDeliveryNotifyDTO.setWarehouseNoList(Lists.newArrayList(1, 2, 3));
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        System.err.println(orderService.queryNeedDeliveryData(batchDeliveryNotifyDTO, loginContextInfoDTO));
    }

    @Test
    public void testList() {

        OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
        orderQueryDTO.setPageIndex(1);
        orderQueryDTO.setPageSize(10);
//        orderQueryDTO.setOrderNo("OR168845214008153");
        //orderQueryDTO.setSupplierIds(Arrays.asList(2189L));
        orderQueryDTO.setStartTime("2023-10-03 00:00:00");
        orderQueryDTO.setEndTime("2023-10-03 23:59:59");
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        PageInfo<OrderVO> list = orderService.list(orderQueryDTO, loginContextInfoDTO);
        String orderJson = JSON.toJSONString(list);
        System.out.println(orderJson);
    }

    @Test
    public void export() {
        OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
        orderQueryDTO.setPageIndex(1);
        orderQueryDTO.setPageSize(10);
        orderQueryDTO.setSupplierIds(Arrays.asList(2189L));
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        orderService.export(orderQueryDTO, loginContextInfoDTO);
    }


    @Test
    public void querySelfLifting() {
        ResultDTO resultDTO = orderService.querySelfLifting("OR170124912007584", 2L);
        System.err.println(JSON.toJSONString(resultDTO));

    }
}

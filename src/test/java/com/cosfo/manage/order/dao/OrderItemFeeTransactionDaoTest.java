package com.cosfo.manage.order.dao;

import com.cosfo.manage.common.context.OrderItemFeeEnum;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderItemFeeTransactionDaoTest {

    @Resource
    private OrderItemFeeTransactionDao orderItemFeeTransactionDao;

    @Test
    void calOrderItemFeeMap() {
        Map<Long, BigDecimal> longBigDecimalMap = orderItemFeeTransactionDao.calOrderItemFeeMap(Lists.newArrayList(68594L), OrderItemFeeEnum.FeeType.AGENT_FEE, OrderItemFeeEnum.TransactionType.PAY, 1003L);
        System.out.println(longBigDecimalMap);
    }
}
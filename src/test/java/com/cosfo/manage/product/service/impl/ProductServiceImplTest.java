package com.cosfo.manage.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.context.AgentTypeEnum;
import com.cosfo.manage.good.model.dto.ProductSkuExcelDataInput;
import com.cosfo.manage.good.service.impl.ProductServiceImpl;
import com.cosfo.manage.product.convert.ProductConverter;
import com.cosfo.manage.product.model.po.ProductSku;
import com.cosfo.manage.product.model.po.ProductSpu;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.cosfo.summerfarm.enums.ProductApplicationTypeEnum;
import com.cosfo.summerfarm.model.dto.product.SummerfarmProductApplicationDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class ProductServiceImplTest {
    @Resource
    private ProductServiceImpl testService;

    @Test
    void assembleMessage() {
        ProductSpu spu = new ProductSpu();
        spu.setTenantId(2L);
       // spu.setMainPicture("testMainPic");
        ProductSku sku = new ProductSku();
        sku.setId(11111L);
        sku.setSpecification("111");
        ProductApplicationTypeEnum typeEnum = ProductApplicationTypeEnum.APPLICATION;
        /*SummerfarmProductApplicationDTO result = testService.assembleMessage(spu, sku, typeEnum);
        log.info(JSON.toJSONString(result));*/
    }

    @Test
    void convertToProductSku (){
        ProductSkuExcelDataInput input = new ProductSkuExcelDataInput();
        input.setLength("1");
        input.setHeight("1");
        input.setWidth("1");
        input.setAgentApplication(AgentTypeEnum.SUMMERFARM_AGENT.getValue());
        ProductSku sku = ProductConverter.convertToProductSku(input, 2L);
        log.info("result:>>>" + JSON.toJSONString(sku));
    }
}
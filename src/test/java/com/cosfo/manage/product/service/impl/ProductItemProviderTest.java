package com.cosfo.manage.product.service.impl;

import com.cosfo.manage.client.productitem.req.ProductItemReq;
import com.cosfo.manage.provider.impl.ProductItemOpenProviderImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ProductItemProviderTest {
    @Resource
    private ProductItemOpenProviderImpl productItemOpenProviderImpl;

    @Test
    public void saveOrUpdate() {
        ProductItemReq productItemReq = new ProductItemReq ();
        productItemReq.setGuaranteePeriod(1);
        productItemReq.setGuaranteeUnit(1);
//        productItemReq.setSkuCode("");
        productItemReq.setTitle("ceshiceshiceshi");
        productItemReq.setCustomerSkuCode("ceshiceshiceshi");
        productItemReq.setSkuTitle("ceshiceshiceshi");
        productItemReq.setSpecificationUnit("g");
        productItemReq.setStockUnit("g");
        productItemReq.setUnitRate(1);
        productItemReq.setAfterSaleUnit("g");
        productItemReq.setMaxAfterSaleAmount(1);
        productItemReq.setSpecification("speccification");
//        productItemReq.setCategoryId(1);
        productItemReq.setMainPicture("");
        productItemReq.setDetailPicture("");
        productItemReq.setStorageLocation(1);
        productItemReq.setStorageTemperature("ceshiceshiceshi");
//        productItemReq.setOnSale();
        productItemReq.setBrandName("brandname");
        productItemReq.setOrigin("hangz");
        productItemReq.setPlaceType(1);
        productItemReq.setVolume("1111");
        productItemReq.setVolumeUnit(1L);
        productItemReq.setWeight(1.00);
        productItemReq.setTaxRateValue(BigDecimal.valueOf (1));
        productItemOpenProviderImpl.saveOrUpdate (productItemReq);
    }
}

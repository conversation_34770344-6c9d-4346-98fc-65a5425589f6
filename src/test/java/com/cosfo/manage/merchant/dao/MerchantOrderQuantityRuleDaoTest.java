package com.cosfo.manage.merchant.dao;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.merchant.model.po.MerchantOrderQuantityRule;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MerchantOrderQuantityRuleDaoTest {

    @Resource
    private MerchantOrderQuantityRuleDao ruleDao;

    @Test
    void delOtherRule() {
        ruleDao.delOtherRule(24457L);
    }

    @Test
    void listOrderQuantitySpecialRuleTest() {
        List<MerchantOrderQuantityRule> merchantOrderQuantityRules = ruleDao.listOrderQuantitySpecialRule(24457L);
        System.out.println(JSON.toJSONString(merchantOrderQuantityRules));
    }
}
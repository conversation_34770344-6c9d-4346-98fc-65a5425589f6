package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.bo.OrderProfitSharingAccountDetailBO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退款分账账户维度明细测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class RefundAccountDetailTest {

    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    @Test
    public void testQueryRefundAccountDetail() {
        // 测试参数
        Long tenantId = 2L;
        Long afterSaleId = 33618L;

        // 调用方法
        List<OrderProfitSharingAccountDetailBO> resultList = 
            billProfitSharingOrderService.queryRefundProfitSharingAccountDetail(tenantId, afterSaleId);

        // 验证结果
        log.info("resultList: {}", resultList);
    }
}

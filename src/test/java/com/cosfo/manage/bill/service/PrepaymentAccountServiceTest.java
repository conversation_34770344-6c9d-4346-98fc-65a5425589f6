package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.PrepaymentAccountQueryDTO;
import com.cosfo.manage.bill.model.dto.PrepaymentTrendQueryDTO;
import com.cosfo.manage.bill.model.vo.PrepaymentAccountVO;
import com.cosfo.manage.bill.model.vo.PrepaymentAmountVO;
import com.cosfo.manage.bill.model.vo.PrepaymentTrendVO;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PrepaymentAccountServiceTest {
    @Resource
    private PrepaymentAccountService prepaymentAccountService;

    @Test
    void getTotalAmount() {
        PrepaymentAmountVO vo = prepaymentAccountService.getTotalAmount(3L);
        System.out.println(vo);
        assertNotNull(vo);
    }

    @Test
    void queryAccountPage() {
        PrepaymentAccountQueryDTO queryDTO = new PrepaymentAccountQueryDTO();
        queryDTO.setPageIndex(1);
        queryDTO.setPageSize(10);
        queryDTO.setTenantId(111L);
        PageInfo<PrepaymentAccountVO> prepaymentAccountVOPageInfo = prepaymentAccountService.queryAccountPage(queryDTO);
        System.out.println(prepaymentAccountVOPageInfo);
        assertNotNull(prepaymentAccountVOPageInfo);
    }

    @Test
    void snapshotTest() {
        prepaymentAccountService.snapshotAccount();
    }

    @Test
    void downloadAccount() {
    }

    @Test
    void queryAmountTrend() {
        PrepaymentTrendQueryDTO queryDTO = new PrepaymentTrendQueryDTO();
        queryDTO.setStart(LocalDateTime.now().plusDays(-2));
        queryDTO.setEnd(LocalDateTime.now());
        List<PrepaymentTrendVO> prepaymentTrendVOS = prepaymentAccountService.queryAmountTrend(queryDTO);
        System.out.println(prepaymentTrendVOS);
        assertNotNull(prepaymentTrendVOS);
    }
}
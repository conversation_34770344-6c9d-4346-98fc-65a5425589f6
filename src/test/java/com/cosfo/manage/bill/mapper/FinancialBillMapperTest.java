package com.cosfo.manage.bill.mapper;

import com.cosfo.manage.bill.model.dto.StoreBillQueryDTO;
import com.cosfo.manage.bill.model.vo.FinancialStoreBillVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class FinancialBillMapperTest {

    @Resource
    private FinancialBillMapper financialBillMapper;

    @Test
    void list() {
        StoreBillQueryDTO queryDTO = new StoreBillQueryDTO();
        queryDTO.setTenantId(2L);
        queryDTO.setCreateBillStartTime(LocalDateTime.now());
        queryDTO.setCreateBillEndTime(LocalDateTime.now());
        List<FinancialStoreBillVO> list = financialBillMapper.list(queryDTO);
        assertEquals(list.size(), 1);
        assertEquals(list.get(0).getBillNo(), "FB169548481486976");
    }

    @Test
    void listStoreUploadCredentialsTime() {
        StoreBillQueryDTO queryDTO = new StoreBillQueryDTO();
        queryDTO.setTenantId(2L);
        queryDTO.setStoreUploadCredentialsStartTime(LocalDateTime.now());
        queryDTO.setStoreUploadCredentialsEndTime(LocalDateTime.now());
        List<FinancialStoreBillVO> list = financialBillMapper.list(queryDTO);
        assertEquals(list.size(), 1);
        assertEquals(list.get(0).getBillNo(), "FB169548481486976");
    }

    @Test
    void listAuditTime() {
        StoreBillQueryDTO queryDTO = new StoreBillQueryDTO();
        queryDTO.setTenantId(2L);
        queryDTO.setStoreName("不吃香菜1");
        queryDTO.setAuditStartTime(LocalDateTime.now());
        queryDTO.setAuditEndTime(LocalDateTime.now());
        List<FinancialStoreBillVO> list = financialBillMapper.list(queryDTO);
        assertEquals(list.size(), 2);
        assertNotNull(list.get(0).getAuditTime());
//        assertEquals(list.get(0).getBillNo(), "FB169548481486976");
    }
}
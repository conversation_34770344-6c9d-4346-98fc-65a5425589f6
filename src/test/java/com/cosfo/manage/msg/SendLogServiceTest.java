package com.cosfo.manage.msg;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.SendLogFacade;
import com.cosfo.manage.msg.model.req.MessageTipPageDTO;
import com.cosfo.manage.msg.model.req.MsgTipMarkDTO;
import com.cosfo.manage.msg.model.resp.MsgTipVO;
import com.cosfo.manage.msg.service.SendLogService;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.ReadStatusEnum;
import com.cosfo.message.client.req.MsgSendLogQueryReq;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.common.result.CommonResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SendLogServiceTest {

    @Resource
    SendLogService sendLogService;
    @Resource
    SendLogFacade sendLogFacade;

    @Test
    public void page() {
        MessageTipPageDTO messageTipPageDTO = new MessageTipPageDTO();
//        messageTipPageDTO.setReadStatus(ReadStatusEnum.UN_READ.getStatus());
        messageTipPageDTO.setPageIndex(1);
        messageTipPageDTO.setPageSize(10);

        LoginContextInfoDTO merchantInfoDTO = new LoginContextInfoDTO();
        merchantInfoDTO.setTenantId(2L);
        CommonResult<PageInfo<MsgTipVO>> pageInfoCommonResult = CommonResult.ok(sendLogService.page(messageTipPageDTO, merchantInfoDTO));
        System.err.println(pageInfoCommonResult);
    }

    @Test
    public void countUnRead() {
        // 测试接入校验
        MsgSendLogQueryReq msgSendLogQueryReq = new MsgSendLogQueryReq();
        Integer integer = sendLogFacade.countByCondition(msgSendLogQueryReq);

        LoginContextInfoDTO merchantInfoDTO = new LoginContextInfoDTO();
        merchantInfoDTO.setTenantId(2L);

//        CommonResult<Integer> ok = CommonResult.ok(sendLogService.countUnRead(messageTipPageDTO, merchantInfoDTO));
//        System.err.println(ok);
    }

//    @Test
//    public void markBatch() {
//        MsgTipMarkDTO msgTipMarkDTO = new MsgTipMarkDTO();
//        msgTipMarkDTO.setContentType(MessageContentTypeEnum.AFTER_TENANT_NOTIFY.getType());
//        msgTipMarkDTO.setIds(Lists.newArrayList(3635L));
//        msgTipMarkDTO.setReadStatus(ReadStatusEnum.READ.getStatus());
//
//        LoginContextInfoDTO merchantInfoDTO = new LoginContextInfoDTO();
//        merchantInfoDTO.setTenantId(2L);
//        sendLogService.markBatch(msgTipMarkDTO, merchantInfoDTO);
//        System.err.println();
//    }
}

package com.cosfo.manage.file.service.impl;

import com.cosfo.manage.file.controller.BitableController;
import com.cosfo.manage.file.model.dto.SubscribeOfficialWebsiteDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import javax.annotation.Resource;

/**
 * @Author: fansongsong
 * @Date: 2023-12-18
 * @Description:
 */
@SpringBootTest
public class BitableControllerTest {

    @Resource
    private BitableController bitableController;

    @Test
    void subscribeOfficialWebsite() {
        SubscribeOfficialWebsiteDTO subscribeOfficialWebsiteDTO = new SubscribeOfficialWebsiteDTO();
        subscribeOfficialWebsiteDTO.setPhone("13588438641");
        subscribeOfficialWebsiteDTO.setRemark("预约");
        bitableController.subscribeOfficialWebsite(subscribeOfficialWebsiteDTO);
    }
}

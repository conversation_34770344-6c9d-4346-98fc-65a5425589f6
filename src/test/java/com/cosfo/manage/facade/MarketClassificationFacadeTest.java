package com.cosfo.manage.facade;

import com.cofso.item.client.req.MarketClassificationQueryReq;
import com.cofso.item.client.resp.MarketClassificationResp;
import com.cosfo.manage.TestApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class MarketClassificationFacadeTest {
    @Resource
    private MarketClassificationFacade marketClassificationFacade;

    @Test
    void queryClassificationByCondition() {
        MarketClassificationQueryReq req = new MarketClassificationQueryReq();
        req.setTenantId(1003L);
        req.setClassificationIds(Arrays.asList(360L,1586L));
        List<MarketClassificationResp> marketClassificationRespList = marketClassificationFacade.queryClassificationByCondition(req);
        log.error(marketClassificationRespList.toString());
    }
}
package com.cosfo.manage.facade.ordercenter;

import com.cosfo.ordercenter.client.req.OrderNeedDeliveryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderQueryFacadeTest {

    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Test
    void queryById() {
        OrderResp orderResp = orderQueryFacade.queryById(112283L);
        System.out.println(orderResp);
    }

    @Test
    void queryByNo() {
        OrderResp orderResp = orderQueryFacade.queryByNo("OR171047164486917");
        System.out.println(orderResp);
    }

    @Test
    void queryByIds() {
        List<OrderResp> orderResps = orderQueryFacade.queryByIds(Lists.newArrayList(112283L, 112291L));
        System.out.println(orderResps);
    }

    @Test
    void queryByNos() {
        List<OrderResp> orderResps = orderQueryFacade.queryByNos(Lists.newArrayList("OR171049233835629", "OR171047164486917"));
        System.out.println(orderResps);
    }

    @Test
    void queryOrderList() {
        OrderQueryReq req = new OrderQueryReq();
        req.setTenantId(2L);
        req.setBatchSize(10);
        List<OrderResp> orderResps = orderQueryFacade.queryOrderList(req);
        System.out.println(orderResps);
    }

    @Test
    void queryOrderPage() {
        OrderQueryReq queryReq = new OrderQueryReq();
        queryReq.setPageNum(1);
        queryReq.setPageSize(10);
        queryReq.setTenantId(2L);
        PageInfo<OrderResp> orderRespPageInfo = orderQueryFacade.queryOrderPage(queryReq);
        System.out.println(orderRespPageInfo);
    }

    @Test
    void queryNeedDeliveryOrder() {
        OrderNeedDeliveryReq queryReq = new OrderNeedDeliveryReq();
        queryReq.setTenantId(2L);
        queryReq.setCreateTime(LocalDateTime.now().minusDays(2));
        List<String> orderResps = orderQueryFacade.queryNeedDeliveryOrder(queryReq);
        System.out.println(orderResps);
    }
}
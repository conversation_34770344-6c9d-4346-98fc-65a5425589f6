package com.cosfo.manage.facade.compare;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.OrderItemDTO;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.client.service.OrderItemQueryService;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderItemQueryCompareTest {
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    
    @DubboReference
    private OrderItemQueryService orderItemQueryService;

    @Test
    void queryByIdCompareTest() {
        DubboResponse<OrderItemResp> orderItemRespDubboResponse = orderItemQueryProvider.queryById(289322L);
        DubboResponse<OrderItemDTO> orderItemDTODubboResponse = orderItemQueryService.queryById(289322L);
        Assertions.assertEquals(JSON.toJSONString(orderItemRespDubboResponse.getData()), JSON.toJSONString(orderItemDTODubboResponse.getData()));
    }

    @Test
    void queryByIdsCompareTest() {
        DubboResponse<List<OrderItemDTO>> listDubboResponse = orderItemQueryService.queryByIds(Lists.newArrayList(289322L, 289285L));
        DubboResponse<List<OrderItemResp>> listDubboResponse1 = orderItemQueryProvider.queryByIds(Lists.newArrayList(289322L, 289285L));
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryDetailByIdCompareTest() {
        DubboResponse<OrderItemAndSnapshotDTO> orderItemDTODubboResponse = orderItemQueryService.queryDetailById(289322L);
        DubboResponse<OrderItemAndSnapshotResp> orderItemRespDubboResponse = orderItemQueryProvider.queryDetailById(289322L);
        Assertions.assertEquals(JSON.toJSONString(orderItemDTODubboResponse.getData()), JSON.toJSONString(orderItemRespDubboResponse.getData()));
    }

    @Test
    void queryByOrderIdCompareTest() {
        DubboResponse<List<OrderItemAndSnapshotDTO>> listDubboResponse = orderItemQueryService.queryByOrderId(112413L);
        DubboResponse<List<OrderItemAndSnapshotResp>> listDubboResponse1 = orderItemQueryProvider.queryByOrderId(112413L);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryOrderItemListCompareTest() {
        DubboResponse<List<OrderItemDTO>> listDubboResponse = orderItemQueryService.queryOrderItemList(112413L);
        DubboResponse<List<OrderItemResp>> listDubboResponse1 = orderItemQueryProvider.queryOrderItemList(112413L);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryOrderItemListByReqCompareTest() {
        OrderItemQueryReq req = new OrderItemQueryReq();
        req.setTenantId(2L);
        req.setOrderIds(Lists.newArrayList(112413L));
        DubboResponse<List<OrderItemAndSnapshotDTO>> listDubboResponse = orderItemQueryService.queryOrderItemList(req);
        DubboResponse<List<OrderItemAndSnapshotResp>> listDubboResponse1 = orderItemQueryProvider.queryOrderItemList(req);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }
}

package com.cosfo.manage.facade.compare;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderAddressQueryProvider;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.client.service.OrderAddressQueryService;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderAddressQueryCompareTest {
    @DubboReference
    private OrderAddressQueryProvider orderAddressQueryProvider;
    @DubboReference
    private OrderAddressQueryService orderAddressQueryService;

    @Test
    void queryByOrderIdCompareTest() {
        DubboResponse<OrderAddressResp> orderAddressRespDubboResponse = orderAddressQueryProvider.queryByOrderId(2L, 112494L);
        DubboResponse<OrderAddressDTO> orderAddressDTODubboResponse = orderAddressQueryService.queryByOrderId(2L, 112494L);
        Assertions.assertEquals(JSON.toJSONString(orderAddressRespDubboResponse.getData()), JSON.toJSONString(orderAddressDTODubboResponse.getData()));
    }

    @Test
    void queryByOrderIdsCompareTest() {
        DubboResponse<List<OrderAddressResp>> orderAddressRespDubboResponse = orderAddressQueryProvider.queryByOrderIds(2L, Lists.newArrayList(112494L, 112502L));
        DubboResponse<List<OrderAddressDTO>> orderAddressDTODubboResponse = orderAddressQueryService.queryByOrderIds(2L, Lists.newArrayList(112494L, 112502L));
        Assertions.assertEquals(JSON.toJSONString(orderAddressRespDubboResponse.getData()), JSON.toJSONString(orderAddressDTODubboResponse.getData()));
    }
}


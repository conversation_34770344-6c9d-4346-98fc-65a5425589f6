package com.cosfo.manage.facade.compare;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderItemExtraQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemExtraDTO;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import com.cosfo.ordercenter.client.service.OrderItemExtraQueryService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderItemExtraQueryCompareTest {
    @DubboReference
    private OrderItemExtraQueryProvider orderItemExtraQueryProvider;
    @DubboReference
    private OrderItemExtraQueryService orderItemExtraQueryService;

    @Test
    void queryOrderItemExtraListNCompareTest() {
        OrderItemExtraQueryReq req = new OrderItemExtraQueryReq();
        req.setTenantId(2L);
        req.setOrderId(110842L);
        req.setCustomerOrderItemIdList(Lists.newArrayList("**********"));
        DubboResponse<List<OrderItemExtraResp>> listDubboResponse = orderItemExtraQueryProvider.queryOrderItemExtraList(req);
        DubboResponse<List<OrderItemExtraDTO>> listDubboResponse1 = orderItemExtraQueryService.queryOrderItemExtraList(req);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }
}

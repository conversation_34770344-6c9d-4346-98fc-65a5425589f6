package com.cosfo.manage.facade.compare;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.provider.OrderStatisticsQueryProvider;
import com.cosfo.ordercenter.client.req.OrderDetailReq;
import com.cosfo.ordercenter.client.req.OrderSkuSaleReq;
import com.cosfo.ordercenter.client.req.OrderSummaryReq;
import com.cosfo.ordercenter.client.req.SupplierOrderTotalReq;
import com.cosfo.ordercenter.client.resp.*;
import com.cosfo.ordercenter.client.resp.order.OrderDetailResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderSkuQuantityResp;
import com.cosfo.ordercenter.client.resp.order.OrderSummaryResp;
import com.cosfo.ordercenter.client.service.OrderReportService;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderStatisticsQueryCompareTest {

    @DubboReference
    private OrderReportService orderReportService;

    @DubboReference
    private OrderStatisticsQueryProvider orderStatisticsQueryProvider;

    @Test
    void queryOrderSummaryCompareTest() {
        OrderSummaryReq orderSummaryReq = new OrderSummaryReq();
        orderSummaryReq.setTenantId(24592L);
        orderSummaryReq.setStoreIds(Lists.newArrayList(162302L));
        orderSummaryReq.setStartTime(LocalDateTime.parse("2024-03-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSummaryReq.setEndTime(LocalDateTime.parse("2024-03-16 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        DubboResponse<OrderSummaryResp> orderSummaryRespDubboResponse = orderStatisticsQueryProvider.queryOrderSummary(orderSummaryReq);
        DubboResponse<OrderSummaryDTO> orderSummaryDTODubboResponse = orderReportService.queryOrderSummary(orderSummaryReq);
        Assertions.assertEquals(JSON.toJSONString(orderSummaryRespDubboResponse.getData()), JSON.toJSONString(orderSummaryDTODubboResponse.getData()));
    }

    @Test
    void getWaitDeliveryQuantityCompareTest() {
        DubboResponse<Integer> waitDeliveryQuantityResp = orderStatisticsQueryProvider.getWaitDeliveryQuantity(24592L);
        DubboResponse<Integer> waitDeliveryQuantityDTO = orderReportService.getWaitDeliveryQuantity(24592L);
        Assertions.assertEquals(JSON.toJSONString(waitDeliveryQuantityResp.getData()), JSON.toJSONString(waitDeliveryQuantityDTO.getData()));
    }

    @Test
    void querySkuSaleQuantityCompareTest() {
        OrderSkuSaleReq orderSummaryReq = new OrderSkuSaleReq();
        orderSummaryReq.setTenantId(2L);
        orderSummaryReq.setSkuIds(Lists.newArrayList(31969L));
        orderSummaryReq.setStartTime(LocalDateTime.parse("2024-03-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSummaryReq.setEndTime(LocalDateTime.parse("2024-03-16 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        DubboResponse<List<OrderSkuQuantityResp>> orderSkuQuantityRespsResp = orderStatisticsQueryProvider.querySkuSaleQuantity(orderSummaryReq);
        DubboResponse<List<OrderSkuQuantityDTO>> orderSkuQuantityRespsDTO = orderReportService.querySkuSaleQuantity(orderSummaryReq);
        Assertions.assertEquals(JSON.toJSONString(orderSkuQuantityRespsResp.getData()), JSON.toJSONString(orderSkuQuantityRespsDTO.getData()));
    }

    @Test
    void querySkuSaleWithStoreNoQuantityCompareTest() {
        OrderSkuSaleReq orderSummaryReq = new OrderSkuSaleReq();
        orderSummaryReq.setTenantId(2L);
        orderSummaryReq.setSkuIds(Lists.newArrayList(31969L));
        orderSummaryReq.setStartTime(LocalDateTime.parse("2024-03-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSummaryReq.setEndTime(LocalDateTime.parse("2024-03-16 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        DubboResponse<List<OrderSkuQuantityResp>> orderSkuQuantityRespsResp = orderStatisticsQueryProvider.querySkuSaleWithStoreNoQuantity(orderSummaryReq);
        DubboResponse<List<OrderSkuQuantityDTO>> orderSkuQuantityRespsDTO = orderReportService.querySkuSaleWithStoreNoQuantity(orderSummaryReq);
        Assertions.assertEquals(JSON.toJSONString(orderSkuQuantityRespsResp.getData()), JSON.toJSONString(orderSkuQuantityRespsDTO.getData()));
    }

    @Test
    void querySkuSaleWithCityQuantityCompareTest() {
        OrderSkuSaleReq orderSummaryReq = new OrderSkuSaleReq();
        orderSummaryReq.setTenantId(2L);
        orderSummaryReq.setSkuIds(Lists.newArrayList(31969L));
        orderSummaryReq.setStartTime(LocalDateTime.parse("2024-03-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSummaryReq.setEndTime(LocalDateTime.parse("2024-03-16 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        DubboResponse<List<OrderSkuQuantityResp>> orderSkuQuantityRespsResp = orderStatisticsQueryProvider.querySkuSaleWithCityQuantity(orderSummaryReq);
        DubboResponse<List<OrderSkuQuantityDTO>> orderSkuQuantityRespsDTO = orderReportService.querySkuSaleWithCityQuantity(orderSummaryReq);
        Assertions.assertEquals(JSON.toJSONString(orderSkuQuantityRespsResp.getData()), JSON.toJSONString(orderSkuQuantityRespsDTO.getData()));
    }

    @Test
    void queryOrderDetailCompareTest() {
        OrderDetailReq orderDetailReq = new OrderDetailReq();
        orderDetailReq.setOrderIds(Lists.newArrayList(112417L));
        orderDetailReq.setTenantId(2L);
        DubboResponse<List<OrderDetailDTO>> listDubboResponse = orderReportService.queryOrderDetail(orderDetailReq);
        DubboResponse<List<OrderDetailResp>> listDubboResponse1 = orderStatisticsQueryProvider.queryOrderDetail(orderDetailReq);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void querySkuSaleQuantity2CompareTest() {
        DubboResponse<OrderItemSaleResp> orderItemSaleRespDubboResponse = orderStatisticsQueryProvider.querySkuSaleQuantity(Lists.newArrayList(112417L), 2L);
        DubboResponse<OrderItemSaleDTO> orderItemSaleRespDubboResponse1 = orderReportService.querySkuSaleQuantity(Lists.newArrayList(112417L), 2L);
        Assertions.assertEquals(JSON.toJSONString(orderItemSaleRespDubboResponse.getData()), JSON.toJSONString(orderItemSaleRespDubboResponse1.getData()));
    }

    @Test
    void querySupplierOrderSummaryCompareTest() {
        SupplierOrderTotalReq req = new SupplierOrderTotalReq();
        req.setTenantId(2L);
        req.setSupplierIds(Lists.newArrayList(2189L));
        req.setWarehouseType(WarehouseTypeEnum.PROPRIETARY.getCode());
        req.setStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode()));
        DubboResponse<List<SupplierOrderTotalResp>> listDubboResponse = orderStatisticsQueryProvider.querySupplierOrderSummary(req);
        DubboResponse<List<SupplierOrderTotalResp>> listDubboResponse1 = orderReportService.querySupplierOrderSummary(req);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }
}

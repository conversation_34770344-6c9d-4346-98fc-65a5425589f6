package com.cosfo.manage.report.service;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;


@SpringBootTest
class ProductSkuOrderSummaryServiceTest {

    @Resource
    private ProductSkuOrderSummaryService productSkuOrderSummaryService;

    @Test
    void reportOrderSkuQuantity() {
        // OR169666120744658
        productSkuOrderSummaryService.reportOrderSkuQuantity(Lists.newArrayList("OR169666851863589"));
    }

    @Test
    void initProductSkuOrderSummary() {
        productSkuOrderSummaryService.initProductSkuOrderSummary(30);
    }
}
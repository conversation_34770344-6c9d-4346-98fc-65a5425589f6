package com.cosfo.manage.report.repository.impl;

import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.repository.PurchasesBackDetailReportRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class PurchasesBackDetailReportRepositoryImplTest {
    @Resource
    PurchasesBackDetailReportRepository purchasesBackDetailReportRepository;

    @Test
    public void testGetPurchaseBackDetailAgg() throws Exception {
        ReportCommonQueryDTO queryDTO = new ReportCommonQueryDTO();
        queryDTO.setTenantId(2L);
        PurchaseDetailAggVO purchaseBackDetailAgg = purchasesBackDetailReportRepository.getPurchaseBackDetailAgg(queryDTO);
        System.out.println(purchaseBackDetailAgg);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
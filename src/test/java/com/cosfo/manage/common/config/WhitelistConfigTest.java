package com.cosfo.manage.common.config;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.converter.AuthMenuPurviewMapper;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class WhitelistConfigTest {

    @Resource
    private WhitelistConfig whitelistConfig;

    @Test
    void filterTest() {
        List<AuthMenuPurview> authMenuPurviews = JSON.parseArray("[{\"childMenuPurviewVOS\":[{\"childMenuPurviewVOS\":[],\"createTime\":\"2023-11-07 14:23:52\",\"defaultType\":null,\"description\":\"\",\"id\":104741,\"lastUpdater\":\"\",\"menuName\":\"查询\",\"parentId\":104740,\"purviewName\":\"查询\",\"sonPurview\":\"\",\"sonPurviewList\":[],\"systemOrigin\":null,\"type\":1,\"updateTime\":null,\"url\":\"cosfo_manage:product_stock_forewaring:query\",\"weight\":0},{\"childMenuPurviewVOS\":[],\"createTime\":\"2023-11-08 13:35:14\",\"defaultType\":null,\"description\":\"\",\"id\":104750,\"lastUpdater\":\"\",\"menuName\":\"编辑配置\",\"parentId\":104740,\"purviewName\":\"编辑配置\",\"sonPurview\":\"\",\"sonPurviewList\":[],\"systemOrigin\":null,\"type\":1,\"updateTime\":null,\"url\":\"cosfo_manage:product_stock_forewaring:update\",\"weight\":0}],\"createTime\":\"2023-11-07 14:23:35\",\"defaultType\":null,\"description\":\"\",\"id\":104740,\"lastUpdater\":\"\",\"menuName\":\"库存预警\",\"parentId\":744,\"purviewName\":\"\",\"sonPurview\":\"\",\"sonPurviewList\":[],\"systemOrigin\":null,\"type\":0,\"updateTime\":null,\"url\":\"cosfo_manage:product_stock_forewaring:menu\",\"weight\":-2}]", AuthMenuPurview.class);
        List<MenuPurviewDTO> menuPurviewDTOS = whitelistConfig.whitelistFilter(AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewDTOList(authMenuPurviews), 2L);
        System.out.println(menuPurviewDTOS);
    }
}
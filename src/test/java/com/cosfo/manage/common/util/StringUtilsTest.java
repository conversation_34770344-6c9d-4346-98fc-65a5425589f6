package com.cosfo.manage.common.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class StringUtilsTest {

    @Test
    void isStoreName() throws IOException{
        String str1 = "123D有da()-~@. （ ）～ 。";
        log.info("str1:" + str1 + " 命中结果：" + StringUtils.isStoreName(str1));
        String str2 = "123D有da()-~@. （ ）～ 。11111111111111111";
        log.info("str2:" + str2 + " 命中结果：" + StringUtils.isStoreName(str2));
        String str3 = "123()-~@. （ ）～ <>";
        log.info("str3:" + str3 + " 命中结果：" + StringUtils.isStoreName(str3));
        String str4 = "123deAJ蝴蝶";
        log.info("str4:" + str4 + " 命中结果：" + StringUtils.isStoreName(str4));
    }

}


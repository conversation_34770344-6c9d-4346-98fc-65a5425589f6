package com.cosfo.manage.common.task;

import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class AutoBatchDeliveryNotifyTaskTest {

    @Resource
    AutoBatchDeliveryNotifyTask autoBatchDeliveryNotifyTask;

    @Test
    void processResult() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setInstanceParameters("[{\"tenantId\":2,\"warehouseNos\":[197,208]}]");
        autoBatchDeliveryNotifyTask.processResult(xmJobInput);
    }
}
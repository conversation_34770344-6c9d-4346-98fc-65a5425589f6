package com.cosfo.manage.common.task;

import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PosBomTaskTest {

    @Resource
    private PosBomTask posBomTask;

    @Test
    void processResult() throws Exception {
        posBomTask.processResult(new XmJobInput());
    }

    @Test
    void processResultDate() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setInstanceParameters("{\"runDate\":\"2023-12-08\",\"finalRunDate\":\"2021-01-02\"}");
        posBomTask.processResult(xmJobInput);
    }
}
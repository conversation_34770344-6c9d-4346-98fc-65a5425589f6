package com.cosfo.manage.common.task;

import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class ProductStockWarningRefreshTaskTest {

    @Resource
    private ProductStockWarningRefreshTask productStockWarningRefreshTask;

    @Test
    void processResult() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        productStockWarningRefreshTask.processResult(xmJobInput);
    }
}
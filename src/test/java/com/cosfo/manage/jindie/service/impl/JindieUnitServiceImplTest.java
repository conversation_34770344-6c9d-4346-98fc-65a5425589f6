package com.cosfo.manage.jindie.service.impl;

import com.cosfo.manage.jindie.service.JindieUnitService;
import com.kingdee.service.data.entity.MaterialUnitDetailRes;
import com.kingdee.service.data.entity.MaterialUnitListRes;
import com.kingdee.service.data.entity.UnitMaterialUnitListReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Collections;

import static org.junit.Assert.*;

/**
 * 金蝶计量单位服务测试类
 *
 * 注意：运行此测试需要确保以下条件：
 * 1. 已配置好金蝶API的相关参数（ClientApiConfig中的clientId和clientSecret）
 * 2. JindieFacade能够正确获取token
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class JindieUnitServiceImplTest {

    @Resource
    private JindieUnitService jindieUnitService;

    /**
     * 测试获取物料计量单位列表
     */
    @Test
    public void getMaterialUnitList() {
        // 创建查询请求
        UnitMaterialUnitListReq request = new UnitMaterialUnitListReq();

        request.setMaterialId(Collections.singletonList("1805193559695968256"));

        MaterialUnitListRes materialUnitList = jindieUnitService.getMaterialUnitList(request);
        System.out.println(materialUnitList);
    }


}

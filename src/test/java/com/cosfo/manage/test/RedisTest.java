package com.cosfo.manage.test;

import com.cosfo.manage.common.util.MD5Util;
import com.cosfo.manage.common.util.RedisUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.InputStream;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisTest {

    @Resource
    private RedisUtils redisUtils;

    @Test
    public void Test(){
        String password = "123456aaaA@";
        Integer level = MD5Util.passwordLevel(password);
    }
    @Test
    public void Test1(){
        redisUtils.delete("hp2");
    }

    @Test
    public void redisTest(){
        Object o = redisUtils.get("eyJhbGciOiJIUzUxMiJ9.eyJwaG9uZSI6IjEzNzMyMjExMTEyIiwidGVuYW50SWQiOjJ9.L_cA69gVrRK23J1ux5tBBdY7kesT1mxDwwUYGhLNJyGUQ32TUd5mAXs6VohMZ_KAIqvofTz0zZtZzIVKm4HHEg");
        Long expire = redisUtils.getExpire("eyJhbGciOiJIUzUxMiJ9.eyJwaG9uZSI6IjEzNzMyMjExMTEyIiwidGVuYW50SWQiOjJ9.L_cA69gVrRK23J1ux5tBBdY7kesT1mxDwwUYGhLNJyGUQ32TUd5mAXs6VohMZ_KAIqvofTz0zZtZzIVKm4HHEg");
        System.out.println(expire);  // 10412 10742
    }

    @Test
    public void hasKeyTest(){
//        Boolean hasKey = redisUtils.hasKey("eyJhbGciOiJIUzUxMiJ9.eyJwaG9uZSI6IjEzNzMyMjExMTEyIiwiQUNDRVNTX1RPS0VOIjoiK0RsWTAwKzhlWVhOOWdwWFhsblQzZz09IiwidGVuYW50SWQiOjJ9.E0YjoFk1e27Bh4o4xkC7_-CPzJEZ3dvBd8KT6dQaYpf7-IrctnS2KgvUbbiMZgYe09iaI5Qi1A0xx_JXS2Rwow");
//        Assert.isTrue(hasKey,"缓存不存在");

        InputStream inputStream1 = this.getClass().getClassLoader()
                .getResourceAsStream("excel/order.xlsx");
    }

}

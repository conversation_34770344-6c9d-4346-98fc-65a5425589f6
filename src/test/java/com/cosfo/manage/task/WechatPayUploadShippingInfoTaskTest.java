package com.cosfo.manage.task;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.common.context.binlog.RocketMqConstant.Topic;
import com.cosfo.manage.common.task.QueryOpenShippingInfoTask;
import com.cosfo.manage.common.task.WechatPayUploadShippingInfoTask;
import com.cosfo.manage.wechat.api.WxaAPI;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoReq;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoReq.OrderKey;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoReq.Payer;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoReq.Shipping;
import com.cosfo.manage.wechat.bean.wxa.UploadShippingInfoResult;
import com.cosfo.manage.wechat.mapper.WxShippingInfoUploadRecordMapper;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.service.WeixinShippingService;
import com.google.common.collect.Lists;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 *
 * @author: xiaowk
 * @time: 2023/7/10 下午12:39
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WechatPayUploadShippingInfoTaskTest {

    @Autowired
    private WechatPayUploadShippingInfoTask wechatPayUploadShippingInfoTask;
    @Resource
    private PaymentMapper paymentMapper;
    @Autowired
    private QueryOpenShippingInfoTask queryOpenShippingInfoTask;
    @Resource
    private WeixinShippingService weixinShippingService;
    @Resource
    MqProducer mqProducer;
    @Resource
    WxShippingInfoUploadRecordMapper wxShippingInfoUploadRecordMapper;

    @Test
    public void testUploadShippingInfo(){
        String accessToken = "70_xMPZ8_uZk7-cab5j6ZyAheHNT5RDtOEdoCVVe7XAjgYC7M8-MQlSoAY4p4esBmisuGm_2mtUlBBHIaSNZxv5PNXSQwOT54kxV1k9hDj98XpsAUf-nvaQ4RL8za9ganu58eIZMg1L5EQdr3LTHYEdAIDWNT";

        List<String> titleList = Lists.newArrayList("凯宝搓饼粉（曲味）", "曲味11号酱（曲味）");
        String titles = StringUtils.join(titleList.toArray(), ";");
        titles = titles.substring(0, Math.min(120, titles.length()));

        UploadShippingInfoReq req = new UploadShippingInfoReq();
        OrderKey orderKey = new OrderKey();
        orderKey.setOrder_number_type(2);
        orderKey.setTransaction_id("4200001878202307085236820907");
        req.setOrder_key(orderKey);
        req.setLogistics_type(2);
        req.setDelivery_mode(1);
        Shipping shipping = new Shipping();
        shipping.setItem_desc(titles);
        req.setShipping_list(Lists.newArrayList(shipping));
        req.setUpload_time(ZonedDateTime.now().format(DatePattern.createFormatter(DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN)));
        req.setPayer(new Payer("ow4yY5WdiNsToAgdKXUSQzUcqLGo"));
        UploadShippingInfoResult result = WxaAPI.upload_shipping_info(accessToken, req);
        System.err.println("上传发货信息返回：" + JSON.toJSONString(result));
        System.err.println(result == null || !result.successed());
    }

    @Test
    public void testDate(){
        System.err.println(ZonedDateTime.now().format(DatePattern.createFormatter(DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN)));

        List<String> titleList = Lists.newArrayList("凯宝搓饼粉（曲味）", "曲味11号酱（曲味）", "1111", "222", "3333");
        titleList = titleList.subList(0, 2);
        System.err.println(titleList);
    }


    @Test
    public void testJob(){
        XmJobInput jobContext = new XmJobInput();
        jobContext.setJobParameters("{\n"
                + "    \"intervalDays\": 10,\n"
                + "    \"jobStartTime\": \"2023-07-01 10:00:00\",\n"
                + "    \"tenantIds\": [\n"
                + "        2,\n"
                + "        1003\n"
                + "    ]\n"
                + "}");
        try {
            wechatPayUploadShippingInfoTask.processResult(jobContext);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testJob1(){
        XmJobInput jobContext = new XmJobInput();
        try {
            queryOpenShippingInfoTask.processResult(jobContext);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Test
    public void testJob2(){
        Map<String, String> map = new HashMap<>();
        map.put("wx972e012725eb69d3", "70_axME6fKXHTtkeowN9_sjcRSEoNHS9cydri9_mIqvcZXKzxEvqHkazXPwHGLe2fr7ZF4SrK5f01r8dJ7OpXuj1OYi0UL77w2XWe-1Wdt6d5nHjmb9zTkL9sfikG1-PsIf8jrRr1vhfwmB0t_gKXNdAIDWJZ");
        map.put("wx04b2ecc24156baa2", "70_LcGUU9E8euZ5sJftmuxDyfiV2yQ77gzAtej-HMNQuSP2i_LPH3hkB4rv2gigw4VCyyYXxYLMq1-if_ONI5b8exn2dGhZc5q7qj460o9HyBm_QAmCRpA2k_A5iruC9PZr3TTXkth456c6HcdECURgAHDIAX");
        map.put("wx1e88f2858958a34a", "70_fbRFnv9R2iqgYIdJ-Imkq2VRiWGTo0AvUiorgeY2FZNNCbf1rbsKZyzDba5REG3SxlW1lT_g7s3gVuudMOZm0M68I_MxF2uLZVrhaqJmKVSV9QOzzvoNtHqfcdR4wY9DAlMZHaGmyNN4N9OaLWQdAEDZKE");
        map.put("wx96d9740a92cac500", "70_vS95Pt6lqYaDWggv1NIh-pbMf0BVJSegZ9dUZTPDPZUYwcvCSZYMAHfZCGgBt20Uo3VfJK1CHdtnwH1NzS0TYlX4Zv84t3VePiF1cBzDkdaXi26qkSJikzkov9maQoR-vBtxxfSOmifc92s7EQZeAMDOAJ");
        map.put("wxff34cc4751dfa43e", "70_IQyupCOp1MDCjy7IQuS_ympnQuxjmheuik0bb8UGXrnNCp9Pgk5__HW26KN9ZVHxaXi7qVNMnIbux64hvthb6UmKN1PyF0lGKaXTj9HAY9Hmdh4wfPGRGWPCZxbNiELS7bA-porqhpcilQzBYQLhAMDSKF");
        map.put("wxb96ed1025110b062", "70_mwO4IGks3uMb0_ccT2WwQvAMwi43YSB5WCIf25YqL229Ra5zctzsOu9__t-CghNGQI4V7cFhUEj6HpSCXPkXWHBSQ-USzosaX1SHGW7oH8MYUXNTkgECWO1qYajrh5vah93bdDfEQ9IP_hS7ZAOeADDVWK");
        map.put("wxf544f0534bd43f30", "70_FXf2Xlsd8NkhEYzbZyXjyW5XmAeaLgd5A8oXIS9d6n5XErENALTuIAshi0a3qrD5_MPgcQ8gCFG-buNChHp3Ni1z3_BxxBnhIvgMJfQsmGV6kobXVlxiej-mzWuQCZpoNp6kCMav77jfaBI7HRAfAGDPZI");
        map.put("wx269b7b952ae2aa27", "70_D3d6cyT0Y07toMIuscLqkx1flN3AoNMaKeRBGyWRV36Wt_sPD8YqorbC4GsuqzIU3DZa22Yn6H3oQ6scX7Yvn5blymo5tcyRNbEZSuVmWobXvyKQwg68ZfBhDM3mlvkAaZIinREsUKsVxdN2MFPaAGDXQA");
        map.put("wx9dc2021e2f3e76b0", "70_ty-EzVrvafa-B9yGMCfViGizN2_88gH1vSZlhQ8GtaFASUnNRAPX0jhmhQK59dQ71WxE_sqDcdzLyRcqu8gtfH0FLnjSeoaFBYosT_QskEXHMEOPplbq2Ivqk_ZH0nxd2wNBTWF8g3EmCJaMNVLgAEDVDT");
        map.put("wx6d46b37d83842112", "70_hLNrFq-ynLjynDBRdOZG5eu49zrihq1V2fHR1iIdk8Ha_7467LEpsC0Y-saE_CIHP-y_npgSSZ4T2WhGirj3G96UmVkErx6WNUyahHPB-YJLfBXquWhuACM6MSWWGun6V_lmq0b-8K2or1UAFDSiAHDVTI");
        map.put("wx26e105c99f24d6ab", "70_kh1wySuTpqWdw7x4bpT2zmw0tlOams8I2yWlrP0adXp0XOhSIVquDuJ_7gDGSldPZ8xqfR7etCv-Q9FzMSSqZrZmh6x7B3MnK6Gu-F7fRNPFMq131x27CFzcCydYhqXxZ2Bq8_O2lrtbyEukDAGiAGDLIO");
        map.put("wxfd3d940929b06c82", "70_4Ib_YVTn5M8b5Snapi1xqDMQg02RwLzRn-iY74N_z0jJ-Ou4CB1Oc55QdF_S-PdN_TVnubRRJXpz7hry-c_y73YTJKHK37d7QSgfLX61yfvJ-GdF4kHneG6gW9XVjU7v_RXT85CuFL6fzkuwBFBhAIDVZV");
        map.put("wx459614dabbe7a648", "70_d0fELt1DnZyVDjaGoslvE-aNinOO8RW9yevCybkD3naytbEnyugq_KYusr8SRW3lfswdTVtzHSvMbhtdL0WY7VMSLTaNhjW3dKGt4GkQ0mOI-EXdxvRzPu7PCT8c6GqYJG_Nqepvkw5-hiAAYKMcALDRDF");
        map.put("wxf0f61b1582738125", "70_CCiqhljIPz-cNG478h1EzFeT29kMFxR10qZEgy3pFT5SMEHbFL2JB-N2WkIm7w30PMk3yD8ZSbgHzGQChgyZeySrU106A1g8-DKI6DAAKgLD7vGVSR6SS4V4BmRqOyNvYY2Q7PuSl2hK5a4BQNUiAEDWTA");
        map.put("wx3691dd2368d2ad5d", "70_KjlPN2TN2skqt-1oFeZNRE087yjO9kRKntF_0Ta1asvOepTZaCunIZF6tiqJr8kZ8w1ktuD7DW2JTOtEQ5jCOJ8ChuXWmmFES5U5GQ8WEwtk0arsBLVJtlPKG1kkm2_EhHUT7lf7OEmee2sbKKRhAIDGGN");
        map.put("wx47718353b75ef2b4", "70_Dvjpuc05J0UBj1WyFCgxL76bRcSx8vpg4nPD6jo_7qpOs2p4bp7_R566zp4YXSPpfurXppENopCVPTVtfsIKsTGWcTAIUR3pzv-YPzmeJeT4dGsVb_FOUPdu0bLVNecCzLomzV1CPW0AcdTwXFWhALDEMD");
        map.put("wx78d2dc353216c475", "70__spIKMNnwoL39PsIgB2l-e9VrO_Dk9GDAKSjtX2VKeBlT1r4pYkqM7vExq9UzCLN59xzcEbmEzwzrjVreQiLHJO_69Q6LOII4JxdeErPH0LSDvfPjAZTSSdVkih2Ne9DSGxkQ7M0xoj8dHf3OKSeAIDSOW");
        map.put("wx00ba89ceae264560", "70_x3V8n3azQRW1w_Qm55o4m8CMkd9mWuVMzX_DSf_naT9g6czboAdsH5NUrz0jCXPVh_GSsv1IRWRpe99HGVJ53T91WRyhmeob2VSW0rO95JlEiVxyqabFAbEANZNZSBfyp7jlf6-VxNwEyt8AERLfAKDYLF");
        map.put("wx924836b40b0c3108", "70_2uaJphNRT87IAq1SdjHGMimzsyUoUCzSvhmcxXkDozecydWVZrvcurwgHcUi7bbpfChnKN9MGAeyDavv3VszvFpz3rVNiuv2Tjm0UuPwbAWAATW-zvu2sAH0-q-S7r8WEn3qMsiWS_m2eeIUBAOaADDPOC");
        map.put("wxccce8cd0bed97cd1", "70_cUijsfHqWPCxrqERID2W4ViglUS2EyGPr8TTOmYeVwH8GIeISct51pnp_XsUAa4dxY32DPQAptcwBDBIX9z2yMorX1G5-Z3EgVWTt9oHx9Lpfd_V1N6m6wu3jjQomKoWtmntPFuf9Rx1RniwPTDjADDZDN");
        map.put("wx3c0d4412b5154b8e", "70_aiAGp6YYg221DiYBbjZhabCSvWvtTf1U4OOuDKyLfsLWh17vWHqndUIhLl9QZq1eSFflXbbj4Narioe9ihdfou2GLvXyqY3p8ubWJvKTvGa1tmMzO_EGtDUiyHup4J8Mua3cWTZtzfxvSB3CTSJjAHDHGX");
        map.put("wx39e28f2a5333ab6e", "70_T2DJZxnskuS5HGn36ZyAheHNT5RDtOEdoCVVezHMzyavx-K5_o8wuXxi0ksg8Nqyn76cqfVNZ69fFJxW78BEFJfEle8c9RPXuysC46hdvUiSSXKRQjm_3r3QiYMos_POdbUXHw_A8ah-rXmhXTWjALDCYB");
        map.put("wx5825ac84bff8c628", "70_EDJG_2mDX7ULGHlyHRKbxBpXwn3jY68k_i2f28zUWBi375YpW5OVh3s-gaZc3DNuL4nMTnutLmK7tr_ymm8fhtmAFpgMgBnngOLBOwWX1i0AuFPaMhtPq2awUfz65ozQf5tGp1BJxyex9j7SWKWfAIDNBN");
        map.put("wx0492195b84586691", "70_sBeE-3TrpCskNQW-aQoASOUkiEwSt-Xgfgoc3DsT8TErLUgIC7PlKCPhJQ7nfzEhVKb7jeJasLE_2gRb3-6KcPqOkhbAC-P7nw2TFZqW_m8i3pZItyJKd1q9R9NvN7OWG7nCRgYnVoa_AcAoICMiAMDCWG");
        List<WechatAuthorizer> list = new ArrayList<>();
        for (Entry<String, String> entry : map.entrySet()) {
            WechatAuthorizer obj = new WechatAuthorizer();
            obj.setAppId(entry.getKey());
            obj.setAccessToken(entry.getValue());
            obj.setFuncInfo("[17,18,25,30,31,37,40,48,49,65,67,70,71,73,76,81,84,85,86,88,93,99,102,104,105,112,116,117,118,142]");
            list.add(obj);
        }
        queryOpenShippingInfoTask.getOpenShippingTenantIds(list);
    }

    @Test
    public void testChange(){
        List<Long> tenantIds = new ArrayList<>();
        tenantIds.add(2L);
        tenantIds.add(1003L);
        weixinShippingService.changeOpenShippingTenantIds(tenantIds);
    }

    @Test
    public void testQueryOpenShippingTenantIds(){
        System.err.println(weixinShippingService.queryOpenShippingTenantIds());
    }

    @Test
    public void testOrderMqProducer(){
        String msg = "{\"data\":[{\"id\":\"71975\",\"tenant_id\":\"1003\",\"store_id\":\"3384\",\"account_id\":\"3339\",\"supplier_tenant_id\":\"1003\",\"order_no\":\"*****************\",\"warehouse_type\":\"0\",\"payable_price\":\"2.0\",\"delivery_fee\":\"0.0\",\"total_price\":\"2.0\",\"status\":\"11\",\"pay_type\":\"2\",\"pay_time\":\"2023-07-12 13:48:40\",\"create_time\":\"2023-07-12 13:48:40\",\"remark\":\"\",\"apply_end_time\":\"48\",\"auto_finished_time\":\"7\",\"combine_order_id\":\"-1\",\"order_type\":\"0\"}],\"database\":\"cosfodb\",\"es\":*************,\"id\":1741,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint\",\"tenant_id\":\"bigint\",\"store_id\":\"bigint\",\"account_id\":\"bigint\",\"supplier_tenant_id\":\"bigint\",\"order_no\":\"varchar(50)\",\"warehouse_type\":\"tinyint\",\"payable_price\":\"decimal(12,2)\",\"delivery_fee\":\"decimal(12,2)\",\"total_price\":\"decimal(12,2)\",\"status\":\"tinyint\",\"pay_type\":\"tinyint\",\"online_pay_channel\":\"int\",\"pay_time\":\"datetime\",\"delivery_time\":\"datetime\",\"finished_time\":\"datetime\",\"create_time\":\"datetime\",\"update_time\":\"datetime\",\"remark\":\"varchar(255)\",\"apply_end_time\":\"int\",\"auto_finished_time\":\"int\",\"warehouse_no\":\"varchar(50)\",\"combine_order_id\":\"bigint\",\"order_type\":\"tinyint\",\"begin_delivery_time\":\"datetime\",\"profit_sharing_finish_time\":\"datetime\"},\"old\":[{\"status\":\"10\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"tenant_id\":-5,\"store_id\":-5,\"account_id\":-5,\"supplier_tenant_id\":-5,\"order_no\":12,\"warehouse_type\":-6,\"payable_price\":3,\"delivery_fee\":3,\"total_price\":3,\"status\":-6,\"pay_type\":-6,\"online_pay_channel\":4,\"pay_time\":93,\"delivery_time\":93,\"finished_time\":93,\"create_time\":93,\"update_time\":93,\"remark\":12,\"apply_end_time\":4,\"auto_finished_time\":4,\"warehouse_no\":12,\"combine_order_id\":-5,\"order_type\":-6,\"begin_delivery_time\":93,\"profit_sharing_finish_time\":93},\"table\":\"order\",\"ts\":*************,\"type\":\"UPDATE\"}";
        mqProducer.send(Topic.MYSQL_BINLOG_SAAS, "order", msg);
    }


    @Test
    public void testMapper(){
        System.err.println(wxShippingInfoUploadRecordMapper.selectByTransactionId("4200001858202307112006232241"));
    }

    @Test
    public void testUpload(){
        weixinShippingService.uploadShippingInfoByOrderId(120890L, 2L);
    }
}

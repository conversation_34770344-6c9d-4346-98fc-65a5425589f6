package com.cosfo.manage.marketing.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.manage.marketing.model.po.ItemSaleLimitConfig;
import com.cosfo.manage.marketing.repository.ItemSaleLimitConfigRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ItemSaleLimitConfigServiceImplTest {

    @Mock
    private ItemSaleLimitConfigRepository mockItemSaleLimitConfigRepository;

    @InjectMocks
    private ItemSaleLimitConfigServiceImpl itemSaleLimitConfigServiceImplUnderTest;

    @Test
    void testQueryItemSaleLimitConfigMap() {
        // Setup
        final Map<Long, ItemSaleLimitConfigDTO> expectedResult = new HashMap<>();

        // Configure ItemSaleLimitConfigRepository.queryByTenantIdAndItemIdList(...).
        final ItemSaleLimitConfig itemSaleLimitConfig = new ItemSaleLimitConfig();
        itemSaleLimitConfig.setId(0L);
        itemSaleLimitConfig.setTenantId(0L);
        itemSaleLimitConfig.setMarketItemId(0L);
        itemSaleLimitConfig.setSaleLimitQuantity(0);
        itemSaleLimitConfig.setSaleLimitRule(0);
        final List<ItemSaleLimitConfig> itemSaleLimitConfigs = Arrays.asList(itemSaleLimitConfig);
        when(mockItemSaleLimitConfigRepository.queryByTenantIdAndItemIdList(0L, Arrays.asList(0L)))
                .thenReturn(itemSaleLimitConfigs);

        // Run the test
        final Map<Long, ItemSaleLimitConfigDTO> result = itemSaleLimitConfigServiceImplUnderTest.queryItemSaleLimitConfigMap(
                0L, Arrays.asList(0L));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryItemSaleLimitConfigMap_ItemSaleLimitConfigRepositoryReturnsNoItems() {
        // Setup
        final Map<Long, ItemSaleLimitConfigDTO> expectedResult = new HashMap<>();
        when(mockItemSaleLimitConfigRepository.queryByTenantIdAndItemIdList(0L, Arrays.asList(0L)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, ItemSaleLimitConfigDTO> result = itemSaleLimitConfigServiceImplUnderTest.queryItemSaleLimitConfigMap(
                0L, Arrays.asList(0L));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryItemSaleLimitConfig() {
        // Setup
        final ItemSaleLimitConfigDTO expectedResult = ItemSaleLimitConfigDTO.builder().build();

        // Configure ItemSaleLimitConfigRepository.queryByTenantIdAndItemIdList(...).
        final ItemSaleLimitConfig itemSaleLimitConfig = new ItemSaleLimitConfig();
        itemSaleLimitConfig.setId(0L);
        itemSaleLimitConfig.setTenantId(0L);
        itemSaleLimitConfig.setMarketItemId(0L);
        itemSaleLimitConfig.setSaleLimitQuantity(0);
        itemSaleLimitConfig.setSaleLimitRule(0);
        final List<ItemSaleLimitConfig> itemSaleLimitConfigs = Arrays.asList(itemSaleLimitConfig);
        when(mockItemSaleLimitConfigRepository.queryByTenantIdAndItemIdList(0L, Arrays.asList(0L)))
                .thenReturn(itemSaleLimitConfigs);

        // Run the test
        final ItemSaleLimitConfigDTO result = itemSaleLimitConfigServiceImplUnderTest.queryItemSaleLimitConfig(0L, 0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryItemSaleLimitConfig_ItemSaleLimitConfigRepositoryReturnsNoItems() {
        // Setup
        final ItemSaleLimitConfigDTO expectedResult = ItemSaleLimitConfigDTO.builder().build();
        when(mockItemSaleLimitConfigRepository.queryByTenantIdAndItemIdList(0L, Arrays.asList(0L)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final ItemSaleLimitConfigDTO result = itemSaleLimitConfigServiceImplUnderTest.queryItemSaleLimitConfig(0L, 0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSaveItemSaleLimitConfig() {
        // Setup
        final ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = ItemSaleLimitConfigDTO.builder().build();
        when(mockItemSaleLimitConfigRepository.saveOrUpdate(any(ItemSaleLimitConfig.class),
                any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        final boolean result = itemSaleLimitConfigServiceImplUnderTest.saveItemSaleLimitConfig(itemSaleLimitConfigDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testSaveItemSaleLimitConfig_ItemSaleLimitConfigRepositoryReturnsTrue() {
        // Setup
        final ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = ItemSaleLimitConfigDTO.builder().build();
        when(mockItemSaleLimitConfigRepository.saveOrUpdate(any(ItemSaleLimitConfig.class),
                any(LambdaUpdateWrapper.class))).thenReturn(true);

        // Run the test
        final boolean result = itemSaleLimitConfigServiceImplUnderTest.saveItemSaleLimitConfig(itemSaleLimitConfigDTO);

        // Verify the results
        assertThat(result).isTrue();
    }
}

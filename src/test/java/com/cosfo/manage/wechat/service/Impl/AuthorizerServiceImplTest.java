package com.cosfo.manage.wechat.service.Impl;

import com.cosfo.manage.common.util.RedisUtils;
import com.cosfo.manage.wechat.model.dto.PreAuthCodeDto;
import com.cosfo.manage.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.manage.wechat.service.AuthorizerService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class AuthorizerServiceImplTest {

    @Resource
    private AuthorizerService authorizerService;
    @Resource
    private RedisUtils redisUtils;


    @Test
    void authorized() {

        //{"pre_auth_code":"preauthcode@@@8nrtFCXJ5YzsOOme-pJ75J2a5L8QwoZpJGzp2tdZgseg2k9oKhAku10dSKOUff-W7S78tv2BcisZZbT1J1mDCA","expires_in":1800}

        PreAuthCodeDto preAuthCodeDto = new PreAuthCodeDto();
        preAuthCodeDto.setPreAuthCode("zjc-123-abc");
        preAuthCodeDto.setTenantId(24457L);


        redisUtils.set(preAuthCodeDto.getPreAuthCode(),preAuthCodeDto,1800);

        WechatAuthorizerDto authorizerDto = new WechatAuthorizerDto();
        WechatAuthorizer authorized = authorizerService.authorized(authorizerDto, "wxf6672acab7524893", "123", "zjc-123-abc");
        System.out.println(authorized);
    }
}